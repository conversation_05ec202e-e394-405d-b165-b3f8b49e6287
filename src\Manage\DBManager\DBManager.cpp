﻿#include <QFile>
#include <memory>

#include "base.h"
#include "DebugLogManager.h"
#include "DBManager.h"
#include "LoopHistoryInterface.h"
#include "AlarmLimitInterface.h"
#include "SystemConfigInterface.h"
#include "SystemSettingInterface.h"

#include "SystemSettingManager.h"
#include "SystemConfigManager.h"

#include "ModuleTestManager.h"

#include "HistoryEventManager.h"

#define DATABASE_NAME QStringLiteral("./Baige.db")
#define DATABASE_TYPE QStringLiteral("QSQLITE")
#define DB_VERSION    QStringLiteral("0.1")
#define COUNT_QUERY_STR QStringLiteral("SELECT count(*) FROM %1")

#define INSERT_CMD_STR QStringLiteral("INSERT INTO %1 (DbVersion,CurSoftwareVersion) VALUES (\"%2\",\"%3\");")

DBManager::DBManager()
{
    for (int i =static_cast<int>(TABLE_ENUM::TABLE_ENUM_BEGIN); i < static_cast<int>(TABLE_ENUM::TABLE_ENUM_END); i++)
        gMappingTableNameToTableCreateQuery.insert(gTableNameMap[static_cast<TABLE_ENUM>(i)], gTableCreateList[i]);
}
bool isDosonthing= true;
DBManager* DBManager::GetInstance()
{
    static DBManager mObject;
    return &mObject;
}

bool DBManager::InitDB()
{
    QFile dbFile(DATABASE_NAME);
    bool IsFirstCreatTable = false;
    if (!dbFile.exists())
    {
        DebugLog << "Database file not exist";
    }

    mDatabaseConnection = QSqlDatabase::addDatabase(DATABASE_TYPE);
    mDatabaseConnection.setDatabaseName(DATABASE_NAME);

    if (!mDatabaseConnection.open())
    {
        DebugLog << "open db fail";

        return false;
    }
    else
    {
        QSqlQuery query(mDatabaseConnection);
        query.exec("PRAGMA synchronous = EXTRA;");
        DebugLog << "open db success";
    }


    QStringList curTableInDB = GetAllTableNameInDB();
    foreach(const QString &table, gTableNameMap)
    {
        //如果在数据库中现存的表中未找到 就创建一个
        if(curTableInDB.indexOf(table) == -1)
        {
            DebugLog << "missing table: " << table;
            CreateTable(table);
            IsFirstCreatTable = true;

        }
    }
    DetectOverflow();

    if(IsFirstCreatTable)
    {
        ModuleTestManager::GetInstance()->UpdateModuleState(TEST_DATABASE, E_MODULE_STATE::DISABLE);
    }
    else
    {
        ModuleTestManager::GetInstance()->UpdateModuleState(TEST_DATABASE, E_MODULE_STATE::ABLE);
    }
    QStringList allTbName =mDatabaseConnection.tables();
    for(auto ite:allTbName)
    {
        GetDBTableModel(ite);
    }
    return true;
}
QSqlTableModel* DBManager::GetDBTableModel(QString tableName)
{
    if(!mAllSqlTable.contains(tableName))
    {
        DebugLog << "Not Find Table Name:" << tableName;
        QSqlTableModel* tbModel(new QSqlRelationalTableModel(this, mDatabaseConnection));
        mAllSqlTable[tableName] = tbModel;
        tbModel->setTable(tableName);
        tbModel->select();
    }
    return mAllSqlTable.value(tableName,NULL);
}

/* *
 * @brief DBManager::CreateTable 创建一个数据表 返回创建成功与否
 * @param tableName
 * @return
   */
bool DBManager::CreateTable(const QString& tableName)
{
    QSqlQuery query(mDatabaseConnection);

    if (query.exec(gMappingTableNameToTableCreateQuery[tableName]))
    {
        DebugLog << "Table" << tableName << "create success.";
        if (gTableNeedToInitAfterCreate.indexOf(static_cast<TABLE_ENUM>(gTableNameMap.key(tableName, TABLE_ENUM_END))) != -1)
            InitialDataTable(tableName);

        return true;
    }
    else
    {
        DebugLog << "Table" << tableName << "create fail.";
        DebugLog << query.lastQuery();
        return false;
    }
}


/* *
 * @brief DBManager::InitialDataTable 某些表需要在创建成功后给入初始化值
 * @param tableName 表名
 * @return bool 初始化成功与否
   */
bool DBManager::InitialDataTable(QString tableName)
{
    int startIndex = 0;
    int endIndex   = 0;

    DebugLog << "Init table " << tableName;

    QMetaEnum metaEnum;

    if (tableName == "SystemConfig")
    {
        startIndex = 0;
        endIndex   = static_cast<int>(SystemConfigManager::SYSTEM_CONFIG_ENUM_END);
        metaEnum = QMetaEnum::fromType<SystemConfigManager::SYSTEM_CONFIG_ENUM>();
    }
    else if (tableName == "SystemSetting")
    {
        startIndex = 0;
        endIndex   = static_cast<int>(SystemSettingManager::SYSTEM_SETTING_ENUM_END);
        metaEnum = QMetaEnum::fromType<SystemSettingManager::SYSTEM_SETTING_ENUM>();
    }
    else if (tableName == "AlarmLimit")
    {
        startIndex = static_cast<int>(ALARM_ID_ENUM::VALUEDATA_ENUM_BEGIN);
        endIndex   = static_cast<int>(ALARM_ID_ENUM::VALUEDATA_ENUM_END);
    }

    QSqlTableModel* tableModel = (DBManager::GetInstance()->GetDBTableModel(tableName));
    if (tableModel == nullptr)
        return false;

    QSqlRecord record = tableModel->record();
    for(int i = startIndex; i < endIndex; i++)
    {
        if (tableName == QStringLiteral("SystemConfig"))
        {
            record.setValue("ConfigId",  metaEnum.key(i));
            record.setValue("Value",
                            SystemConfigManager::ReadDefaultValue(static_cast<SystemConfigManager::SYSTEM_CONFIG_ENUM>(i)));
        }
        else if (tableName == "SystemSetting")
        {
            record.setValue("SettingId", metaEnum.key(i));
            record.setValue("Value",
                            SystemSettingManager::GetDefaultValue(static_cast<SystemSettingManager::SYSTEM_SETTING_ENUM>(i)));
        }
        else if (tableName == "AlarmLimit")
        {
            record.setValue("DataType", gAlarmIdNameTable[i]);
            record.setValue("LowLimit",  gAlarmLimitDefaultValues[i].first);
            record.setValue("HighLimit", gAlarmLimitDefaultValues[i].second);
        }

        if (!tableModel->insertRecord(tableModel->rowCount(), record))
        {
            DebugLog << tableName << "initial error";
            return false;
        }
    }

    return true;
}

//删除数据表中超出所设数量的数据
bool DBManager::DetectOverflow()
{
    const static QHash<TABLE_ENUM, int> gTableNumLimitMap
    {
        {TABLE_ENUM::ALARM_LIMIT,                         1000        }          ,
        {TABLE_ENUM::TREND_HISTORY_DATA,         10000                     }           ,
        {TABLE_ENUM::MACHINE_HISTORY,                1000                 }           ,
        {TABLE_ENUM::LOOP_HISTORY,                        30            }           ,
        {TABLE_ENUM::SYSTEM_SETTING,                   1000              }           ,
        {TABLE_ENUM::SYSTEM_CONFIG,                     1000              }           ,
        {TABLE_ENUM::DB_INFO,                                  2       }           ,
        {TABLE_ENUM::HISTORY_EVENT,                 3000}
    };

    const QString deleteQueryStr = QStringLiteral("DELETE FROM %1 WHERE DefaultPK IN (SELECT DefaultPK FROM %1 ORDER BY DefaultPK LIMIT %2);");

    QSqlQuery query(mDatabaseConnection);

    QStringList curTableInDB = GetAllTableNameInDB();
    for (auto &&tableName : curTableInDB)
    {
        auto cnt = GetRowNums(gTableNameMap.key(tableName));

        if (cnt == -1)
            continue;
        if (gTableNumLimitMap[gTableNameMap.key(tableName)])
        {
            if (cnt >= gTableNumLimitMap[gTableNameMap.key(tableName)])
            {
                auto lineCount = cnt - gTableNumLimitMap[gTableNameMap.key(tableName)];
                QString tmpStr = deleteQueryStr.arg(tableName, QString::number(lineCount));
                query.exec(tmpStr);
            }
        }
    }

    return true;
}

bool DBManager::GetVersionIsChange()
{
    QSqlTableModel* tableModel(GetDBTableModel("DbInfo"));

    tableModel->select();
    while (tableModel->canFetchMore())
        tableModel->fetchMore();

    if (tableModel->rowCount() != 0)
    {
        QString dbVer, softVerInDb;

        QSqlRecord rec = tableModel->record(tableModel->rowCount() - 1);
        if(!rec.isEmpty())
        {
            dbVer = rec.value(0).toString();
            softVerInDb = rec.value(1).toString();
        }

        if (softVerInDb != DEV_SOFT_VERSION)
        {
            QString queryStr = INSERT_CMD_STR.arg("DbInfo")
                    .arg(DB_VERSION)
                    .arg(DEV_SOFT_VERSION);
            QueryDB(queryStr);

            return true;
        }
    }
    else //db表为空
    {
        QString queryStr = INSERT_CMD_STR.arg("DbInfo")
                .arg(DB_VERSION)
                .arg(DEV_SOFT_VERSION);
        QueryDB(queryStr);

        return true;
    }

    return false;
}

void DBManager::MoveToThread()
{
    start();
}



void DBManager::run()
{
    QSqlQuery query(mDatabaseConnection);
    while(1)
    {
        mDatabaseConnection.transaction();
        while(!mCmdStrList.isEmpty())
        {
            QString cmdStr = mCmdStrList.first();
            mMutex.lock();
            mCmdStrList.removeFirst();
            mMutex.unlock();
            bool ExecResult = query.exec(cmdStr);
            if(!ExecResult)
            {
                DebugLog << cmdStr;
                DebugLog << query.lastError().text();
            }
            DebugAssert(ExecResult);
        }
        mDatabaseConnection.commit();
        msleep(20);
    }
}

void DBManager::QueryDB(const QString &cmd)
{
    if(!cmd.isEmpty())
    {
        mMutex.lock();
        mCmdStrList.push_back(cmd);
        mMutex.unlock();
    }
}

int DBManager::GetRowNums(TABLE_ENUM tableEnum)
{
    if (gTableNameMap.contains(tableEnum))
    {
        QString tableName = gTableNameMap.value(tableEnum);
        QSqlQuery query(mDatabaseConnection);

        query.exec(COUNT_QUERY_STR.arg(tableName));
        query.next();

        return query.value(0).toInt();
    }

    return -1;
}

/* *
 * @brief DBManager::GetAllTableNameInDB 获得当前数据库中现存的表名
 * @return 现存的表名字符串列表
   */
QStringList DBManager::GetAllTableNameInDB()
{
    return mDatabaseConnection.tables();
}


