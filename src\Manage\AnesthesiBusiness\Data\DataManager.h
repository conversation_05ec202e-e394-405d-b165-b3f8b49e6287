﻿#ifndef DATAMANAGER_H
#define DATAMANAGER_H

#include <QObject>
#include <QVector>
#include <QMap>
#include <QDateTime>

#include "WaveDataModel.h"
#include "UnitManager.h"
#include "protocol.h"

//--
extern int convert_IE_value_2_percent(short & percent, short value);
//------

enum MONITOR_PARAM_TYPE
{
    VALUEDATA_BEGIN,
    VALUEDATA_VTE = VALUEDATA_BEGIN,
    VALUEDATA_VTI,
    VALUEDATA_MV,
    VALUEDATA_RATE,
    VALUEDATA_R,
    VALUEDATA_C,
    VALUEDATA_C20C,
    VALUEDATA_FSPN,
    VALUEDATA_MVSPN,
    VALUEDATA_IE,

    VALUEDATA_PPEAK,
    VALUEDATA_TOP_PAW,
    VALUEDATA_PPLAT,
    VALUEDATA_PEEP,
    VALUEDATA_PMEAN,

    VALUEDATA_FIO2,
    VALUEDATA_FICO2,
    VALUEDATA_ETCO2,
    VALUEDATA_FIN2O,
    VALUEDATA_ETN2O,


    VALUEDATA_FI_AA1,
    VALUEDATA_FI_AA2,

    VALUEDATA_FIHAL,
    VALUEDATA_FIENF,
    VALUEDATA_FISEV,
    VALUEDATA_FIISO,
    VALUEDATA_FIDES,

    VALUEDATA_ET_AA1,
    VALUEDATA_ET_AA2,

    VALUEDATA_ETHAL,
    VALUEDATA_ETENF,
    VALUEDATA_ETSEV,
    VALUEDATA_ETISO,
    VALUEDATA_ETDES,

    VALUEDATA_MAC,	//tbd
    VALUEDATA_SPO2, //血氧参数
    VALUEDATA_PR,

    VALUEDATA_FLOWMETER_O2,
    VALUEDATA_FLOWMETER_AIR,
    VALUEDATA_FLOWMETER_N2O,
    VALUEDATA_MAX,
};
//Q_ENUM(MONITOR_PARAM_TYPE);

enum class VALUE_STATUS_ENUM
{
    VALUE_VALIDE,
    VALUE_EXPIRED
};

struct DataInfoSt
{
    short mValue;
    int mTimestamp;
};

struct MonitorParamInfoSt
{
    int mParamNameId;
    UNIT_TYPE mDefaultUnit;
    CONVERTIBLE_UNIT_CATEGORY mConvertibleUnitCategory;
    short mMaxValidValue;
    short mMinValidValue;
    short mExponent;
};

struct WaveParamInfoSt
{
    int mParamNameId;
    UNIT_TYPE mDefaultUnit;
    CONVERTIBLE_UNIT_CATEGORY mConvertibleUnitCategory;
    short mMaxValidValue;
    short mMinValidValue;
};

class DataManager : public QObject
{
    Q_OBJECT
public:
    enum WAVE_PARAM_TYPE
    {
        WAVE_PAW = 0,
        WAVE_FLOW,
        WAVE_VOL,
        WAVE_CO2,
        WAVE_MAX
    };

public:
    static DataManager* GetInstance();

    void ResetAllParam();
    static int GetParamNameId(int id);
    static QString GetParamName(MONITOR_PARAM_TYPE paramType);
    static UNIT_TYPE GetDefaultUnit(MONITOR_PARAM_TYPE paramType);
    static QString GetParamCurUnitStr(MONITOR_PARAM_TYPE paramType);
    static UNIT_TYPE GetParamCurUnit(MONITOR_PARAM_TYPE paramType);
    static CONVERTIBLE_UNIT_CATEGORY GetParamUnitCategory(MONITOR_PARAM_TYPE paramType);
    static qreal RevertToFloat(int id, int value);

    static QString ConvertIeValueToStr(short value);
    static int ConvertIeValueToPct(short & percent, short value);
    VALUE_STATUS_ENUM GetMonitorData(int id, short &value);
    void SaveMonitorData(int id, int value);
    bool GetMonitorTypeEnable(int id);

    qreal GetWaveData(WAVE_PARAM_TYPE dataType);
    void UpdateWaveData(WAVE_PARAM_TYPE dataType, short data, bool isSpont);
    static QString GetParamName(WAVE_PARAM_TYPE);
    static UNIT_TYPE GetDefaultUnit(WAVE_PARAM_TYPE paramType);
    static QString GetParamCurUnit(WAVE_PARAM_TYPE paramType);
    static WaveParamInfoSt GetWaveParamDefaultInfo(WAVE_PARAM_TYPE paramType);
    bool GetWaveTypeEnable(int id);
    QPair<int, int> GetRange(int id);

signals:
    void SignalWaveDataChanged(int dataType, qreal data, bool);

private slots:
    void SlotOnDateTimeChanged(QDateTime, QDateTime);

private:
    explicit DataManager(QObject *parent =NULL);
    DataManager(const DataManager&) = delete;
    DataManager& operator=(const DataManager&) = delete;
    ~DataManager(){}

    void RefurbishParamEnable();
    void TimeRepair(QDateTime);

private:
    static const QMap<WAVE_PARAM_TYPE, WaveParamInfoSt> sWaveParamInfoMap;
    static const QMap<MONITOR_PARAM_TYPE, MonitorParamInfoSt> sParamDefaultMap;

    QMap<MONITOR_PARAM_TYPE, DataInfoSt> mMonitorDataMap{};
    QMap<MONITOR_PARAM_TYPE, bool> mMonitorParamEnableMap{};

    QMap<WAVE_PARAM_TYPE, qreal> mWaveDataMap{};
    QMap<WAVE_PARAM_TYPE, bool> mWaveParamEnableMap{};
};

#endif // DATAMANAGER_H
