#ifndef __ALARMMANAGER_H__
#define __ALARMMANAGER_H__

#include "AlarmMenu.h"
#include "String/UIStrings.h"
enum E_ALARM_ID
{
    ALARM_BEGIN=0,

    PHY_ALARM_BEGIN  = ALARM_BEGIN,		/*  通过判断次数来进行报警  */
    ALARM_FIO2_HIGH,    ALARM_FIO2_LOW,
    ALARM_PR_HIGH,      ALARM_PR_LOW,
    ALARM_MV_HIGH,		ALARM_MV_LOW,
    ALARM_RATE_HIGH,	ALARM_RATE_LOW,
    ALARM_VTE_HIGH,		ALARM_VTE_LOW,

    ALARM_FICO2_HIGH,	ALARM_FICO2_LOW,
    ALARM_ETCO2_HIGH,	ALARM_ETCO2_LOW,
    ALARM_FIN2O_HIGH,	ALARM_FIN2O_LOW,
    ALARM_ETN2O_HIGH,	ALARM_ETN2O_LOW,
    ALARM_SPO2_HIGH,	ALARM_SPO2_LOW,

    ALARM_FI_AGENT1_HIGH,ALARM_FI_AGENT1_LOW,
    ALARM_FI_AGENT2_HIGH,ALARM_FI_AGENT2_LOW,
    ALARM_FIHAL_HIGH,	ALARM_FIHAL_LOW,
    ALARM_FIENF_HIGH,	ALARM_FIENF_LOW,
    ALARM_FISEV_HIGH,	ALARM_FISEV_LOW,
    ALARM_FIISO_HIGH,	ALARM_FIISO_LOW,
    ALARM_FIDES_HIGH,	ALARM_FIDES_LOW,
    ALARM_ET_AGENT1_HIGH,ALARM_ET_AGENT1_LOW,
    ALARM_ET_AGENT2_HIGH,ALARM_ET_AGENT2_LOW,
    ALARM_ETHAL_HIGH,	ALARM_ETHAL_LOW,
    ALARM_ETENF_HIGH,	ALARM_ETENF_LOW,
    ALARM_ETSEV_HIGH,	ALARM_ETSEV_LOW,
    ALARM_ETISO_HIGH,	ALARM_ETISO_LOW,
    ALARM_ETDES_HIGH,	ALARM_ETDES_LOW,

    ALARM_PAW_LOW,		ALARM_PAW_HIGH,
    ALARM_APNEA,
    ALARM_APNEA_120,  //呼吸窒息超过120
    ALARM_FLOW_SENSOR_ERR,//流量传感器错误
    ALARM_PRESS_HIGH_CONTINUA,
    ALARM_PRESS_LOW_CONTINUA,
    ALARM_NEGATIVE_PRESS,
    //流量计流速监控
    ALARM_O2_FLOW_HIGH,
    ALARM_N2O_FLOW_HIGH,
    ALARM_AIR_FLOW_HIGH,
    ALARM_FLOW_HIGH,			// 补充总流量报警 2022-02-9
    ALARM_O2_N2O_UNUSUAL_RATIO,
    PHY_ALARM_END,

    TECHALARM_BEGIN=PHY_ALARM_END,
    TECHALARM_BATTERY_LOW= TECHALARM_BEGIN,
    TECHALARM_FLOWMETER_O2_SENSOR_NO_CAL_DATA,
    TECHALARM_FLOWMETER_N2O_SENSOR_NO_CAL_DATA,
    TECHALARM_FLOWMETER_AIR_SENSOR_NO_CAL_DATA,
    TECHALARM_FLOW_TOO_LOW,
    TECHALARM_O2_FLOW_INEXACT,
    TECHALARM_BANLANCE_FLOW_INEXACT,

    TECHALARM_O2_PRESS_LOW,

    TECHALARM_N2O_PRESS_LOW,
    TECHALARM_AIR_PRESS_LOW,
    TECHALARM_BACKUP_FLOW_CONTROL_OPEN,   //后备流量控制系统针阀打开

    TECHALARM_BATTERYEMPTY,  //电池耗尽
    TECHALARM_NOAC,
    TECHALARM_VENT_STATE_ACGO,
    TECHALARM_GAS_LOOP_ERR,
    TECHALARM_GASSOURCE_LOW,
    TECHALARM_GASSOURCE_O2_LOW,
    TECHALARM_ADDO2_OVERTIME,
    TECHALARM_ADDO2_ERR,
    TECHALARM_BYPASS_ERR,
    TECHALARM_FAN_STOP,                //风扇故障
    TECHALARM_O2_SENSOR_ERR,
    TECHALARM_MINOTORING_MODULE_ERR,   //新增报警2011-10-28
    TECHALARM_PRESSURE_SENSOR_ERR,     //新增报警2011-10-28
    TECHALARM_EXP_VALVE_ERR,		   //新增报警2011-10-28
    TECHALARM_EXP_VALVE_CLOSE_OFF,    //新增报警2011-10-28
    TECHALARM_INSP_VALVE_ERR,          //新增报警2011-10-28
    TECHALARM_HEATING_MODULE_ERR,      //新增报警2011-10-28
    TECHALARM_AG_ADAPTOR_ERR,      //新增报警2011-10-28
    TECHALARM_AG_SAMPLING_ERR,
    TECHALARM_CO2_ADAPTOR_ERR,
    TECHALARM_CO2_SAMPLING_ERR,

    TECHALARM_HLM_TIME_UP,			//HLM模式下倒计时报警

    TECHALARM_SPO2_BREAKOFF,

    TECHALARM_AX_OUT_OF_RANGE,
    TECHALARM_CO2_OUT_OF_RANGE,
    TECHALARM_N2O_OUT_OF_RANGE,

    TECHALARM_TEM_OUT_OF_RANGE,
    TECHALARM_PRESS_OUT_OF_RANGE,
    TECHALARM_AG_NEED_RECAIL,
    TECHALARM_AG_MIX_AX,
    TECHALARM_AG_DATA_UNTRUSTABLE,

    TECHALARM_AG_SW_ERR,
    TECHALARM_AG_HW_ERR,
    TECHALARM_AG_MOTO_ERR,
    TECHALARM_AG_LOST_CAIL,
    TECHALARM_AG_REPLACE,
    TECHALARM_CO2_REPLACE,
    TECHALARM_AG_SAMPLING_CLOGGED,
    TECHALARM_CO2_SAMPLING_CLOGGED,
    TECHALARM_DB_FAIL,
    TECHALARM_END,

    PROMPT_BEGIN=TECHALARM_END,
    PROMPT_PHY_ALARM_CLOSED = PROMPT_BEGIN,
    PROMPT_TIMER_TIMEOUT,
    PROMPT_COUNTDOWN_TIMEOUT,
    PROMPT_PRESSURE_LIMITD,
    PROMPT_FLOWMETER_BREAK_OFF,
    PROMPT_MONIT_SENSOR_CAL_FAIL,
    PROMPT_MONITOR_ACCURACY_LOW,
    PROMPT_O2_FM_SENSOR_ERROR,
    PROMPT_N2O_FM_SENSOR_ERROR,
    PROMPT_AIR_FM_SENSOR_ERROR,
    PROMPT_FM_HARDWARE_ERROR,
    PROMPT_SPO2_SENSOR_UNUNITED,
    PROMPT_AG_ERROR,
    PROMPT_O2_CONCENTRATION_ERROR,
    PROMPT_TOUCH_SCREEN_HOLDBACK,
    PROMPT_SYSTEM_TIME_ERR,
    PROMPT_BREATH_LEAK_HIG,
    PROMPT_BATTERY_ERR,
    PROMPT_ONLY_ONE_BATTERY,
    PROMPT_FLOW_TOO_LOW,
    PROMPT_AG_WARMING,
    PROMPT_CO2_WARMING,
    PROMPT_CO2_ERROR,
    PROMPT_O2_FM_SENSOR_COMMUNICATION_STOP,
    PROMPT_BALANCE_GAS_FM_SENSOR_ERROR,
    PROMPT_BACKUP_FLOW_CONTROL_COMMUNICATION_STOP,
    PROMPT_END ,

    ALARM_END = PROMPT_END
};

struct AlarmDisplaySt {
    short id;
    QString text;
    QColor foreColor;
    QColor bgColor;
};

enum E_ALARM_STATE
{
    ALARM_STATE_DISABLED = 0,
    ALARM_STATE_SET_OFF,
    ALARM_STATE_SET_ON
};

struct T_ALARM_CONTROL
{
    unsigned char mAlarmCount;
    E_ALARM_STATE mAlarmState;
};

enum E_ALARMPRIO
{
    ALARMPRIO_DISABLE = 0,
    ALARMPRIO_HIG,
    ALARMPRIO_MID,
    ALARMPRIO_LOW,
    ALARMPRIO_PROMPT
};

struct AlarmInfoSt
{
    E_ALARMPRIO mAlarmPrio;
    ALL_STRINGS_ENUM mAlarmStrId;
};

struct t_alarm_default
{
    unsigned char AlarmNeeds;
    AlarmInfoSt AlarmBase;
};


enum E_PROMPT_STATE
{
    PROMPT_STATE_DISABLED = 0,
    PROMPT_STATE_ON,
    PROMPT_STATE_OFF
};

struct T_PROMPT_CONTROL
{
    //E_PROMPT_ID prompt_id;
    E_PROMPT_STATE state;
    const unsigned short info;
};

class AlarmBox;
class AlarmAudioPauseIcon;
class CurAlarmModel;
class AlarmMenu;
class CurAlarmListView;

class AlarmManager : public QThread
{
    Q_OBJECT
public:
    enum E_ALARM_AUDIO_STATUS
    {
        AUDIO_PAUSE,
        AUDIO_NORMAL,
        AUDIO_DISABLED
    };
    Q_ENUM(E_ALARM_AUDIO_STATUS);

    static AlarmManager* GetInstance();

    void init();

    void TriggerAlarm(E_ALARM_ID alarmId, unsigned char OnOff);
    void ForeverTriggerAlarm(E_ALARM_ID alarmId,bool isTrigger);
    bool IsEnable(E_ALARM_ID id);

    void ShowAlarmMenu(int Index1 = ALARMMENU_INDEX::ALARM_LIMITED, int Index2 = AlarmRangePage::MACHINE_LIMITED);
    void RefurbishWarning(void);

    QVector<E_ALARM_ID> GetSortedAlarms();

    int GetCurAlarmNum();
    AlarmInfoSt GetAlarmInfo(E_ALARM_ID AlarmID);
    E_ALARM_STATE GetAlarmState(E_ALARM_ID AlarmID);
    E_ALARMPRIO GetAlarmPrio(E_ALARM_ID AlarmID);

    void DisablePhyAlarm(void);
    void EnablePhyAlarm(void);
    void DisableAllAlarm();
    void EnableAllAlarm();

    void DisableAnAlarm(E_ALARM_ID alarm_id);
    void EnableAnAlarm(E_ALARM_ID alarm_id);

    void RemoveAllAlarm();
    void RemoveAllPhyAlarm();

    QPair<QColor, QColor> ConvertAlarmPrioToQColorPair(int alarmPrio);
    QVector<AlarmDisplaySt> ConvertAlarmIdToAlarmDisplaySt(QVector<E_ALARM_ID>);
    void MuteAlarmSound();
    bool isOpenAlarmSound();
    void OpenAlarmSound();
    bool IsExist(E_ALARM_ID id);

signals:
    void SignalCurAlarmsChanged();
    void SignalAudioStateChanged();
    void SignalChangeMuteButtonState(int, int);
protected:
    void run();

private:
    AlarmManager();
    AlarmManager(const AlarmManager&) = delete;
    AlarmManager& operator=(const AlarmManager&) = delete;
    ~AlarmManager() = default;

    unsigned char OpenTrigger(E_ALARM_ID AlarmID);
    unsigned char CloseTrigger(E_ALARM_ID AlarmID);

    void InsertNewAlarmItem(E_ALARM_ID AlarmID);
    void RemoveAnAlarmItem(E_ALARM_ID AlarmID);
    bool IsHaveAlarm();

    bool IsPrompt(E_ALARM_ID alarmId);
    bool IsPlayAlarm(E_ALARM_ID AlarmId);
    void SetAudioState(unsigned short AudioState);


private:
    QMutex mMutex;
    T_ALARM_CONTROL mAlarmControl[ALARM_END - ALARM_BEGIN];
    QMap<E_ALARMPRIO, QVector<E_ALARM_ID>> mMultiPrioVecMap;
    QVector<int> mDelayCloseAlarm;
    int mCurPriorityPlayAlarm =-1;
    int mCurPlayAlarm=-1;
    E_ALARM_AUDIO_STATUS mAudioState{AUDIO_DISABLED};
    QVector<int> mForeverTriggerAlarm;

};

#endif // __ALARMMANAGE_H_
