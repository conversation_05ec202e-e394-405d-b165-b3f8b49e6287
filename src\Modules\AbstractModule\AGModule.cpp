﻿#include "DebugLogManager.h"
#include "AGModule.h"
#include "protocol/protocol.h"
#include "SystemSettingManager.h"
#include "ModuleTestManager.h"
#include "AlarmManager.h"
#include "calibration/Co2CalManage.h"
#include "UnitManager.h"
#include "DataManager.h"
#include "IntraData.h"
#include "SystemConfigManager.h"
#include "TrendDataManager.h"

#define BAUDRATE (9600)

#define SYNBIT(ch, n) ((ch)>>(n))

#ifndef __linux__
#define SERIALNAME "com8"
#define STOPBIT QSerialPort::OneStop
#define PARITY QSerialPort::NoParity
#endif

AGModule::AGModule(QObject *parent) : CommProxyBase(parent)
{
#ifdef __linux__
    StUartInfoSpin uartInfo;

    uartInfo.portId = SpiIODevice::PORT_AG;
    uartInfo.baud_rate = BAUDRATE;
    uartInfo.stop_bits = 0;
    uartInfo.parity_enable=0;
    uartInfo.even_parity=0;
    uartInfo.word_bits=8;

    mIoPort = new SpiIODevice(uartInfo);
#else
    QSerialPort *tempSerial = new QSerialPort;
    tempSerial->setPortName(SERIALNAME);
    tempSerial->setBaudRate(BAUDRATE);
    tempSerial->setStopBits(STOPBIT);
    tempSerial->setParity(PARITY);
    mIoPort = tempSerial;
#endif
    mAgRecevBuff = new char[AG_FRAME_DATANUMBER];
    if(!SetCommIODevice(mIoPort))
        DebugLog<<"AGModule serial connect error";
    mWarmingTimer = new QTimer(this);
    mWarmingTimer->setSingleShot(true);
    mWarmingTimer->setInterval(30000);
    connect(mWarmingTimer,&QTimer::timeout,this,[=](){
        ModuleTestManager::GetInstance()->SetAgIsWarming(false);
        if (ModuleTestManager::GetInstance()->GetAgModuleType() == AG)
        {
            AlarmManager::GetInstance()->TriggerAlarm(PROMPT_AG_WARMING, 0);
        }
        else if (ModuleTestManager::GetInstance()->GetAgModuleType() == CO2)
        {
            AlarmManager::GetInstance()->TriggerAlarm(PROMPT_CO2_WARMING, 0);
        }
        mWarmingStatus = WARMING_FINISH;
    });
}

AGModule::~AGModule()
{
    delete[] mAgRecevBuff;
}

void AGModule::DemoSetAgType(int type)
{
    emit SignalAgentTypeChanged(type);
}

void AGModule::StartZeroCal()
{
    char SendPackage[5];

    SendPackage[0] = 0xAA;	//flag1
    SendPackage[1] = 0x55;	//flag2
    SendPackage[2] = 0x06;	//frameID of Zero Cal
    SendPackage[3] = 0xFF;	//parameter, 255
    SendPackage[4] = ~(SendPackage[2] + SendPackage[3]) + 1;


    if(!mAgZeroCalFlag) //如果接收到校零不能执行则不发送指令；
    {
        SendData(SendPackage);
    }
}

int AGModule::GetCurAgentType()
{
    return mCurAgent1Type;
}

int AGModule::GetSecondAgentType()
{
    return mCurAgent2Type;
}

void AGModule::CloseSerial()
{
    if(mIoPort->isOpen())
        mIoPort->close();
}

QPair<MONITOR_PARAM_TYPE, MONITOR_PARAM_TYPE> AGModule::AgTypeToParamType(unsigned int agTypeId)
{

    decltype (AgTypeToParamType(1))  pair{};
    switch (agTypeId)
    {
    case ANESGAS_HAL:
        pair.first        = VALUEDATA_FIHAL;
        pair.second     = VALUEDATA_ETHAL;
        break;
    case ANESGAS_ENF:
        pair.first     = VALUEDATA_FIENF;
        pair.second    = VALUEDATA_ETENF;
        break;
    case ANESGAS_ISO:
        pair.first     = VALUEDATA_FIISO;
        pair.second    = VALUEDATA_ETISO;
        break;
    case ANESGAS_SEV:
        pair.first     = VALUEDATA_FISEV;
        pair.second    = VALUEDATA_ETSEV;
        break;
    case ANESGAS_DES:
        pair.first     = VALUEDATA_FIDES;
        pair.second    = VALUEDATA_ETDES;
        break;
    default:
        pair.first     = VALUEDATA_FI_AA1;
        pair.second     = VALUEDATA_ET_AA1;
        break;
    }

    return pair;
}

void AGModule::ResetWarmingStatus()
{
    if(mWarmingTimer->isActive())
    {
        mWarmingTimer->stop();
    }
    mWarmingStatus = NO_CONNECT_AG;
    mPreAgStatus = AG_ZERO_STATUS_NORMAL;
    mIsFirstConnect = true;
}

void AGModule::SlotReadData()
{
    QByteArray recvData= mIoPort->readAll();
    static int sReceiveByte = 0;
    int i ,j;
    unsigned char iCheckSum =0;

    for (i = 0; i < recvData.length(); i++) {
        switch (sReceiveByte)
        {
        case 0:
            if (recvData[i]==(char)0xAA) {
                mAgRecevBuff[0] =recvData[i];
                sReceiveByte++;
            }
            break;
        case 1:
            if (recvData[i]==(char)0x55) {
                mAgRecevBuff[1] =recvData[i];
                sReceiveByte++;
            }else{
                sReceiveByte=0;
            }
            break;
        case 2:		//Frame ID
        case 3:		//Status Summary
        case 4:
        case 5:		//1W, CO2
        case 6:
        case 7:		//1W, N2O
        case 8:
        case 9:		//1W, Primary Anesthetic Agent
        case 10:
        case 11:	//1W, 2nd Anesthetic Agent
        case 12:
        case 13:	//1W, O2
        case 14:
        case 15:
        case 16:
        case 17:
        case 18:
        case 19:	//6b, Slow Data
            mAgRecevBuff[sReceiveByte] =recvData[i];
            sReceiveByte++;
            break;
        case 20:	//Frame Check Sum
            for (j =2; j <sReceiveByte; j++) {
                iCheckSum =mAgRecevBuff[j] +iCheckSum;
            }
            if (!((recvData[i] +iCheckSum) & 0xFF)) {
                mAgRecevBuff[sReceiveByte] =recvData[i];
                UpPack();
            }else{
                //Check Sum Fail
            }
            sReceiveByte = 0;
            break;

        default:
            sReceiveByte = 0;
            break;
        }
    }
}

void AGModule::UpPack()
{
    unsigned char sAgStatus;
    static unsigned short AtmPressure = 1013;		// 标准大气压 101.3 KPa
    short mac_value = INVALID_VALUE;

    static unsigned char Data_FICO2, Data_ETCO2, Data_FIN2O, Data_ETN2O, Data_FIAX1, Data_ETAX1, Data_FIAX2, Data_ETAX2 = 0;
    static unsigned char AX1_Type, AX2_Type;

    unsigned char i, FrameID;
    unsigned char StatusSummary = 0;
    unsigned short WaveData_CO2 = 0;

    short Valid_ETAX1_Data = INVALID_VALUE, Valid_ETAX2_Data = INVALID_VALUE, Valid_ETN2O_Data = INVALID_VALUE;

    static bool isHave_ETAX1_Data = false, isHave_ETAX2_Data = false, isHave_ETN2O_Data = false;

    static unsigned char brand = 0;
    unsigned char moduleCode = 0;

    FrameID = mAgRecevBuff[2];
    StatusSummary = mAgRecevBuff[3];



    for(i=0; i<8; i++)
    {
        if (0x01 & SYNBIT(StatusSummary,i))
        {
            switch(i)
            {
            case 0:		//0:Breath Detected;
                break;
            case 1:		//1:No Breath wzin Selected Time;
                break;
            case 2:		//2:O2 Sensor Low;
                break;
            case 3:		//3:Replace O2 Sensor;
                break;
            case 4:		//4:Check Airway Adapter;
                break;
            case 5:		//5:At lest 1Parameter out of range;
                break;
            case 6:		//6:Gas Sensor Error;
                break;
            case 7:		//7: Room Air Calibration Required
                break;
            default:
                break;
            }
        }
    }


    unsigned char reg_err1 = mPrevRegError1, reg_err2 = mPrevRegError2, reg_err3 = mPrevRegError3;

    switch(FrameID)
    {
    case 0x00:
        Data_FICO2 = mAgRecevBuff[14];
        Data_FIN2O = mAgRecevBuff[15];
        Data_FIAX1 = mAgRecevBuff[16];
        Data_FIAX2 = mAgRecevBuff[17];
        // 		Data_FIO2  =mAgRecevBuff[18];
        break;
    case 0x01:
        Data_ETCO2 = mAgRecevBuff[14];
        Data_ETN2O = mAgRecevBuff[15];
        Data_ETAX1 = mAgRecevBuff[16];
        Data_ETAX2 = mAgRecevBuff[17];
        // 		Data_ETO2  =mAgRecevBuff[18];
        break;
    case 0x03:
        AX1_Type = mAgRecevBuff[16];
        AX2_Type = mAgRecevBuff[17];
        AtmPressure =CHAR2_TO_INT16(mAgRecevBuff[18],mAgRecevBuff[19]);
        break;
    case 0x04:	// SensorRegs
        reg_err1 = mAgRecevBuff[16];
        reg_err2 = mAgRecevBuff[17];
        reg_err3 = mAgRecevBuff[18];
        break;
    case 0x06:
        mAgZeroCalFlag = mAgRecevBuff[16];
        //ag_n2o_Concentration = mAgRecevBuff[17];
        break;
    case 0x09:
        brand = mAgRecevBuff[14];
        moduleCode = mAgRecevBuff[15];
    default:
        break;
    }

    if (None != ModuleTestManager::GetInstance()->GetAgModuleType())
    {
        ModuleTestManager::GetInstance()->UpdateModuleState(TEST_AG_CO2, ABLE);
    }

    if(ModuleTestManager::GetInstance()->IsModuleAble(TEST_AG_CO2))
    {
        switch (mAgZeroCalFlag & 0x03)
        {
        case 0x00:
            sAgStatus = AG_ZERO_STATUS_NORMAL;
            break;
        case 0x01:
            sAgStatus = AG_ZERO_STATUS_DISABLE;
            break;
        case 0x02:
        case 0x03:
            sAgStatus = AG_ZERO_STATUS_IN_PROGRESS;
            break;
        default:
            DebugAssert(0);
            break;
        }

        //检查适配器是否安装
        if (0x01 & SYNBIT(StatusSummary,4))
        {
            ModuleTestManager::GetInstance()->SetAgIsHaveAdapter(false);

            if(!ConfigManager->GetConfig<int>(SystemConfigManager::AG_TYPE_INDEX))
            {
                if (ModuleTestManager::GetInstance()->GetAgModuleType() == AG)
                {
                    AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_AG_ADAPTOR_ERR, 1);
                }
                else if(ModuleTestManager::GetInstance()->GetAgModuleType() == CO2)
                {
                    AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_CO2_ADAPTOR_ERR, 1);
                }
            }
            else
            {
                if (ModuleTestManager::GetInstance()->GetAgModuleType() == AG)
                {
                    AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_AG_SAMPLING_ERR, 1);
                }
                else if(ModuleTestManager::GetInstance()->GetAgModuleType() == CO2)
                {
                    AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_CO2_SAMPLING_ERR, 1);
                }
            }

            Co2CalManage::GetInstance()->DisableCo2Cal();
        }
        else
        {
            ModuleTestManager::GetInstance()->SetAgIsHaveAdapter(true);

            if(!ConfigManager->GetConfig<int>(SystemConfigManager::AG_TYPE_INDEX))
            {
                if (ModuleTestManager::GetInstance()->GetAgModuleType() == AG)
                {
                    AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_AG_ADAPTOR_ERR, 0);
                }
                else if(ModuleTestManager::GetInstance()->GetAgModuleType() == CO2)
                {
                    AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_CO2_ADAPTOR_ERR, 0);
                }
            }
            else
            {
                if (ModuleTestManager::GetInstance()->GetAgModuleType() == AG)
                {
                    AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_AG_SAMPLING_ERR, 0);
                }
                else if(ModuleTestManager::GetInstance()->GetAgModuleType() == CO2)
                {
                    AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_CO2_SAMPLING_ERR, 0);
                }
            }
        }

        if(sAgStatus == AG_ZERO_STATUS_DISABLE)
        {
            ModuleTestManager::GetInstance()->SetAgIsCanZero(false);
        }
        else if(sAgStatus == AG_ZERO_STATUS_NORMAL)
        {
            ModuleTestManager::GetInstance()->SetAgIsCanZero(true);
        }

        if(mWarmingStatus == NO_CONNECT_AG && sAgStatus == AG_ZERO_STATUS_DISABLE)
        {
            mWarmingStatus = WARMING_UP;
            ModuleTestManager::GetInstance()->SetAgIsWarming(true);
            if (ModuleTestManager::GetInstance()->GetAgModuleType() == AG)
            {
                AlarmManager::GetInstance()->TriggerAlarm(PROMPT_AG_WARMING, 1);
            }
            else if (ModuleTestManager::GetInstance()->GetAgModuleType() == CO2)
            {
                AlarmManager::GetInstance()->TriggerAlarm(PROMPT_CO2_WARMING, 1);
            }
            Co2CalManage::GetInstance()->DisableCo2Cal();
            mWarmingTimer->start();
        }
        else if(mWarmingStatus == WARMING_UP && sAgStatus == AG_ZERO_STATUS_NORMAL)
        {
            mWarmingStatus = WARMING_FINISH;
            ModuleTestManager::GetInstance()->SetAgIsWarming(false);
            if (ModuleTestManager::GetInstance()->GetAgModuleType() == AG)
            {
                AlarmManager::GetInstance()->TriggerAlarm(PROMPT_AG_WARMING, 0);
            }
            else if (ModuleTestManager::GetInstance()->GetAgModuleType() == CO2)
            {
                AlarmManager::GetInstance()->TriggerAlarm(PROMPT_CO2_WARMING, 0);
            }
        }
        else if(mWarmingStatus == WARMING_FINISH && sAgStatus == AG_ZERO_STATUS_DISABLE)
        {
            //预热完成，有气体通过不能校零
            Co2CalManage::GetInstance()->DisableCo2Cal();
        }


        if (mPreAgStatus != sAgStatus || mIsFirstConnect)
        {
            mIsFirstConnect = false;
            Co2CalManage::GetInstance()->SaveCalResult(sAgStatus);
            mPreAgStatus = sAgStatus;
        }

        WaveData_CO2 = CHAR2_TO_INT16(mAgRecevBuff[5], mAgRecevBuff[4]);//CO2 Value in %

        auto idPair = AgTypeToParamType(AX1_Type);
        MONITOR_PARAM_TYPE valueDataFiId = idPair.first;
        MONITOR_PARAM_TYPE valueDataEtId = idPair.second;


        if (mCurAgent1Type != AX1_Type)
        {
            mCurAgent1Type = AX1_Type;
            emit SignalAgentTypeChanged(mCurAgent1Type);
        }


        DataManager::GetInstance()->UpdateWaveData(DataManager::WAVE_CO2, WaveData_CO2, false);

        if (255 != Data_FICO2)
        {
            DataManager::GetInstance()->SaveMonitorData(VALUEDATA_FICO2, Data_FICO2);
        }
        else
        {
            DataManager::GetInstance()->SaveMonitorData(VALUEDATA_FICO2, INVALID_VALUE);
        }

        if (255 != Data_ETCO2)
        {
            DataManager::GetInstance()->SaveMonitorData(VALUEDATA_ETCO2, Data_ETCO2);
        }
        else
        {
            DataManager::GetInstance()->SaveMonitorData(VALUEDATA_ETCO2, INVALID_VALUE);
        }

        if(ModuleTestManager::GetInstance()->GetAgModuleType() == AG)
        {
            if (255 != Data_FIN2O)
            {
                DataManager::GetInstance()->SaveMonitorData(VALUEDATA_FIN2O, Data_FIN2O);
            }
            else
            {
                DataManager::GetInstance()->SaveMonitorData(VALUEDATA_FIN2O, INVALID_VALUE);
            }

            if (255 != Data_ETN2O)
            {
                isHave_ETN2O_Data = true;
                DataManager::GetInstance()->SaveMonitorData(VALUEDATA_ETN2O, Data_ETN2O);
            }
            else
            {
                isHave_ETN2O_Data = false;
                DataManager::GetInstance()->SaveMonitorData(VALUEDATA_ETN2O, INVALID_VALUE);
            }


            bool etax1Valid = isGasValid(Data_ETAX1);
            bool etax2Valid = isGasValid(Data_ETAX2);
            bool fiax1Valid = isGasValid(Data_FIAX1);
            bool fiax2Valid = isGasValid(Data_FIAX2);

            bool triggerAlarm = (etax1Valid && etax2Valid) ||    // 呼出通道混合
                                (fiax1Valid && fiax2Valid) ||    // 吸入通道混合
                                (fiax1Valid && etax2Valid) ||    // 吸入AX1 + 呼出AX2
                                (fiax2Valid && etax1Valid);      // 吸入AX2 + 呼出AX1

            AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_AG_MIX_AX, triggerAlarm ? 1 : 0);

            if(255 == Data_FIAX1 )
            {
                DataManager::GetInstance()->SaveMonitorData(valueDataFiId, INVALID_VALUE);
            }
            else
            {
                DataManager::GetInstance()->SaveMonitorData(valueDataFiId, Data_FIAX1);
            }

            if(255 == Data_ETAX1)
            {
                isHave_ETAX1_Data = false;
                DataManager::GetInstance()->SaveMonitorData(valueDataEtId, INVALID_VALUE);
            }
            else
            {
                isHave_ETAX1_Data = true;
                DataManager::GetInstance()->SaveMonitorData(valueDataEtId, Data_ETAX1);
            }

            if(255 == Data_FIAX2 )
            {
                isHave_ETAX2_Data = false;
                DataManager::GetInstance()->SaveMonitorData(VALUEDATA_FI_AA2, INVALID_VALUE);
            }
            else
            {
                isHave_ETAX2_Data = true;
                DataManager::GetInstance()->SaveMonitorData(VALUEDATA_FI_AA2, Data_FIAX2);
            }

            if(255 == Data_ETAX2)
            {
                DataManager::GetInstance()->SaveMonitorData(VALUEDATA_ET_AA2, INVALID_VALUE);
            }
            else
            {
                DataManager::GetInstance()->SaveMonitorData(VALUEDATA_ET_AA2, Data_ETAX2);
            }

            DataManager::GetInstance()->GetMonitorData(valueDataEtId, Valid_ETAX1_Data);
            DataManager::GetInstance()->GetMonitorData(VALUEDATA_ET_AA2, Valid_ETAX2_Data);
            DataManager::GetInstance()->GetMonitorData(VALUEDATA_ETN2O, Valid_ETN2O_Data);

            if(isHave_ETAX1_Data && isHave_ETAX2_Data && isHave_ETN2O_Data)
            {
                if(INVALID_VALUE != Valid_ETAX1_Data && INVALID_VALUE != Valid_ETAX2_Data && INVALID_VALUE != Valid_ETN2O_Data)
                {
                    mac_value = Valid_ETAX1_Data * 10.0 / mAgentMac[AX1_Type] + Valid_ETAX2_Data * 10.0 / mAgentMac[AX2_Type] + Valid_ETN2O_Data  * 100.0 / 105 + 0.5;
                }
            }
            else if(isHave_ETAX1_Data && isHave_ETN2O_Data)
            {
                if(INVALID_VALUE != Valid_ETAX1_Data && INVALID_VALUE != Valid_ETN2O_Data)
                {
                    mac_value = Valid_ETAX1_Data * 10.0 / mAgentMac[AX1_Type] + Valid_ETN2O_Data  * 100.0 / 105 + 0.5;
                }
            }
            else if(isHave_ETAX1_Data)
            {
                if(INVALID_VALUE != Valid_ETAX1_Data)
                {
                    mac_value = Valid_ETAX1_Data * 10.0 / mAgentMac[AX1_Type];
                }
            }
            else if(isHave_ETN2O_Data)
            {
                if(INVALID_VALUE != Valid_ETN2O_Data)
                {
                    mac_value = Valid_ETN2O_Data  * 100.0 / 105 + 0.5;
                }
            }

            DataManager::GetInstance()->SaveMonitorData(VALUEDATA_MAC, mac_value);
        }

        if (mCurAgent2Type != AX2_Type)
        {
            mCurAgent2Type= AX2_Type;
        }

        if(mPrevRegError1 != reg_err1)
        {
            if(reg_err1 & 0x1)
                AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_AG_SW_ERR, 1);
            else
                AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_AG_SW_ERR, 0);

            if(reg_err1 & 0x2)
                AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_AG_HW_ERR, 1);
            else
                AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_AG_HW_ERR, 0);

            if(reg_err1 & 0x4)
                AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_AG_MOTO_ERR, 1);
            else
                AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_AG_MOTO_ERR, 0);

            if(reg_err1 & 0x8)
                AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_AG_LOST_CAIL, 1);
            else
                AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_AG_LOST_CAIL, 0);

            mPrevRegError1 = reg_err1;
        }

        if(mPrevRegError2 != reg_err2)
        {
            if(reg_err2 & 0x1)
            {
                if(!ConfigManager->GetConfig<int>(SystemConfigManager::AG_TYPE_INDEX))
                {
                    if (ModuleTestManager::GetInstance()->GetAgModuleType() == AG)
                    {
                        AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_AG_REPLACE, 1);
                    }
                    else if(ModuleTestManager::GetInstance()->GetAgModuleType() == CO2)
                    {
                        AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_CO2_REPLACE, 1);
                    }
                }
                else
                {
                    if (ModuleTestManager::GetInstance()->GetAgModuleType() == AG)
                    {
                        AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_AG_SAMPLING_CLOGGED, 1);
                    }
                    else if(ModuleTestManager::GetInstance()->GetAgModuleType() == CO2)
                    {
                        AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_CO2_SAMPLING_CLOGGED, 1);
                    }
                }
            }
            else
            {
                if(!ConfigManager->GetConfig<int>(SystemConfigManager::AG_TYPE_INDEX))
                {
                    if (ModuleTestManager::GetInstance()->GetAgModuleType() == AG)
                    {
                        AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_AG_REPLACE, 0);
                    }
                    else if(ModuleTestManager::GetInstance()->GetAgModuleType() == CO2)
                    {
                        AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_CO2_REPLACE, 0);
                    }
                }
                else
                {
                    if (ModuleTestManager::GetInstance()->GetAgModuleType() == AG)
                    {
                        AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_AG_SAMPLING_CLOGGED, 0);
                    }
                    else if(ModuleTestManager::GetInstance()->GetAgModuleType() == CO2)
                    {
                        AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_CO2_SAMPLING_CLOGGED, 0);
                    }
                }
            }

            mPrevRegError2 = reg_err2;
        }

        if(mPrevRegError3 != reg_err3)
        {
            if(reg_err3 & 0x1)
                AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_CO2_OUT_OF_RANGE, 1);
            else
                AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_CO2_OUT_OF_RANGE, 0);

            if(reg_err3 & 0x2)
                AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_N2O_OUT_OF_RANGE, 1);
            else
                AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_N2O_OUT_OF_RANGE, 0);

            if(reg_err3 & 0x4)
                AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_AX_OUT_OF_RANGE, 1);
            else
                AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_AX_OUT_OF_RANGE, 0);

            if(reg_err3 & 0x10)
                AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_TEM_OUT_OF_RANGE, 1);
            else
                AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_TEM_OUT_OF_RANGE, 0);

            if(reg_err3 & 0x20)
                AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_PRESS_OUT_OF_RANGE, 1);
            else
                AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_PRESS_OUT_OF_RANGE, 0);

            if(reg_err3 & 0x40)
                AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_AG_NEED_RECAIL, 1);
            else
                AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_AG_NEED_RECAIL, 0);

            if(reg_err3 & 0x80)
                AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_AG_DATA_UNTRUSTABLE, 1);
            else
                AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_AG_DATA_UNTRUSTABLE, 0);
            mPrevRegError3 = reg_err3;
        }
    }
}

bool AGModule::isGasValid(unsigned char value)
{
    return (value != 0) && (value != 255);
}
