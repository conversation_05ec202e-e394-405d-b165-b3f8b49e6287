#include <QVBoxLayout>

#include "UiApi.h"
#include "protocol.h"
#include "FlowmeterWidget.h"

#define BORDER_MARGIN       2
#define MAIN_SCALE_WIDTH    8
#define SON_SCALE_WIDTH    5
#define BIG_SMALL_RATE  3
#define FM_SPACING  5
#define BIG_SCALE_TEXT_SIZE     12
#define SMALL_SCALE_TEXT_SIZE     12
#define FLOW_PARAM_LABEL_HEIGHT 75
#define PARAM_LABEL_COLOR COLOR_BLACK

#define COVER_BACKGROUND_PIXMAP   (":/flowmeter/CoverBackground.png")

FlowParamLabel::FlowParamLabel(QWidget *parent) : ParamLabelBase(parent)
{
    InitUi();

}

void FlowParamLabel::InitUi()
{
    mHighLimitLabel->hide();
    mLowLimitLabel->hide();

    QVBoxLayout *mainLayout = new QVBoxLayout;
    mainLayout->setContentsMargins(2, 2, 2, 2);
    mainLayout->setSpacing(0);
    mainLayout->addWidget(mNameLabel);
    mainLayout->addWidget(mValueLabel);
    mainLayout->addWidget(mUnitLabel);
    setLayout(mainLayout);

    StyleSet::SetBackgroundColor(this,COLOR_BLACK);

    SetLabelBgColor(COLOR_BLACK); //TBD

    mNameLabel->setContentsMargins(0, 0, 0, 0);
    mUnitLabel->setContentsMargins(0, 0, 0, 0);
    mValueLabel->setContentsMargins(0, 0, 0, 0);

    mNameLabel->setAlignment(Qt::AlignHCenter);
    mUnitLabel->setAlignment(Qt::AlignHCenter);
    mValueLabel->setAlignment(Qt::AlignBottom |Qt::AlignHCenter);

    mNameLabel->setAttribute(Qt::WA_TranslucentBackground);
    mUnitLabel->setAttribute(Qt::WA_TranslucentBackground);
    mValueLabel->setAttribute(Qt::WA_TranslucentBackground);

    mNameLabel->SetTextFontBold(true);
    mNameLabel->SetTextFontSize(FONT_M);
    mValueLabel->SetTextFontSize(FONT_L);
    mUnitLabel->SetTextFontSize(FONT_M);

    SetUnitColor(COLOR_DARK_GRAY);
    setFixedHeight(FLOW_PARAM_LABEL_HEIGHT);
}

//-----------------------------------------------------------------------------------------------------------
FlowmeterBody::FlowmeterBody(QWidget *parent) :
    QFrame(parent)
{
    mCurValue = 0; //TBD
    mMaxValue = 1300;//TBD
    mMinValue = 0;//TBD
    mScaleNumber = 14;//TBD
    mDigit = 100;//TBD

    mSonMaxValue = 100;//TBD
    mSonMinValue = 0;//TBD
    mSonScaleNumber= 11;//TBD


    mBigRectTextFont.setFamily("Nina");
    mBigRectTextFont.setPixelSize(BIG_SCALE_TEXT_SIZE);

    mSmallRectTextFont.setFamily("Nina");
    mSmallRectTextFont.setPixelSize(SMALL_SCALE_TEXT_SIZE);

    mSmallWidget = new QWidget;
    mBigWidget = new QWidget;

    mSmallWidget->installEventFilter(this);
    mBigWidget->installEventFilter(this);

    auto mainLayout = new QHBoxLayout;
    mainLayout->setContentsMargins(8, 0, 8, 0);
    mainLayout->setSpacing(5);
    mainLayout->addWidget(mSmallWidget);
    mainLayout->addWidget(mBigWidget);
    mainLayout->setStretchFactor(mSmallWidget, 4);
    mainLayout->setStretchFactor(mBigWidget, 6);

    StyleSet::SetBackgroundColor(this,COLOR_BLACK);
    setLayout(mainLayout);
}


bool FlowmeterBody::eventFilter(QObject *obj, QEvent *event)
{
    if (obj == mBigWidget && event->type() == QEvent::Paint)
    {
        DrawBig();
    }

    if (obj == mSmallWidget && event->type() == QEvent::Paint)
    {
        DrawSmall();
    }

    return QFrame::eventFilter(obj, event);
}


void FlowmeterBody::DrawBig()
{
    int bigXBegin = BORDER_MARGIN;
    int bigXEnd = mBigWidget->width() - BORDER_MARGIN;

    int yBegin = BORDER_MARGIN;
    int yEnd = mBigWidget->height() - BORDER_MARGIN;

    QRect borderRectBig(QPoint(bigXBegin, yBegin),QPoint(bigXEnd, yEnd));

    QPainter painter(mBigWidget);
    painter.drawPixmap(borderRectBig, mPattern.copy(0, 0, borderRectBig.width(), borderRectBig.height()), QRectF());
    QPixmap CoverPixmap(COVER_BACKGROUND_PIXMAP);
    auto flowLen = (mCurValue - mMinValue) / (double)(mMaxValue - mMinValue) *(borderRectBig.height()) ;
    auto flowRect = QRectF(borderRectBig.left(), borderRectBig.top(), borderRectBig.width(), borderRectBig.bottom() - flowLen - 1);

    int yPos = 0;
    double value;



    if (mCurValue != 0)
    {
        painter.drawPixmap(flowRect, CoverPixmap.copy(0, 0, flowRect.width(), flowRect.height()), QRectF());
    }
    else
    {
        painter.drawPixmap(borderRectBig, CoverPixmap.copy(0, 0, borderRectBig.width(), borderRectBig.height()), QRectF());
    }


    painter.setPen(mBorderColor);
    painter.drawRect(borderRectBig);

    painter.setPen(COLOR_WHITE);
    for (int i = 0; i < mScaleNumber; i++)
    {
        yPos = yEnd - yEnd * i / (mScaleNumber-1);

        if (i == 0 || i % 2 == 1 || i == mScaleNumber-1)
        {
            if(i != 0 && i != mScaleNumber-1)
            {
                painter.drawLine(bigXEnd, yPos, bigXEnd - SON_SCALE_WIDTH, yPos);
                painter.drawLine(bigXBegin, yPos, bigXBegin + SON_SCALE_WIDTH, yPos);
            }
            continue;
        }
        painter.drawLine(bigXEnd, yPos, bigXEnd - MAIN_SCALE_WIDTH, yPos);
        painter.drawLine(bigXBegin, yPos, bigXBegin + MAIN_SCALE_WIDTH, yPos);

        value = (mMaxValue - mMinValue) * i / (mScaleNumber-1);
        QString text = QString::number(value/mDigit, 'f', 0);

        QFontMetrics fontMetrics(painter.font());
        int fontWidth = fontMetrics.tightBoundingRect(text).width();
        int textXpos = borderRectBig.x()+borderRectBig.width()/2.0-fontWidth/2.0;

        painter.setFont(mBigRectTextFont);
        painter.drawText(textXpos, yPos + BIG_SCALE_TEXT_SIZE/2, text);
    }
}

void FlowmeterBody::DrawSmall()
{
    QPainter painter(mSmallWidget);
    QFontMetrics fontMetrics(mSmallRectTextFont);
    int maxTextWidth = fontMetrics.tightBoundingRect("x.x").width();
    int smallTextXBegin = 0;
    int smallTextXEnd = maxTextWidth;

    int rectXBegin = smallTextXEnd + BORDER_MARGIN;
    int rectXEnd = mSmallWidget->width() - BORDER_MARGIN;
    int yBegin = BORDER_MARGIN;
    int yEnd = mSmallWidget->height() - BORDER_MARGIN;

    QRect borderRectSmall(QPoint(rectXBegin, yBegin),QPoint(rectXEnd, yEnd));

    QPixmap CoverPixmap(COVER_BACKGROUND_PIXMAP);
    painter.drawPixmap(borderRectSmall, mPattern.copy(0, 0, borderRectSmall.width(), borderRectSmall.height()), QRectF());

    int subValue = mCurValue > 100 ? 100 : mCurValue;
    auto flowLen =(subValue / 100.0)  * (borderRectSmall.height());
    auto flowRect = QRectF(QPointF(rectXBegin, yBegin),
                           QPoint(rectXEnd + 1, yEnd + 1 - flowLen));


    if (subValue != 0)
    {
        painter.drawPixmap(flowRect, CoverPixmap.copy(0, 0, flowRect.width(), flowRect.height()), QRectF());
    }
    else
    {
        painter.drawPixmap(borderRectSmall, CoverPixmap.copy(0, 0, borderRectSmall.width(), borderRectSmall.height()), QRectF());
    }

    painter.setPen(mBorderColor);
    painter.drawRect(borderRectSmall);

    painter.setPen(Qt::white);
    painter.setFont(mSmallRectTextFont);

    for(int i=0; i < mSonScaleNumber; i++)
    {
        int  yPos = yEnd - yEnd * i / (mSonScaleNumber-1);
        double tempValue = (mSonMaxValue - mSonMinValue)*i / (mSonScaleNumber-1);
        QString text = QString::number(tempValue/mDigit, 'f', 1);
        QFontMetrics fontMetrics(painter.font());
        int fontHeight = fontMetrics.tightBoundingRect(text).height();
        if((i != 0) && (i != mSonScaleNumber - 1))
        {
            painter.drawLine(rectXBegin, yPos, rectXBegin + SON_SCALE_WIDTH, yPos);
            painter.drawText(smallTextXBegin, yPos + fontHeight/2, text);
        }
        else
        {
            if(i == 0)
                painter.drawText(smallTextXBegin, yEnd, text);
            else
                painter.drawText(smallTextXBegin, yBegin+fontHeight, text);
        }
    }
}

void FlowmeterBody::SetRange(int minValue, int maxValue)
{
    if(minValue >= maxValue)
    {
        DebugLog<<("mininum value is bigger than or equal to maxinum value");
        return;
    }

    mMaxValue = maxValue;
    mMinValue = minValue;
}

void FlowmeterBody::SetValue(double value)
{
    if (value < 0 || mCurValue == value)
    {
        return ;
    }
    if(value <= mMinValue)
    {
        mCurValue = mMinValue;
    }
    else if(value >= mMaxValue)
    {
        mCurValue = mMaxValue;
    }
    else
    {
        mCurValue = value;
    }

    update();
}

void FlowmeterBody::SetPattern(const QPixmap &pattern)
{
    mPattern = pattern;
}

void FlowmeterBody::SetBorderColor(const QColor &color)
{
    mBorderColor = color;
}

void FlowmeterBody::SetSmallVisible(bool visible)
{
    mSmallWidget->setVisible(visible);
}

FlowmeterWidget::FlowmeterWidget(QWidget *parent) : QFrame(parent)
{
    mFlowParamLabel = new FlowParamLabel;
    mFlowParamLabel->installEventFilter(parent);
    mFlowParamLabel->SetValue("---");
    mFlowmeterBody = new FlowmeterBody;


    QVBoxLayout *mainLayout = new QVBoxLayout;
    mainLayout->setContentsMargins(3, 0, 3, 0);
    mainLayout->addWidget(mFlowParamLabel);
    mainLayout->addWidget(mFlowmeterBody);
    setLayout(mainLayout);
}

void FlowmeterWidget::SetRange(int minValue, int maxValue)
{
    mFlowmeterBody->SetRange(minValue, maxValue);
}

void FlowmeterWidget::SetValue(double value)
{
    mValue = value;
    if (mValue == INVALID_VALUE)
    {
        mFlowmeterBody->SetValue(0);
        mFlowParamLabel->SetValue("---");
    }
    else
    {
        mFlowmeterBody->SetValue(value);
        mFlowParamLabel->SetValue(QString::number(value / 100, 'f', 2));
    }
}

double FlowmeterWidget::GetValue()
{
    return mValue;
}

void FlowmeterWidget::SetPattern(const QPixmap &pattern)
{
    mFlowmeterBody->SetPattern(pattern);
}

void FlowmeterWidget::SetName(QString name)
{
    mFlowParamLabel->SetName(name);
}

void FlowmeterWidget::SetUnit(QString unit)
{
    mFlowParamLabel->SetUnit(unit);
}

void FlowmeterWidget::SetColor(const QColor &color)
{
    mFlowParamLabel->SetNameColor(color);
    mFlowParamLabel->SetValueColor(color);
    mFlowmeterBody->SetBorderColor(color);
}

void FlowmeterWidget::SetOnlyBig(bool flag)
{
    if (flag)
    {
        mFlowmeterBody->SetSmallVisible(false);
    }
    else
    {
        mFlowmeterBody->SetSmallVisible(true);
    }
}

