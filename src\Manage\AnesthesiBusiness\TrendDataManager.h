﻿#ifndef TRENDDATAMANAGER_H
#define TRENDDATAMANAGER_H

#include "AlarmManager.h"
#include "DataManager.h"
#include "ScaleStrategy.h"



enum TREND_PARAM_TYPE
{
    TREND_PARAM_TYPE_START,
    TREND_FLOWMETER_O2 = TREND_PARAM_TYPE_START,
    TREND_FLOWMETER_AIR,
    TREND_FLOWMETER_N2O,

    TREND_PPEAK,
    TREND_PPLAT,
    TREND_PEEP,
    TREND_PMEAN,

    TREND_VTE,
    TREND_VTI,
    TREND_MV,
    TREND_MVSPN,
    TREND_RATE,
    TREND_FSPN,
    TREND_R,
    TREND_C,
    TREND_FIO2,

    TREND_AG_START,
    TREND_FICO2 = TREND_AG_START,
    TREND_ETCO2,
    TREND_FIN2O,
    TREND_ETN2O,
    TREND_FIENF,
    TREND_ETENF,
    <PERSON>RE<PERSON>_FIISO,
    TRE<PERSON>_ETISO,
    <PERSON>RE<PERSON>_FISEV,
    TREND_ETSEV,
    TREND_FIHAL,
    TREND_ETHAL,
    TREND_FIDES,
    TREND_ETDES,
    TREND_PARAM_TYPE_END
};


static const QVector<QVector<ScaleSt>> sTrendYDefaultScales
{
    {{0, 12, 5, DIGIT_2, {6}},	{0, 12, 5, DIGIT_2, {6}},	{0, 12, 5, DIGIT_2, {6}},},     //    TREND_FLOWMETER_O2,
    {{0, 12, 5, DIGIT_2, {6}},	{0, 12, 5, DIGIT_2, {6}},	{0, 12, 5, DIGIT_2, {6}},}, 	//    TREND_FLOWMETER_CO2,
    {{0, 12, 5, DIGIT_2, {6}},	{0, 12, 5, DIGIT_2, {6}},	{0, 12, 5, DIGIT_2, {6}},},    //    TREND_FLOWMETER_N2O,

    {{-5, 20, 4, DIGIT_0, {0,10}},	    {-15, 60, 4, DIGIT_0, {0,30}},	{-20, 120, 4, DIGIT_0, {0,60}}},         //TREND_PPEAK
    {{-5, 20, 4, DIGIT_0, {0,10}},	    {-15, 60, 4, DIGIT_0, {0,30}},	{-20, 120, 4, DIGIT_0, {0,60}}},     	//TREND_PPLAT
    {{ 0, 20, 3, DIGIT_0, {10}},	    {  0, 40, 4, DIGIT_0, {20}},	{  0,  70, 4, DIGIT_0, {35}}},         //TREND_PEEP
    {{-5, 20, 4, DIGIT_0, {0,10}},	    {-15, 60, 4, DIGIT_0, {0,30}},	{-20, 120, 4, DIGIT_0, {0,60}}},         //TREND_PMEAN

    {{0, 150, 4, 0, {75}}, {0, 300, 4, 0, {150}}, {0, 800, 4, 0, {400}}, {0, 1500, 4, 0, {750}},{0, 2500, 4, 0, {1250}}},	//TREND_VTE
    {{0, 150, 4, 0, {75}}, {0, 300, 4, 0, {150}}, {0, 800, 4, 0, {400}}, {0, 1500, 4, 0, {750}},{0, 2500, 4, 0, {1250}}},	//TREND_VTI
    {{0,  30, 4, 2, {15}}, {0, 30,  4, 2, {15}},  {0,  60, 4, 2, {30}}},  //TREND_MV  TBD 老版代码是怎么处理的
    {{0,  30, 4, 2, {15}}, {0, 30,  4, 2, {15}},  {0,  60, 4, 2, {30}}},  //TREND_MVSPN

    {{0, 30, 4, 0, {15}},	{0, 50, 4, 0, {25}},	{0, 100, 4, 0, {50}}},     //TREND_RATE
    {{0, 30, 5, 0, {15}},	{0, 50, 5, 0, {25}},	{0, 100, 5, 0, {50}}},     //TREND_FSPN

    {{0, 50, 5, 0, {25}},	{0, 100, 5, 0, {50}},	{0, 200, 5, 0, {100}}},     //TREND_R
    {{0, 50, 5, 0, {25}},	{0, 100, 5, 0, {50}},	{0, 200, 5, 0, {100}}},     //TREND_C

    {{18, 100, 4, 0, {60}},	{18, 100,  4, 0, {60}},	    {18, 100,  4, 0, {60}}},        //TREND_FIO2
    {{0,    8, 4, 0, {4}},	{0,    8,  4, 0, {4}},	    {0,   15,  4, 0, {8}}},     // TREND_FICO2 = TREND_AG_START,
    {{0,    8, 4, 0, {4}},  {0,    8,  4, 0, {4}},	    {0,   15,  4, 0, {8}}},     // TREND_ETCO2,
    {{0,   25, 4, 1, {12}},	{0,   50,  4, 1, {25}},	    {0,  100,  4, 1, {50}}},     // TREND_FIN2O,
    {{0,   25, 4, 1, {12}},	{0,   50,  4, 1, {25}},	    {0,  100,  4, 1, {50}}},   //  TREND_ETN2O,
    {{0,    8, 4, 1, {4}},  {0,    8,  4, 1, {4}},	    {0,    8,  4, 1, {4}}},     //TREND_FIENF,
    {{0,    8, 4, 1, {4}},  {0,    8,  4, 1, {4}},	    {0,    8,  4, 1, {4}}},     //TREND_ETENF,
    {{0,    8, 4, 1, {4}},	{0,    8,  4, 1, {4}},	    {0,    8,  4, 1, {4}}},     //TREND_FIISO,
    {{0,    8, 4, 1, {4}},  {0,    8,  4, 1, {4}},	    {0,    8,  4, 1, {4}}},   // TREND_ETISO,
    {{0,   10, 4, 1, {5}},	{0,   10,  4, 1, {5}},	    {0,   10,  4, 1, {5}}},     //TREND_FISEV,
    {{0,   10, 4, 1, {5}},	{0,   10,  4, 1, {5}},	    {0,   10,  4, 1, {5}}},     //TREND_ETSEV,
    {{0,    8, 4, 1, {4}},	{0,    8,  4, 1, {4}},	    {0,    8,  4, 1, {4}}},     //TREND_FIHAL,
    {{0,    8, 4, 1, {4}},	{0,    8,  4, 1, {4}},	    {0,    8,  4, 1, {4}}},   // TREND_ETHAL,
    {{0,   22, 4, 1, {11}},	{0,   22,  4, 1, {11}},	    {0,   22,  4, 1, {11}}},     // TREND_FIDES,
    {{0,   22, 4, 1, {11}},	{0,   22,  4, 1, {11}},	    {0,   22,  4, 1, {11}}},   //  TREND_ETDES,
};

struct TrendDataSt
{
    qint64 mRelativeTimestamp;
    int mRunMode;
    int mVentMode;

    int mFlowmeterO2;
    int mFlowmeterAIR;
    int mFlowmeterN2O;

    int mPpeak;
    int mPplat;
    int mPeep;
    int mPmean;
    int mVTI;
    int mVTE;
    int mMV;
    int mMVSpn;
    int mRate;
    int mFspn;
    int mR;
    int mC;
    int mFiO2;

    int mFiCo2;
    int mEtCo2;
    int mFiN2O;
    int mEtN2O;
    int mFiENF;
    int mEtENF;
    int mFiISO;
    int mEtISO;
    int mFiSEV;
    int mEtSEV;
    int mFiHAL;
    int mEtHAL;
    int mFiDES;
    int mEtDES;

    QVector<E_ALARM_ID> mAlarmIdList;
    QVector<int> mAlarmParamId{};

    void SetInvalidValue();
};

class TrendDataManager : public QObject
{
    Q_OBJECT
public:
    static TrendDataManager *GetInstance()
    {
        static TrendDataManager sInstance;
        return &sInstance;
    }

    void Init();
    static MONITOR_PARAM_TYPE GetRelevantMonitorParam(TREND_PARAM_TYPE);
    static QString GetTrendName(TREND_PARAM_TYPE);
    static QString GetTrendUnit(TREND_PARAM_TYPE);
    static short GetDisplayPrecision(TREND_PARAM_TYPE);

    static QMap<TREND_PARAM_TYPE, qreal> ConvertTrendDataSt(TrendDataSt dataSt);
    static TrendDataSt ConvertTrendDataMap(QMap<TREND_PARAM_TYPE, qreal> dataMap);

    int GetTrendDataNum();
    QVector<E_ALARM_ID> GetAlarms(qint64 time);
    qreal GetTrendData(qint64 time, TREND_PARAM_TYPE type);
    qreal GetCurTrendData(TREND_PARAM_TYPE type);
    QMap<qint64, TrendDataSt> GetAllTrendDatas();
    TrendDataSt GetCurTrendDataSt();
    TrendDataSt GetTrendDataSt(QDateTime time);
    TrendDataSt GetTrendDataSt(qint64 timeMsec);
    qint64 GetPreAlarmTime(qint64 curTime);
    qint64 GetNextAlarmTime(qint64 curTime);
    QDateTime GetNewestDataTime();
    QDateTime GetOldestDataTime();

    void RefurbishConfig();
    bool GetTrendParamEnable(TREND_PARAM_TYPE);
    QVector<int> GetDisabledParams();
    qint64 GetShowTime(qint64 relativeTime,QDateTime* showTime=NULL);
    QDateTime GetBaseDataTime();
    void SetBaseDataTime(QDateTime dataTime);
signals:
    void SignalDataAdded();
    void SignalDataRemoved(QVector<qint64> deletedVec);

private slots:
    void RefurbishRawData();
    void RefurbishTrendData();
    void SlotOnUnitChanged(CONVERTIBLE_UNIT_CATEGORY, UNIT_TYPE oldType, UNIT_TYPE newType);

private:
    TrendDataManager();

    void InitConnect();
    void DataRecordLimit();
    QVector<E_ALARM_ID> SortAlarm(QVector<E_ALARM_ID> idVec);

private:
    QDateTime mBaseDataTime;
    QMap<TREND_PARAM_TYPE, bool> mTrendParamEnableMap{};
    QTimer *mRefurbishRawDataTimer = new QTimer;
    QTimer *mRefurbishTrendDataTimer = new QTimer;

    QVector<E_ALARM_ID> mAlarmBuf{};
    QHash<TREND_PARAM_TYPE, QQueue<short>> mTrendRawDataBuf{};
    QMap<TREND_PARAM_TYPE, int> mCurDataMap{};

    QMap<qint64, TrendDataSt> mTrendDatas{};
    QVector<qint64> mTimeWithAlarm{};

    int mTimeCnt;
};

#endif // TRENDDATAMANAGER_H
