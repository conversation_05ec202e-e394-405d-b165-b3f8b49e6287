﻿#include <QtGui>


#include "String/UIStrings.h"
#include "UiApi.h"
#include "AlarmManager.h"
#include "ManageFactory.h"
#include "AlarmLed.h"

#include "CurAlarmModel.h"
#include "SystemTimeManager.h"
#include "SystemSoundManager.h"
#include "SystemConfigManager.h"
#include "SystemSettingManager.h"
#include "CurrentAlarm.h"
#include "HistoryEventManager.h"
#include "TrendDataManager.h"

#include "MainWindow/TopTip/AlarmArea.h"
#include "MainWindow/TopTip/AlarmAudioPauseIcon.h"
#include "MainWindow/MainWindow.h"
#include "RunModeManage.h"
#include "ControlButtonManager.h"

extern void TriggerAlarm_Audio_Light(E_ALARMPRIO alarm_type);

static const t_alarm_default sAlarmDefault[ALARM_END - ALARM_BEGIN] =
{                   /* mAlarmId  */                                 /* AlarmNeeds  */   /* mAlarmPrio  */        /* mAlarmStrId  */

                    /* ALARM_BEGIN  */                                    {0,           {ALARMPRIO_DISABLE,  ALL_STRINGS_ENUM::STR_NULL}                                    },

                    /* ALARM_FIO2_HIGH  */                                {1,           {ALARMPRIO_MID,      ALL_STRINGS_ENUM::STR_ALARMINFO_HIGH_FIO2}                     },
                    /* ALARM_FIO2_LOW  */                                 {1,           {ALARMPRIO_HIG,      ALL_STRINGS_ENUM::STR_ALARMINFO_LOW_FIO2}                      },
                    /* ALARM_PR_HIGH,  */                                 {1,           {ALARMPRIO_MID,      ALL_STRINGS_ENUM::STR_ALARMINFO_HIGH_PR}                       },
                    /* ALARM_PR_LOW,  */                                  {1,           {ALARMPRIO_MID,	     ALL_STRINGS_ENUM::STR_ALARMINFO_LOW_PR}                        },
                    /* ALARM_MV_HIGH  */                                  {1,           {ALARMPRIO_MID,	     ALL_STRINGS_ENUM::STR_ALARMINFO_HIGH_MV}                       },
                    /* ALARM_MV_LOW  */                                   {1,           {ALARMPRIO_MID,	     ALL_STRINGS_ENUM::STR_ALARMINFO_LOW_MV}                        },
                    /* ALARM_RATE_HIGH  */                                {1,           {ALARMPRIO_LOW,	     ALL_STRINGS_ENUM::STR_ALARMINFO_HIGH_RATE}                     },
                    /* ALARM_RATE_LOW  */                                 {1,           {ALARMPRIO_LOW,	     ALL_STRINGS_ENUM::STR_ALARMINFO_LOW_RATE}                      },
                    /* ALARM_VTE_HIGH  */                                 {1,           {ALARMPRIO_MID,	     ALL_STRINGS_ENUM::STR_ALARMINFO_HIGH_VTE}                      },
                    /* ALARM_VTE_LOW  */                                  {1,           {ALARMPRIO_MID,	     ALL_STRINGS_ENUM::STR_ALARMINFO_LOW_VTE}                       },
                    /* ALARM_FICO2_HIGH  */                               {1,           {ALARMPRIO_HIG,	     ALL_STRINGS_ENUM::STR_ALARMINFO_HIGH_FICO2}                    },
                    /* ALARM_FICO2_LOW  */                                {1,           {ALARMPRIO_DISABLE,	 ALL_STRINGS_ENUM::STR_ALARMINFO_LOW_FICO2}                     },  //删除
                    /* ALARM_ETCO2_HIGH  */                               {1,           {ALARMPRIO_HIG,	     ALL_STRINGS_ENUM::STR_ALARMINFO_HIGH_ETCO2}                    },
                    /* ALARM_ETCO2_LOW  */                                {1,           {ALARMPRIO_MID,	     ALL_STRINGS_ENUM::STR_ALARMINFO_LOW_ETCO2}                     },
                    /* ALARM_FIN2O_HIGH  */                               {1,           {ALARMPRIO_HIG,	     ALL_STRINGS_ENUM::STR_ALARMINFO_HIGH_FIN2O}                    },
                    /* ALARM_FIN2O_LOW  */                                {1,           {ALARMPRIO_LOW,	     ALL_STRINGS_ENUM::STR_ALARMINFO_LOW_FIN2O}                     },
                    /* ALARM_ETN2O_HIGH  */                               {1,           {ALARMPRIO_HIG,	     ALL_STRINGS_ENUM::STR_ALARMINFO_HIGH_ETN2O}                    },
                    /* ALARM_ETN2O_LOW  */                                {1,           {ALARMPRIO_LOW,	     ALL_STRINGS_ENUM::STR_ALARMINFO_LOW_ETN2O}                     },

                    /* ALARM_SPO2_HIGH  */                                {1,           {ALARMPRIO_DISABLE,	 ALL_STRINGS_ENUM::STR_ALARMINFO_HIGH_SPO2}                     },
                    /* ALARM_SPO2_LOW  */                                 {1,           {ALARMPRIO_HIG,	     ALL_STRINGS_ENUM::STR_ALARMINFO_LOW_SPO2}                      },
                    /* ALARM_FI_AGENT1_HIGH  */                           {1,           {ALARMPRIO_DISABLE,  ALL_STRINGS_ENUM::STR_NULL}                                    },
                    /* ALARM_FI_AGENT1_LOW  */                            {1,           {ALARMPRIO_DISABLE,  ALL_STRINGS_ENUM::STR_NULL}                                    },
                    /* ALARM_FI_AGENT2_HIGH  */                           {1,           {ALARMPRIO_DISABLE,  ALL_STRINGS_ENUM::STR_NULL}                                    },
                    /* ALARM_FI_AGENT2_LOW  */                            {1,           {ALARMPRIO_DISABLE,  ALL_STRINGS_ENUM::STR_NULL}                                    },
                    /* ALARM_FIHAL_HIGH  */                               {1,           {ALARMPRIO_MID,	     ALL_STRINGS_ENUM::STR_ALARMINFO_HIGH_FIHAL}                    },
                    /* ALARM_FIHAL_LOW  */                                {1,           {ALARMPRIO_LOW,	     ALL_STRINGS_ENUM::STR_ALARMINFO_LOW_FIHAL}                     },
                    /* ALARM_FIENF_HIGH  */                               {1,           {ALARMPRIO_MID,	     ALL_STRINGS_ENUM::STR_ALARMINFO_HIGH_FIENF}                    },
                    /* ALARM_FIENF_LOW  */                                {1,           {ALARMPRIO_LOW,	     ALL_STRINGS_ENUM::STR_ALARMINFO_LOW_FIENF}                     },
                    /* ALARM_FISEV_HIGH  */                               {1,           {ALARMPRIO_MID,	     ALL_STRINGS_ENUM::STR_ALARMINFO_HIGH_FISEV}                    },
                    /* ALARM_FISEV_LOW  */                                {1,           {ALARMPRIO_LOW,	     ALL_STRINGS_ENUM::STR_ALARMINFO_LOW_FISEV}                     },
                    /* ALARM_FIISO_HIGH  */                               {1,           {ALARMPRIO_MID,	     ALL_STRINGS_ENUM::STR_ALARMINFO_HIGH_FIISO}                    },
                    /* ALARM_FIISO_LOW  */                                {1,           {ALARMPRIO_LOW,	     ALL_STRINGS_ENUM::STR_ALARMINFO_LOW_FIISO}                     },
                    /* ALARM_FIDES_HIGH  */                               {1,           {ALARMPRIO_MID,	     ALL_STRINGS_ENUM::STR_ALARMINFO_HIGH_FIDES}                    },
                    /* ALARM_FIDES_LOW  */                                {1,           {ALARMPRIO_LOW,	     ALL_STRINGS_ENUM::STR_ALARMINFO_LOW_FIDES}                     },
                    /* ALARM_ET_AGENT1_HIGH  */                           {1,           {ALARMPRIO_DISABLE,  ALL_STRINGS_ENUM::STR_NULL}                                    },
                    /* ALARM_ET_AGENT1_LOW  */                            {1,           {ALARMPRIO_DISABLE,  ALL_STRINGS_ENUM::STR_NULL}                                    },
                    /* ALARM_ET_AGENT2_HIGH  */                           {1,           {ALARMPRIO_DISABLE,  ALL_STRINGS_ENUM::STR_NULL}                                    },
                    /* ALARM_ET_AGENT2_LOW  */                            {1,           {ALARMPRIO_DISABLE,  ALL_STRINGS_ENUM::STR_NULL}                                    },
                    /* ALARM_ETHAL_HIGH  */                               {1,           {ALARMPRIO_MID,	     ALL_STRINGS_ENUM::STR_ALARMINFO_HIGH_ETHAL}                    },
                    /* ALARM_ETHAL_LOW  */                                {1,           {ALARMPRIO_LOW,	     ALL_STRINGS_ENUM::STR_ALARMINFO_LOW_ETHAL}                     },
                    /* ALARM_ETENF_HIGH  */                               {1,           {ALARMPRIO_MID,	     ALL_STRINGS_ENUM::STR_ALARMINFO_HIGH_ETENF}                    },
                    /* ALARM_ETENF_LOW  */                                {1,           {ALARMPRIO_LOW,	     ALL_STRINGS_ENUM::STR_ALARMINFO_LOW_ETENF}                     },
                    /* ALARM_ETSEV_HIGH  */                               {1,           {ALARMPRIO_MID,	     ALL_STRINGS_ENUM::STR_ALARMINFO_HIGH_ETSEV}                    },
                    /* ALARM_ETSEV_LOW  */                                {1,           {ALARMPRIO_LOW,	     ALL_STRINGS_ENUM::STR_ALARMINFO_LOW_ETSEV}                     },
                    /* ALARM_ETISO_HIGH  */                               {1,           {ALARMPRIO_MID,	     ALL_STRINGS_ENUM::STR_ALARMINFO_HIGH_ETISO}                    },
                    /* ALARM_ETISO_LOW  */                                {1,           {ALARMPRIO_LOW,	     ALL_STRINGS_ENUM::STR_ALARMINFO_LOW_ETISO}                     },
                    /* ALARM_ETDES_HIGH  */                               {1,           {ALARMPRIO_MID,	     ALL_STRINGS_ENUM::STR_ALARMINFO_HIGH_ETDES}                    },
                    /* ALARM_ETDES_LOW  */                                {1,           {ALARMPRIO_LOW,	     ALL_STRINGS_ENUM::STR_ALARMINFO_LOW_ETDES}                     },

                    /* ALARM_PAW_LOW  */                                  {1,           {ALARMPRIO_MID,      ALL_STRINGS_ENUM::STR_ALARMINFO_LOW_PAW}                       },
                    /* ALARM_PAW_HIGH  */                                 {1,           {ALARMPRIO_HIG,	     ALL_STRINGS_ENUM::STR_ALARMINFO_HIGH_PAW}                      },
                    /* ALARM_APNEA  */                                    {1,           {ALARMPRIO_MID,	     ALL_STRINGS_ENUM::STR_TECHALARM_APNEAALARM}                    },
                    /* ALARM_APNEA_120  */                                {1,           {ALARMPRIO_HIG,      ALL_STRINGS_ENUM::STR_TECHALARM_APNEA_120_ALARM}               },
                    /* ALARM_FLOW_SENSOR_ERR  */                          {1,           {ALARMPRIO_HIG,      ALL_STRINGS_ENUM::STR_FLOW_SENSOR_ERR_ALARM}                   },
                    /* ALARM_PRESS_HIGH_CONTINUA  */                      {1,           {ALARMPRIO_HIG,      ALL_STRINGS_ENUM::STR_ALARMINFO_GASHIGH}                       }, //生理报警
                    /* ALARM_PRESS_LOW_CONTINUA  */                       {1,           {ALARMPRIO_MID,      ALL_STRINGS_ENUM::STR_ALARMINFO_GASLOW}                        },	//生理报警
                    /* ALARM_NEGATIVE_PRESS  */                           {1,           {ALARMPRIO_HIG,      ALL_STRINGS_ENUM::STR_ALARM_NEGPRESS}                          },//生理报警

                    //流量计报警
                    /* ALARM_O2_FLOW_HIGH,  */                            {1,           {ALARMPRIO_LOW,	     ALL_STRINGS_ENUM::STR_ALARMINFO_O2_FLOW_HIGH}                  },// 流量计生理报警
                    /* ALARM_N2O_FLOW_HIGH,  */                           {1,           {ALARMPRIO_LOW,	     ALL_STRINGS_ENUM::STR_ALARMINFO_N2O_FLOW_HIGH}                 },
                    /* ALARM_AIR_FLOW_HIGH,  */                           {1,           {ALARMPRIO_LOW,	     ALL_STRINGS_ENUM::STR_ALARMINFO_AIR_FLOW_HIGH}                 },
                    /* ALARM_FLOW_HIGH,  */                               {1,           {ALARMPRIO_LOW,	     ALL_STRINGS_ENUM::STR_ALARMINFO_FLOW_HIGH}                     },
                    /* ALARM_O2_N2O_UNUSUAL_RATIO,  */                    {1,           {ALARMPRIO_HIG,      ALL_STRINGS_ENUM::STR_ALARMINFO_O2_N2O_UNUSUAL_RATIO}          },


                    /* TECHALARM_BATTERY_LOW  */                          {1,           {ALARMPRIO_MID,      ALL_STRINGS_ENUM::STR_TECHALARM_BATTERYLOW}                    },
                    /* TECHALARM_FLOWMETER_O2_SENSOR_NO_CAL_DATA  */  	  {1,           {ALARMPRIO_MID,      ALL_STRINGS_ENUM::STR_FLOWMETER_O2_SENSOR_ERROR}               },
                    /* TECHALARM_FLOWMETER_N2O_SENSOR_NO_CAL_DATA  */  	  {1,           {ALARMPRIO_MID,      ALL_STRINGS_ENUM::STR_FLOWMETER_N2O_SENSOR_ERROR}              },
                    /* TECHALARM_FLOWMETER_AIR_SENSOR_NO_CAL_DATA  */  	  {1,           {ALARMPRIO_MID,      ALL_STRINGS_ENUM::STR_FLOWMETER_AIR_SENSOR_ERROR}              },
                    /* TECHALARM_FLOW_TOO_LOW,  */                        {1,           {ALARMPRIO_MID,	     ALL_STRINGS_ENUM::STR_FLOW_TOO_LOW}                            },
                    /* TECHALARM_O2_FLOW_INEXACT,  */                     {1,           {ALARMPRIO_LOW,	     ALL_STRINGS_ENUM::STR_O2_FLOW_INEXACT}                         },
                    /* TECHALARM_BANLANCE_FLOW_INEXACT,  */               {1,           {ALARMPRIO_LOW,	     ALL_STRINGS_ENUM::STR_BANLANCE_FLOW_INEXACT}                   },
                    /* TECHALARM_O2_PRESS_LOW,  */                        {1,           {ALARMPRIO_LOW,	     ALL_STRINGS_ENUM::STR_O2_PRESS_LOW}                            },
                    /* TECHALARM_N2O_PRESS_LOW,  */                       {1,           {ALARMPRIO_MID,      ALL_STRINGS_ENUM::STR_N2O_PRESS_LOW}                           },
                    /* TECHALARM_AIR_PRESS_LOW,  */                       {1,           {ALARMPRIO_MID,      ALL_STRINGS_ENUM::STR_AIR_PRESS_LOW}                           },
                    /* TECHALARM_BACKUP_FLOW_CONTROL_OPEN  */             {1,           {ALARMPRIO_MID,      ALL_STRINGS_ENUM::STR_BACKUP_FLOW_CONTROL_OPEN}                },
                    /* TECHALARM_BATTERYEMPTY  */                         {1,           {ALARMPRIO_HIG,      ALL_STRINGS_ENUM::STR_TECHALARM_BATTERYEMPTY}                  },
                    /* TECHALARM_NOAC  */                                 {1,           {ALARMPRIO_LOW,      ALL_STRINGS_ENUM::STR_TECHALARM_PLUGOUT}                       },
                    /* TECHALARM_VENT_STATE_ACGO  */                      {1,           {ALARMPRIO_MID,      ALL_STRINGS_ENUM::STR_TECHALARM_ACGO}                          },
                    /* TECHALARM_GAS_LOOP_ERR  */                         {1,           {ALARMPRIO_HIG,      ALL_STRINGS_ENUM::STR_TECHALARM_GASLOOPERR}                    },
                    /* TECHALARM_GASSOURCE_LOW  */                        {1,           {ALARMPRIO_HIG,      ALL_STRINGS_ENUM::STR_TECHALARM_GASSOURCELOW}                  },
                    /* TECHALARM_GASSOURCE_O2_LOW  */                     {1,           {ALARMPRIO_HIG,      ALL_STRINGS_ENUM::STR_TECHALARM_O2_GASSOURCELOW}               },

                    /* TECHALARM_ADDO2_OVERTIME,  */                      {1,           {ALARMPRIO_MID,      ALL_STRINGS_ENUM::STR_TECHALARM_ADDO2TIMEOUT}                  },
                    /* TECHALARM_ADDO2_OVERERR  */                        {1,           {ALARMPRIO_HIG,      ALL_STRINGS_ENUM::STR_TECHALARM_ADDO2ERR}                      },
                    /* TECHALARM_BYPASS_ERR  */                           {1,           {ALARMPRIO_HIG,      ALL_STRINGS_ENUM::STR_TECHALARM_BYPASSERR}                     },
                    /* TECHALARM_FAN_STOP  */                             {1,           {ALARMPRIO_HIG,      ALL_STRINGS_ENUM::STR_TECHALARM_FAN_STOP}                      },
                    /* TECHALARM_O2_SENSOR_ERR  */                        {1,           {ALARMPRIO_MID,      ALL_STRINGS_ENUM::STR_TECHALARM_O2_SENSOR_ERR}                 },
                    /* TECHALARM_MINOTORING_MODULE_ERR,  */               {1,           {ALARMPRIO_HIG,      ALL_STRINGS_ENUM::STR_TECHALARM_MINOTORING_MODULE_ERR}         },
                    /* TECHALARM_PRESSURE_SENSOR_ERR,  */                 {1,           {ALARMPRIO_HIG,      ALL_STRINGS_ENUM::STR_TECHALARM_PRESSURE_SENSOR_ERR}           },
                    /* TECHALARM_EXP_VALVE_ERR,  */                       {1,           {ALARMPRIO_HIG,      ALL_STRINGS_ENUM::STR_TECHALARM_EXP_VALVE_ERR}                 },
                    /* TECHALARM_EXP_VALVE_CLOSE_OFF,  */                 {1,           {ALARMPRIO_HIG,      ALL_STRINGS_ENUM::STR_TECHALARM_EXP_VALVE_CLOSE_OFF}           },
                    /* TECHALARM_INSP_VALVE_ERR,    */                    {1,           {ALARMPRIO_HIG,      ALL_STRINGS_ENUM::STR_TECHALARM_INSP_VALVE_ERR}                },
                    /* TECHALARM_HEATING_MODULE_ERR,  */                  {1,           {ALARMPRIO_LOW,      ALL_STRINGS_ENUM::STR_TECHALARM_HEATING_MODULE_ERR}            },
                    /* TECHALARM_AG_ADAPTOR_ERR,  */                      {1,           {ALARMPRIO_LOW,      ALL_STRINGS_ENUM::STR_TECHALARM_AG_ADAPTOR_ERR}                },
                    /* TECHALARM_AG_SAMPLING_ERR,  */                     {1,           {ALARMPRIO_LOW,      ALL_STRINGS_ENUM::STR_TECHALARM_AG_SAMPLING_ERR}               },
                    /* TECHALARM_CO2_ADAPTOR_ERR,  */                     {1,           {ALARMPRIO_LOW,      ALL_STRINGS_ENUM::STR_TECHALARM_CO2_ADAPTOR_ERR}               },
                    /* TECHALARM_CO2_SAMPLING_ERR,  */                    {1,           {ALARMPRIO_LOW,      ALL_STRINGS_ENUM::STR_TECHALARM_CO2_SAMPLING_ERR}              },
                    /* TECHALARM_HLM_TIME_UP  */                          {1,           {ALARMPRIO_LOW,      ALL_STRINGS_ENUM::STR_TECHALARM_TIME_UP}                       },
                    /* TECHALARM_SPO2_BREAKOFF  */                        {1,           {ALARMPRIO_MID,      ALL_STRINGS_ENUM::STR_TECHALARM_SPO2_BREAKOFF}                 },
                    /* TECHALARM_AX_OUT_OF_RANGE  */                      {1,           {ALARMPRIO_MID,      ALL_STRINGS_ENUM::STR_TECHALARM_AX_OUT_OF_RANGE}               },
                    /* TECHALARM_CO2_OUT_OF_RANGE  */                     {1,           {ALARMPRIO_MID,      ALL_STRINGS_ENUM::STR_TECHALARM_CO2_OUT_OF_RANGE}              },
                    /* TECHALARM_N2O_OUT_OF_RANGE  */                     {1,           {ALARMPRIO_MID,      ALL_STRINGS_ENUM::STR_TECHALARM_N2O_OUT_OF_RANGE}              },
                    /* TECHALARM_TEM_OUT_OF_RANGE  */                     {1,           {ALARMPRIO_MID,      ALL_STRINGS_ENUM::STR_TECHALARM_TMP_OUT_OF_RANGE}              },
                    /* TECHALARM_PRESS_OUT_OF_RANGE  */                   {1,           {ALARMPRIO_MID,      ALL_STRINGS_ENUM::STR_TECHALARM_PRESS_OUT_OF_RANGE}            },
                    /* TECHALARM_AG_NEED_RECAIL  */                       {1,           {ALARMPRIO_MID,      ALL_STRINGS_ENUM::STR_TECHALARM_AG_NEED_RECAIL}                },
                    /* TECHALARM_AG_MIX_AX  */                            {1,           {ALARMPRIO_LOW,      ALL_STRINGS_ENUM::STR_TECHALARM_AG_AGENT_MIX}                  },
                    /* TECHALARM_AG_DATA_UNTRUSTABLE  */                  {1,           {ALARMPRIO_MID,      ALL_STRINGS_ENUM::STR_TECHALARM_AG_UNSPECIFIED_ACCURACY}       },
                    /* TECHALARM_AG_SW_ERR  */                            {1,           {ALARMPRIO_HIG,      ALL_STRINGS_ENUM::STR_TECHALARM_AG_SW_ERR}                     },
                    /* TECHALARM_AG_HW_ERR  */                            {1,           {ALARMPRIO_HIG,      ALL_STRINGS_ENUM::STR_TECHALARM_AG_HW_ERR}                     },
                    /* TECHALARM_AG_MOTO_ERR  */                          {1,           {ALARMPRIO_HIG,      ALL_STRINGS_ENUM::STR_TECHALARM_MOTO_ERR}                      },
                    /* TECHALARM_AG_LOST_CAIL  */                         {1,           {ALARMPRIO_MID,      ALL_STRINGS_ENUM::STR_TECHALARM_AG_LOST_CAIL}                  },
                    /* TECHALARM_AG_REPLACE  */                           {1,           {ALARMPRIO_MID,      ALL_STRINGS_ENUM::STR_TECHALARM_AG_REPLACE}                    },
                    /* TECHALARM_CO2_REPLACE  */                          {1,           {ALARMPRIO_MID,      ALL_STRINGS_ENUM::STR_TECHALARM_CO2_REPLACE}                   },
                    /* TECHALARM_AG_SAMPLING_CLOGGED  */                  {1,           {ALARMPRIO_MID,      ALL_STRINGS_ENUM::STR_TECHALARM_AG_SAMPLING_CLOGGED}           },
                    /* TECHALARM_CO2_SAMPLING_CLOGGED  */                 {1,           {ALARMPRIO_MID,      ALL_STRINGS_ENUM::STR_TECHALARM_CO2_SAMPLING_CLOGGED}          },
                    /* TECHALARM_DB_FAIL*/                                {1,           {ALARMPRIO_MID,      ALL_STRINGS_ENUM::STR_DB_FAIL}                                 },



                    /*PROMPT_PHY_ALARM_CLOSED          */                 {1,           {ALARMPRIO_PROMPT,   ALL_STRINGS_ENUM::STR_BIO_ALARM_CLOSE}                         },
                    /* PROMPT_TIMER_TIMEOUT,           */                 {1,           {ALARMPRIO_PROMPT,   ALL_STRINGS_ENUM::STR_TIMER_MAX}                               },
                    /* PROMPT_COUNTDOWN_TIMEOUT,       */                 {1,           {ALARMPRIO_PROMPT,   ALL_STRINGS_ENUM::STR_COUNTDOWN_END}                           },
                    /* PROMPT_PRESSURE_LIMITD,         */                 {1,           {ALARMPRIO_PROMPT,   ALL_STRINGS_ENUM::STR_PRESSURE_REACH_LIMIT}                    },
                    /* PROMPT_FLOWMETER_BREAK_OFF,     */                 {1,           {ALARMPRIO_PROMPT,   ALL_STRINGS_ENUM::STR_FLOWMETER_STOP}                          },
                    /* PROMPT_MONIT_SENSOR_CAL_FAIL,   */                 {1,           {ALARMPRIO_PROMPT,   ALL_STRINGS_ENUM::STR_MONIT_SENSOR_CAL_FAIL }                  },
                    /* PROMPT_MONITOR_ACCURACY_LOW,    */                 {1,           {ALARMPRIO_PROMPT,   ALL_STRINGS_ENUM::STR_MONITOR_ACCURACY_LOW}                    },
                    /* PROMPT_O2_FM_SENSOR_ERROR,      */                 {1,           {ALARMPRIO_PROMPT,   ALL_STRINGS_ENUM::STR_O2_FM_SENSOR_ERROR}                      },
                    /* PROMPT_N2O_FM_SENSOR_ERROR,     */                 {1,           {ALARMPRIO_PROMPT,   ALL_STRINGS_ENUM::STR_N2O_FM_SENSOR_ERROR }                    },
                    /* PROMPT_AIR_FM_SENSOR_ERROR,     */                 {1,           {ALARMPRIO_PROMPT,   ALL_STRINGS_ENUM::STR_AIR_FM_SENSOR_ERROR}                     },
                    /* PROMPT_FM_HARDWARE_ERROR,       */                 {1,           {ALARMPRIO_PROMPT,   ALL_STRINGS_ENUM::STR_FM_HARDWARE_ERROR}                       },
                    /* PROMPT_SPO2_SENSOR_UNUNITED,    */                 {1,           {ALARMPRIO_PROMPT,   ALL_STRINGS_ENUM::STR_SPO2_SENSOR_UNUNITED}                    },
                    /* PROMPT_AG_ERROR,            */                     {1,           {ALARMPRIO_PROMPT,   ALL_STRINGS_ENUM::STR_AG_ERROR}                                },
                    /* PROMPT_O2_CONCENTRATION_ERROR,  */                 {1,           {ALARMPRIO_PROMPT,   ALL_STRINGS_ENUM::STR_O2_CONCENTRATION_ERROR}                  },
                    /* PROMPT_TOUCH_SCREEN_HOLDBACK,   */                 {1,           {ALARMPRIO_PROMPT,   ALL_STRINGS_ENUM::STR_TOUCH_SCREEN_HOLDBACK }                  },
                    /* PROMPT_SYSTEM_TIME_ERR,         */                 {1,           {ALARMPRIO_PROMPT,   ALL_STRINGS_ENUM::STR_SYS_TIME_ERR}                            },
                    /* PROMPT_BREATH_LEAK_HIG,         */                 {1,           {ALARMPRIO_PROMPT,   ALL_STRINGS_ENUM::STR_BREATH_LEAK_HIGH}                        },
                    /* PROMPT_BATTERY_ERR,  //TBD      */                 {1,           {ALARMPRIO_PROMPT,   ALL_STRINGS_ENUM::STR_BATTERYERR}                              },
                    /* PROMPT_ONLY_ONE_BATTERY,        */                 {1,           {ALARMPRIO_PROMPT,   ALL_STRINGS_ENUM::STR_ONLY_ONE_BATTER}                         },
                    /* PROMPT_FLOW_TOO_LOW,            */                 {1,           {ALARMPRIO_PROMPT,   ALL_STRINGS_ENUM::STR_FLOW_TOO_LOW_PROMPT}                     },
                    /* PROMPT_AG_WARMING,             */                  {1,           {ALARMPRIO_PROMPT,   ALL_STRINGS_ENUM::STR_AG_WARMING}                              },
                    /* PROMPT_CO2_WARMING,            */                  {1,           {ALARMPRIO_PROMPT,   ALL_STRINGS_ENUM::STR_CO2_WARMING}                             },
                    /* PROMPT_CO2_ERROR,               */                 {1,           {ALARMPRIO_PROMPT,   ALL_STRINGS_ENUM::STR_CO2_ERROR}                               },
                    /* PROMPT_O2_FM_SENSOR_COMMUNICATION_STOP,       */   {1,           {ALARMPRIO_PROMPT,   ALL_STRINGS_ENUM::STR_O2_FM_SENSOR_COMMUNICATION_STOP}         },
                    /* PROMPT_BALANCE_GAS_FM_SENSOR_ERROR,           */   {1,           {ALARMPRIO_PROMPT,   ALL_STRINGS_ENUM::STR_BALANCE_GAS_FM_SENSOR_ERROR}             },
                    /* PROMPT_BACKUP_FLOW_CONTROL_COMMUNICATION_STOP,*/   {1,           {ALARMPRIO_PROMPT,   ALL_STRINGS_ENUM::STR_BACKUP_FLOW_CONTROL_COMMUNICATION_STOP}  },
};


AlarmManager::AlarmManager()
{
    mMultiPrioVecMap =
    {
        {ALARMPRIO_HIG, {}},
        {ALARMPRIO_MID, {}},
        {ALARMPRIO_LOW, {}},
        {ALARMPRIO_PROMPT, {}},
    };
    init();
//    for(int i=0;i<ALARM_END - ALARM_BEGIN;i++)
//    {
//        DebugLog<<"报警ID："<<i<<",报警内容:"<<UIStrings::GetInstance()->GetStr(sAlarmDefault[i].AlarmBase.mAlarmStrId,CHINESE);
//    }

}

AlarmManager *AlarmManager::GetInstance()
{
    static AlarmManager sInstance;
    if(!sInstance.isRunning())
        sInstance.start();
    return &sInstance;
}

void AlarmManager::init(void)
{
    for (int i = 0; i < ALARM_END - ALARM_BEGIN; i++)
    {
        mAlarmControl[i].mAlarmCount = 0;
        mAlarmControl[i].mAlarmState = ALARM_STATE_SET_OFF;
    }
}

void AlarmManager::InsertNewAlarmItem(E_ALARM_ID alarmID)
{
    mMutex.lock();
    mMultiPrioVecMap[sAlarmDefault[alarmID].AlarmBase.mAlarmPrio].push_front(alarmID);
    mAlarmControl[alarmID].mAlarmState = ALARM_STATE_SET_ON;
    mMutex.unlock();

    emit SignalCurAlarmsChanged();
}

void AlarmManager::RemoveAnAlarmItem(E_ALARM_ID alarmID)
{
    mMutex.lock();
    mMultiPrioVecMap[sAlarmDefault[alarmID].AlarmBase.mAlarmPrio].removeOne(alarmID);

    mAlarmControl[alarmID].mAlarmState = ALARM_STATE_SET_OFF;
    mAlarmControl[alarmID].mAlarmCount = 0;
    mMutex.unlock();

    emit SignalCurAlarmsChanged();
}

bool AlarmManager::IsHaveAlarm()
{
    return (!mMultiPrioVecMap[ALARMPRIO_HIG].isEmpty()||!mMultiPrioVecMap[ALARMPRIO_MID].isEmpty()||!mMultiPrioVecMap[ALARMPRIO_LOW].isEmpty());
}

bool AlarmManager::IsPlayAlarm(E_ALARM_ID alarmId)
{
    if(/*AlarmId==mCurPlayAlarm&&*/ManageFactory::GetInstance()->GetDeviceManage()->IsPlayAlarm())
        return true;
    else
        return false;
}

void AlarmManager::SetAudioState(unsigned short AudioState)
{
    switch (AudioState)
    {
    case AUDIO_DISABLED:
        if (mAudioState != AUDIO_DISABLED)
        {
            mAudioState = AUDIO_DISABLED;
            emit AlarmManager::GetInstance()->SignalAudioStateChanged();
            emit AlarmManager::GetInstance()->SignalChangeMuteButtonState(ControlButtonManager::MUTE, ControlButtonManager::NORMAL);
        }
        break;

    case AUDIO_NORMAL:
        if (mAudioState != AUDIO_NORMAL)
        {
            mAudioState = AUDIO_NORMAL;
            emit AlarmManager::GetInstance()->SignalChangeMuteButtonState(ControlButtonManager::MUTE, ControlButtonManager::NORMAL);
            ManageFactory::GetInstance()->GetDeviceManage()->PasuePlayAlarm(false);
            emit AlarmManager::GetInstance()->SignalAudioStateChanged();
        }
        break;
    case AUDIO_PAUSE:
        if (mAudioState != AUDIO_PAUSE)
        {
            mAudioState = AUDIO_PAUSE;
            ManageFactory::GetInstance()->GetDeviceManage()->PasuePlayAlarm(true);
            emit AlarmManager::GetInstance()->SignalChangeMuteButtonState(ControlButtonManager::MUTE, ControlButtonManager::PAUSSE);
            emit AlarmManager::GetInstance()->SignalAudioStateChanged();
        }
        break;
    default:
        break;
    }
}

unsigned char AlarmManager::OpenTrigger(E_ALARM_ID AlarmID)
{
    T_ALARM_CONTROL* pAlarmControl;
    unsigned char cRet = 0;

    if (ALARM_END <= AlarmID || ALARM_BEGIN >= AlarmID)
    {
        return cRet;
    }

    pAlarmControl = &mAlarmControl[AlarmID];

    switch (pAlarmControl->mAlarmState)
    {
    case ALARM_STATE_SET_OFF:
    {
        pAlarmControl->mAlarmCount++;
        if (pAlarmControl->mAlarmCount >= sAlarmDefault[AlarmID].AlarmNeeds)
        {
            InsertNewAlarmItem(AlarmID);
            cRet = 1;
        }
    }
        break;
    case ALARM_STATE_SET_ON:
        break;
    case ALARM_STATE_DISABLED:
        break;
    default:
        break;
    }
    return cRet;
}

unsigned char AlarmManager::CloseTrigger(E_ALARM_ID alarmID)
{
    unsigned char cRet = 0;
    if(mForeverTriggerAlarm.indexOf(alarmID)==-1)
    {
        T_ALARM_CONTROL* pAlarmControl;

        if (ALARM_END <= alarmID && ALARM_BEGIN > alarmID)
        {
            return cRet;
        }

        pAlarmControl = &mAlarmControl[alarmID - ALARM_BEGIN];
        switch (pAlarmControl->mAlarmState)
        {
        case ALARM_STATE_SET_OFF:
            pAlarmControl->mAlarmCount = 0;
            break;
        case ALARM_STATE_SET_ON:
            if(IsPlayAlarm(alarmID) && !IsPrompt(alarmID)) //TBD 先粗暴实现，只要在播放报警音就不允许清除报警
            {
                if(mDelayCloseAlarm.indexOf(alarmID) == -1)
                    mDelayCloseAlarm << alarmID;
            }
            else
            {
                mDelayCloseAlarm.removeAll(alarmID);
                RemoveAnAlarmItem(alarmID);
                cRet = 1;
            }
            break;
        case ALARM_STATE_DISABLED:
            break;
        default:
            break;
        }
    }
    return cRet;
}

AlarmInfoSt AlarmManager::GetAlarmInfo(E_ALARM_ID AlarmID)
{
    AlarmInfoSt tAlarmBase = {ALARMPRIO_DISABLE, ALL_STRINGS_ENUM::STR_BLANK};

    if (ALARM_END > AlarmID && ALARM_BEGIN <= AlarmID)
    {
        tAlarmBase = sAlarmDefault[AlarmID - ALARM_BEGIN].AlarmBase;
    }
    return tAlarmBase;
}

E_ALARM_STATE AlarmManager::GetAlarmState(E_ALARM_ID AlarmID)
{
    E_ALARM_STATE eAlarmState = ALARM_STATE_DISABLED;
    if (ALARM_END > AlarmID && ALARM_BEGIN <= AlarmID)
    {
        eAlarmState = (E_ALARM_STATE)(mAlarmControl[AlarmID - ALARM_BEGIN].mAlarmState);
    }
    return eAlarmState;
}

void AlarmManager::DisablePhyAlarm(void)
{
    int i = 0;
    for (i = ALARM_BEGIN; i < TECHALARM_BEGIN; i++)
    {
        if(mAlarmControl[i].mAlarmState != ALARM_STATE_DISABLED)
        {
            RemoveAnAlarmItem((E_ALARM_ID)i);
            mAlarmControl[i].mAlarmState = ALARM_STATE_DISABLED;
        }
    }
}

void AlarmManager::EnablePhyAlarm(void)
{
    int i = 0;
    for (i = ALARM_BEGIN; i < TECHALARM_BEGIN; i++)
    {
        if(mAlarmControl[i].mAlarmState == ALARM_STATE_DISABLED)
        {
            mAlarmControl[i].mAlarmState = ALARM_STATE_SET_OFF;
        }
    }
}

void AlarmManager::DisableAllAlarm()
{
    int i = 0;
    for (i = ALARM_BEGIN; i < PROMPT_BEGIN; i++)
    {
        if(mAlarmControl[i].mAlarmState != ALARM_STATE_DISABLED)
        {
            RemoveAnAlarmItem((E_ALARM_ID)i);
            mAlarmControl[i].mAlarmState = ALARM_STATE_DISABLED;
        }
    }
}

void AlarmManager::EnableAllAlarm()
{
    int i = 0;
    for (i = ALARM_BEGIN; i < PROMPT_BEGIN; i++)
    {
        if(mAlarmControl[i].mAlarmState == ALARM_STATE_DISABLED)
        {
            mAlarmControl[i].mAlarmState = ALARM_STATE_SET_OFF;
        }
    }
}

void AlarmManager::DisableAnAlarm(E_ALARM_ID alarm_id)
{
    if(mAlarmControl[alarm_id].mAlarmState != ALARM_STATE_DISABLED)
    {
        RemoveAnAlarmItem((E_ALARM_ID)alarm_id);
        mAlarmControl[alarm_id].mAlarmState = ALARM_STATE_DISABLED;
    }
}

void AlarmManager::EnableAnAlarm(E_ALARM_ID alarm_id)
{
    if (mAlarmControl[alarm_id].mAlarmState == ALARM_STATE_DISABLED)
    {
        mAlarmControl[alarm_id].mAlarmState = ALARM_STATE_SET_OFF;
    }
}

void AlarmManager::RemoveAllAlarm()
{
    int i = 0;
    for (i = ALARM_BEGIN; i < ALARM_END; i++)
    {
        if(mAlarmControl[i].mAlarmState != ALARM_STATE_DISABLED)
        {
            RemoveAnAlarmItem((E_ALARM_ID)i);
        }
    }
}

void AlarmManager::RemoveAllPhyAlarm()
{
    int i = 0;
    for (i = PHY_ALARM_BEGIN; i < PHY_ALARM_END; i++)
    {
        if(mAlarmControl[i].mAlarmState != ALARM_STATE_DISABLED)
        {
            CloseTrigger((E_ALARM_ID)i);
        }
    }
}

QVector<AlarmDisplaySt> AlarmManager::ConvertAlarmIdToAlarmDisplaySt(QVector<E_ALARM_ID> idVec)
{
    QVector<AlarmDisplaySt> infoStVec{};
    foreach (auto alarmId, idVec)
    {
        AlarmDisplaySt tmpSt;
        tmpSt.id = alarmId;
        tmpSt.text = UIStrings::GetStr(GetAlarmInfo(alarmId).mAlarmStrId);
        tmpSt.bgColor =  ConvertAlarmPrioToQColorPair(sAlarmDefault[alarmId - ALARM_BEGIN].AlarmBase.mAlarmPrio).first;
        tmpSt.foreColor =  ConvertAlarmPrioToQColorPair(sAlarmDefault[alarmId - ALARM_BEGIN].AlarmBase.mAlarmPrio).second;
        infoStVec << tmpSt;
    }
    return infoStVec;
}

void AlarmManager::MuteAlarmSound()
{
    if(!AlarmManager::GetInstance()->IsExist(TECHALARM_GASSOURCE_O2_LOW))
    {
        if(IsHaveAlarm())
            this->SetAudioState(AUDIO_PAUSE);
    }
}

bool AlarmManager::isOpenAlarmSound()
{
    return (mAudioState != AUDIO_PAUSE);
}

void AlarmManager::OpenAlarmSound()
{
    this->SetAudioState(AUDIO_NORMAL);
}


void AlarmManager::RefurbishWarning()
{
    QVector<E_ALARM_ID> curAlarms = GetSortedAlarms();
    foreach (auto ite, mDelayCloseAlarm)
        CloseTrigger(E_ALARM_ID(ite));


    E_ALARMPRIO curPrio = (curAlarms.size() == 0) ?  ALARMPRIO_DISABLE : GetAlarmInfo(curAlarms.first()).mAlarmPrio;

    mMutex.lock();
    mCurPriorityPlayAlarm = curPrio;
    mMutex.unlock();

    if (curPrio == ALARMPRIO_DISABLE  || curPrio == ALARMPRIO_PROMPT)
        this->SetAudioState(AUDIO_DISABLED);
}


void AlarmManager::run()
{
    if (QThread::currentThread()->isInterruptionRequested())
    {
        return;
    }
    mMutex.lock();
    int curPrio = mCurPriorityPlayAlarm;
    mMutex.unlock();

    if (curPrio != mCurPlayAlarm && !ManageFactory::GetInstance()->GetDeviceManage()->IsPlayAlarm())
    {
        //在报警级别发生改变时，对报警灯、声进行改变
        mCurPlayAlarm = curPrio;
        switch (curPrio)
        {//报警灯
        case ALARMPRIO_HIG:
            ManageFactory::GetInstance()->GetDeviceManage()->SetLedStatu(LED_YELLOW,DeviceManageBase::LED_OP_CLOSE);
            ManageFactory::GetInstance()->GetDeviceManage()->SetLedStatu(LED_RED,DeviceManageBase::LED_OP_BLINK,250,250);
            break;
        case ALARMPRIO_MID:
            ManageFactory::GetInstance()->GetDeviceManage()->SetLedStatu(LED_RED,DeviceManageBase::LED_OP_CLOSE);
            ManageFactory::GetInstance()->GetDeviceManage()->SetLedStatu(LED_YELLOW,DeviceManageBase::LED_OP_BLINK,1000,1000);
            break;
        case ALARMPRIO_LOW:
            ManageFactory::GetInstance()->GetDeviceManage()->SetLedStatu(LED_RED,DeviceManageBase::LED_OP_CLOSE);
            ManageFactory::GetInstance()->GetDeviceManage()->SetLedStatu(LED_YELLOW,DeviceManageBase::LED_OP_OPEN);
            break;
        default:
            ManageFactory::GetInstance()->GetDeviceManage()->SetLedStatu(LED_RED,DeviceManageBase::LED_OP_CLOSE);
            ManageFactory::GetInstance()->GetDeviceManage()->SetLedStatu(LED_YELLOW,DeviceManageBase::LED_OP_CLOSE);
            break;
        }
        //报警声
        ManageFactory::GetInstance()->GetDeviceManage()->PlayAlarmAudio(curPrio);
        QThread::msleep(200);
    }
    QThread::msleep(50);
}

void AlarmManager::ShowAlarmMenu(int Index1, int Index2)
{
    if(RunModeManage::GetInstance()->GetCurRunMode() != E_RUNMODE::MODE_RUN_SELFTEST)
    {
        //mCurAlarmMenu->ShowTab(Index1, Index2);
        MainWindow::GetInstance()->showDialog(MainWindow::DLG_ALARM_MENU);
        MainWindow::GetInstance()->SetAlarmDialogPage(Index1,Index2);

    }
}


QVector<E_ALARM_ID> AlarmManager::GetSortedAlarms()
{
    QVector<E_ALARM_ID> highPrioAlarms{};
    mMutex.lock();
    highPrioAlarms << mMultiPrioVecMap[ALARMPRIO_HIG]
                      << mMultiPrioVecMap[ALARMPRIO_MID]
                         << mMultiPrioVecMap[ALARMPRIO_LOW]
                            << mMultiPrioVecMap[ALARMPRIO_PROMPT];
    mMutex.unlock();
    return highPrioAlarms;
}

int AlarmManager::GetCurAlarmNum()
{
    return                   mMultiPrioVecMap[ALARMPRIO_HIG].size() +
            mMultiPrioVecMap[ALARMPRIO_MID].size() +
            mMultiPrioVecMap[ALARMPRIO_LOW].size() +
            mMultiPrioVecMap[ALARMPRIO_PROMPT].size();
}


void AlarmManager::TriggerAlarm(E_ALARM_ID AlarmID, unsigned char OnOff)
{
    if (OnOff)
    {
        if (OpenTrigger(AlarmID))     //将报警项加入相应队列
        {
            if (!IsPrompt(AlarmID))
                OpenAlarmSound();
            HistoryEventManager::GetInstance()->AddHistoryEvent(HistoryEventManager::ALARM_EVENT, AlarmID);
        }
    }
    else
    {
        //关闭了一个报警
        CloseTrigger(AlarmID);
    }
}

void AlarmManager::ForeverTriggerAlarm(E_ALARM_ID alarmId, bool isTrigger)
{
    if(isTrigger)
    {
        if(mForeverTriggerAlarm.indexOf(alarmId)==-1)
            mForeverTriggerAlarm.push_back(alarmId);
        TriggerAlarm(alarmId,true);
    }
    else
    {
        mForeverTriggerAlarm.removeOne(alarmId);
        RemoveAnAlarmItem(alarmId);
    }
}

bool AlarmManager::IsPrompt(E_ALARM_ID alarmId)
{
    if (PROMPT_END > alarmId && PROMPT_BEGIN <= alarmId)
    {
        return true;
    }
    else
    {
        return false;
    }
}

bool AlarmManager::IsExist(E_ALARM_ID id)
{
    if(mAlarmControl[id].mAlarmState == ALARM_STATE_SET_ON)
        return true;
    else
        return false;
}

bool AlarmManager::IsEnable(E_ALARM_ID id)
{
    return !(mAlarmControl[id].mAlarmState == ALARM_STATE_DISABLED);
}


//获取报警类型优先级
E_ALARMPRIO AlarmManager::GetAlarmPrio(E_ALARM_ID AlarmID)
{
    if (AlarmID < ALARM_BEGIN)
    {
        DebugAssert(0);
        return ALARMPRIO_DISABLE;
    }

    if (ALARM_STATE_DISABLED == mAlarmControl[AlarmID - ALARM_BEGIN].mAlarmState)
    {
        return ALARMPRIO_DISABLE;
    }

    return sAlarmDefault[AlarmID].AlarmBase.mAlarmPrio;
}

QPair<QColor, QColor> AlarmManager::ConvertAlarmPrioToQColorPair(int alarmPrio)
{
    static QMap<E_ALARMPRIO, QPair<QColor, QColor>> sAlarmCtrlDiscribe=
    {
        //                                              背景色           前景色
        {ALARMPRIO_DISABLE,     {COLOR_BLACK, COLOR_BLACK}},
        {ALARMPRIO_HIG,             {COLOR_RED, COLOR_WHITE}},
        {ALARMPRIO_MID,            {COLOR_LIGHT_YELLOW, COLOR_BLACK}},
        {ALARMPRIO_LOW,           {COLOR_LIGHT_YELLOW, COLOR_BLACK}},
        {ALARMPRIO_PROMPT,      {COLOR_BLACK, COLOR_WHITE}},
    };

    return sAlarmCtrlDiscribe.value(static_cast<E_ALARMPRIO>(alarmPrio), {COLOR_BLACK, COLOR_BLACK});
}


