﻿#include "ChartManager.h"

#include "SystemSettingManager.h"
#include "HistoryEventManager.h"
#include "DataLimitManager.h"
#include "DataManager.h"
#include "DataModel/WaveDataModel.h"

#include "UiApi.h"
#include "ScaleStrategy.h"
#include "WaveChart.h"
#include "LoopChart.h"
#include "DemoModules.h"
#include "Monitoring.h"
#include "ControlButtonManager.h"
#include "VentModeSettingManager.h"
#include "RunModeManage.h"

#define SPONT_COLOR COLOR_PURPLE_1
#define REFUSH_CHART_TIME 40
static ScaleSt sWaveXDefaultScale
{
    0,
    25 / 1000.0 * 1000,
    6,
    0,
    {},
    0
};


static const QMap<DataManager::WAVE_PARAM_TYPE, QVector<ScaleSt>> sWaveDefaultScales
{
    {DataManager::WAVE_PAW  ,  {{-3, 12, 6, DIGIT_0, {-3, 0, 6, 6, 12}},    {-5, 20, 6, DIGIT_0, {-5, 0, 10, 10, 20}},	    {-10, 40, 6, DIGIT_0, {-10, 0, 20, 20, 40}},	{-15, 60, 6, <PERSON><PERSON>IT_0, {-15, 0, 30, 30, 60}},	{-20, 120, 6, DIG<PERSON>_0, {-20, 0, 60, 60, 120}}}},
    {DataManager::WAVE_FLOW ,  {{-30, 30, 6, DIGIT_0, {-30, -30, 0, 30, 30}},   {-60, 60, 6, DIGIT_0, {-60, -60, 0, 60, 60}},	{-120, 120, 6, DIGIT_0, {-120, -120, 0, 120, 120}},	{-180, 180, 6, DIGIT_0, {-180, -180, 0, 180, 180}},  {-180, 180, 6, DIGIT_0, {-180, -180, 0, 180, 180}}}},
    {DataManager::WAVE_VOL  ,  {{0, 100, 6, DIGIT_0, {0, 50, 50, 100, 100}},    {0, 400, 6, DIGIT_0, {0, 200, 200, 400, 400}},	    {0, 600, 6, DIGIT_0, {0, 300, 300, 600, 600}},	{0, 1000, 6, DIGIT_0, {0, 500, 500, 1000, 1000}},	{0, 2500,  6, DIGIT_0, {0, 1250, 1250, 2500, 2500}}}},
    {DataManager::WAVE_CO2  ,  {{0, 8, 6, DIGIT_0, {0,4,8,8}},	 {0, 8, 6, DIGIT_0, {0,4,8,8}},    {0, 8, 6, DIGIT_0, {0,4,8,8}},    {0, 15, 6, DIGIT_1, {0, 7.5, 15, 15}},   {0, 15, 6, DIGIT_1, {0, 7.5, 15, 15}}}}
};

const QVector<WaveInfoSt> ChartManager::sWaveDescriptors
{     /* name  */          /* unit  */                                                               /* name_color  */  		           /* unit_color  */  		/* scale_value_color  */  /* axis_color  */  	  /* mWaveColor  */  		/* WAVE_2_SCALE  */      /* SCALE_2_WAVE  */
    {STR_GRAPHNAME_PAW,   U_SECOND, NONE_CATEGORY,  sWaveDefaultScales[DataManager::WAVE_PAW],    RGB_TO_QCOLOR(BG_RGB(230,109,49)),    RGB_TO_QCOLOR(BG_RGB(150, 70, 30)),   RGB_TO_QCOLOR(BG_RGB(53, 53, 53)), RGB_TO_QCOLOR(BG_RGB(255,124,42)),    },       /* WAVE_PAW  */
    {STR_GRAPHNAME_FLOW,  U_SECOND, NONE_CATEGORY,  sWaveDefaultScales[DataManager::WAVE_FLOW],   RGB_TO_QCOLOR(BG_RGB(49,153,222)),    RGB_TO_QCOLOR(BG_RGB(30, 120, 160)),  RGB_TO_QCOLOR(BG_RGB(53, 53, 53)), RGB_TO_QCOLOR(BG_RGB(48,152,237)),    },       /* WAVE_FLOW */
    {STR_GRAPHNAME_VOL,   U_SECOND, NONE_CATEGORY,  sWaveDefaultScales[DataManager::WAVE_VOL],    RGB_TO_QCOLOR(BG_RGB(83, 239, 106)),  RGB_TO_QCOLOR(BG_RGB(50, 170, 60)),   RGB_TO_QCOLOR(BG_RGB(53, 53, 53)), RGB_TO_QCOLOR(BG_RGB(83, 239, 106)),  },       /* WAVE_VOL  */
    {STR_GRAPHNAME_CO2,   U_SECOND, NONE_CATEGORY,  sWaveDefaultScales[DataManager::WAVE_CO2],    RGB_TO_QCOLOR(BG_RGB(229, 229, 229)), RGB_TO_QCOLOR(BG_RGB(200, 200, 200)), RGB_TO_QCOLOR(BG_RGB(53, 53, 53)), RGB_TO_QCOLOR(BG_RGB(229, 229, 229)), },       /* WAVE_CO2  */
};

static const QVector<LoopInfoSt> sLoopDescriptors
{     /* name  */                /* mXWaveType  */  	 /* mYWaveType  */            /* name_color  */  		/* unit_color  */  		/* scale_value_color  */  	/* axis_color  */  		/* mLoopColor  */
    {STR_GRAPHNAME_VF,    DataManager::WAVE_VOL,  DataManager::WAVE_FLOW,  BG_RGB(134, 134, 134)/*深灰*/,   BG_RGB(134, 134, 134)/*深灰*/, BG_RGB(255,124,42),		BG_RGB(65,64,65),	BG_RGB(48,152,237)},                       /* LOOP_VF  */
    {STR_GRAPHNAME_PV,    DataManager::WAVE_PAW,  DataManager::WAVE_VOL,   BG_RGB(134, 134, 134)/*深灰*/,   BG_RGB(134, 134, 134)/*深灰*/, BG_RGB(48,152,237),		BG_RGB(65,64,65),	BG_RGB(255,124,42)},                       /* LOOP_PV  */
    {STR_GRAPHNAME_FP,    DataManager::WAVE_FLOW, DataManager::WAVE_PAW,   BG_RGB(134, 134, 134)/*深灰*/,   BG_RGB(134, 134, 134)/*深灰*/, BG_RGB(83, 239, 106),		BG_RGB(65,64,65),	BG_RGB(255,124,42)},                   /* LOOP_FP  */
    {STR_GRAPHNAME_VCO2,  DataManager::WAVE_VOL,  DataManager::WAVE_CO2,   BG_RGB(134, 134, 134)/*深灰*/,	BG_RGB(134, 134, 134)/*深灰*/, BG_RGB(0,255,0),		BG_RGB(65,64,65),	BG_RGB(131,129,255)},                      /* LOOP_VC  */
};
//------------------------------------------------------

QMap<ChartManager::CHART_TYPE_ENUM, QMetaObject> metaObjectMap
{
    {ChartManager::WAVE_CHART, WaveScaleStrategy::staticMetaObject},
    {ChartManager::LOOP_CHART, LoopScaleStrategy::staticMetaObject},
};

QMap<int, int> sWaveSpeedToDataNum
{
    {ChartManager::SPEED_LEVEL_LOW,  1250},
    {ChartManager::SPEED_LEVEL_MID,  1000},
    {ChartManager::SPEED_LEVEL_HIGH, 750}
};

ChartManager::ChartManager()
{
    mLoopBackupStrategy = new LoopScaleStrategy;

    mLastPoint.resize(DataManager::WAVE_PARAM_TYPE::WAVE_MAX);
    mWaveCurScales = sWaveDefaultScales;
    mChartStMap.insert(WAVE_CHART,{WAVE_CHART,
                                   QVector<ScaleAdjustStrategy *>{DataManager::WAVE_MAX, nullptr},
                                   QMap<int, QVector<BaseChart *>>(), InitWave});

    mChartStMap.insert(LOOP_CHART,{LOOP_CHART,
                                   QVector<ScaleAdjustStrategy *>{LOOP_TYPE::LOOP_MAX, nullptr},
                                   QMap<int, QVector<BaseChart *>>(), InitLoop});

    foreach (auto &chartSt, mChartStMap)
    {
        auto eChartType = chartSt.chartType;
        for (int i = 0; i < chartSt.strategys.size(); ++i)
        {
            auto paramType = i;

            mChartStMap[eChartType].strategys[paramType] = qobject_cast<ScaleAdjustStrategy *>(metaObjectMap[eChartType].newInstance());
            switch(eChartType)
            {
            case CHART_TYPE_ENUM::WAVE_CHART:
            {
                auto scales = sWaveDefaultScales[(DataManager::WAVE_PARAM_TYPE)paramType];
                mChartStMap[eChartType].strategys[paramType]->SetScaleArray({}, scales);
            }
                break;
            case CHART_TYPE_ENUM::LOOP_CHART:
            {
                auto xWaveParamType = sLoopDescriptors[paramType].mXWaveType;
                auto yWaveParamType = sLoopDescriptors[paramType].mYWaveType;
                mChartStMap[eChartType].strategys[paramType]->SetScaleArray(sWaveDefaultScales[xWaveParamType], sWaveDefaultScales[yWaveParamType]);
            }
                break;
            default:
                break;
            }
        }
    }

    mRefurbishChartTimer = new QTimer(this);
    mRefurbishChartTimer->start(REFUSH_CHART_TIME);
    mRefurbishChartTimer->setTimerType(Qt::PreciseTimer);

    InitConnect();

    for (int i = PRESSURE_CATEGORY; i < NONE_CATEGORY; ++i)
        SlotOnUnitChanged((CONVERTIBLE_UNIT_CATEGORY)i, U_MAX, UnitManager::GetInstance()->GetCategoryCurUnit((CONVERTIBLE_UNIT_CATEGORY)i));

    SetDrawMode(SettingManager->GetIntSettingValue(SystemSettingManager::WAVE_DRAWMODE));

    mPlimitLineEnable = (bool)(SettingManager->GetIntSettingValue(SystemSettingManager::LIMITLINE_SETTING));
    mPlimitLineVisible = true;

    LoopHistoryInterface::GetInstance();
}
ChartManager *ChartManager::GetInstance()
{
    static ChartManager sInstance;
    return &sInstance;
}

void ChartManager::InitChart(int chartType, int paramType, BaseChart *chart)
{
    DebugAssert(mChartStMap.contains((CHART_TYPE_ENUM)chartType));
    mChartStMap[(CHART_TYPE_ENUM)chartType].mInitFunc(paramType, chart);
    UpdatePlimitLine();
}

void ChartManager::RegisterChart(int chartType, int paramType, BaseChart *chartPtr)
{
    DebugAssert(chartType >= WAVE_CHART && chartType < CHART_TYPE_ENUM_END);
    if (!chartPtr)
        return ;
    CHART_TYPE_ENUM eChartType = static_cast<CHART_TYPE_ENUM>(chartType);

    foreach (auto i, mChartStMap[eChartType].mChartsPtrMap[paramType])
        if (i == chartPtr)
            return ;

    foreach (ChartSt chartSt, mChartStMap)
    {
        auto it = chartSt.mChartsPtrMap.cbegin();
        while (it != chartSt.mChartsPtrMap.cend())
        {
            foreach (BaseChart *chart, *it)
            {
                if (chart == chartPtr)
                {
                    CHART_TYPE_ENUM oldChartType = chartSt.chartType;
                    int oldParamType = it.key();
                    mChartStMap[oldChartType].mChartsPtrMap[oldParamType].removeAll(chartPtr);
                    break;
                }
            }
            ++it;
        }
    }

    mChartStMap[eChartType].mChartsPtrMap[paramType] << chartPtr;
    mChartStMap[eChartType].mInitFunc(paramType, chartPtr);

    RefurbishChartScale(chartType, paramType);

    if (eChartType == LOOP_CHART)
    {
        auto backupPointsVec = ConvertDataFromDefaultUnit((LOOP_TYPE)paramType, GetCurChoosenBackup((LOOP_TYPE)paramType).mLoopPointsVec);
        ((LoopChart *)chartPtr)->DrawBackupLoop(backupPointsVec);
        auto bufPointsVec = ConvertDataFromDefaultUnit((LOOP_TYPE)paramType, mLoopBuf[(LOOP_TYPE)paramType]);
        foreach (auto p, bufPointsVec)
        {
            ((LoopChart *)chartPtr)->AddData(p.x(), p.y());
        }
    }
}

QPair<ScaleSt, ScaleSt> ChartManager::GetAppropriateScale(int chartType, int paramType, const QVector<QPointF> &datas)
{
    auto eChartType = static_cast<CHART_TYPE_ENUM>(chartType);
    std::unique_ptr<ScaleAdjustStrategy> p(qobject_cast<ScaleAdjustStrategy *>(metaObjectMap[eChartType].newInstance()));
    switch(eChartType)
    {
    case CHART_TYPE_ENUM::WAVE_CHART:
        p->SetScaleArray({}, sWaveDefaultScales[(DataManager::WAVE_PARAM_TYPE)paramType]);
        break;
    case CHART_TYPE_ENUM::LOOP_CHART:
    {
        auto xWaveParamType = sLoopDescriptors[paramType].mXWaveType;
        auto yWaveParamType = sLoopDescriptors[paramType].mYWaveType;
        p->SetScaleArray(sWaveDescriptors[xWaveParamType].mYDefaultScales, sWaveDescriptors[yWaveParamType].mYDefaultScales);
    }
        break;
    default:
        break;
    }
    for (auto data : datas)
    {
        p->DataFilter(data.x(), data.y());
    }
    p->SetTimesToSwitch(1);
    p->AdjustScale();


    auto scaleId = p->GetCurScale();

    if (chartType == LOOP_CHART)
    {
        auto xWaveParamType = sLoopDescriptors[paramType].mXWaveType;
        auto yWaveParamType = sLoopDescriptors[paramType].mYWaveType;

        auto xScales = sWaveDefaultScales[xWaveParamType];
        auto yScales = sWaveDefaultScales[yWaveParamType];

        auto loopStrategys = qobject_cast<LoopScaleStrategy *>(p.get());
        return {xScales[loopStrategys->GetCurXScale()], yScales[loopStrategys->GetCurYScale()]};
    }
    else
    {
        ScaleSt reX = sWaveXDefaultScale;
        reX.mMax=datas.last().x();
        return {reX, sWaveDefaultScales[(DataManager::WAVE_PARAM_TYPE)paramType][scaleId]};
    }

}

void ChartManager::RefurbishChart(CHART_TYPE_ENUM chartType, QMap<DataManager::WAVE_PARAM_TYPE, QVector<QPair<qreal, bool>>> dataMap)
{
    switch(chartType)
    {
    case CHART_TYPE_ENUM::WAVE_CHART:
    {
        if (mFreezeFlag)
            return;
        for (int waveType = DataManager::WAVE_PAW; waveType < DataManager::WAVE_MAX; waveType++)
        {
            QVector<ColorDataSt> colorDataVec{};

            foreach (auto dataPair, dataMap[(DataManager::WAVE_PARAM_TYPE)waveType])
            {
                qreal tmpdata = dataPair.first;
                colorDataVec.append({tmpdata,dataPair.second ? SPONT_COLOR : (sWaveDescriptors[waveType].mWaveColor)});
            }

            foreach (auto chart, mChartStMap[chartType].mChartsPtrMap[waveType])
            {
                auto wave = dynamic_cast<WaveChart *>(chart);
                if (chart)
                {
                    wave->AddData(colorDataVec);
                }
            }
        }
    }
        break;

    case CHART_TYPE_ENUM::LOOP_CHART:
    {
        for (int loopParamType = LOOP_VF; loopParamType < LOOP_MAX; ++loopParamType)
        {
            if(RunModeManage::GetInstance()->GetCurRunMode() == E_RUNMODE::MODE_RUN_ACGO)
                break;
            auto xWaveParamType = sLoopDescriptors[loopParamType].mXWaveType;
            auto yWaveParamType = sLoopDescriptors[loopParamType].mYWaveType;

            QVector<QPair<qreal, bool>> xBuf = dataMap[xWaveParamType], yBuf = dataMap[yWaveParamType];

            foreach (auto chart, mChartStMap[chartType].mChartsPtrMap[loopParamType])
            {
                auto loop = dynamic_cast<LoopChart *>(chart);
                if (chart)
                {
                    for (int j = 0; j < xBuf.size(); j++)
                    {
                        loop->AddData(xBuf[j].first, yBuf[j].first);
                    }
                }
            }
        }
    }
        break;
    default:
        break;
    }
}

void ChartManager::RefurbishLoopBackup(int chartType, int paramType)
{
    if (!mChartStMap[(CHART_TYPE_ENUM)chartType].strategys[paramType])
        return;

    foreach (BaseChart *chart, mChartStMap[(CHART_TYPE_ENUM)chartType].mChartsPtrMap[paramType])
    {
        if (chart)
        {
            auto backupPointsVec = ConvertDataFromDefaultUnit((LOOP_TYPE)paramType, GetCurChoosenBackup((LOOP_TYPE)paramType).mLoopPointsVec);
            ((LoopChart *)chart)->DrawBackupLoop(backupPointsVec);
        }
    }
}

void ChartManager::UpChartData()
{
    if (!RunModeManage::GetInstance()->IsModeActive(MODE_RUN_VENT))
        return;
    if(VentModeSettingManager::GetInstance()->GetCurMode() == VENTMODE_FLAG_HLM
            && RunModeManage::GetInstance()->GetCurRunMode() != E_RUNMODE::MODE_RUN_MANUAL)
        return;

    if(mIsPeriodEnd)
    {
        if (!GetIsFreeze())
        {
            AdjustScale(CHART_TYPE_ENUM::WAVE_CHART);
        }
        ClearLoopData();
        AdjustScale(CHART_TYPE_ENUM::LOOP_CHART);
        mIsPeriodEnd = false;
    }
    auto waveDataBuf = mWaveDataBuf;
    for (int i = 0; i < mWaveDataBuf.size(); ++i)
    {
        auto buf = mWaveDataBuf[(DataManager::WAVE_PARAM_TYPE)i];
        if(!buf.empty())
        {
           mWaveDataBuf[(DataManager::WAVE_PARAM_TYPE)i].clear();
        }
    }
    auto bufCnt = waveDataBuf[DataManager::WAVE_PAW].size();

    for (int i = 0; i < waveDataBuf.size(); ++i)
    {
        QVector<QPair<qreal, bool>> &buf = waveDataBuf[(DataManager::WAVE_PARAM_TYPE)i];
        if  (buf.size() < bufCnt)
        {
            while (buf.size() < bufCnt)
            {
                if (buf.size() == 0)
                    if (mWaveDataBackup[i].size() != 0)
                        buf.append(mWaveDataBackup[i].last());
                    else
                        buf.append({0, 0});
                else
                    buf.append(buf.last());
            }
        }
        else if (buf.size() > bufCnt)
        {
            while (buf.size() > bufCnt)
                buf.removeLast();
        }
    }

    for (int loopParamType = LOOP_VF; loopParamType < LOOP_MAX; ++loopParamType)
    {
        if(RunModeManage::GetInstance()->GetCurRunMode() == E_RUNMODE::MODE_RUN_ACGO)
            break;
        auto xWaveParamType = sLoopDescriptors[loopParamType].mXWaveType;
        auto yWaveParamType = sLoopDescriptors[loopParamType].mYWaveType;

        for (int j = 0; j < bufCnt; j++)
        {
            if (mLoopBuf[(LOOP_TYPE)loopParamType].size() >= 800)
                mLoopBuf[(LOOP_TYPE)loopParamType].remove(0, 50);
            mLoopBuf[(LOOP_TYPE)loopParamType].append({waveDataBuf[xWaveParamType][j].first, waveDataBuf[yWaveParamType][j].first});

            if (mChartStMap[LOOP_CHART].strategys[loopParamType])
                mChartStMap[LOOP_CHART].strategys[loopParamType]->DataFilter(waveDataBuf[xWaveParamType][j].first, waveDataBuf[yWaveParamType][j].first);
        }
    }


    for (int waveType = DataManager::WAVE_PAW; waveType < DataManager::WAVE_MAX; ++waveType)
    {
        if (mWaveDataBackup[(DataManager::WAVE_PARAM_TYPE)waveType].size() >= 2000)
            mWaveDataBackup[(DataManager::WAVE_PARAM_TYPE)waveType].remove(0, 500);
        mWaveDataBackup[(DataManager::WAVE_PARAM_TYPE)waveType] << waveDataBuf[(DataManager::WAVE_PARAM_TYPE)waveType];
        foreach (auto data, waveDataBuf[(DataManager::WAVE_PARAM_TYPE)waveType])
        {
            if (mFreezeFlag == false)
            {
                mChartStMap[WAVE_CHART].strategys[waveType]->DataFilter(0, data.first);
            }
        }
    }

    for (int waveType = DataManager::WAVE_PAW; waveType < DataManager::WAVE_MAX; ++waveType)
    {
        ConvertDataFromDefaultUnit((DataManager::WAVE_PARAM_TYPE)waveType, waveDataBuf[(DataManager::WAVE_PARAM_TYPE)waveType]);
    }

    static QTime temp = QTime::currentTime();
    QTime temp2 = QTime::currentTime();
    if(temp.msecsTo(temp2)>100)
    {
        DebugLog<<"add Data out 100MS!!!!"<<temp.msecsTo(temp2) ;
    }
    temp=temp2;
    RefurbishChart(CHART_TYPE_ENUM::WAVE_CHART, waveDataBuf);
    RefurbishChart(CHART_TYPE_ENUM::LOOP_CHART, waveDataBuf);
}

void ChartManager::AdjustScale(int chartType)
{
    foreach (auto stradegy, mChartStMap[static_cast<CHART_TYPE_ENUM>(chartType)].strategys)
    {
        if (stradegy)
            stradegy->AdjustScale();
    }
}

void ChartManager::SetFreeze(bool freeze)
{
    if (freeze == mFreezeFlag || !RunModeManage::GetInstance()->IsModeActive(E_RUNMODE::MODE_RUN_VENT))
        return;

    if(RunModeManage::GetInstance()->GetCurRunMode() != E_RUNMODE::MODE_RUN_MANUAL
            && RunModeManage::GetInstance()->GetCurRunMode() != E_RUNMODE::MODE_RUN_ACGO)
    {
        if (VentModeSettingManager::GetInstance()->GetCurMode() == VENTMODE_FLAG_HLM
                && mFreezeFlag == false)
            return ;
    }

    mWaveFreezenDataBuf->clear();
    if (freeze)
    {
        auto needDataCnt = sWaveSpeedToDataNum[mWaveSpeed];

        for (int dataType = DataManager::WAVE_PAW; dataType < DataManager::WAVE_MAX; ++dataType)
        {
            QVector<QPair<qreal, bool>> backupDataVec{};
            if (mWaveDataBackup->size() < needDataCnt)
                backupDataVec = mWaveDataBackup[dataType];
            else
            {
                backupDataVec = mWaveDataBackup[dataType].mid(mWaveDataBackup[dataType].size() - needDataCnt);
            }

            mWaveFreezenDataBuf[dataType] = backupDataVec;

            ConvertDataFromDefaultUnit((DataManager::WAVE_PARAM_TYPE)dataType, backupDataVec);

            QVector<QPair<qreal, QColor>> dataVec{};
            foreach (auto &&data, backupDataVec)
            {
                dataVec.append(qMakePair(data.first, data.second ? SPONT_COLOR : (sWaveDescriptors[dataType].mWaveColor)));
            }

            foreach (auto chart, mChartStMap[WAVE_CHART].mChartsPtrMap[dataType])
            {
                auto wave = dynamic_cast<WaveChart *>(chart);
                if (wave)
                {
                    wave->ClearData();
                    wave->SetHighLimitLineVisible(true);
                    if (dataVec.size() != 0)
                    {
                        wave->ReplaceData(dataVec);
                        wave->SetYCursorVisible(true);
                    }
                }
            }
        }
    }
    else
    {
        for (int dataType = DataManager::DataManager::WAVE_PAW; dataType < DataManager::DataManager::WAVE_MAX; ++dataType)
        {
            foreach (auto chart, mChartStMap[CHART_TYPE_ENUM::WAVE_CHART].mChartsPtrMap[dataType])
            {
                auto wave = dynamic_cast<WaveChart *>(chart);
                wave->ClearData();
                wave->SetHighLimitLineVisible(true);
                wave->SetYCursorVisible(false);
                wave->Redraw();
            }
        }
    }

    mFreezeFlag = freeze;
    emit SignalFreezeFlagChanged(mFreezeFlag);
}

bool ChartManager::GetIsFreeze()
{
    return mFreezeFlag;
}


void ChartManager::UpdatePlimitLine()
{
    auto srcHighPawLimit = VentModeSettingManager::GetInstance()->GetSettingdata(SETTINGDATA_PLIMIT);
    qreal highPawLimit = UnitManager::GetInstance()->ConvertValue(PRESSURE_CATEGORY,
                                                                  DataManager::GetDefaultUnit(DataManager::WAVE_PAW),
                                                                  UnitManager::GetInstance()->GetCategoryCurUnit(PRESSURE_CATEGORY),
                                                                  srcHighPawLimit);

    foreach (auto chart, mChartStMap[CHART_TYPE_ENUM::WAVE_CHART].mChartsPtrMap[DataManager::WAVE_PAW])
    {
        auto wave = dynamic_cast<WaveChart *>(chart);
        if (wave)
        {
            if(!mPlimitLineEnable)
            {
                wave->SetHighLimitLine(highPawLimit,Qt::transparent);
                wave->Redraw();
            }
            else
            {
                wave->SetHighLimitLine(highPawLimit, mPlimitLineVisible ? COLOR_RED : Qt::transparent);
                wave->Redraw();
            }
        }
    }
}

void ChartManager::SetPlimitLineEnable(bool visible)
{
    if(mPlimitLineEnable == visible)
        return;
    mPlimitLineEnable = visible;
    UpdatePlimitLine();
}

bool ChartManager::GetPlimitLineVisible()
{
    return mPlimitLineVisible;
}

void ChartManager::SetWaveSpeed(int speed)
{
    if (speed == mWaveSpeed)
        return;
    ClearChartData(WAVE_CHART, false);
    mWaveSpeed = speed;

    int dataNumPerWindow = 0;
    if (sWaveSpeedToDataNum.contains(mWaveSpeed))
        dataNumPerWindow = sWaveSpeedToDataNum[mWaveSpeed];
    else
        DebugAssert("sWaveSpeedToDataNum map error");

    sWaveXDefaultScale.mMax = 25 * dataNumPerWindow * 1.0 / sWaveSpeedToDataNum[ChartManager::SPEED_LEVEL_LOW];

    for (int dataType = DataManager::WAVE_PAW; dataType < DataManager::WAVE_MAX; ++dataType)
    {
        foreach (auto chart, mChartStMap[CHART_TYPE_ENUM::WAVE_CHART].mChartsPtrMap[dataType])
        {
            auto wave = dynamic_cast<WaveChart *>(chart);
            wave->SetXScale(sWaveXDefaultScale);
            wave->SetDataNumPerWindow(dataNumPerWindow);
        }
    }

    emit SignalWaveSpeedChanged(mWaveSpeed);
    SetFreeze(false);
    UpdatePlimitLine();
}

int ChartManager::GetWaveSpeed()
{
    return mWaveSpeed;
}

QString ChartManager::GetWaveName(DataManager::WAVE_PARAM_TYPE type)
{
    auto waveNameId = sWaveDescriptors[(int)type].mWaveName;
    return UIStrings::GetStr((ALL_STRINGS_ENUM)waveNameId);
}

QColor ChartManager::GetWaveColor(DataManager::WAVE_PARAM_TYPE type)
{
    return sWaveDescriptors[type].mWaveColor;
}

QVector<QPointF> ChartManager::ConvertDataFromDefaultUnit(LOOP_TYPE loopType, QVector<QPointF> data)
{
    if (data.size() == 0)
        return {};
    auto xWaveParamType = sLoopDescriptors[loopType].mXWaveType;
    auto yWaveParamType = sLoopDescriptors[loopType].mYWaveType;

    auto xInfo = DataManager::GetWaveParamDefaultInfo(xWaveParamType);
    auto yInfo = DataManager::GetWaveParamDefaultInfo(yWaveParamType);

    auto category = xInfo.mConvertibleUnitCategory;

    if (category != CONVERTIBLE_UNIT_CATEGORY::NONE_CATEGORY)
    {
        for (int i = 0; i < data.size(); ++i)
        {
            data[i].setX(UnitManager::GetInstance()->ConvertValue(category,
                                                                  DataManager::GetDefaultUnit(xWaveParamType),
                                                                  UnitManager::GetInstance()->GetCategoryCurUnit(category),
                                                                  data[i].x()));
        }
    }

    category = yInfo.mConvertibleUnitCategory;
    if (category != CONVERTIBLE_UNIT_CATEGORY::NONE_CATEGORY)
    {
        for (int i = 0; i < data.size(); ++i)
        {
            data[i].setY(UnitManager::GetInstance()->ConvertValue(category,
                                                                  DataManager::GetDefaultUnit(yWaveParamType),
                                                                  UnitManager::GetInstance()->GetCategoryCurUnit(category),
                                                                  data[i].y()));
        }
    }

    return data;
}

void ChartManager::ConvertDataFromDefaultUnit(DataManager::WAVE_PARAM_TYPE paramType, QVector<QPair<qreal, bool> > &dataVec)
{
    for (auto &dataPair : dataVec)
    {
        qreal tmpdata = dataPair.first;
        auto category = DataManager::GetWaveParamDefaultInfo(paramType).mConvertibleUnitCategory;
        if (category != CONVERTIBLE_UNIT_CATEGORY::NONE_CATEGORY)
        {
            dataPair.first = UnitManager::GetInstance()->ConvertValue(category,
                                                                      DataManager::GetDefaultUnit(paramType),
                                                                      UnitManager::GetInstance()->GetCategoryCurUnit(category),
                                                                      tmpdata);
        }
    }
}

void ChartManager::SlotOnCurPeriodEnd()
{
    if(RunModeManage::GetInstance()->GetCurRunMode() != MODE_RUN_MANUAL
            && VentModeSettingManager::GetInstance()->GetCurMode() == VENTMODE_FLAG_HLM)
        return;

    for(int i=0;i<DataManager::WAVE_PARAM_TYPE::WAVE_CO2;i++)
    {
        if(!mWaveDataBuf[(DataManager::WAVE_PARAM_TYPE)i].isEmpty())
            mLastPoint[i] = mWaveDataBuf[(DataManager::WAVE_PARAM_TYPE)i].last(); //获取呼吸周期最后一个点
    }
    UpChartData();
    for(int i=0;i<DataManager::WAVE_PARAM_TYPE::WAVE_CO2;i++)
    {
        mWaveDataBuf[(DataManager::WAVE_PARAM_TYPE)i].append(mLastPoint[i]);
    }
    mIsPeriodEnd = true;
}

void ChartManager::RefurbishChartScale(int chartType, int paramType)
{
    if (!mChartStMap[(CHART_TYPE_ENUM)chartType].strategys[paramType])
        return;

    foreach (BaseChart *chart, mChartStMap[(CHART_TYPE_ENUM)chartType].mChartsPtrMap[paramType])
    {
        if (chart)
        {
            if (chartType == LOOP_CHART)
            {
                auto xWaveParamType = sLoopDescriptors[paramType].mXWaveType;
                auto yWaveParamType = sLoopDescriptors[paramType].mYWaveType;

                auto xScales = mWaveCurScales[xWaveParamType];
                auto yScales = mWaveCurScales[yWaveParamType];

                auto loopStrategys = qobject_cast<LoopScaleStrategy *>(mChartStMap[(CHART_TYPE_ENUM)chartType].strategys[paramType]);
                chart->SetScales(xScales[loopStrategys->GetCurXScale()], yScales[loopStrategys->GetCurYScale()]);
            }
            else
            {
                auto scaleId = mChartStMap[(CHART_TYPE_ENUM)chartType].strategys[paramType]->GetCurScale();
                chart->SetYScale(mWaveCurScales[(DataManager::WAVE_PARAM_TYPE)paramType][scaleId]);
                chart->SetCurveColor(sWaveDescriptors[(DataManager::WAVE_PARAM_TYPE)paramType].mWaveColor);
            }
        }
    }
}


QPair<ScaleSt, ScaleSt> ChartManager::GetCurScale(int chartType, int paramType)
{
    auto eChartType = (CHART_TYPE_ENUM)chartType;
    auto paramScaleStategy = mChartStMap[eChartType].strategys[paramType];

    if (chartType == LOOP_CHART)
    {
        auto xWaveParamType = sLoopDescriptors[paramType].mXWaveType;
        auto yWaveParamType = sLoopDescriptors[paramType].mYWaveType;

        auto xScales = mWaveCurScales[xWaveParamType];
        auto yScales = mWaveCurScales[yWaveParamType];

        auto loopStrategys = qobject_cast<LoopScaleStrategy *>(paramScaleStategy);
        return {xScales[loopStrategys->GetCurXScale()], yScales[loopStrategys->GetCurYScale()]};
    }
    else
    {
        auto scaleId = paramScaleStategy->GetCurScale();
        return {sWaveXDefaultScale, mWaveCurScales[(DataManager::WAVE_PARAM_TYPE)paramType][scaleId]};
    }

}

void ChartManager::SlotOnUnitChanged(CONVERTIBLE_UNIT_CATEGORY category, UNIT_TYPE oldType, UNIT_TYPE newType)
{
    if (category == CONVERTIBLE_UNIT_CATEGORY::NONE_CATEGORY)
        return;

    ALL_STRINGS_ENUM unitStrId{};
    short digit{};

    for (int type = DataManager::WAVE_PAW; type < DataManager::WAVE_MAX; ++type)
    {
        auto waveParamType = static_cast<DataManager::WAVE_PARAM_TYPE>(type);
        if (DataManager::GetWaveParamDefaultInfo(waveParamType).mConvertibleUnitCategory == category)
        {
            UnitInfoSt unitInfo = UnitManager::GetInfoSt(newType);
            unitStrId = (ALL_STRINGS_ENUM)unitInfo.mUnitStrId;
            digit = unitInfo.digit;

            auto scales = sWaveDefaultScales[waveParamType];
            if (newType == DataManager::GetDefaultUnit(waveParamType)) //co2参数值单位互转会损失精度，特此补丁 lijin
            {
                foreach (auto chart, mChartStMap[WAVE_CHART].mChartsPtrMap[waveParamType])
                {
                    chart->SetYUnit(UIStrings::GetStr(unitStrId));
                    auto wavePtr = dynamic_cast<WaveChart *>(chart);
                    auto dataCnt = wavePtr->GetCurDataCnt();

                    auto historydata = mWaveDataBackup[waveParamType].mid(mWaveDataBackup[waveParamType].size() - dataCnt);
                    if (mFreezeFlag)
                        historydata = mWaveFreezenDataBuf[waveParamType];
                    QVector<ColorDataSt> colorDataVec{};
                    foreach (auto dataPair, historydata)
                    {
                        qreal tmpdata = dataPair.first;
                        colorDataVec.append({tmpdata, dataPair.second ? SPONT_COLOR : (sWaveDescriptors[waveParamType].mWaveColor)});
                    }

                    if (dataCnt < wavePtr->GetDataNumPerWindow())
                    {
                        wavePtr->ClearData();
                    }
                    wavePtr->AddData(colorDataVec);
                    auto limits = DataManager::GetInstance()->GetRange(waveParamType);
                    wavePtr->SetValueLimit(limits.first, limits.second);
                    if(newType == U_PERCENT)
                        wavePtr->SetYCursorDigit(DIGIT_1);
                    else
                        wavePtr->SetYCursorDigit(DIGIT_0);
                    wavePtr->RefurbishCursor();
                }

                mWaveCurScales[waveParamType] = scales;
                RefurbishChartScale(WAVE_CHART, waveParamType);
            }
            else
            {
                auto convertFunc = UnitManager::GetInstance()->ConvertFunctor(category, DataManager::GetDefaultUnit(waveParamType), newType);
                foreach (auto chart, mChartStMap[WAVE_CHART].mChartsPtrMap[waveParamType])
                {
                    chart->SetYUnit(UIStrings::GetStr(unitStrId));
                    auto wavePtr = dynamic_cast<WaveChart *>(chart);
                    auto limits = DataManager::GetInstance()->GetRange(waveParamType);
                    wavePtr->SetValueLimit(convertFunc(limits.first), convertFunc(limits.second));
                    wavePtr->ConvertWaveData(UnitManager::GetInstance()->ConvertFunctor(category, oldType, newType));
                    if(newType == U_KPA)
                        wavePtr->SetYCursorDigit(DIGIT_1);
                    else
                        wavePtr->SetYCursorDigit(DIGIT_0);
                    wavePtr->RefurbishCursor();
                }

                for (auto &scale : scales)
                {
                    if(newType == U_MMHG) // TBD 需求需要刻度取整
                    {
                        scale.mMin = 0;
                        scale.mMax = scale.mMax == 8 ? 60 : 120;
                        for (auto &i : scale.mDetailScale)
                        {
                            if(i == 4) i = 30;
                            else if(i == 7.5) i = 60;
                            else if(i == 0) i = 0;
                            else i = i == 8 ? 60 : 120;
                        }
                    }
                    else
                    {
                        scale.mMin = convertFunc(scale.mMin);
                        scale.mMax = convertFunc(scale.mMax);
                        for (auto &i : scale.mDetailScale)
                            i = convertFunc(i);
                    }
                    scale.mDisplayPrecison = digit;
                }
                mWaveCurScales[waveParamType] = scales;
                RefurbishChartScale(WAVE_CHART, waveParamType);
            }
            UpdatePlimitLine();
        }
    }
    for (int loopType = LOOP_VF; loopType < LOOP_MAX; loopType++)
    {
        auto xWaveParamType = sLoopDescriptors[loopType].mXWaveType;
        auto yWaveParamType = sLoopDescriptors[loopType].mYWaveType;


        bool isXNeedChange = DataManager::GetWaveParamDefaultInfo(xWaveParamType).mConvertibleUnitCategory == category;
        bool isYNeedChange = DataManager::GetWaveParamDefaultInfo(yWaveParamType).mConvertibleUnitCategory == category;


        if (isXNeedChange)
        {
            RefurbishChartScale(LOOP_CHART, loopType);

            foreach (auto chart, mChartStMap[LOOP_CHART].mChartsPtrMap[loopType])
            {
                chart->SetXUnit(UIStrings::GetStr(unitStrId));
            }
        }
        if (isYNeedChange)
        {
            RefurbishChartScale(LOOP_CHART, loopType);
            foreach (auto chart, mChartStMap[LOOP_CHART].mChartsPtrMap[loopType])
            {
                chart->SetYUnit(UIStrings::GetStr(unitStrId));
            }
        }

        if (isXNeedChange || isYNeedChange)
        {
            auto backupPointsVec = ConvertDataFromDefaultUnit((LOOP_TYPE)loopType, GetCurChoosenBackup((LOOP_TYPE)loopType).mLoopPointsVec);
            foreach (auto chartPtr, mChartStMap[LOOP_CHART].mChartsPtrMap[loopType])
            {
                ((LoopChart *)chartPtr)->DrawBackupLoop(backupPointsVec);
            }
        }
    }
}


void ChartManager::ClearChartData(int chartType, bool isClearBackups)
{
    foreach (auto stradegy, mChartStMap[static_cast<CHART_TYPE_ENUM>(chartType)].strategys)
    {
        if (stradegy)
            stradegy->ClearMinMax();
    }

    for (int dataType = DataManager::WAVE_PAW; dataType < DataManager::WAVE_MAX; ++dataType)
    {
        if(isClearBackups)
            mWaveDataBackup[(DataManager::WAVE_PARAM_TYPE)dataType].clear();
        mWaveDataBuf[(DataManager::WAVE_PARAM_TYPE)dataType].clear();
    }

    switch(chartType)
    {
    case CHART_TYPE_ENUM::WAVE_CHART:
        for (int dataType = DataManager::WAVE_PAW; dataType < DataManager::WAVE_MAX; ++dataType)
        {
            foreach (auto chartPtr, mChartStMap[WAVE_CHART].mChartsPtrMap[dataType])
            {
                auto wave = dynamic_cast<WaveChart *>(chartPtr);
                if (wave)
                {
                    wave->ClearData();
                    wave->Redraw();
                }
            }
        }
        break;

    case CHART_TYPE_ENUM::LOOP_CHART:
        mLoopBuf.clear();

        for (int dataType = 0; dataType < LOOP_TYPE::LOOP_MAX; ++dataType)
        {
            foreach (auto chartPtr, mChartStMap[LOOP_CHART].mChartsPtrMap[dataType])
            {
                auto loop = dynamic_cast<LoopChart *>(chartPtr);
                if (loop)
                {
                    loop->ClearData();
                    loop->Redraw();
                }
            }
        }

        break;
    default:
        break;
    }

    SetFreeze(false);
}

void ChartManager::RefurbishData(int dataType, qreal data, bool isSpont)
{
    if (!RunModeManage::GetInstance()->IsModeActive(MODE_RUN_VENT))
        return;
    if (data == INVALID_VALUE)
        return;

    mWaveDataBuf[(DataManager::WAVE_PARAM_TYPE)dataType].append({data, isSpont});
}

void ChartManager::SetDrawMode(int drawMode)
{
    if(mDrawMode == drawMode)
        return;
    mDrawMode = drawMode;
    emit SignalDrawModeChanged(drawMode);
    SetFreeze(false);
}

int ChartManager::GetDrawMode()
{
    return mDrawMode;
}
void ChartManager::ClearLoopData()
{
    for (int i = LOOP_VF; i < LOOP_MAX; i++)
    {
        foreach (auto chart, mChartStMap[LOOP_CHART].mChartsPtrMap[i])
        {
            auto loopChart = dynamic_cast<LoopChart *>(chart);
            loopChart->ClearData();
        }

        if (mCurChoosenTime != -1)
            if (mLoopBackups.size() != 0)
            {
                foreach (auto &backupVec, mLoopBackups)
                {
                    DebugAssert(!backupVec.empty());
                    if (backupVec.first().mTimestamp == mCurChoosenTime)
                    {
                        auto backup = backupVec[i];
                        auto backupPointsVec = backup.mLoopPointsVec;
                        foreach (auto &data, backupPointsVec)
                            mChartStMap[LOOP_CHART].strategys[i]->DataFilter(data.x(), data.y());
                        break;
                    }
                }
            }
    }


    if (mLoopBackupFlag)
    {
        auto curTime = QDateTime::currentMSecsSinceEpoch();
        QVector<LoopHistorySt> tmpVec{};
        for (int loopType = LOOP_VF; loopType < LOOP_MAX; ++loopType)
        {
            short mvTmp, vteTmp, rateTmp, ieTmp, peepTmp, ppeakTmp, pplatTmp, pmeanTemp, cTmp, rTmp;
            DataManager::GetInstance()->GetMonitorData(VALUEDATA_VTE,    vteTmp);
            DataManager::GetInstance()->GetMonitorData(VALUEDATA_MV,     mvTmp);
            DataManager::GetInstance()->GetMonitorData(VALUEDATA_RATE,   rateTmp);
            DataManager::GetInstance()->GetMonitorData(VALUEDATA_PPLAT,  pplatTmp);
            DataManager::GetInstance()->GetMonitorData(VALUEDATA_PMEAN,  pmeanTemp);
            DataManager::GetInstance()->GetMonitorData(VALUEDATA_PPEAK,  ppeakTmp);
            DataManager::GetInstance()->GetMonitorData(VALUEDATA_PEEP,  peepTmp);
            DataManager::GetInstance()->GetMonitorData(VALUEDATA_C,      cTmp );
            DataManager::GetInstance()->GetMonitorData(VALUEDATA_R,      rTmp);
            DataManager::GetInstance()->GetMonitorData(VALUEDATA_IE,      ieTmp);

            LoopHistorySt loopSt{};
            loopSt.mTimestamp = curTime;
            loopSt.mLoopPointsVec = std::move(mLoopBuf[(LOOP_TYPE)loopType]);
            loopSt.mLoopType = loopType;
            loopSt.mVte = vteTmp;
            loopSt.mMv = mvTmp;
            loopSt.mRate = rateTmp;
            loopSt.mIE = ieTmp;
            loopSt.mPeep = peepTmp;
            loopSt.mPpeak = ppeakTmp;
            loopSt.mPplat = pplatTmp;
            loopSt.mPmean = pmeanTemp;
            loopSt.mC = cTmp;
            loopSt.mR = rTmp;

            LoopHistoryInterface::GetInstance()->AddLoopData(loopSt);
            tmpVec << loopSt;
        }
        mLoopBackups << tmpVec;

        emit SignalLoopBackupChanged();
        mLoopBackupFlag = false;
    }

    for (int i = LOOP_VF; i < LOOP_MAX; i++)
    {
        mLoopBuf[(LOOP_TYPE)i].clear();
    }
}

void ChartManager::AddBackUp()
{
    if(RunModeManage::GetInstance()->GetCurRunMode() != MODE_RUN_MANUAL
            && VentModeSettingManager::GetInstance()->GetCurMode() == VENTMODE_FLAG_HLM)
        return;
    mLoopBackupFlag = true;
}

int ChartManager::GetLoopBackupsCnt()
{
    return mLoopBackups.count();
}

QVector<LoopHistorySt> ChartManager::GetBackups(LOOP_TYPE type)
{
    QVector<LoopHistorySt> tmp{};
    foreach (auto &vec, mLoopBackups)
        foreach (auto &loopSt, vec)
            if (loopSt.mLoopType == type)
                tmp.append(loopSt);
    return tmp;
}


void ChartManager::DeleteBackup(qint64 timeStamp)
{
    for (auto i = 0; i < mLoopBackups.size(); ++ i)
    {
        DebugAssert(!mLoopBackups.empty());
        if (mLoopBackups[i].first().mTimestamp == timeStamp)
        {
            mLoopBackups.remove(i);
            emit SignalLoopBackupChanged();
        }
    }

    if (mLoopBackups.size() == 0)
    {
        HideLoopBackup();
    }
}

void ChartManager::DeleteAllBackup()
{
    mLoopBackups.clear();
    mLoopBackupFlag = false;
    emit SignalLoopBackupChanged();
}

void ChartManager::DrawLoopBackups(qint64 timeStamp)
{
    if (mLoopBackups.empty())
        return;
    foreach (auto &backupVec, mLoopBackups)
    {
        DebugAssert(!backupVec.empty());
        if (backupVec.first().mTimestamp == timeStamp)
        {
            bool isAdjust = false;
            for (auto &backup : backupVec)
            {
                auto backupPointsVec = backup.mLoopPointsVec;
                auto xWaveParamType = sLoopDescriptors[backup.mLoopType].mXWaveType;
                auto yWaveParamType = sLoopDescriptors[backup.mLoopType].mYWaveType;
                mLoopBackupStrategy->SetScaleArray(sWaveDefaultScales[xWaveParamType], sWaveDefaultScales[yWaveParamType]);
                auto xScales = mWaveCurScales[xWaveParamType];
                auto yScales = mWaveCurScales[yWaveParamType];
                auto loopStrategys = qobject_cast<LoopScaleStrategy *>(mChartStMap[LOOP_CHART].strategys[backup.mLoopType]);
                if(!isAdjust)
                {
                    if( RunModeManage::GetInstance()->GetCurRunMode() == MODE_RUN_STANDBY || RunModeManage::GetInstance()->GetCurRunMode() == MODE_RUN_ACGO ||
                            (VentModeSettingManager::GetInstance()->GetCurMode() == VENTMODE_FLAG_HLM
                             && RunModeManage::GetInstance()->GetCurRunMode() != MODE_RUN_MANUAL) )
                    {
                        isAdjust = true;
                    }
                    else
                    {
                        qreal xMax = xScales[loopStrategys->GetCurXScale()].mMax;
                        qreal xMin = xScales[loopStrategys->GetCurXScale()].mMin;
                        qreal yMax = yScales[loopStrategys->GetCurYScale()].mMax;
                        qreal yMin = yScales[loopStrategys->GetCurYScale()].mMin;
                        foreach (auto &data, ConvertDataFromDefaultUnit((LOOP_TYPE)backup.mLoopType, backupPointsVec))
                        {
                            if(data.x() > xMax || data.x() < xMin
                                    || data.y() > yMax || data.y() < yMin)
                            {
                                isAdjust = true;
                                break;
                            }
                        }
                    }
                }
                if(isAdjust)
                {
                    foreach (auto &data, backupPointsVec)
                        mLoopBackupStrategy->DataFilter(data.x(), data.y());
                    mLoopBackupStrategy->AdjustScale();
                    loopStrategys->SetScaleId(mLoopBackupStrategy->GetCurXScale(),
                                              mLoopBackupStrategy->GetCurYScale());
                }
                backupPointsVec = ConvertDataFromDefaultUnit((LOOP_TYPE)backup.mLoopType, backup.mLoopPointsVec);
                foreach (auto chart, mChartStMap[LOOP_CHART].mChartsPtrMap[(LOOP_TYPE)backup.mLoopType])
                {
                    auto loop = dynamic_cast<LoopChart *>(chart);
                    if (loop)
                    {
                        if(isAdjust)
                        {
                            loop->SetScales(xScales[mLoopBackupStrategy->GetCurXScale()],
                                    yScales[mLoopBackupStrategy->GetCurYScale()]);
                        }
                        loop->DrawBackupLoop(backupPointsVec);
                        loop->RedrawLine();
                    }
                }
            }
        }
    }
    mCurChoosenTime = timeStamp;
}

void ChartManager::RedrawLoopChart()
{
    for (int i = LOOP_VF; i < LOOP_MAX; i++)
    {
        foreach (auto chart, mChartStMap[LOOP_CHART].mChartsPtrMap[i])
        {
            auto loopChart = dynamic_cast<LoopChart *>(chart);
            loopChart->RedrawLine();
        }
    }
}

LoopHistorySt ChartManager::GetBackup(LOOP_TYPE type, qint64 timeStamp)
{
    foreach (auto &backupVec, mLoopBackups)
    {
        DebugAssert(!backupVec.empty());

        if (backupVec.size() > type && backupVec[type].mTimestamp == timeStamp)
        {
            return backupVec[type];
        }
    }
    return {};
}

void ChartManager::HideLoopBackup()
{
    mCurChoosenTime = -1;
    for (int loopType = LOOP_TYPE::LOOP_VF; loopType < LOOP_TYPE::LOOP_MAX; ++loopType)
    {
        foreach (auto chart, mChartStMap[LOOP_CHART].mChartsPtrMap[loopType])
        {
            auto loop = dynamic_cast<LoopChart *>(chart);
            if (loop)
            {
                loop->DrawBackupLoop({});
            }
        }
    }
}

qint64 ChartManager::GetCurChoosenTime()
{
    return mCurChoosenTime;
}

LoopHistorySt ChartManager::GetCurChoosenBackup(LOOP_TYPE type)
{
    return GetBackup(type, GetCurChoosenTime());
}

void ChartManager::InitWave(int waveType, BaseChart* chart)
{
    auto wavePtr = dynamic_cast<WaveChart *>(chart);
    if (wavePtr == NULL)
        DebugAssert(0);

    connect(ChartManager::GetInstance(), &ChartManager::SignalDrawModeChanged,
            wavePtr, &WaveChart::SetDrawMode);

    wavePtr->ClearData();
    wavePtr->SetYName(UIStrings::GetStr(sWaveDescriptors[waveType].mWaveName));
    wavePtr->SetCurveColor((sWaveDescriptors[waveType].mWaveColor));
    wavePtr->SetNameColor(sWaveDescriptors[waveType].mNameColor);
    wavePtr->SetUnitColor(sWaveDescriptors[waveType].mUnitColor);
    wavePtr->SetScaleColor((sWaveDescriptors[waveType].mScaleColor));
    wavePtr->SetDrawMode(ChartManager::GetInstance()->GetDrawMode());
    wavePtr->SetYUnit(DataManager::GetParamCurUnit((DataManager::WAVE_PARAM_TYPE)waveType));
    auto xUnitStrId = UnitManager::GetStrId(sWaveDescriptors[waveType].mXDefaultUnit);
    wavePtr->SetXUnit(UIStrings::GetStr(xUnitStrId));

    auto convertUnitCategory = DataManager::GetWaveParamDefaultInfo((DataManager::WAVE_PARAM_TYPE)waveType).mConvertibleUnitCategory;
    if(waveType == DataManager::WAVE_FLOW)
        wavePtr->SetYCursorDigit(DIGIT_2);
    else
        wavePtr->SetYCursorDigit(DIGIT_0);

    auto limits = DataManager::GetInstance()->GetRange(waveType);
    if(convertUnitCategory != NONE_CATEGORY)
    {
        auto nowUnit = UnitManager::GetInstance()->GetCategoryCurUnit(convertUnitCategory);
        if(nowUnit == U_KPA || nowUnit == U_PERCENT)
            wavePtr->SetYCursorDigit(DIGIT_1);
        auto convertFunc = UnitManager::GetInstance()->ConvertFunctor(convertUnitCategory,
                                                                      DataManager::GetDefaultUnit((DataManager::WAVE_PARAM_TYPE)waveType),
                                                                      nowUnit);
        wavePtr->SetValueLimit(convertFunc(limits.first), convertFunc(limits.second));
    }
    else
    {
        wavePtr->SetValueLimit(limits.first, limits.second);
    }

    int dataNumPerWindow = sWaveSpeedToDataNum[ChartManager::GetInstance()->GetWaveSpeed()];
    wavePtr->SetXScale(sWaveXDefaultScale);
    wavePtr->SetDataNumPerWindow(dataNumPerWindow);
    wavePtr->SetYScale(ChartManager::GetInstance()->GetCurScale(WAVE_CHART, waveType).second);

    if (DataManager::WAVE_PAW == waveType)
    {
        short plimit = VentModeSettingManager::GetInstance()->GetSettingdata(SETTINGDATA_PLIMIT);
        wavePtr->SetHighLimitLine(plimit, COLOR_RED);
    }
    else
    {
        wavePtr->SetHighLimitLine(0, Qt::transparent);
    }

    wavePtr->Redraw();
}


void ChartManager::InitLoop(int type, BaseChart *chart)
{
    auto loopChart = dynamic_cast<LoopChart *>(chart);
    if (loopChart == NULL)
        return;

    auto loopType = (LOOP_TYPE)type;
    auto xWaveParamType = sLoopDescriptors[loopType].mXWaveType;
    auto yWaveParamType = sLoopDescriptors[loopType].mYWaveType;
    int xName{}, yName{};
    xName = sWaveDescriptors[xWaveParamType].mWaveName;
    yName = sWaveDescriptors[yWaveParamType].mWaveName;
    loopChart->SetXName(UIStrings::GetStr(xName));
    loopChart->SetYName(UIStrings::GetStr(yName));
    loopChart->SetNameColor(RGB_TO_QCOLOR(sLoopDescriptors[loopType].mNameColor));
    loopChart->SetUnitColor(RGB_TO_QCOLOR(sLoopDescriptors[loopType].mLoopUnitColor));
    loopChart->SetScaleColor(RGB_TO_QCOLOR(sLoopDescriptors[loopType].mScaleColor));
    loopChart->SetCurveColor(RGB_TO_QCOLOR(sLoopDescriptors[loopType].mLoopColor));
    loopChart->SetXUnit(DataManager::GetParamCurUnit(xWaveParamType));
    loopChart->SetYUnit(DataManager::GetParamCurUnit(yWaveParamType));
    auto curScale = ChartManager::GetInstance()->GetCurScale(LOOP_CHART, type);
    loopChart->SetScales(curScale.first, curScale.second); //TBD 5MS 2023.10.18
    loopChart->ClearData();
    auto loopBackups = ChartManager::GetInstance()->GetBackups((LOOP_TYPE)type);
    if (loopBackups.size() != 0)
    {
        foreach (auto &backup, loopBackups)
        {
            if (backup.mTimestamp ==  ChartManager::GetInstance()->GetCurChoosenTime())
            {
                loopChart->DrawBackupLoop(backup.mLoopPointsVec);
                break;
            }
        }
    }
}

void ChartManager::InitConnect()
{
    foreach (auto &chartSt, mChartStMap)
    {
        auto eChartType = chartSt.chartType;
        for (int i = 0; i < chartSt.strategys.size(); ++i)
        {
            auto paramType = i;

            connect(mChartStMap[eChartType].strategys[paramType], &ScaleAdjustStrategy::SignalScaleChanged, this, [this, eChartType, paramType]{
                RefurbishChartScale(eChartType, paramType);
                if(eChartType == LOOP_CHART)
                {
                    RefurbishLoopBackup(eChartType, paramType);
                }
            },
            Qt::DirectConnection);
        }
    }

    connect(VentModeSettingManager::GetInstance(), static_cast<void(VentModeSettingManager::*)(E_SETTING_DATA_ID,short)>(&VentModeSettingManager::SignalSettingDataChanged),
            this, [this](E_SETTING_DATA_ID id, short){
        if (id == E_SETTING_DATA_ID::SETTINGDATA_PLIMIT)
            UpdatePlimitLine();
    });

    connect(mRefurbishChartTimer, &QTimer::timeout, this, &ChartManager::UpChartData);

    static bool isPreHlm = false;
    connect(VentModeSettingManager::GetInstance(), &VentModeSettingManager::SignalModeChanged, this, [=](int mode){
        if(RunModeManage::GetInstance()->GetCurRunMode() != E_RUNMODE::MODE_RUN_MANUAL
                && RunModeManage::GetInstance()->GetCurRunMode() != E_RUNMODE::MODE_RUN_ACGO)
            SetFreeze(false);
        if(mode == VENTMODE_FLAG_HLM
                && RunModeManage::GetInstance()->GetCurRunMode() != E_RUNMODE::MODE_RUN_MANUAL)
        {
            ClearLoopData();
            isPreHlm = true;
        }
        if(mode != VENTMODE_FLAG_HLM && isPreHlm)
        {
            isPreHlm = false;
            ClearChartData(WAVE_CHART);
            ClearChartData(LOOP_CHART);
        }
        if(mode == VENTMODE_FLAG_VCV||
                mode == VENTMODE_FLAG_VCVSIMV||
                mode == VENTMODE_FLAG_PMODE||
                mode == VENTMODE_FLAG_PRVC)
            mPlimitLineVisible = true;
        else
            mPlimitLineVisible = false;
        UpdatePlimitLine();
    });

    connect(DataManager::GetInstance(), &DataManager::SignalWaveDataChanged, this, &ChartManager::RefurbishData);
    connect(RunModeManage::GetInstance(), &RunModeManage::SignalModeChanged, this, [this](E_RUNMODE preRunMode, E_RUNMODE curMode){
        if (curMode == preRunMode)
            return;
        if (curMode == E_RUNMODE::MODE_RUN_SPONT || preRunMode == E_RUNMODE::MODE_RUN_SPONT)
            return;
        else if (preRunMode != E_RUNMODE::MODE_RUN_SHUTDOWN_DELAY && curMode != E_RUNMODE::MODE_RUN_SHUTDOWN_DELAY)
        {
            ClearChartData(WAVE_CHART);
            ClearChartData(LOOP_CHART);
        }
        else
        {
            for (int dataType = DataManager::WAVE_PAW; dataType < DataManager::WAVE_MAX; ++dataType)
            {
                foreach (auto chartPtr, mChartStMap[WAVE_CHART].mChartsPtrMap[dataType])
                {
                    auto wave = dynamic_cast<WaveChart *>(chartPtr);
                    if (wave)
                    {
                        wave->ClearData();
                        wave->Redraw();
                    }
                }
            }

            for (int dataType = 0; dataType < LOOP_TYPE::LOOP_MAX; ++dataType)
            {
                foreach (auto chartPtr, mChartStMap[LOOP_CHART].mChartsPtrMap[dataType])
                {
                    auto loop = dynamic_cast<LoopChart *>(chartPtr);
                    if (loop)
                    {
                        loop->ClearData();
                        loop->Redraw();
                    }
                }
            }
        }
        SetFreeze(false);
        UpdatePlimitLine();
    });

    connect(UnitManager::GetInstance(), &UnitManager::SignalCategoryUnitChanged, this, &ChartManager::SlotOnUnitChanged);

    if (SettingManager->GetIntSettingValue(SystemSettingManager::SOFT_MODE_SETTING) == DEMO_MODE)
    {
        connect(DemoThread::GetInstance(), &DemoThread::SignalCurWavePeriodEnd, this, &ChartManager::SlotOnCurPeriodEnd, Qt::DirectConnection);
        connect(Monitoring::GetInstance(), &Monitoring::SignalCurWavePeriodEnd, this, &ChartManager::SlotOnCurPeriodEnd, Qt::DirectConnection);
    }
    else
    {
        connect(Monitoring::GetInstance(), &Monitoring::SignalCurWavePeriodEnd, this, &ChartManager::SlotOnCurPeriodEnd, Qt::DirectConnection);
    }

    connect(UIStrings::GetInstance(), &UIStrings::SignalLanguageChanged, this, [this]{
        for (int type = DataManager::WAVE_PAW; type < DataManager::WAVE_MAX; ++type)
        {
            auto waveParamType = static_cast<DataManager::WAVE_PARAM_TYPE>(type);
            foreach (auto chart, mChartStMap[WAVE_CHART].mChartsPtrMap[waveParamType])
            {
                auto wavePtr = dynamic_cast<WaveChart *>(chart);
                if (wavePtr == NULL)
                    DebugAssert(0);
                wavePtr->SetYName(UIStrings::GetStr(sWaveDescriptors[type].mWaveName));
                wavePtr->SetYUnit(DataManager::GetParamCurUnit((DataManager::WAVE_PARAM_TYPE)type));
                auto xUnitStrId = UnitManager::GetStrId(sWaveDescriptors[type].mXDefaultUnit);
                wavePtr->SetXUnit(UIStrings::GetStr(xUnitStrId));

            }
        }

        for (int loopType = LOOP_VF; loopType < LOOP_MAX; loopType++)
        {
            foreach (auto chartPtr, mChartStMap[LOOP_CHART].mChartsPtrMap[loopType])
            {
                auto loopChart = dynamic_cast<LoopChart *>(chartPtr);
                if (loopChart == NULL)
                    return;
                auto xWaveParamType = sLoopDescriptors[loopType].mXWaveType;
                auto yWaveParamType = sLoopDescriptors[loopType].mYWaveType;
                int xName{}, yName{};
                xName = sWaveDescriptors[xWaveParamType].mWaveName;
                yName = sWaveDescriptors[yWaveParamType].mWaveName;
                loopChart->SetXName(UIStrings::GetStr(xName));
                loopChart->SetYName(UIStrings::GetStr(yName));
                loopChart->SetXUnit(DataManager::GetParamCurUnit(xWaveParamType));
                loopChart->SetYUnit(DataManager::GetParamCurUnit(yWaveParamType));
            }
        }
    });
}
