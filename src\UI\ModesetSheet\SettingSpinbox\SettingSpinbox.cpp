﻿
#include "UiApi.h"
#include "SettingSpinbox.h"
#include "UiConfig.h"
#include "IntraData.h"
#include "String/UIStrings.h"
#include "UnitManager.h"
#include "UiApi.h"
#include "SettingSpinboxManager.h"
#include "KeyConfig.h"
#include "SystemConfigManager.h"
#include "MainWindow/MainWindow.h"

#define AUTO_EXIT_TIME  (30 * 1000)
#define TIP_DIALOG_WIDTH (160 * 2)
#define TIP_DIALOG_HEIGHT (80)
#define NAME_LABEL_MAX_HEIGHT (20)
#define DIALOG_BOTTOM_WIDTH 7
#define DIALOG_BOTTOM_HEIGHT 9

#define TIP_DIAOLOG_ICON_TOP ":/Dialog/BubblesTop.png"
#define TIP_DIAOLOG_ICON_BOTTOM ":/Dialog/BubblesBottom.png"

SettingSpinbox::SettingSpinbox(QWidget *parent) :
    QFrame(parent)
{
    InitValue();
    InitUi();
    InitConnect();
}

void SettingSpinbox::InitUi()
{
    StyleSet::SetBackgroundColor(this,COLOR_BLACK_GRAY);
    StyleSet::SetBorder(this,COLOR_BLACK_GRAY,1,2,2);
    mPopup = new PopupAdapter(this);
    mPopup->SetPopupType(PopupAdapter::NUMBER_POPUB);
    mPopup->SetCheckValueFunc([this](QString value,QString& errorStr)->bool{ return DataValidator(value,errorStr);});

    mNameLabel = new QLabel("Name",this);
    mNameLabel->setAlignment(Qt::AlignCenter);
    mNameLabel->setFixedHeight(NAME_LABEL_MAX_HEIGHT);
    StyleSet::SetBackgroundColor(mNameLabel,COLOR_TRANSPARENT);
    StyleSet::SetTextColor(mNameLabel,COLOR_LIGHT_GRAY);
    StyleSet::SetTextFont(mNameLabel,FONT_M);

    mEditWidget = new QFrame(this);
    StyleSet::SetBackgroundColor(mEditWidget,COLOR_BLACK_GRAY);

    mMin = new QLabel("min",mEditWidget);
    mMin->setAlignment(Qt::AlignLeft | Qt::AlignBottom);
    mMin->hide();
    StyleSet::SetBackgroundColor(mMin,COLOR_TRANSPARENT);
    StyleSet::SetTextColor(mMin,COLOR_BLACK);
    StyleSet::SetTextFont(mMin,FONT_S);

    mMax = new QLabel("max",mEditWidget);
    mMax->setAlignment(Qt::AlignLeft | Qt::AlignBottom);
    mMax->hide();
    StyleSet::SetBackgroundColor(mMax,COLOR_TRANSPARENT);
    StyleSet::SetTextColor(mMax,COLOR_BLACK);
    StyleSet::SetTextFont(mMax,FONT_S);

    mUnitLabel = new QLabel("Unit",mEditWidget);
    StyleSet::SetBackgroundColor(mUnitLabel,COLOR_TRANSPARENT);
    StyleSet::SetTextColor(mUnitLabel,COLOR_LIGHT_GRAY);
    StyleSet::SetTextFont(mUnitLabel,FONT_S);
    mMax->setAlignment(Qt::AlignLeft | Qt::AlignBottom);

    mCurrentVal = new QLabel(mEditWidget);
    mCurrentVal->setAlignment(Qt::AlignCenter);
    StyleSet::SetBackgroundColor(mCurrentVal,COLOR_TRANSPARENT);
    StyleSet::SetTextColor(mCurrentVal,COLOR_LIGHT_GRAY);
    StyleSet::SetTextFont(mCurrentVal,FONT_XL);




    mProgressBar = new DoubleProgressBarSettingbox(nullptr);
    QSizePolicy sp = mProgressBar->sizePolicy();
    sp.setRetainSizeWhenHidden(true);
    mProgressBar->setSizePolicy(sp);
    mProgressBar->hide();



    QHBoxLayout * hLayout = new QHBoxLayout;

    hLayout->setContentsMargins(0,0,0,0);
    hLayout->addWidget(mMin, 0, Qt::AlignLeft);
    hLayout->addWidget(mUnitLabel, 0, Qt::AlignCenter);
    hLayout->addWidget(mMax, 0, Qt::AlignRight);

    QVBoxLayout *vLayout = new QVBoxLayout(mEditWidget);
    vLayout->setSpacing(0);
    vLayout->setContentsMargins(2,0,2,0);
    vLayout->addStretch(1);
    vLayout->addWidget(mCurrentVal);
    vLayout->addStretch(1);
    vLayout->addWidget(mProgressBar);
    vLayout->addLayout(hLayout);
    vLayout->setAlignment(mCurrentVal, Qt::AlignCenter);
    vLayout->setAlignment(hLayout,Qt::AlignBottom);

    QVBoxLayout *boxLayout = new QVBoxLayout;
    boxLayout->setSpacing(4);
    boxLayout->setContentsMargins(0,0,0,0);
    boxLayout->addWidget(mNameLabel,Qt::AlignTop);
    boxLayout->addWidget(mEditWidget);

    QHBoxLayout *mainLayout = new QHBoxLayout;
    mainLayout->addLayout(boxLayout);
    mainLayout->setContentsMargins(1, 1, 1, 1);
    setLayout(mainLayout);

    setFocusPolicy(Qt::StrongFocus);
    setFixedWidth(UiSettingBoxWidth);
    mEditWidget->setFixedWidth(UiSettingBoxWidth-2);
    mNameLabel->setFixedWidth(UiSettingBoxWidth-2);
    setSizePolicy(QSizePolicy::Fixed, QSizePolicy::Minimum);

    mPopup->setPopupShowDirs(PopupAdapter::SHOW_UP);
    mPopup->setPointYSub(-35);//TBD 常量
    mAutoExitFocusTime.setSingleShot(true);
}

void SettingSpinbox::InitConnect()
{   
    connect(&mAutoExitEditTime, &QTimer::timeout, this, &SettingSpinbox::SlotAutoOutEditState);
    connect(this,&SettingSpinbox::SignalEditState, this, [this](bool isEdit){
        if(isEdit)
        {
            mRangeTipStr.clear();
            mPopup->show();
        }
        else
            mPopup->close();
    });
    connect(mPopup, &PopupAdapter::SignalNeedUpPopupText,this, &SettingSpinbox::SlotPopupNeedTextData);
    connect(mPopup, &PopupAdapter::SignalValueChanged,this, &SettingSpinbox::SlotPopupValueChanged);
    connect(mPopup, &PopupAdapter::SignalEditing, this, [this]()
    {
        SlotRestartAutoOutEditTimer();
        emit SignalValueChanging();
    });
    connect(mPopup, &PopupAdapter::SignalTempVal, this, &SettingSpinbox::SlotSetTempVal);

    connect(&mAutoExitFocusTime, &QTimer::timeout, this, [this]{
        if(mState != e_Edit)
        {
            StyleSet::SetBorder(this,COLOR_BLACK_GRAY,1,2,2);
            update();
        }
    });

}


//设置当前值
void SettingSpinbox::setValue(int val, bool isNotify)
{
    val= val>mInnerData->maxValue() ? mInnerData->maxValue() : val;
    val= val<mInnerData->minValue() ? mInnerData->minValue() : val;
    if(isNotify)
        mInnerData->setValue(val);
    else
        mInnerData->setDumbValue(val);
    refreshText();
}


//设置取值范围
void SettingSpinbox::SetRange(int minValue, int maxValue, bool isNotify)
{
    if(mTrueMax<0&&mTrueMin<0)
    {
        mInnerData->SetRange(minValue, maxValue, isNotify);
    }
    else if(minValue>=mTrueMin&&maxValue<=mTrueMax)
    {
        mInnerData->SetRange(minValue, maxValue, isNotify);
    }
    else
    {
        minValue= minValue<mTrueMin?mTrueMin:minValue;
        maxValue= maxValue>mTrueMax?mTrueMax:maxValue;
        mInnerData->SetRange(minValue, maxValue, isNotify);
    }
    refreshText();
}

void SettingSpinbox::setBaseMinMax(int min, int max)
{
    mTrueMin = min;
    mTrueMax = max;

    UpBaseRangeText();
}

void SettingSpinbox::setStep(int step)
{
    mInnerData->setStep(step);
}

//获取当前值
int SettingSpinbox::value() const
{
    return mInnerData->value();
}

void SettingSpinbox::setSettingDataId(unsigned short id)
{
    mSettingDataId = id;
    ALL_MODESET_SPN_DISCRIPTOR p = SettingSpinboxManager::GetInstance()->GetModesetSpnDiscriptor(mSettingDataId);
    mTrueMax = p.max_value;
    mTrueMin = p.min_value;
}


//设置内部变化的值模型
void SettingSpinbox::setDataModel(IntraData *model)
{
    delete mInnerData;
    mInnerData = model;
    connect(mInnerData, &IntraData::SignalValueChanged, this, &SettingSpinbox::SignalValueChanged);

    connect(mInnerData, &IntraData::SignalValueOutMax, this, [this](){
        mITimeTipStr.clear();
        mRangeTipStr = GetErrorValueTipStr(IntraData::OUT_MAX);
        ShowTip();
    });

    connect(mInnerData, &IntraData::SignalValueOutMin, this, [this](){
        mITimeTipStr.clear();
        mRangeTipStr = GetErrorValueTipStr(IntraData::OUT_MIN);
        ShowTip();
    });

    connect(mInnerData, &IntraData::SignalValueChangedTemporary, this, [this](int value)
    {
        mRangeTipStr.clear();

        TryCloseTipDialog();
        emit SignalValueChanging(value);
    });
}

void SettingSpinbox::SetErrorValueTipStr(IntraData::VALUE_CHECK_TYPE checkFailType, QString str)
{
    mErrorValueTipStr[checkFailType]=str;
}

void SettingSpinbox::setITimeTipStr(QString str)
{
    mITimeTipStr = str;
    if(!str.isEmpty())
        ShowTip();
    else
        TryCloseTipDialog();
}

void SettingSpinbox::setName(unsigned short nameId)
{
    mNameId = nameId;
    mNameLabel->setText(UIStrings::GetStr((ALL_STRINGS_ENUM)nameId));
}

void SettingSpinbox::setNameColor(const QColor &color)
{
    StyleSet::SetTextColor(mNameLabel,color);
}

void SettingSpinbox::setUnit(unsigned short unitId)
{
    mUnitId = unitId;
    mUnitLabel->setText(UIStrings::GetStr((ALL_STRINGS_ENUM)unitId));
}

void SettingSpinbox::SetPopupType(int type)
{
    mPopup->SetPopupType(type);
}

void SettingSpinbox::SetPopupCheckValueFunc(CHECK_CALL_BACK func)
{
    mPopup->SetCheckValueFunc(func);
}

void SettingSpinbox::SetPopupInputRegex(QString s)
{
    mPopup->SetPopupInputRegex(s);
}

void SettingSpinbox::focusInEvent(QFocusEvent *)
{
    if(mState==e_Edit) {
        OutEditState(false);
    }
    else {
        mState = e_Focus;
        UpUiStyleSheet();
    }
}

//失去焦点则离开编辑状态
void SettingSpinbox::focusOutEvent(QFocusEvent *)
{
    if(mState==e_Edit)
    {
        OutEditState(false);
    }
    mState = e_Normal;
    UpUiStyleSheet();
    refreshText();
}


//点击进入编辑状态或退出编辑状态
void SettingSpinbox::mouseReleaseEvent(QMouseEvent *)
{
    if(e_Edit == mState)
    {
        OutEditState();
        refreshText();
    }
    else
    {
        InEditState();
    }
}

void SettingSpinbox::InEditState()
{
    mState = e_Edit;
    mInnerData->BackupCurValue();
    mAutoExitEditTime.start(AUTO_EXIT_TIME);
    UpUiStyleSheet();
    //ShowTip();

    emit SignalEditState(true);
}

void SettingSpinbox::UpBaseRangeText()
{
    if (mMinStr.size() != 0)
    {
        mMin->setText(QString::number((mTrueMin + mInnerData->step()) * m_zoomScale));
    }
    else
        mMin->setText(QString::number(mTrueMin * m_zoomScale));

    if (mMaxStr.size() != 0)
    {
        mMax->setText(QString::number((mTrueMax - mInnerData->step()) * m_zoomScale));
    }
    else
        mMax->setText(QString::number(mTrueMax * m_zoomScale));
}

//结束编辑
void SettingSpinbox::OutEditState(bool isSaveChange)
{
    if(isSaveChange)
        mInnerData->confirmChange();
    else
        mInnerData->RestoreValue();

    mState = e_Focus;
    mAutoExitEditTime.stop();
    UpUiStyleSheet();
    CloseTipDialog();
    refreshText();
    emit SignalEditState(false);
}

//在编辑状态回车设置完成
void SettingSpinbox::keyPressEvent(QKeyEvent *ev)
{
    switch(ev->key())
    {
    case KEY_PRESS:
        if(e_Edit == mState)
        {
            OutEditState();
        }
        else    //开始编辑
        {
            InEditState();
        }
        break;
    case KEY_ANTI_CLOSEWISE:
        if(e_Edit == mState)
        {
            SlotRestartAutoOutEditTimer();
            mInnerData->decrease();
        }
        else
        {
            ev->ignore();
        }
        break;
    case KEY_CLOSEWISE:
        if(e_Edit == mState)
        {
            SlotRestartAutoOutEditTimer();
            mInnerData->increase();
        }
        else
        {
            ev->ignore();
        }

        break;
    default:
        ev->ignore();
        break;
    }
    refreshText();
}

void SettingSpinbox::paintEvent(QPaintEvent *event)
{
    QFrame::paintEvent(event);
}



void SettingSpinbox::refreshText()
{
    auto textCurVal = valueToStr(mInnerData->value());
    if (textCurVal != mMinStr && textCurVal != mMaxStr)
    {
        textCurVal = QString::number(textCurVal.toDouble() * m_zoomScale, 'f', mDigit);
    }
    mCurrentVal->setText(textCurVal);
    mPopup->UpPopupText(mCurrentVal->text());

    UpBaseRangeText();
    UpProcessBar();
}

void SettingSpinbox::InitUiText()
{
    mUnitLabel->setText(UIStrings::GetStr((ALL_STRINGS_ENUM)mUnitId));
    mNameLabel->setText(UIStrings::GetStr((ALL_STRINGS_ENUM)mNameId));
    mErrorValueTipStr[IntraData::VALID]= QString();
    mErrorValueTipStr[IntraData::OUT_MAX]= UIStrings::GetStr(STR_OUT_OF_UPPER_LIMIT);
    mErrorValueTipStr[IntraData::OUT_MIN]= UIStrings::GetStr(STR_OUT_OF_LOWER_LIMIT);
    mErrorValueTipStr[IntraData::FORMAL_ERROR]= UIStrings::GetStr(STR_FORMAT_ERROR);
    mErrorValueTipStr[IntraData::INVALID]= UIStrings::GetStr(STR_INVALID_VALUE);
}


QString SettingSpinbox::GetErrorValueTipStr(int checkFailType)
{
    if(checkFailType >= IntraData::VALID && checkFailType <= IntraData::INVALID)
    {
        return mErrorValueTipStr[checkFailType];
    }
    return QString();
}



bool SettingSpinbox::isEditingState()
{
    if(e_Edit == mState)
    {
        return true;
    }
    return false;
}

void SettingSpinbox::UpUiStyleSheet()
{
    switch (mState)
    {
    case e_Normal:
    {
        StyleSet::SetBackgroundColor(this,COLOR_BLACK_GRAY);
        StyleSet::SetBorder(this,COLOR_BLACK_GRAY,1,2,2);


        StyleSet::SetBackgroundColor(mEditWidget,COLOR_BLACK_GRAY);

        StyleSet::SetBackgroundColor(mUnitLabel,COLOR_TRANSPARENT);
        StyleSet::SetTextColor(mUnitLabel,COLOR_LIGHT_GRAY);
        StyleSet::SetTextFont(mUnitLabel,FONT_S);

        StyleSet::SetBackgroundColor(mCurrentVal,COLOR_TRANSPARENT);
        StyleSet::SetTextColor(mCurrentVal,COLOR_LIGHT_GRAY);
        StyleSet::SetTextFont(mCurrentVal,FONT_XL);
        mMin->hide();
        mMax->hide();
        mProgressBar->hide();
    }
        break;
    case e_Focus:
    {
        StyleSet::SetBackgroundColor(this,COLOR_BLACK_GRAY);
        StyleSet::SetBorder(this,COLOR_BRIGHT_BLUE,2,2,2);


        StyleSet::SetBackgroundColor(mEditWidget,COLOR_BLACK_GRAY);

        StyleSet::SetBackgroundColor(mUnitLabel,COLOR_TRANSPARENT);
        StyleSet::SetTextColor(mUnitLabel,COLOR_LIGHT_GRAY);
        StyleSet::SetTextFont(mUnitLabel,FONT_S);

        StyleSet::SetBackgroundColor(mCurrentVal,COLOR_TRANSPARENT);
        StyleSet::SetTextColor(mCurrentVal,COLOR_LIGHT_GRAY);
        StyleSet::SetTextFont(mCurrentVal,FONT_XL);
        mMin->hide();
        mMax->hide();
        mProgressBar->hide();
        if(!ConfigManager->IsFullTouch())
        {
            mAutoExitFocusTime.start(AUTO_EXIT_TIME);
        }
    }
        break;
    case e_Edit:
    {
        StyleSet::SetBorder(this,COLOR_LIGHT_GRAY,1,2,2);
        StyleSet::SetBackgroundColor(mEditWidget,COLOR_LIGHT_GRAY);


        StyleSet::SetBackgroundColor(mUnitLabel,COLOR_TRANSPARENT);
        StyleSet::SetTextColor(mUnitLabel,COLOR_BLACK);
        StyleSet::SetTextFont(mUnitLabel,FONT_S);

        StyleSet::SetBackgroundColor(mCurrentVal,COLOR_TRANSPARENT);
        StyleSet::SetTextColor(mCurrentVal,COLOR_BLACK);
        StyleSet::SetTextFont(mCurrentVal,FONT_XL);
        UpProcessBar();
        mMin->show();
        mMax->show();
        mProgressBar->show();
    }
        break;
    default:
        return ;
        break;
    }
    update();
}

QStringList SettingSpinbox::GetAllValueStr()
{
    int value = mInnerData->minValue();
    QStringList reValue;
    while(value<=mInnerData->maxValue())
    {
        reValue.push_back(valueToStr(value));
        value+=mInnerData->step();
    }
    return reValue;
}

void SettingSpinbox::InitValue()
{
    setDataModel(new commonInnerData);
    mState = e_Normal;
    mDigit = 0;
    mSettingDataId = 0;
    mTipDialog = new SpnTipDialog;
    CloseTipDialog();
    mErrorValueTipStr[IntraData::VALID]= QString();
    mErrorValueTipStr[IntraData::OUT_MAX]= UIStrings::GetStr(STR_OUT_OF_UPPER_LIMIT);
    mErrorValueTipStr[IntraData::OUT_MIN]= UIStrings::GetStr(STR_OUT_OF_LOWER_LIMIT);
    mErrorValueTipStr[IntraData::FORMAL_ERROR]= UIStrings::GetStr(STR_FORMAT_ERROR);
    mErrorValueTipStr[IntraData::INVALID]= UIStrings::GetStr(STR_INVALID_VALUE);
}

bool SettingSpinbox::ShowTip()
{
    //展示先屏蔽所有参数关联弹框
    QStringList tipStrList;
    if(!mRangeTipStr.isEmpty())
        tipStrList<<mRangeTipStr;
    if(!mITimeTipStr.isEmpty())
        tipStrList<<mITimeTipStr;
    if(!tipStrList.isEmpty())
    {
        QString tipStr;
        foreach(auto &ite, tipStrList)
        {
            tipStr.append(ite);
            tipStr.append("\n");
        }
        tipStr[tipStr.count()-1] = ' ';
        int size = tipStr.size();
        if (size > 0 && tipStr[size - 1] == '\n') {
            tipStr.remove(tipStr.count()-1, 1);
        }

        if (ConfigManager->IsFullTouch())
            mPopup->SlotShowTip(tipStr);
        else
        {
            mTipDialog->show(tipStr);
        }

    }

    if(mTipDialog->isVisible())
    {
        QPoint mainWPoint = MainWindow::GetInstance()->mapToGlobal(QPoint(0,0));

        QPoint posPoint = mapToGlobal(QPoint((width() - TIP_DIALOG_WIDTH) / 2, -TIP_DIALOG_HEIGHT - 3));

        if(posPoint.x() < mainWPoint.x())
        {
            posPoint = mapToGlobal(QPoint(0, -TIP_DIALOG_HEIGHT - 3));
        }

        mTipDialog->move(posPoint);

        QPoint midPos = mapToGlobal(QPoint(width()/2, -DIALOG_BOTTOM_HEIGHT - 3));
        mTipDialog->MoveBottomIcon(midPos);
    }

    return mTipDialog->isVisible();
}

void SettingSpinbox::CloseTipDialog()
{
    mTipDialog->close();
}

bool SettingSpinbox::TryCloseTipDialog()
{
    if(mRangeTipStr.isEmpty()&&mITimeTipStr.isEmpty())
    {
        if (ConfigManager->IsFullTouch())
            mPopup->SlotShowTip(mRangeTipStr);
        else
            CloseTipDialog();
        return true;
    }
    else
        ShowTip();
    return false;
}

bool SettingSpinbox::DataValidator(QString value, QString &errorStr)
{
    bool reValue = false;

    if (value == mMinStr)
    {
        value = QString::number(mInnerData->minValue());
    }
    else if (value == mMaxStr)
    {
        value = QString::number(mInnerData->maxValue());
    }
    else
    {
        double val = value.toDouble(&reValue);
        if (reValue)
        {
            reValue = false;
            val = val * qPow(10, mDigit);

            if(val > GetVaildRangeMax())
            {
                value = QString::number(std::numeric_limits<short>::max());
            }
            else if(val < GetVaildRangeMin())
            {
                value = QString::number(std::numeric_limits<short>::min());
            }
            else
                value = QString::number(val);
        }
    }
    mInnerData->AdjustStep(value.toDouble());
    mInnerData->StringTovalue(value);
    int valueCheckRe = mInnerData->GetPrevStrToValueCheckeRe();
    if(valueCheckRe==IntraData::VALID)
        reValue =true;
    if(!reValue)
        errorStr = GetErrorValueTipStr(valueCheckRe);
    return reValue;
}

QString SettingSpinbox::valueToStr(int value)
{
    QString strText = mInnerData->valueToString(value);
    if(value >= mTrueMax)
        strText= mMaxStr.isEmpty() ? strText : mMaxStr;
    else if(value <= mTrueMin)
        strText= mMinStr.isEmpty() ? strText : mMinStr;
    return strText;
}

int SettingSpinbox::strToValue(QString str)
{
    int value;
    if (str == mMaxStr)
        value = mTrueMax;
    else if (str == mMinStr)
        value = mTrueMin;
    else
        value = mInnerData->StringTovalue(QString::number(qRound(str.toDouble() * qPow(10, mDigit))));
    return value;
}

void SettingSpinbox::UpProcessBar()
{
    if(mState==e_Edit)
    {
        if(mProgressBar->GetBaseBeing()!=mTrueMin
                ||mProgressBar->GetBaseEnd()!=mTrueMin
                ||mProgressBar->GetCureEnd()!=mInnerData->maxValue()
                ||mProgressBar->GetCurBeing()!=mInnerData->minValue())
        {
            mProgressBar->setBaseRange(mTrueMin,mTrueMax);
            int visableMin = 0;
            int visableMax = 0;
            if(mInnerData->minValue()%mInnerData->step()!=0)
                visableMin = mInnerData->minValue() + (mInnerData->step() - mInnerData->minValue()%mInnerData->step());
            else
                visableMin = mInnerData->minValue();
            if(mInnerData->maxValue()%mInnerData->step()!=0)
                visableMax = mInnerData->maxValue() - mInnerData->maxValue()%mInnerData->step();
            else
                visableMax = mInnerData->maxValue();
            mProgressBar->setCurRange(visableMin,visableMax);

        }
        mProgressBar->setValue(mInnerData->value());
    }
}

int SettingSpinbox::GetVaildRangeMax()
{
    mInnerData->AdjustStep(mInnerData->maxValue());
    int tMax = mMaxStr.isEmpty() ? mInnerData->maxValue() : (mTrueMax - mInnerData->step());
    mInnerData->AdjustStep(mInnerData->value());
    return tMax;
}

int SettingSpinbox::GetVaildRangeMin()
{
    mInnerData->AdjustStep(mInnerData->minValue());
    int tMin = mMinStr.isEmpty() ? mInnerData->minValue() : (mTrueMin + mInnerData->step());
    mInnerData->AdjustStep(mInnerData->value());
    return tMin;
}

int SettingSpinbox::GetTrueMax() const
{
    return mTrueMax;
}

int SettingSpinbox::GetTrueMin() const
{
    return mTrueMin;
}

void SettingSpinbox::SlotAutoOutEditState()
{
    if (mState == e_Edit)
    {
        OutEditState(false);
    }
}

void SettingSpinbox::SlotPopupNeedTextData()
{
    mPopup->UpPopupListText(GetAllValueStr());
    QString textCurVal = valueToStr(mInnerData->value());
    if (textCurVal != mMinStr && textCurVal != mMaxStr)
        textCurVal = QString::number(textCurVal.toDouble() * m_zoomScale, 'f', mDigit);
    mPopup->UpPopupText(textCurVal);
}

void SettingSpinbox::SlotPopupValueChanged(QString value)
{
    if(!value.isEmpty())
    {
        int neeValue = strToValue(value);
        if(neeValue !=INVALID_VALUE)
        {
            setValue(neeValue);
        }

    }
    else
    {
        OutEditState(false);
    }
}

void SettingSpinbox::SlotRestartAutoOutEditTimer()
{
    mAutoExitEditTime.start(AUTO_EXIT_TIME);
}

void SettingSpinbox::SlotSetTempVal(QString str) {
    mTempVal = str;
}

/***************************************************************/
/***************************************************************/
SpnTipDialog::SpnTipDialog(QWidget *parent):QFrame(parent)
{
    setFocusPolicy(Qt::NoFocus);
    setWindowFlags(Qt::ToolTip | Qt::FramelessWindowHint);
    setAttribute(Qt::WA_TranslucentBackground);

    setFixedSize(TIP_DIALOG_WIDTH,TIP_DIALOG_HEIGHT);
    StyleSet::SetBackgroundColor(this,COLOR_TRANSPARENT);

    mBottomIcon = new QFrame(this);
    mBottomIcon->setFixedSize(DIALOG_BOTTOM_WIDTH,DIALOG_BOTTOM_HEIGHT);
    StyleSet::SetBackGroundIcon(mBottomIcon, TIP_DIAOLOG_ICON_BOTTOM);
    StyleSet::SetBackgroundColor(mBottomIcon, COLOR_TRANSPARENT);

    QFrame* topIcon = new QFrame(this);
    StyleSet::SetBackGroundIcon(topIcon, TIP_DIAOLOG_ICON_TOP);
    StyleSet::SetBackgroundColor(topIcon, COLOR_TRANSPARENT);

    mTipLabel= new QLabel(this);
    StyleSet::SetTextFont(mTipLabel,FONT_M,FONT_FAMILY_NINA,true);
    StyleSet::SetTextColor(mTipLabel,COLOR_WHITE);
    QHBoxLayout* layout = new QHBoxLayout(topIcon);
    layout->setContentsMargins(15, 0, 10, 0);
    layout->addWidget(mTipLabel);

    QVBoxLayout* iconLayout = new QVBoxLayout(this);
    iconLayout->addWidget(topIcon);
    iconLayout->setContentsMargins(0, 0, 0, mBottomIcon->height());
    iconLayout->setSpacing(0);
}


void SpnTipDialog::show(QString strTip)
{
    mTipLabel->setText(strTip);
    QFrame::show();
}

void SpnTipDialog::MoveBottomIcon(QPoint pos)
{
    QPoint temp = this->mapFromGlobal(pos);
    if(temp != mBottomIcon->pos())
        mBottomIcon->move(temp);
}

void SpnTipDialog::paintEvent(QPaintEvent *event)
{
    QFrame::paintEvent(event);
}
/***************************************************************/
/***************************************************************/
