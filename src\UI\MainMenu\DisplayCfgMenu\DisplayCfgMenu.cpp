﻿#include "UiApi.h"
#include "DisplayCfgMenu.h"
#include "String/UIStrings.h"
#include "UiSettingPage.h"

DisplayCfgMenu::DisplayCfgMenu(QWidget *parent) :
    FocusWidget(parent)
{
    InitUi();
}

void DisplayCfgMenu::InitUi()
{
    mVerticalTabWidget = new VerticalTabWidget(this);

    mVolAndBrightCtrlPage = new VolAndBrightCtrlPage;
    mVerticalTabWidget->addTab(mVolAndBrightCtrlPage, UIStrings::GetStr(ALL_STRINGS_ENUM::STR_VOL_BRIGHT));
    mUiSettingPage = new UiSettingPage;
    mVerticalTabWidget->addTab(mUiSettingPage, UIStrings::GetStr(ALL_STRINGS_ENUM::STR_UI_SETTING));
    mWaveformsSettingPage = new WaveformsSettingPage;
    mVerticalTabWidget->addTab(mWaveformsSettingPage, UIStrings::GetStr(ALL_STRINGS_ENUM::STR_WAVEFORMS_SETTINGS));

    QVBoxLayout *layout = new QVBoxLayout(this);
    layout->addWidget(mVerticalTabWidget);
    layout->setContentsMargins(0,0,0,0);
}

void DisplayCfgMenu::InitUiText()
{
    mVerticalTabWidget->setTabText(mVerticalTabWidget->indexOf(mVolAndBrightCtrlPage),
                                   UIStrings::GetStr(ALL_STRINGS_ENUM::STR_VOL_BRIGHT));
    mVerticalTabWidget->setTabText(mVerticalTabWidget->indexOf(mUiSettingPage),
                                   UIStrings::GetStr(ALL_STRINGS_ENUM::STR_UI_SETTING));
    mVerticalTabWidget->setTabText(mVerticalTabWidget->indexOf(mWaveformsSettingPage),
                                   UIStrings::GetStr(ALL_STRINGS_ENUM::STR_WAVEFORMS_SETTINGS));

    mVolAndBrightCtrlPage->InitUiText();
    mUiSettingPage->InitUiText();
    mWaveformsSettingPage->InitUiText();
}

void DisplayCfgMenu::hideEvent(QHideEvent *event)
{
    mUiSettingPage->CloseDialog();
    FocusWidget::hideEvent(event);
}

