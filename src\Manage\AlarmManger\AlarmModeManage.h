﻿#ifndef _ALARMMODEMANAGE_H_
#define _ALARMMODEMANAGE_H_

#include "AlarmManager.h"
#include "SystemConfigManager.h"
enum E_ALARM_MODE_ID
{
    ALARM_MODE_IN_ACGO = 0,
    ALARM_MODE_IN_MANUAL,
    ALARM_MODE_IN_STANDBY,
    ALARM_MODE_IN_VENT_JUST_START, //除了HLM模式，其它机控模式刚开始的报警管理
    ALARM_MODE_IN_VENT,
    ALARM_MODE_IN_HLM_AND_OPEN_PHY_ALARM,
    ALARM_MODE_IN_SELF_TEST,
    ALARM_MODE_IN_SHUT_DOWN,
    ALARM_MODE_GENER,
    ALARM_MODE_INVALID,
    ALARM_MODE_MAX
};
const QMap<MACHINE_SERIAL,QVector<E_ALARM_ID>> disable_By_MachineType{
    {CENAR_30,{PROMPT_ONLY_ONE_BATTERY}},
    {CENAR_40,{PROMPT_ONLY_ONE_BATTERY}},
    {CENAR_50,{PROMPT_ONLY_ONE_BATTERY}},
    {ELUNA_60,{PROMPT_ONLY_ONE_BATTERY}},
    {ELUNA_70,{}},
    {ELUNA_80,{}},
    {CENAR_30M,{PROMPT_ONLY_ONE_BATTERY,
               PROMPT_FLOWMETER_BREAK_OFF,
               PROMPT_FLOW_TOO_LOW,
               TECHALARM_BACKUP_FLOW_CONTROL_OPEN,
               TECHALARM_FLOWMETER_O2_SENSOR_NO_CAL_DATA ,
               TECHALARM_FLOWMETER_N2O_SENSOR_NO_CAL_DATA,
               TECHALARM_FLOWMETER_AIR_SENSOR_NO_CAL_DATA ,
               TECHALARM_AIR_PRESS_LOW,
               TECHALARM_N2O_PRESS_LOW,
               TECHALARM_O2_FLOW_INEXACT,
               TECHALARM_BANLANCE_FLOW_INEXACT,
               PROMPT_O2_FM_SENSOR_ERROR,
               PROMPT_N2O_FM_SENSOR_ERROR,
               PROMPT_AIR_FM_SENSOR_ERROR,
               ALARM_O2_FLOW_HIGH,
               ALARM_N2O_FLOW_HIGH,
               ALARM_AIR_FLOW_HIGH,
               ALARM_FLOW_HIGH,
               ALARM_O2_N2O_UNUSUAL_RATIO
               }
    },
    {OEM,{}}
};
struct S_ALARM_CONTROL
{
    QVector<E_ALARM_ID> mAlarmIdVec;
    bool mOpenFlag;
    unsigned short prio;
};

class AlarmModeManage : public QObject
{
    Q_OBJECT
public:
    static AlarmModeManage* GetInstance()
    {
        static AlarmModeManage sInstance;
        return &sInstance;
    }

private:
    AlarmModeManage();
    AlarmModeManage(const AlarmModeManage&);
    AlarmModeManage& operator=(const AlarmModeManage&);
    ~AlarmModeManage(){}
    void UpCurAlarmModel();
    void SetAlarmEnableForModel(int oldAlarmModel);
private:
    int mCurAlarmModel=ALARM_MODE_INVALID;
    int mRespiratoryCycleCount=0;
};
#endif

