﻿#include "LoopChartWithSwitcher.h"

LoopChartWithSwitcher::LoopChartWithSwitcher(LOOP_TYPE type, QWidget *parent) : <PERSON><PERSON><PERSON>(parent)
{
    mSwitchTypeComboBox = new IconComboBox(this);
    mSwitchTypeComboBox->setPixmap(QPixmap(":/button/SwitchButton.png"));
    mSwitchTypeComboBox->setFixedSize(30, 30);


    ValueStrIntraData *dataModel = new ValueStrIntraData;
    QMap<int, ALL_STRINGS_ENUM> strIdMap =
    {
        {LOOP_VF, ALL_STRINGS_ENUM::STR_GRAPHNAME_VF},
        {LOOP_PV, ALL_STRINGS_ENUM::STR_GRAPHNAME_PV},
        {LOOP_FP, ALL_STRINGS_ENUM::STR_GRAPHNAME_FP}
    };

    QMap<int, QString> strMap{};
    foreach (auto id, strIdMap)
    {
        strMap.insert(strIdMap.key(id), UIStrings::GetStr(id));
    }

    dataModel->SetValueAndStr(strMap);
    mSwitchTypeComboBox->setDataModel(dataModel);
    mSwitchTypeComboBox->setValue(type);

    connect(mSwitchTypeComboBox, &ComboBox::SignalValueChanged, this, [this](int value){
        ChartManager::GetInstance()->RegisterChart(ChartManager::CHART_TYPE_ENUM::LOOP_CHART, (LOOP_TYPE)value, this);
        emit SignalLoopTypeChanged((LOOP_TYPE)value);
    });

    this->setFocusPolicy(Qt::FocusPolicy::StrongFocus);
    this->setFocusProxy(mSwitchTypeComboBox);
}

bool LoopChartWithSwitcher::IsInCombox(QPoint pos)
{
    if(mSwitchTypeComboBox)
    {
        return mSwitchTypeComboBox->geometry().contains(pos);
    }
    return false;
}

void LoopChartWithSwitcher::SetLoopType(LOOP_TYPE type)
{
    mSwitchTypeComboBox->setValue(type);
}

LOOP_TYPE LoopChartWithSwitcher::GetType()
{
    return (LOOP_TYPE)mSwitchTypeComboBox->value();
}

void LoopChartWithSwitcher::resizeEvent(QResizeEvent *event)
{
    LoopChart::resizeEvent(event);
    mSwitchTypeComboBox->move(width() - mSwitchTypeComboBox->width(), 0);
}

void LoopChartWithSwitcher::mousePressEvent(QMouseEvent * ev)
{
    if(!IsInCombox(ev->pos()))
    {
        emit SignalLoopClick();
    }
}
