﻿#include <QHeaderView>
#include <QVBoxLayout>

#include "SelftestInfoTable.h"
#include "UiConfig.h"
#include "String/UIStrings.h"

#include "RichTextStyle.h"
#include "LineDelegate.h"
#include "TableWidget.h"
#include "PowerOnSelfTest/SystemSelftestModel.h"
#include "ModuleTestManager.h"
#include "SystemTimeManager.h"

#define TABLE_COL_COUNT    2
#define SELF_TEST_INFO_CNT 12

SelftestInfoTable::SelftestInfoTable(QWidget *parent) :
    FocusWidget(parent)
{
    InitUi();
    InitTable();
}

void SelftestInfoTable::InitUiText()
{
    RefurbishResult();
}

void SelftestInfoTable::InitUi()
{
    mSysSelfTestItemModel = new QStandardItemModel(this);
    mSysSelfTestItemModel->setColumnCount(TABLE_COL_COUNT);
    mSysSelfTestItemModel->setRowCount(SELF_TEST_INFO_CNT);

    mSysSelfTestTableView = new TableWidget(this);
    mSysSelfTestTableView->setObjectName("mSysSelfTestTableView");

    mSysSelfTestTableView->GetTableView()->setModel(mSysSelfTestItemModel);
    mSysSelfTestTableView->GetTableView()->horizontalHeader()->setSectionResizeMode(QHeaderView::Stretch);
    StyleSet::SetTextFont(mSysSelfTestTableView, mSysSelfTestTableView->font().pointSize(), FONT_FAMILY_TAHOMA);

    QVBoxLayout *mainLayout = new QVBoxLayout;
    mainLayout->setSpacing(2);
    mainLayout->setContentsMargins(0,0,0,0);
    mainLayout->addWidget(mSysSelfTestTableView);

    setLayout(mainLayout);
    
}

void SelftestInfoTable::InitTable()
{
    for (int i = 0; i < SELF_TEST_INFO_CNT; ++i)
    {
        for (int j = 0; j < TABLE_COL_COUNT; j++)
        {
            QStandardItem *newItem = new QStandardItem;
            QFont tempFont(FONT_FAMILY_NINA);
            tempFont.setPixelSize(FONT_M);
            newItem->setFont(tempFont);
            newItem->setFlags(Qt::ItemIsEnabled);
            mSysSelfTestItemModel->setItem(i, j, newItem);
        }

        mSysSelfTestItemModel->item(i, 0)->setText("");
        mSysSelfTestItemModel->item(i, 1)->setTextAlignment(Qt::AlignVCenter | Qt::AlignRight);
    }
}

//将系统自检的结果写入表
void SelftestInfoTable::RefurbishResult()
{
    auto title =  UIStrings::GetStr(ALL_STRINGS_ENUM::STR_LATEST_SELFTEST);
    auto testResult =  SystemSelftestModel::GetInstance()->GetSystemTestState();
    QString testResultStr = SystemSelftestModel::GetInstance()->GetSystemStateStr(testResult);
    auto testTime = SystemSelftestModel::GetInstance()->GetLastestTestTime();

    mSysSelfTestTableView->SetTableName(UIStrings::GetStr(ALL_STRINGS_ENUM::STR_LATEST_SELFTEST)
                                        + "\t"
                                        + SystemTimeManager::GetInstance()->GetDateTimeStr(testTime)
                                        + "\t\t\t\t\t"
                                        + testResultStr);


    static const QMap<SystemSelftestModel::ITEM_STATE, QColor> sColorMap
    {
        {SystemSelftestModel::ITEM_STATE::CHECKED_SUCCESS, COLOR_LIGHT_GREEN},
        {SystemSelftestModel::ITEM_STATE::CHECKED_FAIL, COLOR_RED},
        {SystemSelftestModel::ITEM_STATE::SKIP, COLOR_LIGHT_YELLOW},
    };

    auto itemsVec = SystemSelftestModel::GetInstance()->GetItemsInfo();

    for (int i = 0; i < SELF_TEST_INFO_CNT; ++i)
    {
        mSysSelfTestItemModel->item(i, 0)->setText("");
        mSysSelfTestItemModel->item(i, 1)->setText("");
    }

    for(int curIndex = 0; curIndex < itemsVec.size(); curIndex ++)
    {
        mSysSelfTestItemModel->item(curIndex, 0)->setText(UIStrings::GetStr(itemsVec[curIndex].mStrId));
        mSysSelfTestItemModel->item(curIndex, 1)->setText(SystemSelftestModel::GetInstance()->GetItemStateStr(itemsVec[curIndex].mState));
        mSysSelfTestItemModel->item(curIndex, 1)->setForeground(/*sColorMap.value(itemsVec[curIndex].mState,*/ COLOR_BLACK);
    }
}

void SelftestInfoTable::showEvent(QShowEvent *ev)
{
    RefurbishResult();
    FocusWidget::showEvent(ev);
}
