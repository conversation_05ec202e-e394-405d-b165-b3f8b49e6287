﻿#include "CalibrateBlock.h"
#include "PushButton.h"
#include "String/UIStrings.h"
#include "StandByPageInfoLabel.h"
#include "MainWindow/MainWindow.h"
#include "calibration/CalModuleManage.h"
#include "SystemConfigManager.h"
#include "ModuleTestManager.h"
#include "AGModule.h"

CalibrateBlock::CalibrateBlock(QWidget *parent) : FocusWidget(parent)
{
    mLeakResultLabel = new StandByPageInfoLabel;
    mFlowResultLabel = new StandByPageInfoLabel;
    mO2ResultLabel   = new StandByPageInfoLabel;
    mCO2ResultLabel = new StandByPageInfoLabel;
    mAGResultLabel = new StandByPageInfoLabel;

    mLeakResultLabel ->hide();
    mFlowResultLabel ->hide();
    mO2ResultLabel   ->hide();

    mCalButton = new PushButton;
    connect(mCalButton, &PushButton::clicked, this, []{
        MainWindow::GetInstance()->ShowCalibrationMenu();
    });

    mainLayout = new QVBoxLayout;
    mainLayout->setContentsMargins(3, 5, 5, 20);
    mainLayout->setSpacing(2);
    mainLayout->addWidget(mLeakResultLabel);
    mainLayout->addWidget(mFlowResultLabel);
    mainLayout->addWidget(mO2ResultLabel);
    if(ModuleTestManager::GetInstance()->GetAgModuleType() == AG)
    {
        mainLayout->addWidget(mAGResultLabel);
    }
    else if(ModuleTestManager::GetInstance()->GetAgModuleType() == CO2)
    {
        mainLayout->addWidget(mCO2ResultLabel);
    }
    mainLayout->addStretch();
    mainLayout->addWidget(mCalButton);
    mainLayout->setAlignment(mCalButton, Qt::AlignHCenter);
    setLayout(mainLayout);

    connect(CalModuleManage::GetInstance(), &CalModuleManage::SignalCalResultChanged, this, [this](CAL_TYPE type){
        if (type == CAL_TYPE::MANUAL_TEST)
        {
            UpdateLabel(mLeakResultLabel);
        }
        else if (type == CAL_TYPE::CAL_TYPE_FLOW_ZERO)
        {
            UpdateLabel(mFlowResultLabel);
        }
        else if (type == CAL_TYPE::CAL_TYPE_O2_100)
        {
            UpdateLabel(mO2ResultLabel);
        }
        else if (type == CAL_TYPE::CAL_TYPE_CO2)
        {
            UpdateLabel(mCO2ResultLabel);
        }
        else if (type == CAL_TYPE::CAL_TYPE_AG)
        {
            UpdateLabel(mAGResultLabel);
        }
    });
}

void CalibrateBlock::InitUiText()
{
    mLeakResultLabel->SetTitle(UIStrings::GetStr(ALL_STRINGS_ENUM::STR_LEAK));
    mFlowResultLabel->SetTitle(UIStrings::GetStr(ALL_STRINGS_ENUM::STR_FLOWMETER_ZERO_CAL));
    mO2ResultLabel->SetTitle(UIStrings::GetStr(ALL_STRINGS_ENUM::STR_MAINMENU_O2CAL));
    mCalButton->setText(UIStrings::GetStr(ALL_STRINGS_ENUM::STR_MAINMENU_CAL));

    UpdateLabel(mLeakResultLabel);
    UpdateLabel(mFlowResultLabel);
    UpdateLabel(mO2ResultLabel);
    if(ModuleTestManager::GetInstance()->GetAgModuleType() == AG)
    {
        mAGResultLabel->SetTitle(UIStrings::GetStr(STR_AG_ZERO_CAL));
        UpdateLabel(mAGResultLabel);
    }
    else if(ModuleTestManager::GetInstance()->GetAgModuleType() == CO2)
    {
        mCO2ResultLabel->SetTitle(UIStrings::GetStr(STR_CO2_ZERO_CAL));
        UpdateLabel(mCO2ResultLabel);
    }

    if(!ConfigManager->GetConfig<bool>(SystemConfigManager::O2_BOOL_INDEX))
    {
        mO2ResultLabel->hide();
    }
    if(ModuleTestManager::GetInstance()->GetAgModuleType() == None)
    {
        mCO2ResultLabel->hide();
        mAGResultLabel->hide();
    }
    if(SystemConfigManager::GetInstance()->GetConfig<int>(SystemConfigManager::MACHINE_MODEL_INDEX) == CENAR_30M)
    {
        mFlowResultLabel->hide();
    }
}

void CalibrateBlock::UpdateLabel(StandByPageInfoLabel *label)
{
    static QHash<StandByPageInfoLabel*, CAL_TYPE> typeMap
    {
        {mLeakResultLabel, CAL_TYPE::MANUAL_TEST},
        {mFlowResultLabel, CAL_TYPE::CAL_TYPE_FLOW_ZERO},
        {mO2ResultLabel, CAL_TYPE::CAL_TYPE_O2_100},
        {mCO2ResultLabel, CAL_TYPE::CAL_TYPE_CO2},
        {mAGResultLabel, CAL_TYPE::CAL_TYPE_AG}
    };

    static QMap<CAL_RESULT, QPair<ALL_STRINGS_ENUM, QColor>> map
    {
        {CAL_RESULT::CAL_SUCCESS, {ALL_STRINGS_ENUM::STR_SUCCESS, Qt::green}},
        {CAL_RESULT::CAL_FAIL, {ALL_STRINGS_ENUM::STR_FAIL, Qt::red}},
        {CAL_RESULT::CAL_CANCEL, {ALL_STRINGS_ENUM::STR_CANCEL, Qt::yellow}},
    };

    auto resultInfo = CalModuleManage::GetInstance()->GetLatestCalResult(typeMap[label]);

    if (resultInfo.first == -1)
    {
        label->hide();
        return;
    }
    else
    {
        label->show();
    }
    auto resultDisplay = map[resultInfo.second.mCalResult];
    auto dateTime = QDateTime::fromSecsSinceEpoch(resultInfo.first);

    label->SetResult(UIStrings::GetStr(resultDisplay.first), resultDisplay.second);
    label->SetCalTime(dateTime);
}
