//2025-06-04 14:34:25
//Generated by BAIGE
#pragma once
#include <QStringList>
#include "StringEnum.h"

namespace French
{
    QStringList UiStr
    {

		u8R"()", //STR_NULL
		u8R"(VCV)", //STR_LABEL_VCV
		u8R"(PCV)", //STR_LABEL_PCV
		u8R"(PSV)", //STR_LABEL_PSV
		u8R"(SIMV-VC)", //STR_LABEL_SIMVVCV
		u8R"(SIMV-PC)", //STR_LABEL_SIMVPCV
		u8R"(PRVC)", //STR_LABEL_PRVC
		u8R"(P-mode)", //STR_LABEL_PMODE
		u8R"(HLM)", //STR_LABEL_HLM
		u8R"(CPB)", //STR_LABEL_CPB
		u8R"(MECHANIQUE)", //STR_MODE_MECHANICAL
		u8R"(ACGO)", //STR_MODE_ACGO
		u8R"(En attente)", //STR_MODE_STANDBY
		u8R"(Manuel)", //STR_MODE_MANUAL
		u8R"(Rate)", //STR_VALUEPARAM_RATE
		u8R"(fspn)", //STR_VALUEPARAM_FSPN
		u8R"(I:E)", //STR_VALUEPARAM_IE
		u8R"(VTe)", //STR_VALUEPARAM_VTE
		u8R"(VTi)", //STR_VALUEPARAM_VTI
		u8R"(MV)", //STR_VALUEPARAM_MV
		u8R"(MVspn)", //STR_VALUEPARAM_MVSPN
		u8R"(Ppeak)", //STR_VALUEPARAM_PPEAK
		u8R"(Pplat)", //STR_VALUEPARAM_PPLAT
		u8R"(PEEP)", //STR_VALUEPARAM_PEEP
		u8R"(Pmean)", //STR_VALUEPARAM_PMEAN
		u8R"(Pmin)", //STR_VALUEPARAM_PMIN
		u8R"(R)", //STR_VALUEPARAM_R
		u8R"(C)", //STR_VALUEPARAM_C
		u8R"(FiO2)", //STR_VALUEPARAM_FIO2
		u8R"(EtCO2)", //STR_VALUEPARAM_ETCO2
		u8R"(FiCO2)", //STR_VALUEPARAM_FICO2
		u8R"(PR)", //STR_VALUEPARAM_PR
		u8R"(AA)", //STR_VALUEPARAM_AG_NULL
		u8R"(FiAA)", //STR_VALUEPARAM_AG_FINULL
		u8R"(FiN2O)", //STR_VALUEPARAM_FIN2O
		u8R"(FiHAL)", //STR_VALUEPARAM_FIHAL
		u8R"(FiENF)", //STR_VALUEPARAM_FIENF
		u8R"(FiSEV)", //STR_VALUEPARAM_FISEV
		u8R"(FiISO)", //STR_VALUEPARAM_FIISO
		u8R"(FiDES)", //STR_VALUEPARAM_FIDES
		u8R"(EtAA)", //STR_VALUEPARAM_AG_ETNULL
		u8R"(EtN2O)", //STR_VALUEPARAM_ETN2O
		u8R"(EtHAL)", //STR_VALUEPARAM_ETHAL
		u8R"(EtENF)", //STR_VALUEPARAM_ETENF
		u8R"(EtSEV)", //STR_VALUEPARAM_ETSEV
		u8R"(EtISO)", //STR_VALUEPARAM_ETISO
		u8R"(EtDES)", //STR_VALUEPARAM_ETDES
		u8R"(Apneé)", //STR_VALUEPARAM_ASPHY
		u8R"(VT)", //STR_SETTINGPARAM_VT
		u8R"(Rate)", //STR_SETTINGPARAM_RATE
		u8R"(I:E)", //STR_SETTINGPARAM_IE
		u8R"(Tip:Ti)", //STR_SETTINGPARAM_TITP
		u8R"(Pinsp)", //STR_SETTINGPARAM_PINSP
		u8R"(CPAP)", //STR_SETTINGPARAM_CPAP
		u8R"(Plimit)", //STR_SETTINGPARAM_PLIMIT
		u8R"(PEEP)", //STR_SETTINGPARAM_PEEP
		u8R"(Ftrig)", //STR_SETTINGPARAM_FTRIG
		u8R"(Ptrig)", //STR_SETTINGPARAM_PTRIG
		u8R"(Tinsp)", //STR_SETTINGPARAM_TINSP
		u8R"(Pente)", //STR_SETTINGPARAM_SLOPE
		u8R"(Psupp)", //STR_SETTINGPARAM_PSUPP
		u8R"(Au niveau final)", //STR_SETTINGPARAM_INENDLEVEL
		u8R"(VTe)", //STR_SETTINGLIMIT_VTE
		u8R"(Paw)", //STR_SETTINGLIMIT_PAW
		u8R"(FiO2)", //STR_SETTINGLIMIT_FIO2
		u8R"(Rate)", //STR_SETTINGLIMIT_RATE
		u8R"(MV)", //STR_SETTINGLIMIT_MV
		u8R"(1/(Min.L))", //STR_UNIT_1PERMINL
		u8R"(s)", //STR_UNIT_S
		u8R"(cmH2O)", //STR_UNIT_CMH2O
		u8R"(cmH2O/(L/s))", //STR_UNIT_CMH2OLS
		u8R"(mL/cmH2O)", //STR_UNIT_MLCMH2O
		u8R"(mmHg)", //STR_UNIT_MMHG
		u8R"(%)", //STR_UNIT_PERCENTAGE
		u8R"(L)", //STR_UNIT_L
		u8R"(mL)", //STR_UNIT_ML
		u8R"(L/min)", //STR_UNIT_LPERMIN
		u8R"(min)", //STR_UNIT_MIN
		u8R"(bpm)", //STR_UNIT_BPM
		u8R"(kg)", //STR_UNIT_KG
		u8R"(lb)", //STR_UNIT_LB
		u8R"(m)", //STR_UNIT_M
		u8R"(ft)", //STR_UNIT_FT
		u8R"(hPa)", //STR_UNIT_HPA
		u8R"(kPa)", //STR_UNIT_KPA
		u8R"(mBar)", //STR_UNIT_MBAR
		u8R"(---)", //STR_DASH
		u8R"(Insp.)", //STR_INSP
		u8R"(Exp.)", //STR_EXP
		u8R"(MAC)", //STR_MAC
		u8R"(Paw)", //STR_GRAPHNAME_PAW
		u8R"(Débit)", //STR_GRAPHNAME_FLOW
		u8R"(Volume)", //STR_GRAPHNAME_VOL
		u8R"(CO2)", //STR_GRAPHNAME_CO2
		u8R"(SPO2)", //STR_GRAPHNAME_SPO2
		u8R"(C%10 / C)", //STR_GRAPHNAME_C20C
		u8R"(Vol-Déb)", //STR_GRAPHNAME_VF
		u8R"(Paw-Vol)", //STR_GRAPHNAME_PV
		u8R"(Déb-Paw)", //STR_GRAPHNAME_FP
		u8R"(Vol-CO2)", //STR_GRAPHNAME_VCO2
		u8R"(O2)", //STR_GASNAME_O2
		u8R"(N2O)", //STR_GASNAME_N2O
		u8R"(CO2)", //STR_GASNAME_CO2
		u8R"(Air)", //STR_GASNAME_AIR
		u8R"(Pinsp)", //STR_CPAP
		u8R"(Alarme-Phy)", //STR_BIO_ALARM
		u8R"(Regl-Temps)", //STR_SET_TIME
		u8R"(VENTURA)", //STR_OEM
		u8R"(ELUNA-60)", //STR_ELUNA_60
		u8R"(ELUNA-70)", //STR_ELUNA_70
		u8R"(ELUNA-80)", //STR_ELUNA_80
		u8R"(CENAR-30)", //STR_CENAR_30
		u8R"(CENAR-40)", //STR_CENAR_40
		u8R"(CENAR-50)", //STR_CENAR_50
		u8R"(Niveau PSV)", //STR_PSV_LEVEL
		u8R"(Sauvegarde)", //STR_BACKUP
		u8R"(SN-)", //STR_SN_SHORT_CUT
		u8R"()", //STR_BLANK
		u8R"(RETOUR)", //STR_BACK
		u8R"(ENTREE)", //STR_ENTER
		u8R"(Le test de fuite invalide. cause :
1.En mode manuel, ou 
2.ACGO Ouvert, ou
3. Débitmètres non fermés.)", //STR_DISABLE_TEST
		u8R"(Veuillez faire le test comme suit :)", //STR_LABEL_BST_HEAD
		u8R"(1. Connecter la pièce en Y au bouchon de test de fuite, comme indiqué.
2. Gardez l'interrupteur de ventilation manuelle/mécanique en position mécanique.
3.Appuyez sur le bouton O2 Flush pour remplir complètement le soufflet.
4. Réglez tous les débitmètres à zéro. 
5.Appuyez sur le bouton « Démarrer » pour tester.)", //STR_LABEL_BST_INIT
		u8R"(Test)", //STR_TEST
		u8R"(Test d'étanchéité en cours.
Veuillez patienter environ 1 minute.)", //STR_LABEL_BST_TESTING
		u8R"(Le test de fuite a échoué.
Veuillez vérifier le circuit et réessayer.)", //STR_LABEL_BST_FAIL
		u8R"(Compliance)", //STR_COMPLIANCE
		u8R"(Test de fuite)", //STR_LEAK
		u8R"(Echec)", //STR_FAIL
		u8R"(Passer)", //STR_QULIFIED
		u8R"(Echec)", //STR_UNQULIFIED
		u8R"(Dernier Test)", //STR_RecentSTT
		u8R"(Dernière Calibration)", //STR_RECENT_CAL
		u8R"(Débit total)", //STR_TOTAL
		u8R"(Sortie)", //STR_EXIT
		u8R"(OK)", //STR_CONFIRM
		u8R"(Annuler)", //STR_CANCEL
		u8R"(Démarrer)", //STR_START
		u8R"(Réinitialiser)", //STR_RESET
		u8R"(Formes d'onde)", //STR_LABEL_WAVEFORMS
		u8R"(Boucles)", //STR_LABEL_SPIROMETRY
		u8R"(Tendances graphiques)", //STR_LABEL_TREND
		u8R"(Toutes les Valeurs)", //STR_LABEL_VALUES
		u8R"(sauvegarder la boucle)", //STR_LOOP_BUTTON
		u8R"(Reprendre)", //STR_LOOP_BUTTONRELEASE
		u8R"(Débitmètre)", //STR_LABEL_FLOWMETER
		u8R"(Alarme!)", //STR_ALARM
		u8R"(message)", //STR_MESSAGE
		u8R"(Menu Principal)", //STR_MAINMENU
		u8R"(Zero)", //STR_MAINMENU_ZERO
		u8R"(Valeurs par défaut)", //STR_MAINMENU_DEFAULT
		u8R"(Etalonnage)", //STR_CALIBRATION
		u8R"(L'étalonnage n'est pas disponible en mode manuel ou avec le commutateur ACGO activé)", //STR_CAL_DIABLE
		u8R"(Etalonnage)", //STR_MAINMENU_CAL
		u8R"(Etalonnage valide)", //STR_MAINMENU_CALDONE
		u8R"(Etalonnage non valide!)", //STR_MAINMENU_CALFAIL
		u8R"(Veuillez effectuer l'étalonnage comme suit)", //STR_MAINMENU_CAL_TIP
		u8R"(Étalonnage du débitmètre)", //STR_MAINMENU_FLOWCAL
		u8R"(1.Assurez-vous d'être en mode veille.
2. Réglez tous les débitmètres à zéro.
3. Cliquez sur le bouton « Démarrer ».)", //STR_MAINMENU_FLOWCAL_TXT2
		u8R"(sélectionner 'Etalonnage')", //STR_MAINMENU_FLOWCAL_TXT3
		u8R"(Zéro Etalonnage en cours, cela peut prendre 1 à 2 minutes.)", //STR_MAINMENU_FLOWCAL_TXT4
		u8R"(Etalonnage Cellule d'O2)", //STR_MAINMENU_O2CAL
		u8R"(Etal à 21%.)", //STR_MAINMENU_21PO2CAL
		u8R"(Etal à 100%.)", //STR_MAINMENU_100PO2CAL
		u8R"(Fait)", //STR_MAINMENU_CAL_FIN
		u8R"(1.Assurez-vous d' être en mode veille.
2. Éteignez tous les flux de gaz frais.
3. Ouvrez le bouchon et le clapet anti-retour inspiratoire, comme indiqué.
4.Cliquez sur le bouton « Démarrer » pour effectuer un étalonnage de la concentration d'oxygène à 21 %.)", //STR_MAINMENU_O2CAL_TXT3
		u8R"(L'étalonnage de la cellule d'O2 est en cours, veuillez patienter quelques minutes…)", //STR_MAINMENU_CAL_IN_PROCESS
		u8R"(L'étalonnage à 21 % est terminé, veuillez installer le bouchon et le clapet anti-retour inspiratoire comme indiqué, puis sélectionner « 100 % Cal. » ou 'Terminé'.)", //STR_MAINMENU_CAL_21_DONE
		u8R"(L'étalonnage de la concentration d'O2 à 100 % est terminé.)", //STR_MAINMENU_CAL_100_DONE
		u8R"(Échec de l’étalonnage de l’O2 à 21 % ; veuillez revenir au système de circuit respiratoire comme indiqué.)", //STR_MAINMENU_CAL_21_FAIL
		u8R"(L'étalonnage à 100 % de l'O2 a échoué.)", //STR_MAINMENU_CAL_100_FAIL
		u8R"(L'étalonnage de l'O2 est terminé, veuillez vérifier le système du circuit respiratoire.)", //STR_MAINMENU_CAL_FINISH
		u8R"(AG/CO2)", //STR_MAINMENU_AGCO2CAL
		u8R"(1.Assurez-vous d'être en mode veille.
2. Gardez la concentration de CO2 la même que dans l'atmosphère.)", //STR_MAINMENU_AGCO2CAL_TXT1_TITLE
		u8R"(Echaufement du module AG/CO2 est en cours)", //STR_MAINMENU_CAL_STATUS_DISABLE
		u8R"(Remise à zéro)", //STR_MAINMENU_CAL_STATUS_IN_PROG
		u8R"(Zero Etalonnage Reussite)", //STR_MAINMENU_CAL_STATUS_ZERODONE
		u8R"(Zero Etalonnage Echec)", //STR_MAINMENU_CAL_STATUS_FAILED
		u8R"(Dernier Zero Etalonnage)", //STR_LATEST_ZERO_CAL_TIME
		u8R"(Capteur Débit)", //STR_MAINMENU_SENSORCAL
		u8R"(Capteur de Pression)", //STR_MAINMENU_SENSORCAL_TXT3
		u8R"(Système)", //STR_MAINMENU_SYSSETTING
		u8R"(Paramètres généraux)", //STR_MAINMENU_EQUIPSET
		u8R"(Volume du système)", //STR_MAINMENU_EQUIPSET_TXT2
		u8R"(ON)", //STR_ON
		u8R"(OFF)", //STR_OFF
		u8R"(Unité de pression)", //STR_PAW_UNIT
		u8R"(Unité de CO2)", //STR_CO2_UNIT
		u8R"(Temps)", //STR_MAINMENU_TIMEDATE
		u8R"(Date)", //STR_MAINMENU_TIMEDATE_DATE
		u8R"(Heure)", //STR_MAINMENU_TIMEDATE_TIME
		u8R"(Heure(AM))", //STR_MAINMENU_TIMEDATE_12HOUR_AM
		u8R"(Heure(PM))", //STR_MAINMENU_TIMEDATE_12HOUR_PM
		u8R"(Format de date)", //STR_DATE_FORMAT
		u8R"(Format de l'heure)", //STR_TIME_FORMAT
		u8R"(Y - M - D)", //STR_MAINMENU_YMD
		u8R"(M - D - Y)", //STR_MAINMENU_MDY
		u8R"(D - M - Y)", //STR_MAINMENU_DMY
		u8R"(12h)", //STR_TIME_FORMAT_12H
		u8R"(24h)", //STR_TIME_FORMAT_24H
		u8R"(Changer type trigger)", //STR_MAINMENU_TRIGSET
		u8R"(IP)", //STR_MAINMENU_IP
		u8R"(Type Trigger)", //STR_MAINMENU_TRIGSET_TXT1
		u8R"(Afficage)", //STR_MAINMENU_DISPSETTING
		u8R"(Rapide)", //STR_MAINMENU_WAVESET_HIGH_SPEED
		u8R"(Normale)", //STR_MAINMENU_WAVESET_MID_SPEED
		u8R"(Lent)", //STR_MAINMENU_WAVESET_LOW_SPEED
		u8R"(Boucles)", //STR_MAINMENU_LOOPSET
		u8R"(Valeur)", //STR_MAINMENU_VALUE
		u8R"(Tendance graphique)", //STR_MAINMENU_TRENDSET
		u8R"(Temps de tendance)", //STR_MAINMENU_TRENDSET_TXT4
		u8R"(Info Système)", //STR_MAINMENU_SYSINFO
		u8R"(Version)", //STR_MAINMENU_VER
		u8R"(Nom)", //STR_MAINMENU_VER_TITLE1
		u8R"(Logiciel hôte)", //STR_MAINMENU_VER_TITLE1_TXT1
		u8R"(Version)", //STR_MAINMENU_VER_TITLE2
		u8R"(Date)", //STR_MAINMENU_VER_TITLE3
		u8R"(Config)", //STR_MAINMENU_CONFIG
		u8R"(Numéro Serie)", //STR_MAINMENU_CONFIG_TXT1
		u8R"(vérification du système)", //STR_MAINMENU_SELFTEST
		u8R"(Test Cellule d'O2)", //STR_MAINMENU_SELFTEST_TXT1
		u8R"(Test Capteur Débit d'Air)", //STR_MAINMENU_SELFTEST_TXT2
		u8R"(Test du capteur débit expiratoire)", //STR_MAINMENU_SELFTEST_TXT3
		u8R"(Test Capteur de Pression)", //STR_MAINMENU_SELFTEST_TXT4
		u8R"(Test Valve de Peep)", //STR_MAINMENU_SELFTEST_TXT5
		u8R"(Test soupape de sécurité)", //STR_MAINMENU_SELFTEST_TXT6
		u8R"(Test Capteur d'O2)", //STR_MAINMENU_SELFTEST_TXT7
		u8R"(Fuite(mL/min))", //STR_MAINMENU_SELFTEST_TXT8
		u8R"(Compliance(mL/cmH2O))", //STR_MAINMENU_SELFTEST_TXT9
		u8R"(Résistance des circuits (cmH2O/L/s))", //STR_MAINMENU_SELFTEST_TXT10
		u8R"(Fait)", //STR_FINISH
		u8R"(ingénieur)", //STR_MAINMENU_EGRSETTING
		u8R"(Configuration de l'ingénieur)", //STR_EGNEER_SETTING
		u8R"(Journal des opérations)", //STR_OPER_LOG
		u8R"(Étalonnage d'ingénieur)", //STR_EG_CAL
		u8R"(Info de configuration)", //STR_PRODUCT_CFG
		u8R"(Etalonnage Débit)", //STR_FLOW_CAL
		u8R"(Etalonnage Pression)", //STR_PRESS_CAL
		u8R"(En Veille)", //STR_STANDBY
		u8R"(Passer en mode veille ?)", //STR_STANDBY_STRING1
		u8R"(Temps)", //STR_ALARMSET_TIME
		u8R"(Éléments d'alarme)", //STR_ALARMSET_ALARMITEM
		u8R"(Priorité)", //STR_ALARMSET_PRIORITY
		u8R"(Bas)", //STR_ALARMSET_LOW
		u8R"(Haut)", //STR_ALARMSET_HIGH
		u8R"(Confi d'alarm)", //STR_ALARMSET
		u8R"(Limites d'alarme)", //STR_ALARMSET_ALARMLIMIT
		u8R"(Journal d'alarme)", //STR_ALARMSET_ALARMLOG
		u8R"(FiO2 Haut!!)", //STR_ALARMINFO_HIGH_FIO2
		u8R"(FiO2 Trop Bas!!!)", //STR_ALARMINFO_LOW_FIO2
		u8R"(PR Haut!!)", //STR_ALARMINFO_HIGH_PR
		u8R"(PR Bas!!)", //STR_ALARMINFO_LOW_PR
		u8R"(MV Très haut!!)", //STR_ALARMINFO_HIGH_MV
		u8R"(MV Trop Bas!!)", //STR_ALARMINFO_LOW_MV
		u8R"(Rate  Très haut!!)", //STR_ALARMINFO_HIGH_RATE
		u8R"(Rate  Trop Bas!!)", //STR_ALARMINFO_LOW_RATE
		u8R"(VTe  Très haut!!)", //STR_ALARMINFO_HIGH_VTE
		u8R"(VTe Trop Bas!!)", //STR_ALARMINFO_LOW_VTE
		u8R"(Paw Trop Bas!!)", //STR_ALARMINFO_LOW_PAW
		u8R"(Paw  Très haut!!!)", //STR_ALARMINFO_HIGH_PAW
		u8R"(Limite de Pression)", //STR_PRESSURE_REACH_LIMIT
		u8R"(FiN2O  Très haut!!!)", //STR_ALARMINFO_HIGH_FIN2O
		u8R"(FiN2O  Trop Bas!)", //STR_ALARMINFO_LOW_FIN2O
		u8R"(EtN2O  Très haut!!!)", //STR_ALARMINFO_HIGH_ETN2O
		u8R"(EtN2O Trop Bas!)", //STR_ALARMINFO_LOW_ETN2O
		u8R"(FiCO2  Très haut!!!)", //STR_ALARMINFO_HIGH_FICO2
		u8R"(FiCO2 Trop Bas!!!)", //STR_ALARMINFO_LOW_FICO2
		u8R"(EtCO2  Très haut!!!)", //STR_ALARMINFO_HIGH_ETCO2
		u8R"(EtCO2 Trop Bas!!)", //STR_ALARMINFO_LOW_ETCO2
		u8R"(High SPO2!!)", //STR_ALARMINFO_HIGH_SPO2
		u8R"(Low SPO2!!!)", //STR_ALARMINFO_LOW_SPO2
		u8R"(FiHAL  Très haut!!)", //STR_ALARMINFO_HIGH_FIHAL
		u8R"(FiHAL Trop Bas!)", //STR_ALARMINFO_LOW_FIHAL
		u8R"(FiENF  Très haut!!)", //STR_ALARMINFO_HIGH_FIENF
		u8R"(FiENF Trop Bas!)", //STR_ALARMINFO_LOW_FIENF
		u8R"(FiSEV  Très haut!!)", //STR_ALARMINFO_HIGH_FISEV
		u8R"(FiSEV Trop Bas!)", //STR_ALARMINFO_LOW_FISEV
		u8R"(FiISO Très haut!!)", //STR_ALARMINFO_HIGH_FIISO
		u8R"(FiISO Trop Bas!)", //STR_ALARMINFO_LOW_FIISO
		u8R"(FiDES  Très haut!!)", //STR_ALARMINFO_HIGH_FIDES
		u8R"(FiDES Trop Bas!)", //STR_ALARMINFO_LOW_FIDES
		u8R"(EtHAL  Très haut!!)", //STR_ALARMINFO_HIGH_ETHAL
		u8R"(EtHAL Trop Bas!)", //STR_ALARMINFO_LOW_ETHAL
		u8R"(EtENF Très haut!!)", //STR_ALARMINFO_HIGH_ETENF
		u8R"(EtENF Trop Bas!)", //STR_ALARMINFO_LOW_ETENF
		u8R"(EtSEV  Très haut!!)", //STR_ALARMINFO_HIGH_ETSEV
		u8R"(EtSEV Trop Bas!)", //STR_ALARMINFO_LOW_ETSEV
		u8R"(EtISO Très haut!!)", //STR_ALARMINFO_HIGH_ETISO
		u8R"(EtISO Trop Bas!)", //STR_ALARMINFO_LOW_ETISO
		u8R"(EtDES  Très haut!!)", //STR_ALARMINFO_HIGH_ETDES
		u8R"(EtDES Trop Bas!)", //STR_ALARMINFO_LOW_ETDES
		u8R"(Débit d'O2 trop élevé!)", //STR_ALARMINFO_O2_FLOW_HIGH
		u8R"(Débit de N2O trop élevé!)", //STR_ALARMINFO_N2O_FLOW_HIGH
		u8R"(Débit d'air excessif!)", //STR_ALARMINFO_AIR_FLOW_HIGH
		u8R"(Débit excessif!)", //STR_ALARMINFO_FLOW_HIGH
		u8R"(Rapport anormal O2 /  N2O!!!)", //STR_ALARMINFO_O2_N2O_UNUSUAL_RATIO
		u8R"(Batterie Faible!!)", //STR_TECHALARM_BATTERYLOW
		u8R"(Batterie : épuisée, arrêt!!!)", //STR_TECHALARM_BATTERYEMPTY
		u8R"(Pas de courant alternatif!)", //STR_TECHALARM_PLUGOUT
		u8R"(Erreur d'état d'alimentation!)", //STR_TECHALARM_POWERERR
		u8R"(ACGO Ouvert!!)", //STR_TECHALARM_ACGO
		u8R"(Pression continue des voies respiratoires trop élevée!!!)", //STR_ALARMINFO_GASHIGH
		u8R"(Pression continue des voies respiratoires trop faible!!)", //STR_ALARMINFO_GASLOW
		u8R"(Installation imparfaite du système de circuit!!!)", //STR_TECHALARM_GASLOOPERR
		u8R"(Pression du gaz Moteur basse!!!)", //STR_TECHALARM_GASSOURCELOW
		u8R"(Faible pression d'oxygène!!!)", //STR_TECHALARM_O2_GASSOURCELOW
		u8R"(Pression négative dans les voies respiratoires!!!)", //STR_ALARM_NEGPRESS
		u8R"(Oxygène Flush Au fil du temps!!)", //STR_TECHALARM_ADDO2TIMEOUT
		u8R"(Erreur de rinçage à l'oxygène!!!)", //STR_TECHALARM_ADDO2ERR
		u8R"(Le bidon d'absorbeur de CO2 ouvert!!!)", //STR_TECHALARM_BYPASSERR
		u8R"(Apnée!!)", //STR_TECHALARM_APNEAALARM
		u8R"(Tempsd'Apnée>120s!!!)", //STR_TECHALARM_APNEA_120_ALARM
		u8R"(Panne du capteur de débit!!!)", //STR_FLOW_SENSOR_ERR_ALARM
		u8R"(Température élevée de la CARTE PUISSANCE!!!)", //STR_TECHALARM_FAN_STOP
		u8R"(Capteur O2 déconnecté!!)", //STR_TECHALARM_O2_SENSOR_ERR
		u8R"(Communication du module de surveillance Arrêtée!!!)", //STR_TECHALARM_MINOTORING_MODULE_ERR
		u8R"(Erreur du capteur de pression!!!)", //STR_TECHALARM_PRESSURE_SENSOR_ERR
		u8R"(Peep Valve Déconnectée!!!)", //STR_TECHALARM_EXP_VALVE_ERR
		u8R"(Peep Valve Bloquée!!!)", //STR_TECHALARM_EXP_VALVE_CLOSE_OFF
		u8R"(Inspiratoire Valve Erreur!!!)", //STR_TECHALARM_INSP_VALVE_ERR
		u8R"(Erreur chauffement du modulee!)", //STR_TECHALARM_HEATING_MODULE_ERR
		u8R"(Module AG Adaptateur non installé!)", //STR_TECHALARM_AG_ADAPTOR_ERR
		u8R"(Temps écoulé!)", //STR_TECHALARM_TIME_UP
		u8R"(Capteur SpO2 Déconnecté!!)", //STR_TECHALARM_SPO2_BREAKOFF
		u8R"(AX Concentration Hors de portée!!)", //STR_TECHALARM_AX_OUT_OF_RANGE
		u8R"(CO2 Concentration Au-delà de la plage!!)", //STR_TECHALARM_CO2_OUT_OF_RANGE
		u8R"(N2O Concentration Au-delà de la plage!!)", //STR_TECHALARM_N2O_OUT_OF_RANGE
		u8R"(AG Capteur Température hors plage!!)", //STR_TECHALARM_TMP_OUT_OF_RANGE
		u8R"(AG Capteur Pression Hors plage!!)", //STR_TECHALARM_PRESS_OUT_OF_RANGE
		u8R"(AG Capteur Besoin d'un réétalonnage!!)", //STR_TECHALARM_AG_NEED_RECAIL
		u8R"(Mixte Anesthésique Agent!)", //STR_TECHALARM_AG_AGENT_MIX
		u8R"(AG Capteur Les données sont inexactes!!)", //STR_TECHALARM_AG_UNSPECIFIED_ACCURACY
		u8R"(AG Capteur Erreur logicielle!!!)", //STR_TECHALARM_AG_SW_ERR
		u8R"(AG Capteur Erreur matérielle!!!)", //STR_TECHALARM_AG_HW_ERR
		u8R"(AG Capteur Erreur Moto!!!)", //STR_TECHALARM_MOTO_ERR
		u8R"(AG Capteur : Perte d'étalonnage Données!!)", //STR_TECHALARM_AG_LOST_CAIL
		u8R"(Besoin de remplacer AG Capteur Adaptateur!!)", //STR_TECHALARM_AG_REPLACE
		u8R"(Commutateur manuel)", //STR_TIP_MANUAL
		u8R"(Haut)", //STR_HIGH
		u8R"(Mlieu)", //STR_MID
		u8R"(Bas)", //STR_LOW
		u8R"(Minuteur)", //STR_TIMER1
		u8R"(Temps écoulé)", //STR_TIMER2
		u8R"(réinitialiser)", //STR_TIMER3
		u8R"(Ouvrir)", //STR_OPEN
		u8R"(Fermer)", //STR_CLOSE
		u8R"(Certains alarme physique fermée)", //STR_BIO_ALARM_CLOSE
		u8R"(Limite de portée du paramètre)", //STR_PARAM_LIMITED
		u8R"(Vérifier Syst.)", //STR_SYSTEM_SELFTEST
		u8R"(Réessayer)", //STR_RETRY
		u8R"(Ignorer)", //STR_IGNORE
		u8R"(Test du module de surveillance)", //STR_SELFTEST_MONITORING
		u8R"(Test de courant alternatif)", //STR_SELFTEST_AC
		u8R"(Test Batterie)", //STR_SELFTEST_BATTERY
		u8R"(Test de l'horloge)", //STR_SELFTEST_SYS_TIME
		u8R"(Test du débitmètre)", //STR_SELFTEST_FLOWMETER
		u8R"(Test du module AG/CO2)", //STR_SELFTEST_AG_CO2
		u8R"(Test du capteur d'O2)", //STR_SELFTEST_O2
		u8R"(Test du module SPO2)", //STR_SELFTEST_SPO2
		u8R"(Test d'écran tactile)", //STR_SELFTEST_TOUCHSCREEN
		u8R"(Alimentation en gaz Pression Test)", //STR_SELFTEST_GAS_SOURCE
		u8R"(Base de données Chargement)", //STR_SELFTEST_DATABASE
		u8R"(PEP Valve Test)", //STR_SELFTEST_EXHALATION_VALUE
		u8R"(Valve d'inhalation Test)", //STR_SELFTEST_INHALATION_VALUE
		u8R"(Arrêt de la communication du débitmètre)", //STR_FLOWMETER_STOP
		u8R"(Échec de l’étalonnage du capteur de surveillance)", //STR_MONIT_SENSOR_CAL_FAIL
		u8R"(La précision de la surveillance n'est pas élevée)", //STR_MONITOR_ACCURACY_LOW
		u8R"(Erreur du capteur du débitmètre O2)", //STR_O2_FM_SENSOR_ERROR
		u8R"(Erreur du capteur du débitmètre N2O)", //STR_N2O_FM_SENSOR_ERROR
		u8R"(Erreur du capteur du débitmètre d'air)", //STR_AIR_FM_SENSOR_ERROR
		u8R"(Erreur matérielle du débitmètre)", //STR_FM_HARDWARE_ERROR
		u8R"(Le capteur SPO2 déconnecté)", //STR_SPO2_SENSOR_UNUNITED
		u8R"(Le capteur AG Déconnecté)", //STR_AG_ERROR
		u8R"(Le capteur d'O2 est déconnecté)", //STR_O2_CONCENTRATION_ERROR
		u8R"(Écran tactile Appuyé Vers le bas)", //STR_TOUCH_SCREEN_HOLDBACK
		u8R"(Erreur d'heure système)", //STR_SYS_TIME_ERR
		u8R"(Fuite excessive du circuit)", //STR_BREATH_LEAK_HIGH
		u8R"(Erreur de batterie)", //STR_BATTERYERR
		u8R"(Résultat)", //STR_RESULT
		u8R"(Passer)", //STR_PASS
		u8R"(Événement d’opération)", //STR_OPER_EVENT
		u8R"(Informations détaillées)", //STR_DETAIL_INFO
		u8R"(Allumer)", //STR_SYS_START
		u8R"(Sélectionner le mode)", //STR_RUNMODE_CHAGNE
		u8R"(Mode de ventilation modifié)", //STR_VENTMODE_CHAGNE
		u8R"(Paramètre de ventilation modifié)", //STR_VENT_PARAM_CHANGE
		u8R"(Changer Limite haute d'alarme)", //STR_HIGH_ALARM_LIMIT_CHANGE
		u8R"(Changer Limite basse d'alarme)", //STR_LOW_ALARM_LIMIT_CHANGE
		u8R"(Paramètre du mode HLM modifié)", //STR_HLM_PARAM_CHANGE
		u8R"(Modèle de produit)", //STR_PRODUCT_MODE
		u8R"(Numéro de série)", //STR_SN
		u8R"(Date de production)", //STR_P_DATE
		u8R"(Version du logiciel)", //STR_S_VERION
		u8R"(Sauter)", //STR_SKIP
		u8R"(Cliquez sur le bouton « Démarrer » pour démarrer l'étalonnage.)", //STR_CAL_START
		u8R"(L’étalonnage du débit est-il en cours…)", //STR_CAL_IN_FLOW_PROC
		u8R"(Échec de l’étalonnage du débit.)", //STR_CAL_FLOW_FAIL
		u8R"(Calibrage du débit réussi.)", //STR_CAL_FLOW_DONE
		u8R"(Dans Étalonnage de pression...)", //STR_CAL_IN_PEEP_PROC
		u8R"(L'étalonnage de la pression a réussi.)", //STR_CAL_PEEP_DONE
		u8R"(Échec de l’étalonnage de la pression.)", //STR_CAL_PEEP_FAIL
		u8R"(Exportation Enregistrements)", //STR_EXPRT_DATA
		u8R"(Exporter des données)", //STR_READ_CAL
		u8R"(Cliquez sur « Démarrer » pour exporter les données d'étalonnage.)", //STR_EXPRT_START
		u8R"(Étalonnage du débitmètre)", //STR_FLOWMETER_CAL
		u8R"(Input 0 to start Calibration)", //STR_FLOWMETER_CAL_START
		u8R"(Cal data)", //STR_FM_CAL_DATA
		u8R"(Data calibration failed. Please try this point again.)", //STR_FM_CAL_FAIL
		u8R"(Current point done, please input the next point.)", //STR_FM_CAL_DONE_NEXT
		u8R"(Flowmeter calibration is completed, please select 'OK' or 'CANCEL'.)", //STR_FM_CAL_FINISH
		u8R"(Flowmeter calibration is in progress...)", //STR_FM_CAL_WAIT
		u8R"(Flowmeter Calibration Successful.)", //STR_FM_CAL_SUCCEED
		u8R"(Adresse IP)", //STR_IPADDR
		u8R"(Masque)", //STR_NETMASK
		u8R"(Porte)", //STR_GATEWAY
		u8R"(MAC)", //STR_MACADDR
		u8R"(Confirmer)", //STR_CONFIRM_NET
		u8R"(.)", //STR_DOT
		u8R"(CODE CLÉ)", //STR_KEYCODE
		u8R"(MISE À JOUR)", //STR_FIRMUPDATE
		u8R"(Démo)", //STR_SOFT_MODE
		u8R"(Astuce : Redémarrez le système s'il vous plaît !)", //STR_SOFT_MODE_SWITCHED_TIP
		u8R"(Mode démo)", //STR_SOFT_DEMO_MODE
		u8R"(Mode exécution)", //STR_SOFT_RUN_MODE
		u8R"(Étalonner)", //STR_CALI
		u8R"(L'étalonnage est-il en cours... Cliquez sur la touche programmable pour annuler)", //STR_CALI_PROC
		u8R"(Configuration du logiciel)", //STR_SOFT_CFG_TITLE
		u8R"(Erreur de code de configuration)", //STR_SOFT_CFG_INPUT_TIP
		u8R"(Échec de la configuration)", //STR_SOFT_CFG_FAIL
		u8R"(Configuration réussie)", //STR_SOFT_CFG_SUCCEED
		u8R"(Luminosité de l'écran)", //STR_MAINMENU_EQUIPSET_LCD
		u8R"(Débit d'O2 Anormal!)", //STR_O2_FLOW_INEXACT
		u8R"(Débit de gaz d'équilibrage Anormal!)", //STR_BANLANCE_FLOW_INEXACT
		u8R"(Pas de gaz frais!!)", //STR_FLOW_TOO_LOW
		u8R"(Panne d’approvisionnement en O2!)", //STR_O2_PRESS_LOW
		u8R"(Panne d’approvisionnement en N2O!!)", //STR_N2O_PRESS_LOW
		u8R"(Panne d’alimentation en air!!)", //STR_AIR_PRESS_LOW
		u8R"(Le capteur O2 ne lit pas les données d'étalonnage!!)", //STR_FLOWMETER_O2_SENSOR_ERROR
		u8R"(Le capteur N2O ne lit pas les données d'étalonnage!!)", //STR_FLOWMETER_N2O_SENSOR_ERROR
		u8R"(Le capteur d'air ne lit pas les données d'étalonnage!!)", //STR_FLOWMETER_AIR_SENSOR_ERROR
		u8R"(Le système de contrôle de flux de sauvegarde est activé!!)", //STR_BACKUP_FLOW_CONTROL_OPEN
		u8R"(Histoire)", //STR_HISTORY_DATA
		u8R"(Installation)", //STR_SETTINGS
		u8R"(Geler)", //STR_FREEZING
		u8R"(Alarme Muet)", //STR_ALARM_MUTE
		u8R"(BoucleInfo)", //STR_LOOPINFO
		u8R"(Dern. Vérif. système)", //STR_LATEST_SELFTEST
		u8R"(Étape précédente)", //STR_PRE_STEP
		u8R"(Étape suivante)", //STR_NEXT_STEP
		u8R"(Fait)", //STR_ACCOMPLISH
		u8R"(Arrêt)", //STR_STOP
		u8R"(Continuer)", //STR_CONTINUE
		u8R"(Êtes-vous sûr d'ignorer la vérification du système ?)", //STR_SELFTEST_SKIP_TIP
		u8R"(Test de fuite)", //STR_MANUAL_LEAK_CHECK
		u8R"(1. Assurez-vous que le système est en mode veille et que l'interrupteur de ventilation manuelle/mécanique est en position manuelle.
2. Connectez le Ballon manuel au port du Ballon manuel.
3. Branchez la pièce en Y dans le bouchon de test de fuite pour bloquer la sortie d'air.
4. Ajustez le bouton de la valve APL à 70 cmH2O.
5. Ajustez le débit d'O2 à 0,15 L/min.
6. Appuyez sur le bouton de rinçage O2 pour augmenter la pression des voies respiratoires à environ 30 cmH2O.
7. Relâchez le bouton de rinçage O2. Observez le manomètre des voies respiratoires.)", //STR_MANUAL_LEAK_CHECK_TIP
		u8R"(Test du circuit de gaz)", //STR_GAS_CIRCUIT_CHECK
		u8R"(1. Assurez-vous que le système respiratoire est correctement connecté et que les conduites respiratoires ne sont pas endommagées. Assurez-vous que le système respiratoire est verrouillé.
2. Vérifiez si l'absorbeur de CO2 est correctement installé.
3. Vérifiez si la couleur de la sodalime est évidente, veuillez remplacer la sodalime immédiatement si elle l'est.)", //STR_GAS_CIRCUIT_CHECK_TIP
		u8R"(Test du débitmètre)", //STR_FLOWMETER_CHECK
		u8R"(1. Assurez-vous que le système d'alimentation en gaz est correctement connecté et que la pression est normale. La lecture du débitmètre est nulle lorsque le bouton du débitmètre est réglé sur zéro.
2. Assurez-vous que la paroi intérieure du débitmètre monotube n'est pas sale.)", //STR_FLOWMETER_CHECK_TIP
		u8R"(Vérifier le vaporisateur)", //STR_ANESTHETIC_GAS_EVAPORATOR_CHECK
		u8R"(1. Assurez-vous que le vaporisateur est dans la bonne position verrouillée.
2. Assurez-vous que le vaporisateur est réglé sur 0.
3. Assurez-vous que le vaporisateur est rempli à un niveau normal.
4. Assurez-vous que l'évaporateur est rempli et verrouillé en toute sécurité.)", //STR_ANESTHETIC_GAS_EVAPORATOR_CHECK_TIP
		u8R"(Chronomètre)", //STR_TIMER
		u8R"(Compte à rebours)", //STR_COUNTDOWN
		u8R"(Ventilateur Alarme Limite)", //STR_VENTILATOR_ALARM_LIMIT
		u8R"(AG Alarme Limite)", //STR_ANESTHETIC_GAS_ALARM_LIMIT
		u8R"(Actuel Alarme)", //STR_CUR_ALARM
		u8R"(4 formes d'onde)", //STR_FOUR_WAVE
		u8R"(3 formes d'onde)", //STR_THREE_WAVE
		u8R"(Boucles)", //STR_LOOP
		u8R"(Contrôle des gaz frais)", //STR_FLOW_CONTROL_MENU
		u8R"(Prise rapide)", //STR_QUICK_SET
		u8R"(Mode de contrôle)", //STR_CONTROL_MODE
		u8R"(Débit total)", //STR_TOTAL_FLOW_CONTROL
		u8R"(Flux direct)", //STR_SINGLE_PIPE_FLOW_CONTROL
		u8R"(Gaz d’équilibre)", //STR_BALANCE_FLOW
		u8R"(Tendances graphiques)", //STR_TREND_GRAPH
		u8R"(Minutes)", //STR_MINUTE
		u8R"(Pré-événement)", //STR_PRE_EVENT
		u8R"(Prochain événement)", //STR_NEXT_EVENT
		u8R"(Tendances de la liste)", //STR_TREND_TABLE
		u8R"(TOUS)", //STR_ALL_PARAMS
		u8R"(Paramètres de pression)", //STR_PRESSURE_PARAMS
		u8R"(Paramètres de vol)", //STR_VOLUME_PARAMS
		u8R"(Paramètres de temps)", //STR_TIME_PARAMS
		u8R"(Débitmètre Par.)", //STR_FLOWMETER_PARAMS
		u8R"(Paramètres agricoles)", //STR_AG_PARAMS
		u8R"(Détail de la boucle)", //STR_LOOP_DETAIL
		u8R"(Enreg. boucle)", //STR_SAVE_LOOP
		u8R"(Supprimer)", //STR_DELETE_LOOP
		u8R"(Afficher la référence)", //STR_SWITCH_REFER_LOOP
		u8R"(Comparer la boucle)", //STR_COMPARE_LOOP
		u8R"(Boucle VF)", //STR_LOOP_VF
		u8R"(Boucle PV)", //STR_LOOP_PV
		u8R"(Boucle FP)", //STR_LOOP_FP
		u8R"(Le confirmer comme boucle de base ?)", //STR_CONFIRM_REF_LOOP_TIP
		u8R"(Confirmer pour supprimer la boucle ?)", //STR_CONFIRM_DELETE_LOOP_TIP
		u8R"(Confirmez-le comme boucle de contraste ?)", //STR_CONFIRM_COMPARE_LOOP_TIP
		u8R"(Événements hist.)", //STR_HISTORY_EVENT
		u8R"(Date)", //STR_DATE
		u8R"(Tous les événements)", //STR_ALL_EVENT
		u8R"(Alarm niv.élevé)", //STR_HIGH_LEVEL_ALARM
		u8R"(Alarm niv.Moyen)", //STR_MID_LEVEL_ALARM
		u8R"(Alarm niv.bas)", //STR_LOW_LEVEL_ALARM
		u8R"(Rapide)", //STR_PROMPT
		u8R"(Fonctionner)", //STR_OPERATE
		u8R"(Succès)", //STR_SUCCESS
		u8R"(Volume/Luminosité)", //STR_VOL_BRIGHT
		u8R"(Configuration de l'écran)", //STR_UI_SETTING
		u8R"(Entretien)", //STR_FACTORY_MAINTENANCE
		u8R"(Langue)", //STR_LANGUAGE
		u8R"(Russian)", //STR_LANGUAGE_RUS
		u8R"(Chinese)", //STR_LANGUAGE_CHN
		u8R"(English)", //STR_LANGUAGE_ENG
		u8R"(Ukrainian)", //STR_LANGUAGE_UKR
		u8R"(French)", //STR_LANGUAGE_FRE
		u8R"(Protocole)", //STR_PROTOCAL
		u8R"(Config du protocole)", //STR_PROTOCAL_SETTINGS
		u8R"(Protocole série)", //STR_SERIAL_PROTOCAL
		u8R"(Aucun)", //STR_NONE
		u8R"(Débit en bauds)", //STR_BAUD_RATE
		u8R"(Bits de données)", //STR_DATA_BITS
		u8R"(Parité)", //STR_VERIFY
		u8R"(Impair)", //STR_ODD
		u8R"(Même)", //STR_EVEN
		u8R"(Bits d'arrêt)", //STR_STOP_BIT
		u8R"(Protocole réseau)", //STR_NETWORK_PROTOCAL
		u8R"(Masque de sous-réseau)", //STR_SUBNET_MASK
		u8R"(Statut)", //STR_LINK_STATE
		u8R"(Connecté)", //STR_CONNECTED
		u8R"(Déconnecté)", //STR_DISCONNECTED
		u8R"(Intervalle)", //STR_SEND_INTERVAL
		u8R"(IP cible)", //STR_TARGET_IP
		u8R"(Port cible)", //STR_TARGET_PORT
		u8R"(Confirmer la connexion ?)", //STR_CONFIRM_CONNECT_TIP
		u8R"(Confirmer pour se déconnecter ?)", //STR_CONFIRM_DISCONNECT_TIP
		u8R"(Informations système)", //STR_SYSTEM_INFO
		u8R"(Informations de configuration)", //STR_CONFIG_INFO
		u8R"(Activé)", //STR_CONFIGURED
		u8R"(Ligne Plimite)", //STR_PLIMIT_LINE
		u8R"(SUR)", //STR_SHOW
		u8R"(DÉSACTIVÉ)", //STR_HIDE
		u8R"(Dessiner une vague)", //STR_DRAW_CURVE
		u8R"(Courbe)", //STR_DRAW_LINE
		u8R"(Remplir)", //STR_FILL_AREA
		u8R"(Vitesse de balayage)", //STR_SWEEP_SPEED
		u8R"(Débitmètre zéro)", //STR_FLOWMETER_ZERO_CAL
		u8R"(Début ventilation)", //STR_START_VENTILATION
		u8R"(Retour)", //STR_RETURN
		u8R"(Sélectionner une boucle de ligne de base)", //STR_SWITCH_BASE_LOOP
		u8R"(Une seule batterie détectée)", //STR_ONLY_ONE_BATTER
		u8R"(Base de données Endommagée!!)", //STR_DB_FAIL
		u8R"(AG Le capteur est en train de se réchauffer)", //STR_AG_WARMING
		u8R"(Le capteur de CO2 chauffe)", //STR_CO2_WARMING
		u8R"(La minuterie a atteint sa limite maximale)", //STR_TIMER_MAX
		u8R"(Le compte à rebours est terminé)", //STR_COUNTDOWN_END
		u8R"(L'étalonnage n'est disponible qu'en mode veille)", //STR_CALIB_USE_ONLY_STANBY
		u8R"(Entrez dans la maintenance)", //STR_ENTER_MAINTENANCE
		u8R"(La maintenance n'est disponible qu'en mode veille)", //STR_MAINTENANCE_USE_ONLY_STANBY
		u8R"(Paramètres d'usine)", //STR_P_SETTINGS
		u8R"(Restaurer d'usine)", //STR_RESTORE_FACTORY_SETTING
		u8R"(Panne)", //STR_BREAKDOWN
		u8R"(Normale)", //STR_NORMAL
		u8R"(Fin de la suppression)", //STR_END_DELETE
		u8R"(Fin du commutateur)", //STR_END_SWITCH
		u8R"(Veuillez effectuer l'étalonnage du zéro comme suit)", //STR_ZERO_CAL_TIP
		u8R"(Changer le mode de ventilation)", //STR_CHANGE_VENT_MODE
		u8R"(Lim sup Dépassé)", //STR_OUT_OF_UPPER_LIMIT
		u8R"(Lim inf Dépassé)", //STR_OUT_OF_LOWER_LIMIT
		u8R"(Valeur saisie incorrecte)", //STR_FORMAT_ERROR
		u8R"(Valeur invalide)", //STR_INVALID_VALUE
		u8R"(L'étalonnage O2 est annulé ; veuillez revenir au système de circuit respiratoire comme indiqué.)", //STR_O2_CAL_CANCEL
		u8R"(Attendre)", //STR_HOTKEY_STANDBY
		u8R"(Délai d'arrêt)", //STR_SHUTDOWN_DELAY
		u8R"(Gaz)", //STR_EXTERNAL_MODULE
		u8R"(RM)", //STR_BREATHING_MECHANICS_PARAM
		u8R"(Vent.Fonction)", //STR_VENTILATORY_FUNCTION_PARAM
		u8R"(Changer le mot de passe)", //STR_CHANGE_PASSWORD
		u8R"(Entrez le nouveau mot de passe)", //STR_ENTER_NEW_PASSWORD
		u8R"(Confirmer le nouveau mot de passe)", //STR_NEW_PASSWORD_CONFIRM
		u8R"(Confirmer Change)", //STR_CONFIRM_CHANGE
		u8R"(*Le mot de passe peut comporter de zéro à six caractères, tous les caractères doivent être des chiffres.)", //STR_PASSWORD_TIP
		u8R"(Erreur de format de saisie du mot de passe)", //STR_PASSWORD_INPUT_FAULT_TIP
		u8R"(Confirmer pour changer le mot de passe ?)", //STR_PASSWORD_CHANGE_TIP
		u8R"(Tableau de commande principal)", //STR_MAIN_CONTROL_BOARD
		u8R"(Tableau de surveillance)", //STR_MONITOR_BOARD
		u8R"(U-Boot)", //STR_U_BOOT
		u8R"(Système de fichiers Linux)", //STR_LINUX_FILE_SYSTEM
		u8R"(Noyau Linux)", //STR_LINUX_KERNEL
		u8R"(Carte débitmètre)", //STR_FLOWMETER_BOARD
		u8R"(Carte d'alimentation)", //STR_POWER_BOARD
		u8R"(Vt650)", //STR_VT_650
		u8R"(VtPlus)", //STR_VT_PLUS
		u8R"(VtMini)", //STR_VT_MINI
		u8R"(Calibrage Appareil Modèle)", //STR_VT_MODEL_CHOICE
		u8R"(Audition)", //STR_AUDITION
		u8R"(Entrez votre mot de passe)", //STR_ENTER_PASSWORD
		u8R"(Test du pavé tactile)", //STR_TOUCH_PAD
		u8R"(Écran tactile)", //STR_TOUCH_SCREEN
		u8R"(Pas de gaz frais)", //STR_FLOW_TOO_LOW_PROMPT
		u8R"(Changement)", //STR_CHANGE
		u8R"(Démarrez la ventilation. Mode de ventilation actuel)", //STR_LOG_ENTER_VENT_MODE
		u8R"(Délai d'arrêt)", //STR_LOG_SHUTDOWN_DELAY
		u8R"(Annuler le délai d'arrêt)", //STR_LOG_CANCEL_SHUTDOWN_DELAY
		u8R"(Ventilation mécanique)", //STR_VENT
		u8R"(Valeur par défaut de restauration de la limite d'alarme)", //STR_ALARM_LIMIT_RESTORE_DEFAULT
		u8R"(Changer le mode de contrôle)", //STR_CHANGE_CONTROL_MODE
		u8R"(Changer le mode de solde)", //STR_CHANGE_BALANCE_MODE
		u8R"(Modifier la valeur du débit)", //STR_CHANGE_FLOW_VALUE
		u8R"(Mettre à jour vers)", //STR_UPGRADE
		u8R"(CÉNAR-30M)", //STR_CENAR_30M
		u8R"(AG Capteur)", //STR_SELFTEST_AG
		u8R"(CO2 Capteur)", //STR_SELFTEST_CO2
		u8R"(Modifier le paramètre du mode de ventilation)", //STR_CHANGE_VENT_MODE_PARAM
		u8R"(Date de changement)", //STR_CHANGE_SYSTEM_DATE
		u8R"(Changer l'heure)", //STR_CHANGE_SYSTEM_TIME
		u8R"(Erreur de mot de passe)", //STR_PASSWORD_ERROR_TIP
		u8R"(Deux mots de passe ne correspondent pas)", //STR_PASSWORD_DONOT_MATCH
		u8R"(Unité de patte de commutation)", //STR_SWITCH_PAW_UNIT
		u8R"(Changer de CO2 Concentration Unité)", //STR_SWITCH_CO2_UNIT
		u8R"(AM)", //STR_TIME_AM
		u8R"(PM)", //STR_TIME_PM
		u8R"(Ventilateur alarme limite de restauration par défaut)", //STR_VENTILATOR_LIMIT_RESTORE_DEFAULT
		u8R"(Limite de gaz anesthésique restaurer la valeur par défaut)", //STR_ANESTHETIC_GAS_LIMIT_RESTORE_DEFAULT
		u8R"(Le réglage de la minuterie ne peut pas dépasser)", //STR_TIMER_LIMIT
		u8R"(Dépasse la limite d'alarme)", //STR_INPUT_ALARM_LIMIT
		u8R"(Code de configuration)", //STR_CFG_CODE
		u8R"(Étalonnage PV du débitmètre)", //STR_PV_CAL
		u8R"(Temps d'arrêt restant)", //STR_SHUTDOWN_TIME
		u8R"(Pour annuler l'arrêt, veuillez rallumer l'interrupteur du système)", //STR_SHUTDOWN_TIP
		u8R"(Inactivé)", //STR_NOT_CONFIG
		u8R"(Entrer)", //STR_IN
		u8R"(Sortie)", //STR_OUT
		u8R"(Le capteur de CO2 est déconnecté)", //STR_CO2_ERROR
		u8R"(Augmenter le débit)", //STR_ADD_FLOW
		u8R"(Diminuer le débit)", //STR_REDUCE_FLOW
		u8R"(ENVOYER)", //STR_SEND
		u8R"(Les paramètres d'usine ont été restaurés, veuillez redémarrer la machine)", //STR_RESTORE_TIP
		u8R"(Remplacez l'adresse IP par ************ pour l'étalonnage du débit. Cliquez sur OK pour modifier l'adresse IP)", //STR_FLOW_TIP
		u8R"(Module AG en remise à zéro, veuillez patienter)", //STR_AG_ZEROING_TIP
		u8R"(L'étalonnage n'est pas valide en mode manuel ou ACGO)", //STR_MENUAL_ACGO_STATE_TIP
		u8R"(La communication du capteur de débit d'O2 s'est arrêtée)", //STR_O2_FM_SENSOR_COMMUNICATION_STOP
		u8R"(Erreur du capteur de débit de gaz d'équilibrage)", //STR_BALANCE_GAS_FM_SENSOR_ERROR
		u8R"(Communication capteur de contrôle de flux de secours s'est arrêtée)", //STR_BACKUP_FLOW_CONTROL_COMMUNICATION_STOP
		u8R"(Capteur CO2 Adaptateur non installé!)", //STR_TECHALARM_CO2_ADAPTOR_ERR
		u8R"(1. Assurez-vous que le système d'alimentation en air est correctement connecté et que la pression est normale. Assurez-vous que la bouée est à son point le plus bas lorsque le bouton du débitmètre est à zéro.
2. Assurez-vous qu'il n'y a pas de saleté sur la paroi intérieure du tube en verre du débitmètre. »)", //STR_FLOWMETER_CHECK_TIP_FOR_80
		u8R"(Mode manuel/ACGO bloque le test de fuite)", //STR_MENUAL_ACGO_LEAK_STATE_TIP
		u8R"(1. Assurez-vous que le système d'alimentation en gaz est correctement connecté, que la pression est normale et que le débit est au niveau le plus bas lorsque le bouton du débitmètre est fermé (valeur minimale).
2. Assurez-vous que la paroi intérieure du débitmètre monotube est propre.)", //STR_FLOWMETER_CHECK_TIP_FOR_C30M
		u8R"(AG Capteur Zéro Calibrage)", //STR_AG_ZERO_CAL
		u8R"(CO2 Capteur Zéro Calibrage)", //STR_CO2_ZERO_CAL
		u8R"(CO2 Capteur Zéro Calibrage Est En Progression, veuillez patienter)", //STR_CO2_ZEROING_TIP
		u8R"(excès)", //STR_OVER
		u8R"(Lim sup d'alarm)", //STR_ALARM_HIGH_LIMIT
		u8R"(Lim inf d'alarm)", //STR_ALARM_LOW_LIMIT
		u8R"(Direct)", //STR_DIRECT_FLOW
		u8R"(Veuillez entrer la valeur de calibrage !)", //STR_EMPTY_INPUT_TIP
		u8R"(Veuillez entrer la valeur de calibrage valide !)", //STR_INVALID_INPUT_TIP
		u8R"(C)", //STR_SERIAL_C
		u8R"(E)", //STR_SERIAL_E
		u8R"(21 % Étalonnage)", //STR_21PO2CAL
		u8R"(100 % Étalonnage)", //STR_100PO2CAL
		u8R"(Paramètre de forme d'onde)", //STR_WAVEFORMS_SETTINGS
		u8R"(fermer)", //STR_SHUT_DOWN
		u8R"(Test préopératoire)", //STR_PREOPERATIVE_CHECK
		u8R"(La concentration de CO2 dans la boucle est trop élevée)", //STR_CO2_CANNOT_ZERO_TIP
		u8R"(Gas source configuration)", //STR_FLOWMETER_GAS_CONFIG
		u8R"(Flowmeter board type)", //STR_FLOWMETER_SENSOR_TYPE
		u8R"(Software update)", //STR_SOFTWARE_UPDATE
		u8R"(Update switch)", //STR_UPDATE_SWITCH
		u8R"(Update type)", //STR_UPDATE_TYPE
		u8R"(Previous version)", //STR_SRC_VERSION
		u8R"(Target version)", //STR_TARGET_VERSION
		u8R"(Update progress)", //STR_PROGRESS
		u8R"(Software needs to restart to complete the update, it will automatically restart later!)", //STR_UPDATE_FINISH_TIP
		u8R"(Restart immediately)", //STR_RESTART_NOW
		u8R"(Communication error during software transfer.)", //STR_UPDATE_COM_ERROR_TIP
		u8R"(Software verification failed during transfer; requesting retransmission.)", //STR_UPDATE_CRC_ERROR_TIP
		u8R"(Online configuration in progress...)", //STR_MACHINE_CONFIGURING
		u8R"(Device tree)", //STR_DEVICE_TREE
		u8R"(Custom update)", //STR_CUSTOM_UPDATE
		u8R"(Logo png)", //STR_LOGO_PNG
		u8R"(Logo bin)", //STR_LOGO_BIN
		u8R"(Daemon version)", //STR_DAEMON_VERSION
		u8R"(Online configuration completed)", //STR_UPDATE_CONFIG
		u8R"(Custom product model configuration completed)", //STR_UPDATE_PRODUCT_MODEL
		u8R"(Custom program)", //STR_UPDATE_CUSTOM_STEP
		u8R"(Software boot logo upgrade completed)", //STR_UPDATE_LOGO
		u8R"(Uboot boot logo upgrade completed)", //STR_UPDATE_LOGO_BIN
		u8R"(AG module type)", //STR_AG_MODULE_TYPE
		u8R"(Mainstream)", //STR_MAIN_STREAM
		u8R"(Sidestream)", //STR_SIDE_STREAM
		u8R"(Check AG sampling line!)", //STR_TECHALARM_AG_SAMPLING_ERR
		u8R"(Check CO2 sampling line!)", //STR_TECHALARM_CO2_SAMPLING_ERR
		u8R"(Need To Replace CO2 Sensor Adapter!!)", //STR_TECHALARM_CO2_REPLACE
		u8R"(AG sampling line clogged!!)", //STR_TECHALARM_AG_SAMPLING_CLOGGED
		u8R"(CO2 sampling line clogged!!)", //STR_TECHALARM_CO2_SAMPLING_CLOGGED
		u8R"(O2 flow rate cannot be lower than 0.2 L/min)", //STR_FLOWMETER_O2_FLOW_TIPS
		u8R"(O2 concentration cannot be lower than 25%)", //STR_FLOWMETER_O2_CONCENTRATION_TIPS

    };
}

