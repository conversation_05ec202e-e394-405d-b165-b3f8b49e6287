﻿#include <QMutex>
#include <QMutexLocker>

#include "HistoryEventManager.h"
#include "HistoryDataDialog/TrendTable.h"
#include "RunModeManage.h"
#include "VentModeSettingManager.h"
#include "SettingSpinboxManager.h"
#include "ParamLabelManage.h"
#include "DataLimitManager.h"
#include "DataManager.h"
#include "VentModeSettingManager.h"
#include "HistoryEventDbInterface.h"
#include "calibration/CalModuleManage.h"
#include "String/UIStrings.h"
#include "SystemConfigManager.h"
#include "PowerOnSelfTest/SystemSelftestModel.h"
#include "ScaleStrategy.h"
#include "base.h"

static QMutex sMutex;

HistoryEventManager::HistoryEventManager()
{
    mHistoryEventStList = HistoryEventDbInterface::GetInstance()->GetHistoryEvents();
    InitConnect();

    for (int i = PRESSURE_CATEGORY; i < NONE_CATEGORY; ++i)
        SlotOnUnitChanged((CONVERTIBLE_UNIT_CATEGORY)i, U_MAX, UnitManager::GetInstance()->GetCategoryCurUnit((CONVERTIBLE_UNIT_CATEGORY)i));

    RegisterOperateLogConvertFunc(LOG_UPGRADE, [](const HistoryEventSt& paramSt){

        QString version1 = QString("%1").arg(paramSt.mParameter1, 2, 10, QChar('0'));
        QString version2 = QString("%1").arg(paramSt.mParameter2, 2, 10, QChar('0'));
        QString version3 = QString("%1").arg(paramSt.mParameter3, 2, 10, QChar('0'));
        return UIStrings::GetStr(ALL_STRINGS_ENUM::STR_UPGRADE) + " V" + version1 + "." + version2 + "." + version3 + " " + UIStrings::GetStr(ALL_STRINGS_ENUM::STR_MAINMENU_VER);
    });
    RegisterOperateLogConvertFunc(LOG_RESET_FACTORY_SETTINGS, [](const HistoryEventSt& paramSt){
        return UIStrings::GetStr(ALL_STRINGS_ENUM::STR_RESTORE_FACTORY_SETTING);
    });
    RegisterOperateLogConvertFunc(LOG_UPDATE_DATE_ID, [](const HistoryEventSt& paramSt){
        QString preDateStr=QString::number(paramSt.mParameter1/100)+"-"+QString("%1").arg(paramSt.mParameter1%100, 2, 10, QChar('0'))+"-"+QString("%1").arg(paramSt.mParameter2, 2, 10, QChar('0'));
        QString nowDateStr=QString::number(paramSt.mParameter3/100)+"-"+QString("%1").arg(paramSt.mParameter3%100, 2, 10, QChar('0'))+"-"+QString("%1").arg(paramSt.mParameter4, 2, 10, QChar('0'));
        QString resultStr=UIStrings::GetStr(ALL_STRINGS_ENUM::STR_CHANGE_SYSTEM_DATE);
        resultStr += ": " + preDateStr + " —> " + nowDateStr;
        return resultStr;
    });
    RegisterOperateLogConvertFunc(LOG_UPDATE_TIME_ID, [](const HistoryEventSt& paramSt){
        QString preTimeStr=QString("%1").arg(paramSt.mParameter1/3600, 2, 10, QChar('0'))+":"+QString("%1").arg((paramSt.mParameter1/60)%60, 2, 10, QChar('0'))+":"+QString("%1").arg(paramSt.mParameter1%60, 2, 10, QChar('0'));
        QString nowTimeStr=QString("%1").arg(paramSt.mParameter2/3600, 2, 10, QChar('0'))+":"+QString("%1").arg((paramSt.mParameter2/60)%60, 2, 10, QChar('0'))+":"+QString("%1").arg(paramSt.mParameter2%60, 2, 10, QChar('0'));
        QString resultStr=UIStrings::GetStr(ALL_STRINGS_ENUM::STR_CHANGE_SYSTEM_TIME);
        resultStr += ": " + preTimeStr + " —> " + nowTimeStr;
        return resultStr;
    });

    RegisterOperateLogConvertFunc(LOG_SWITCH_PAW_UNIT, [](const HistoryEventSt&paramSt){
        QString logString = UIStrings::GetStr(STR_SWITCH_PAW_UNIT);
        logString += ": "
                + UIStrings::GetStr(UnitManager::GetStrId((UNIT_TYPE)paramSt.mParameter1))
                + "->"
                + UIStrings::GetStr(UnitManager::GetStrId((UNIT_TYPE)paramSt.mParameter2));
        return logString;
    });
    RegisterOperateLogConvertFunc(LOG_SWITCH_CO2_UNIT, [](const HistoryEventSt&paramSt){
        QString logString = UIStrings::GetStr(STR_SWITCH_CO2_UNIT);
        logString += ": "
                + UIStrings::GetStr(UnitManager::GetStrId((UNIT_TYPE)paramSt.mParameter1))
                + "->"
                + UIStrings::GetStr(UnitManager::GetStrId((UNIT_TYPE)paramSt.mParameter2));
        return logString;
    });

}

void HistoryEventManager::InitConnect()
{
    connect(UnitManager::GetInstance(), &UnitManager::SignalCategoryUnitChanged, this, &HistoryEventManager::SlotOnUnitChanged);
    connect(this, &HistoryEventManager::SignalEventAdded, this, [&](const HistoryEventSt &logSt){
        HistoryEventDbInterface::GetInstance()->AddHistoryEvent(logSt);
    }, Qt::QueuedConnection);
}

void HistoryEventManager::DataRecordLimit()
{

}

void HistoryEventManager::AddHistoryEvent(HISTORY_EVENT_TYPE eventType, int eventId, int param1, int param2, int param3, int param4, int param5)
{
    auto curTimeStamp = QDateTime::currentSecsSinceEpoch();
    HistoryEventSt logSt{};
    logSt.mTimestamp = curTimeStamp;
    logSt.mEventType = eventType;
    logSt.mEventId = eventId;
    logSt.mParameter1 = param1;
    logSt.mParameter2 = param2;
    logSt.mParameter3 = param3;
    logSt.mParameter4 = param4;
    logSt.mParameter5 = param5;

    {
        QMutexLocker locker(&sMutex);
        mHistoryEventStList.push_front(logSt);
    }

    emit SignalEventAdded(logSt);
}

const QVector<HistoryEventSt>& HistoryEventManager::GetAllHistoryEvent()
{
    QMutexLocker locker(&sMutex);
    return mHistoryEventStList;
}


void HistoryEventManager::ClearAllData()
{
    QApplication::processEvents();
    {
        QMutexLocker locker(&sMutex);
        mHistoryEventStList.clear();
    }
    HistoryEventDbInterface::GetInstance()->ClearAllData();
}

QString HistoryEventManager::TranslateOperateLogToStr(const HistoryEventSt& paramSt)
{

    return mOperateLogConvertFuncMap[(OPERATE_EVENT_TYPE)paramSt.mEventId](paramSt);
}


QVector<HistoryEventSt> HistoryEventManager::GetLogByType(OPERATE_EVENT_TYPE id)
{
    QVector<HistoryEventSt> re;
    for(int i=0;i<mHistoryEventStList.length();i++)
    {
        if(mHistoryEventStList[i].mEventId==id&&mHistoryEventStList[i].mEventType==HISTORY_EVENT_TYPE::OPERATE_EVENT)
            re.push_back(mHistoryEventStList[i]);
    }
    return re;
}

void HistoryEventManager::RegisterOperateLogConvertFunc(OPERATE_EVENT_TYPE id, std::function<QString (HistoryEventSt)> func)
{
   mOperateLogConvertFuncMap[id] = func;
}


void HistoryEventManager::SlotOnUnitChanged(CONVERTIBLE_UNIT_CATEGORY category, UNIT_TYPE oldType, UNIT_TYPE newType)
{

}

