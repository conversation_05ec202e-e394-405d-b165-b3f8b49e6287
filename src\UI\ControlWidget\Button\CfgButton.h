﻿#ifndef CFGBUTTON_H
#define CFGBUTTON_H

#include <QLabel>


#include "IntraData.h"
#include "PopupAdapter.h"
/* !
*---具备三种状态的按钮，在各状态具有不同外观
  */


class TristateBtn : public QLabel
{
    Q_OBJECT
public:
    explicit TristateBtn(QWidget *parent = NULL);

    void setEnableEdit(bool newEnableEdit);

signals:
    void SignalEndEdit();
    void SignalBeginEdit();
protected:
    enum States
    {
        e_Normal,
        e_Focus,
        e_Edit,
    };

    virtual void NormalStyle();
    virtual void FocusStyle();
    virtual void EditStyle();
    virtual void DisableStyle();
    void focusInEvent(QFocusEvent *);
    //失去焦点则离开编辑状态
    void focusOutEvent(QFocusEvent *ev);
    //点击进入编辑状态或退出编辑状态
    void mouseReleaseEvent(QMouseEvent *e);
    //在编辑状态回车设置完成
    void keyPressEvent(QKeyEvent * ev);
    //在编辑状态回车设置完成
    void changeEvent(QEvent *event);
    inline States state()const
    {
        return mState;
    }

private:
    void setNormalState();
    void setFocusState();
    void setEditState();

private:
    bool mEnableEdit = true;
    States mState = e_Normal;
    States m_preState = e_Normal;
};

/* ***************************************  */

class CfgButton : public TristateBtn
{
    Q_OBJECT
public:
    explicit CfgButton(QWidget *parent =NULL, bool IsCloseRefreshPopup = true);

    virtual ~CfgButton()
    {
        delete mPopup;
        delete mInnerData;
    }

    QStringList GetAllValueStr();
    virtual void setText(QString Text);
    virtual void refreshText();
    virtual void InitUiText();
    void setIsCloseRefershPopup(bool mySet);
    void SetPopupType(int type);
    void setValue(int val);
    void setDumbValue(int val);
    virtual void SetRange(int minValue, int maxValue, bool isDumb = false);
    void setStep(int step);
    void setITimeTipStr(const QString& str);

    int step() const;
    int value()const;
    int minValue()const;
    int maxValue()const;
    void setDataModel(IntraData *model);

    void SetErrorValueTipStr(IntraData::VALUE_CHECK_TYPE,QString str);
    void SetPopupInputRegex(QString s);

signals:
    void SignalValueChanged(int);
    void SignalValueChangedTemporary(int);
    void clicked();
    void SignalRangeChanged();

public slots:
    void SlotPopupValueChanged(QString value);
    void SlotPopupNeedTextData();
    void SlotSetValue(int);

protected:
    virtual bool DataValidator(QString value, QString& error);

    QString GetErrorValueTipStr(int checkFailType);

    void focusInEvent(QFocusEvent * ev);

    //失去焦点则离开编辑状态
    void focusOutEvent(QFocusEvent *ev);

    //设置正常、焦点、编辑三种状态的外观
    void paintEvent(QPaintEvent * ev);

    //点击进入编辑状态或退出编辑状态
    void mouseReleaseEvent(QMouseEvent *e);

    //在编辑状态回车设置完成
    void keyPressEvent(QKeyEvent * ev);
    void hideEvent(QHideEvent* ev);
    void showEvent(QShowEvent *);

    virtual QString valueToStr(int value);
    virtual int strToValue(QString str);

    virtual void drawLeftArrow(QPainter *painter)const;
    virtual void drawRightArrow(QPainter *painter)const;
    virtual void drawTriangle(QPainter *painter)const;
    virtual void drawRect(QPainter *painter)const;
    virtual void InitConnectDateModel();
    virtual void InitPopupAdapter();

protected:
    PopupAdapter* mPopup{};
    IntraData *mInnerData{};
    QMap<int,QString> mErrorValueTipStr;
private:
    bool mIsCloseAutoRefrenshText = true;

};

#endif // CFGBUTTON_H

