﻿#include <QStyleOption>
#include <QPainter>

#include "LeakTestPage.h"
#include "String/UIStrings.h"
#include "UiApi.h"
#include "LeakTestManage.h"
#include "UiConfig.h"
#include "PushButton.h"
#include "RunModeManage.h"
#include "ModuleTestManager.h"
#define TIP_IMAGE_PATH ":/Calibration/LeakTest.png"
#include "protocol.h"
#define TIP_IMAGE_WIDTH 210
#define TIP_IMAGE_HEIGHT 300
#define RESULT_WIDGET_HEIGHT 150
LeakTestPage::LeakTestPage(QWidget *parent) :
    FocusWidget(parent)
{
    mCalStepTextId = STR_LABEL_BST_INIT;
    mCalBtnTextId = STR_START;
    mTestResultTextId = STR_PASS;
    mCalStateTipTextId = STR_TECHALARM_MINOTORING_MODULE_ERR;

    InitUi();
    InitUiText();
    LeakTestManage::GetInstance()->register_test_ctrl(this);

    connect(mCalButton, &PushButton::clicked, this, &LeakTestPage::SlotOnTestBtn);
    connect(LeakTestManage::GetInstance(), &LeakTestManage::SignalStateChanged, this, [this](LeakTestManage::LEAK_TEST_STATE state){
        if (state == LeakTestManage::LEAK_TEST_STATE::STATE_NORMAL)
            RefurbishCalResult();
    });
    connect(RunModeManage::GetInstance(), &RunModeManage::SignalModeChanged, this, [this](E_RUNMODE preMode, E_RUNMODE curMode){
        if(RunModeManage::GetInstance()->IsModeActive(MODE_RUN_MANUAL) || RunModeManage::GetInstance()->IsModeActive(MODE_RUN_ACGO))
        {
            mCalStateTipTextId = STR_MENUAL_ACGO_LEAK_STATE_TIP;
            mCalModuleStateTip->setText(UIStrings::GetStr(mCalStateTipTextId));
            mCalModuleStateTip->show();
            mCalButton->setEnabled(false);
            
            if(LeakTestManage::GetInstance()->GetTestStatus() == LeakTestManage::STATE_IN_TEST)
            {
                LeakTestManage::GetInstance()->EndLeakTest(false);
            }
        }
        else
        {
            if(ModuleTestManager::GetInstance()->GetModuleState(TEST_MONITOR) == ABLE)
            {
                mCalModuleStateTip->hide();
                mCalButton->setEnabled(true);
                
            }
            else
            {
                mCalStateTipTextId = STR_TECHALARM_MINOTORING_MODULE_ERR;
                mCalModuleStateTip->setText(UIStrings::GetStr(mCalStateTipTextId));
            }
        }
    });
    connect(ModuleTestManager::GetInstance(), &ModuleTestManager::SignalModuleStateChanged, this, [this](unsigned short moduleId, unsigned short state){
        if (moduleId == TEST_MONITOR)
        {
            if (state != ABLE)
            {
                mCalStateTipTextId = STR_TECHALARM_MINOTORING_MODULE_ERR;
                mCalModuleStateTip->setText(UIStrings::GetStr(mCalStateTipTextId));
                mCalModuleStateTip->show();
                mCalButton->setEnabled(false);
                
                if(LeakTestManage::GetInstance()->GetTestStatus() == LeakTestManage::STATE_IN_TEST)
                {
                    LeakTestManage::GetInstance()->ReceiveTestPack(SELF_CHECK_CMD::SELFTEST_FAIL);
                }
            }
            else
            {
                if(!RunModeManage::GetInstance()->IsModeActive(MODE_RUN_MANUAL) && !RunModeManage::GetInstance()->IsModeActive(MODE_RUN_ACGO))
                {
                    mCalModuleStateTip->hide();
                    mCalButton->setEnabled(true);
                    
                }
                else
                {
                    mCalStateTipTextId = STR_MENUAL_ACGO_LEAK_STATE_TIP;
                    mCalModuleStateTip->setText(UIStrings::GetStr(mCalStateTipTextId));
                }
            }
        }
    });
}

void LeakTestPage::InitUi()
{
    mCalStepTipTitle = new QLabel(UIStrings::GetStr(STR_LABEL_BST_HEAD));
    mCalStepTipTitle->setFixedHeight(35);
    mCalStepTipTitle->setContentsMargins(5, 0, 0, 0);
    mCalStepTipTitle->setAlignment(Qt::AlignVCenter);
    mCalStepTipTitle->setObjectName("mCalStepTipTitle");
    StyleSet::SetTextFont(mCalStepTipTitle, mCalStepTipTitle->font().pointSize(), FONT_FAMILY_TAHOMA);

    mCalTipImage = new QLabel;
    mCalTipImage->setFixedWidth(TIP_IMAGE_WIDTH);
    mCalTipImage->setFixedHeight(TIP_IMAGE_HEIGHT);
    StyleSet::SetBackgroundColor(mCalTipImage,COLOR_WHITE_GRAY);
    mCalTipImage->setScaledContents(true);
    mCalTipImage->setPixmap(QPixmap(TIP_IMAGE_PATH));

    mCalStepTipText = new QTextEdit(UIStrings::GetStr(mCalStepTextId));
    mCalStepTipText->setFocusPolicy(Qt::NoFocus);
    mCalStepTipText->setFrameStyle(QFrame::Box);
    mCalStepTipText->setLineWidth(0);
    StyleSet::SetTextFont(mCalStepTipText,FONT_M);
    StyleSet::SetBackgroundColor(mCalStepTipText,COLOR_WHITE_GRAY);
    mCalStepTipText->setReadOnly(true);
    mCalStepTipText->setTextInteractionFlags(Qt::NoTextInteraction);

    mCalModuleStateTip = new QLabel;
    mCalModuleStateTip->setText(UIStrings::GetStr(STR_TECHALARM_MINOTORING_MODULE_ERR));
    mCalModuleStateTip->hide();

    mCalProgressBar = new CountdownProgressBar;
    mCalProgressBar->hide();
    mCalProgressBar->SetCountTime(LeakTestManage::GetInstance()->GetLimitTime());

    mCalButton = new PushButton(UIStrings::GetStr(STR_START));
    mCalButton->setCheckable(true);

    mTestResultTitle = new QLabel;
    mTestResultTitle->setObjectName("mTestResultTitle");
    StyleSet::SetTextFont(mTestResultTitle, FONT_M);

    mTestResult = new QLabel;

    mComplianceNameLabel = new QLabel;
    mComplianceValueLabel = new QLabel;
    mComplianceUnitLabe = new QLabel;

    StyleSet::SetTextFont(mComplianceNameLabel, FONT_M);
    StyleSet::SetTextFont(mComplianceValueLabel, FONT_M);
    StyleSet::SetTextFont(mComplianceUnitLabe, FONT_M);

    mPreCalTime = new DateTimeWidget(this, Qt::Horizontal);
    mPreCalTime->SetDateColor(COLOR_BLACK);
    mPreCalTime->SetTimeColor(COLOR_BLACK);

    auto resultTopLayout = new QHBoxLayout;
    resultTopLayout->setContentsMargins(0, 0, 0, 0);
    resultTopLayout->addWidget(mTestResultTitle);
    resultTopLayout->addWidget(mTestResult);
    resultTopLayout->addStretch();

    auto resultMidLayout = new QHBoxLayout;
    resultMidLayout->setContentsMargins(0, 0, 0, 0);
    resultMidLayout->addWidget(mComplianceNameLabel);
    resultMidLayout->addWidget(mComplianceValueLabel);
    resultMidLayout->addWidget(mComplianceUnitLabe);
    resultMidLayout->addStretch();

    auto resultLayout = new QVBoxLayout;
    resultLayout->setContentsMargins(13, 0, 0, 0);
    resultLayout->addLayout(resultTopLayout);
    resultLayout->addLayout(resultMidLayout);
    resultLayout->addWidget(mPreCalTime);


    mResultBlock = new QFrame;
    mResultBlock->setLayout(resultLayout);
    StyleSet::SetBackgroundColor(mTestResultTitle,COLOR_WHITE_GRAY);
    StyleSet::SetBackgroundColor(mTestResult,COLOR_WHITE_GRAY);
    StyleSet::SetBackgroundColor(mPreCalTime,COLOR_WHITE_GRAY);
    StyleSet::SetBackgroundColor(mResultBlock,COLOR_WHITE_GRAY);
    StyleSet::SetTextFont(mTestResult, FONT_M);


    auto centerLayout = new QHBoxLayout;
    centerLayout->addWidget(mCalStepTipText);
    centerLayout->addWidget(mCalTipImage);

    QHBoxLayout *buttonLayout = new QHBoxLayout;
    buttonLayout->addWidget(mCalModuleStateTip);
    buttonLayout->addStretch();
    buttonLayout->addWidget(mCalButton);
    buttonLayout->setAlignment(mCalButton, Qt::AlignRight);

    QVBoxLayout *bottomLayout = new QVBoxLayout;
    bottomLayout->addWidget(mResultBlock);
    bottomLayout->addStretch();
    bottomLayout->addWidget(mCalProgressBar);
    bottomLayout->addLayout(buttonLayout);
    bottomLayout->setAlignment(buttonLayout, Qt::AlignRight | Qt::AlignBottom);

    auto mainLayout = new QVBoxLayout;
    mainLayout->setContentsMargins(0, 0, 0, 0);
    mainLayout->setSpacing(0);
    mainLayout->addWidget(mCalStepTipTitle);
    mainLayout->addLayout(centerLayout);
    mainLayout->addSpacing(2);
    mainLayout->addLayout(bottomLayout);

    RefurbishCalResult();
    setLayout(mainLayout);
    StyleSet::SetBackgroundColor(this,COLOR_WHITE_GRAY);
}

//原始状态显示
void LeakTestPage::dispNormal()
{
    mCalProgressBar->SetBarFinish();
    mCalProgressBar->hide();
    mCalProgressBar->ResetTimer();

    mCalStepTextId = STR_LABEL_BST_INIT;
    SetStepText(UIStrings::GetStr(mCalStepTextId));

    mCalBtnTextId = STR_START;
    mCalButton->setText(UIStrings::GetStr(mCalBtnTextId));

    mResultBlock->show();
}

void LeakTestPage::dispCancel()
{
    dispNormal();
}

void LeakTestPage::dispTesting()
{
    mCalStepTextId = STR_LABEL_BST_TESTING;
    SetStepText(UIStrings::GetStr(mCalStepTextId));

    mCalBtnTextId = STR_CANCEL;
    mCalButton->setText(UIStrings::GetStr(STR_CANCEL));

    mResultBlock->hide();

    mCalProgressBar->ResetTimer();
    mCalProgressBar->show();
    mCalProgressBar->StartCount();
}

void LeakTestPage::dispDone(unsigned short qualified, short value)
{
    //根据状态判断检查是否合格
    if (qualified)
    {
        mTestResult->setText(UIStrings::GetStr(STR_QULIFIED));
    }
    else
    {
        mTestResult->setText(UIStrings::GetStr(STR_UNQULIFIED));
    }

    mComplianceValueLabel->setText(QString::number(value));
    mPreCalTime->SetDateTime(QDateTime::currentDateTime());

    dispNormal();
}


void LeakTestPage::SetStepText(QString text)
{
    mCalStepTipText->clear();
    QTextDocument *doc = mCalStepTipText->document();
    QTextCursor cursor(doc);

    QTextBlockFormat blockFormat;
    blockFormat.setAlignment(Qt::AlignTop);
    blockFormat.setTopMargin(0);
    blockFormat.setBottomMargin(3);
    blockFormat.setLeftMargin(10);
    blockFormat.setRightMargin(3);
    blockFormat.setLineHeight(3, QTextBlockFormat::LineDistanceHeight);

    QFont tempFont;
    tempFont.setFamily(FONT_FAMILY_TAHOMA);
    tempFont.setPixelSize(FONT_M);
    mCalStepTipText->setFont(tempFont);


    cursor.insertBlock(blockFormat);
    cursor.insertText(text);
    cursor.insertBlock();


    mCalStepTipText->setDocument(doc);
    mCalStepTipText->setTextCursor(cursor);
}

void LeakTestPage::InitUiText()
{
    mCalStepTipTitle->setText(UIStrings::GetStr(STR_LABEL_BST_HEAD));
    SetStepText(UIStrings::GetStr(mCalStepTextId));
    mTestResultTitle->setText(UIStrings::GetStr(ALL_STRINGS_ENUM::STR_RecentSTT) + ":");
    mTestResult->setText(UIStrings::GetStr(mTestResultTextId));
    mCalButton->setText(UIStrings::GetStr(mCalBtnTextId));
    mComplianceNameLabel->setText(UIStrings::GetStr(ALL_STRINGS_ENUM::STR_COMPLIANCE) + ":");
    mComplianceUnitLabe->setText(UIStrings::GetStr(STR_UNIT_MLCMH2O));
    mCalModuleStateTip->setText(UIStrings::GetStr(mCalStateTipTextId));
}

void LeakTestPage::SetCalResult(QDateTime calTime, CAL_RESULT result, int calValue)
{
    switch(result)
    {
    case CAL_RESULT::CAL_SUCCESS:
        mTestResultTextId = STR_SUCCESS;
        StyleSet::SetTextColor(mTestResult,COLOR_LIGHT_GREEN);
        break;
    case CAL_RESULT::CAL_FAIL:
        mTestResultTextId = STR_FAIL;
        StyleSet::SetTextColor(mTestResult,COLOR_RED);
        break;
    case CAL_RESULT::CAL_CANCEL:
        mTestResultTextId = STR_CANCEL;
        StyleSet::SetTextColor(mTestResult,COLOR_YELLOW);
        break;
    }

    mTestResult->setText(UIStrings::GetStr(mTestResultTextId));
    if (result != CAL_SUCCESS)
        mComplianceValueLabel->setText("---");
    else
        mComplianceValueLabel->setText(QString::number(calValue));

    mPreCalTime->SetDateTime(calTime);
}

void LeakTestPage::showEvent(QShowEvent *event)
{
    emit SignalChangeCalState(true);
    FocusWidget::showEvent(event);
}

void LeakTestPage::hideEvent(QHideEvent *event)
{
    auto TestStatus = LeakTestManage::GetInstance()->GetTestStatus();
    if(TestStatus == LeakTestManage::STATE_IN_TEST)
    {
        LeakTestManage::GetInstance()->EndLeakTest();
    }
    emit SignalChangeCalState(false);
    FocusWidget::hideEvent(event);
}

void LeakTestPage::RefurbishCalResult()
{
    auto calResult = CalModuleManage::GetInstance()->GetLatestCalResult(CAL_TYPE::MANUAL_TEST);
    if (calResult.first == -1)
    {
        mResultBlock->hide();
        return;
    }
    else
    {
        mResultBlock->show();
    }

    SetCalResult(QDateTime::fromSecsSinceEpoch(calResult.first), (CAL_RESULT)calResult.second.mCalResult, calResult.second.mCalValue);
}

void LeakTestPage::SlotOnTestBtn()
{
    switch (LeakTestManage::GetInstance()->GetTestStatus())
    {
    case LeakTestManage::STATE_NORMAL:
        LeakTestManage::GetInstance()->StartLeakTest();
        break;
    case LeakTestManage::STATE_IN_TEST:
        LeakTestManage::GetInstance()->EndLeakTest();
        break;
    default:
        break;
    }
}
