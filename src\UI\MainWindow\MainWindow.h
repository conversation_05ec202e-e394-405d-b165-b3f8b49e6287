﻿#ifndef BG_MAIN_WINDOW_H
#define BG_MAIN_WINDOW_H

#include "FocusWidget.h"
#include "SystemTimeManager.h"
#include "ChartManager.h"
#include "DemoModules.h"

class MainDisplayWidget;
class SettingsMenu;
class MonitorParamView;
class MessageBoard;
class ModeSetTabWidget;
class AlarmMenu;
class EngneerMenu;
class ManualTestMenu;
class StandbyPage;
class GeneralLeftView;
class HotKeyWidget;
class TipDialog;
class AgView;
class FlowmeterView;
class PoweroffConfirmDlg;

class MainWindow : public FocusWidget
{
    Q_OBJECT
public:
    enum MAINWINDOW_COMPONENT_ENUM
    {
        MESSAGE_BOARD,
        LEFT_VIEW,
        GRAPH,
        PARAMLIST,
        MODESET,
        AGVIEW,
        HOT_KEY_LIST,
        POPUP,
        TIPS_DIALOG
    };

    enum MAINWINDOW_DIALOG_ENUM
    {
        DLG_STANDBY_CONFIRM, //standby 确认界面
        DLG_STANDBY,         //standby 界面
        DLG_SYS_SETTING_MENU,       //主菜单界面
        DLG_ALARM_MENU,      //报警菜单界面
        DLG_ENGNEER_CAL,     //工程师定标菜单
        DLG_DEBUG_VIEW,     //调试页面
        DLG_HISTORY_DATA,
        DLG_SYSTEM_SELFTEST,
        DLG_FLOWMETER_CONTROL,
        DLG_MAX_NUM,
        DLG_NONE,
        DLG_ALL
    };
    Q_ENUM(MAINWINDOW_DIALOG_ENUM);

    static MainWindow *GetInstance();

    void show();
    Q_INVOKABLE void showDialog(unsigned short ctrl);
    Q_INVOKABLE void hideDialog(unsigned short ctrl);
    void SetHistoryDialogPage(int index);
    void handleKey(int key);
    void ShowTip(QWidget *parent, QString text);
    void ShowCalibrationMenu();
    void SetAlarmDialogPage(int index,int index2);

    MonitorParamView *GetMonitorParam() { return mMonitorParam; }

public Q_SLOTS:
    void SlotRefurbishAutoHideTimer();
    void InitUiText(int initDialogType = DLG_NONE);
    void SlotRefreshAutoExitTimer(bool CurIsCal);

protected:
    bool eventFilter(QObject *obj, QEvent *ev);
    void showEvent(QShowEvent* ev);

private:
    explicit MainWindow(QWidget *parent = NULL);
    void CleanUp();
    void InitUi();
    void InitConnect();

    void CloseAllFocus(QWidget* parent);
    void MakeDialog(int DialogType);

Q_SIGNALS:
    void SignalKeyPressed(unsigned keyType);
    void SignalIsOpenDialog(bool isOpen);


private:
    MessageBoard *mMessageBoard{};
    AgView *mAgView{};
    FlowmeterView *mFlowmeterWidget{};

    ModeSetTabWidget *mModeWidget{};
    MainDisplayWidget *mMainDisplayWidget{};
    MonitorParamView *mMonitorParam{};
    HotKeyWidget* mHotKeyWidget{};

    TipDialog *mTipDialog{};
    QVector<QWidget *> mAllDialog;
    QLabel* mFpsLabel{};
    QLabel* mAutoCloseDialogCountLabel{};

    PoweroffConfirmDlg* mPowerOffDlg{};

    QTimer mAutoExitEditTime;
    QTimer mAutoCloseCountTime;

    bool mIsBlockDialogShow = false;
};

Q_DECLARE_METATYPE(MainWindow::MAINWINDOW_DIALOG_ENUM);

#endif // BG_MAIN_WINDOW_H
