﻿#include <QStyle>
#include "ScrollBar.h"
#include "UiApi.h"



/***************************************************************/
/***************************************************************/
ScrollBar::ScrollBar(Qt::Orientation ori, QWidget *parent): QScrollBar(ori, parent)
{
    setStyleSheet(GetQssFileStr("ScrollBar.qss"));
    setFocusPolicy(Qt::StrongFocus);
    setProperty("state", "normal");
}

void ScrollBar::setEnable(bool enable)
{
    if(enable)
    {
        setProperty("state","normal");
        style()->polish(this);
        setEnabled(true);
    }
    else
    {
        setProperty("state","disable");
        style()->polish(this);
        setEnabled(false);
    }
}

void ScrollBar::keyPressEvent(QKeyEvent *event)
{
    auto key = dynamic_cast<QKeyEvent *>(event)->key();
    if(mIsEdit)
    {
        if (KEY_PRESS == key)
        {
            setProperty("state","focus");
            mIsEdit=false;
            style()->polish(this);
        }
        else if (KEY_CLOSEWISE == key)
        {
            triggerAction(QScrollBar::SliderSingleStepAdd);
        }
        else if (KEY_ANTI_CLOSEWISE == key )
        {
            triggerAction(QScrollBar::SliderSingleStepSub);
        }
        else
            event->ignore();
    }
    else if(KEY_PRESS == key)
    {
        setProperty("state","edit");
        style()->polish(this);
        mIsEdit=true;
    }
    else
        event->ignore();
}

void ScrollBar::focusInEvent(QFocusEvent *event)
{
    setProperty("state","focus");
    style()->polish(this);
    QScrollBar::focusInEvent(event);
}

void ScrollBar::focusOutEvent(QFocusEvent *event)
{
    setProperty("state","normal");
    mIsEdit=false;
    style()->polish(this);
    QScrollBar::focusOutEvent(event);
}
