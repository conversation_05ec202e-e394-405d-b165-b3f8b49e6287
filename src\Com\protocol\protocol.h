﻿#ifndef PROTOCOL_H
#define PROTOCOL_H
#include <qglobal.h>

#ifndef INVALID_VALUE
#define INVALID_VALUE  (std::numeric_limits<short>::min())//定义无效的数据
#define INVALID_VALUE_STR QString("---")
#endif

#ifdef __GNUC__
#define STRUCT_BYTE_RULE __attribute__((packed))
#endif


#ifdef MSVC
#define STRUCT_BYTE_RULE __declspec(align(1))
#endif

#define CHAR2_TO_INT16(X,Y) ((Y<<8)|X)
#define PACKAGE_MAX_LENGTH		20
#define DEFAULT_PACK_HEAD (0xff0F)
#define PACK_HEAD_START (0x0F)
#define PACK_HEAD_END (0xFF)
#define PACK_DAT_LEN        512
#define MAX_DATA_SIZE   250

/*Monotor_state_definde########################begin*/
#define STATE_MANUAL_BIT					0X0001		//手动/机控模式
#define STATE_ACGO_BIT						0X0002		//ACGO
//#define STATE_SPONT_OR_NOT_BIT				0X0004		//自主呼吸
#define STATE_PIPE_FALL_OFF_BIT				0X0004

#define STATE_GAS_LOOP_ERR_BIT			    0X0008
#define STATE_GAS_SOURCE_PRESS_LOW_BIT		0X0010
#define STATE_O2_SOURCE_PRESS_LOW_BIT		0X0020
#define STATE_BYPASS_NOT_INSTALL_BIT		0X0040

#define STATE_PRESS_SENSOR_ERR_BIT			0X0080
#define STATE_SENSOR_CAL_ZERO_FAILD_BIT		0X0100
#define STATE_SENSOR_CAL_DATA_EXCEPTION_BIT	0X0200
#define STATE_EXP_VALVE_INVALID_BIT			0X0400
#define STATE_EXP_VALVE_SEAL_OFF_BIT		0X0800

#define STATE_INSP_VALVE_ERR_BIT			0X1000
#define STATE_HEATING_MODULE_INVALID_BIT	0X2000
#define STATE_ADDO2_OVERTIME_BIT			0X4000
#define STATE_ADDO2_ERR_BIT				    0X8000
/* MonitorAlarm */
#define ALARM_PRESS_OVER_LIMIT_BIT			0X0001
#define ALARM_PRESS_LAST_HIGH_BIT			0X0002
#define ALARM_NEGATIVE_PRESS_BIT			0X0004
#define ALARM_APNEA_BIT					    0X0008

#define ALARM_APNEA_120S_BIT				0X0010
#define ALARM_O2_SENSOR_ERR_BIT			0x0020
#define ALARM_FLOW_SENSOR_ERR_BIT         0x0040
#define ALARM_FRESH_FLOW_ERR_BIT          0x0080

/*Monotor_state_definde########################end*/


/*Flowmeter_state_definde########################begin*/
#define O2_ALARM_STATUS(alarm_status)     (((alarm_status) >> 0) & 0x01)
#define N2O_ALARM_STATUS(alarm_status)    (((alarm_status) >> 1) & 0x01)
#define AIR_ALARM_STATUS(alarm_status)    (((alarm_status) >> 2) & 0x01)

#define O2_SENSOR_ALARM_STATUS(alarm_status)     (((alarm_status) >> 3) & 0x01)
#define N2O_SENSOR_ALARM_STATUS(alarm_status)    (((alarm_status) >> 4) & 0x01)
#define AIR_SENSOR_ALARM_STATUS(alarm_status)    (((alarm_status) >> 5) & 0x01)
#define O2_INEXAT_ALARM_STATUS(alarm_status)     (((alarm_status) >> 6) & 0x01)
#define BANLANCE_INEXAT_ALARM_STATUS(alarm_status)    (((alarm_status) >> 7) & 0x01)
#define FLOW_TO_LOW_ALARM_STATUS(alarm_status)    (((alarm_status) >> 8) & 0x01)
#define O2_PRESS_ALARM_STATUS(alarm_status)     (((alarm_status) >> 9) & 0x01)
#define N2O_PRESS_ALARM_STATUS(alarm_status)    (((alarm_status) >> 10) & 0x01)
#define AIR_PRESS_ALARM_STATUS(alarm_status)    (((alarm_status) >>11) & 0x01)
#define BACKUP_FLOW_CONTROL_OPEN(alarm_status)      (((alarm_status) >> 12) & 0x01)
#define BACKUP_FLOW_COMMUNICATION_STOP(alarm_status)      (((alarm_status) >> 13) & 0x01)
/*Flowmeter_state_definde########################end*/


/*AG_definde########################begin*/

#define AG_FRAME_DATANUMBER	21
/*AG_definde########################end*/
enum O2_CAL_CMD
{
    CAL_CMD_O2_NULL,
    //21%氧浓度校
    O2_CAL_21_START,
    O2_CAL_21_DONE,
    O2_CAL_21_FAIL,
    O2_CAL_21_CONFIRM,
    //100%氧浓度校
    O2_CAL_100_START,
    O2_CAL_100_DONE,
    O2_CAL_100_FAIL,
    O2_CAL_100_CONFIRM,
    O2_CAL_CANCEL,

    DEMO_CAL_RESPOND =999
};

enum SELF_CHECK_CMD
{
    SELFTEST_RESPOND=0,
    SELFTEST_START,              //自检开
    SELFTEST_FAIL,
    SELFTEST_DONE,
    SELFTEST_CANCEL,             //公用取消命令
};
enum FLOW_PRESS_CAL_CMD
{
    CAL_CMD_NULL,
    //低流速定标
    CAL_CMD_LOW_FLOW_START,         //低流速开始
    CAL_CMD_LOW_FLOW_START_ERROR,   //低流速试探失败
    CAL_CMD_LOW_FLOW_PRO,			//低流速定标过程中
    CAL_CMD_LOW_FLOW_FAIL,          //低流速定标失败
    CAL_CMD_LOW_FLOW_SUCCESS,       //低流速定标成功
    CAL_CMD_LOW_FLOW_CONFIRM,
    //高流速定标
    CAL_CMD_HIGH_FLOW_START,        //高流速定标开始
    CAL_CMD_HIGH_FLOW_START_ERROR,  //高流速试探失败
    CAL_CMD_HIGH_FLOW_PRO,			//9高流速定标过程中
    CAL_CMD_HIGH_FLOW_FAIL,         //高流速定标失败
    CAL_CMD_HIGH_FLOW_SUCCESS,      //高流速定标成功
    CAL_CMD_HIGH_FLOW_CONFIRM,      //流速定标确认命令

    //压力定标
    CAL_CMD_PRESS_START,            //压力定标开始
    CAL_CMD_PRESS_START_ERROR,      //压力定标试探失败
    CAL_CMD_PRESS_PRO,				//压力定标过程中
    CAL_CMD_PRESS_FAIL,             //压力定标失败
    CAL_CMD_PRESS_SUCCESS,          //压力定标成功
    CAL_CMD_PRESS_CONFIRM,          //压力定标确认命令
    //校准工具定标
    CAL_CMD_CORRECTION_TOOL_START,
    CAL_CMD_CORRECTION_TOOL_START_ERR,
    CAL_CMD_CORRECTION_TOOL_FAIL,
    CAL_CMD_CORRECTION_TOOL_PRO,
    CAL_CMD_CORRECTION_TOOL_SUCCESS,
    CAL_CMD_CORRECTION_TOOL_CONFIRM,

    CAL_CMD_CANCLE
};

enum MONITORING_MSG_TYPE
{
    MM_COMM_MSGID_BEGIN,
    MM_COMM_WAVE_MSGID,             //波形包
    MM_COMM_MONITOR_PARA_MSGID,     //监控参数包
    MM_COMM_ALARM_MSGID,            //报警包
    MM_COMM_SETTING_MSGID,          //控制参数设置包   上位机下发包
    MM_COMM_O2_CORRECT_MSGID,       //O2浓度校准包
    MM_COMM_FLOW_CORRETC_MSGID,     //吸入呼出端流速校准包
    MM_COMM_CAL_MSGID,              //流速、压力、校准工具定标包
    MM_COMM_IMPORT_CAL_DATA_MSGID,  //导入流速与压力定标数据
    MM_COMM_EXPORT_CAL_DATA_MSGID,  //导出流速与压力定标数据
    MM_COMM_SELFCHECK_MSGID,        //泄漏检测-自检通信协议
    MM_COMM_QUERY_VERSION_ID,       //版本信息
    MM_COMM_FLOWMETER_ID,           //流量计
    MM_COMM_HEAT_ID,                //加热
    MM_COMM_MSGID_END
};

enum FLOWMETER_MSG_TYPE
{
    FLOWMETER_PACK_BEGIN,
    FLOWMETER_MONIT_PACK,
    FLOWMETER_CAL_COMMAND_PACK,
    FLOWMETER_CONTROL_PACK,
    PROPORTIONAL_VALVE_CONTROL_PACK,
    PROPORTIONAL_VALVE_CAL_COMMAND_PACK,
    FLOWMETER_QUERY_VERSION_ID = 9,
    FLOWMETER_REPORT_MSG_SWITCH_ID,   //上报信息开关
    FLOWMETER_QUERY_GAS_TYPE_ID = 13,
    FLOWMETER_QUERY_SENSOR_TYPE_ID,
    FLOWMETER_PACK_END
};

enum FLOWMETER_CAL_TYPE
{
    FLOWMETER_NULL_ID,
    FLOWMETER_CAL_ZERO_ID,
    FLOWMETER_O2_CALIBRATION_ID,
    FLOWMETER_AIR_CALIBRATION_ID,
    FLOWMETER_N2O_CALIBRATION_ID,
    FLOWMETER_BACKUP_CALIBRATION_ID
};

enum FLOWMETER_CONTROL_CMD
{
    PV_CONTROL_INC_FLOW = 1,
    PV_CONTROL_DEC_FLOW
};

enum  FLOWMETER_CAL_CMD
{
    //主控板下
    FLOW_COMMAND_VALUE_BEGIN = 0,
    FLOW_METER_CAL_START,//1
    FLOW_METER_CAL_CANCEL,//2

    //流量计板上发
    FLOW_METER_CAL_DONE,//3
    FLOW_METER_CAL_FAIL,//4
    FLOW_METER_CAL_END, //5

    //定标命令
    FM_CAL_BEGIN,  		//往下发
    FM_CAL_NEXT,			//往下发
    FM_CAL_OK,			//往下发
    FM_CAL_CANCEL,		//往下发

    FM_CAL_SINGLE_DATA_FAIL,
    FM_CAL_SINGLE_DATA_DONE,
    FM_CAL_ALL_DATA_END,
    FM_CAL_NULL
};

enum AG_ZERO_CAL_RESULT
{
    AG_ZERO_STATUS_NORMAL,
    AG_ZERO_STATUS_DISABLE,
    AG_ZERO_STATUS_IN_PROGRESS,
    AG_ZERO_STATUS_DONE,
    AG_ZERO_STATUS_FAIL,
};

enum AG_TYPE		//should be same order wz AllDataID
{
    ANESGAS_NULL = 0,
    ANESGAS_HAL,
    ANESGAS_ENF,
    ANESGAS_ISO,
    ANESGAS_SEV,
    ANESGAS_DES
};

enum POWER_CMD
{
    POWER_INFO =1,
    POWER_QUERY_VERSION_ID
};


struct MonitorWatchParam
{
    short unsigned Vte;
    short unsigned Vti;
    short unsigned MV;
    short unsigned MVSpont;
    short unsigned Rate;
    short unsigned RateSpont;
    short unsigned IE;
    short unsigned Cdyn;
    short unsigned C20C;
    short unsigned R;
    short unsigned Ppeak;
    short unsigned Pplat;
    short unsigned Peep;
    short unsigned Pmean;
};

struct MonitorAlarmStr
{
    unsigned short MonitorState;
    unsigned short MonitorAlarm;
    unsigned short FiO2;
};

struct STRUCT_BYTE_RULE LeakTestResult
{
    qint16 mCmd;        //表示当前命令
    qint16 c_data;                   //顺应
    qint16 r_data;
};

#ifdef MSVC
#pragma pack(push, 1)
#endif
 struct STRUCT_BYTE_RULE   PackageHeard
{
    void Reset()
    {
        mHeard= 0;
        mPackId=0;
        mPackDir=0;
        mMsgType=0;
        mMsgLength=0;
        mMsgLengthCheck=0;
    }
    bool isValid()
    {
        return (mHeard==DEFAULT_PACK_HEAD)&&(mMsgLength!=0);
    }
    unsigned short mHeard= 0;
    unsigned char  mPackId=0;
    unsigned char  mPackDir=0;
    unsigned char  mMsgType=0;
    unsigned char  mMsgLength=0;
    unsigned char  mMsgLengthCheck=0;
};
#ifdef MSVC
#pragma pack(pop)
#endif

struct MonitorWaveDataStr
{
    short Press;
    short Flow;
    unsigned short Vol;
    unsigned short spontFlag;
};



struct FlowmeterValuePack
{
    unsigned short flow_o2;
    unsigned short Back_up_flow_o2;
    unsigned short flow_n2o;
    unsigned short flow_air;
    unsigned short alarm_status;
};

struct FlowmeterCalPack
{
    unsigned char FlowCommandID;  //ID
    unsigned char FlowCommandValue;//Value
    unsigned short FlowValue;      //流速;
};
struct STRUCT_BYTE_RULE FloweControlPack
{
    short int controlO2;
    char controlAssistType;
    short int controlAssist;
};

struct FlowemeterSimpleControlPack
{
    unsigned char gasType;
    unsigned char controlType;
};


struct STRUCT_BYTE_RULE FlowPressCmdAndData
{
    short cur_cal_cmd;     //表示当前命令.
    short cur_vtplus_cmd;  //表示当前vtplus命令
    short cur_cal_index;   //表示当前定标进度
    short cur_da_flow_press; //表示当前定标数据，上发DA下发Flow、press
    short cur_ins_ad;     //当前吸入端流速或者压力AD
    short cur_exp_ad;     //当前呼出端流速或者压力AD
};

struct StPowerInof
{
    short mPower1PCT;
    short mPower2PCT;
    short mPower1Statu = -1;
    short mPower2Statu =-1;
    short mAcStatu = -1;
};


/*********************MiniVt*********************/
struct STRUCT_BYTE_RULE MinVtMessage
{
    unsigned int mDataSize = 0x03;
    unsigned char mPacketType = 0x21;
    unsigned char mMsgId;
    unsigned char mMsgLength = 0x00;
};//VT信息确定定标成功后修改

//守护进程通信所需共享内存数据
struct SharedMemorySt
{
    unsigned char mMachineStatus;       // 麻醉机状态标志  0=活跃 1=异常
    unsigned char mDaemonVersion;       // 守护进程版本号
    unsigned char mReserved[98];        // 预留字段，大小为 98 字节
};

//标识符
#define TARGET_MAINBOARD        0xC0
#define TARGET_MONITORBOARD     0xC1
#define TARGET_SOFT_CONFIG      0xC2
#define TARGET_EXHALATION_VALUE 0xC3
#define TARGET_PROPORTION_VALUE 0xC4
#define TARGET_DWIN             0xC5
#define TARGET_PRODUCT_MODEL    0xC6
#define TARGET_LOGO             0xC7
#define TARGET_NEW_E_SERIES     0xC8
#define TARGET_FLOWMETER        0xC9

//在线更新配置包头
#define HEAD_PACK               0xA0
#define CMD_PACK                0xA1
#define DATA_PACK               0xA2
#define VERSION_DATA_PACK       0xA3

//在线更新配置指令
#define CMD_READY               0x5A
#define CMD_REQUEST_DATA        0x5B
#define CMD_SEND_DATA_END       0x5C
#define CMD_RESEND_FILE         0x5D
#define CMD_RECIVE_FINISH       0x5E
#define CMD_RESEND_PART         0x5F
#define CMD_SEND_ALLFILE_END    0x6A
#define CMD_UPDATE_FINISH       0x6B

//Qt5升级文件名
#define MAIN_SOFT_NAME      "MasterGui"
#define MONITOR_SOFT_NAME   "GasControl.bin"
#define KERNEL_NAME         "zImage"
#define UBOOT_NAME          "u-boot.imx"
#define DEVICE_TREE_NAME    "imx6dl-sabresd.dtb"
#define FLOWMETER_NAME      "Flowmeter.hex"
#define POWER_BOARD_NAME    "powerBoard.hex"
#define CUSTOM_STEP_NAME    "CustomStep"

struct UpdateData
{
    int mSize;
    char mData[PACK_DAT_LEN];
};

//在线更新请求包
struct UpdateHeadPack
{
    unsigned char mPackType;
    unsigned char mProtName[16];
    unsigned char mTargetType;
    unsigned int mKeyCode;
};

//新E系升级请求包
typedef struct
{
    unsigned char mPackType;
    unsigned char mProtName[16];
    unsigned char mTargetType;
    unsigned char mFileEnum;
    unsigned short mFileCrc;
    unsigned int mFileSize;
    unsigned char mFileVersion[10];
}UpdateNewESeriesHeadPack;

enum E_UPDATE_FILE_TYPE
{
    E_UPDATE_CONFIG = 0,
    E_UPDATE_PRODUCT_MODEL = 1,

    E_UPDATE_MAIN_SOFT = 0xD1,
    E_UPDATE_MONITOR_SOFT = 0xD2,
    E_UPDATE_KERNEL = 0xD3,
    E_UPDATE_UBOOT = 0xD4,
    E_UPDATE_DEVICE_TREE = 0xD5,
    E_UPDATE_FLOWMETER = 0xD6,
    E_UPDATE_POWER_BOARD = 0xD7,
    E_UPDATE_CUSTOM_STEP = 0xD8,
    E_UPDATE_LOGO = 0xE1,
    E_UPDATE_LOGO_BIN = 0xE2,
    E_UPDATE_MAX
};

//在线更新操作指令包
struct UpdateCmdPack
{
    unsigned char mPackType;
    unsigned char mCmd;
    unsigned char mTargrtType;
};

//在线更新数据包(带校验和)
struct UpdateDateCrcPack
{
    unsigned char mPackType;
    unsigned short mDataLen;
    unsigned char mData[PACK_DAT_LEN];
    unsigned short mCrc16Value;
};

//更新产品型号包
struct UpdateProductModelPack
{
    unsigned char mPackType;
    unsigned short mDataLen;
    unsigned char mData[16];
    unsigned short mCrc16Value;
};

//新E系在线升级版本信息包
typedef struct
{
    unsigned char mPackType;
    unsigned char mVersionInfo[205];
    unsigned short mCrc16Value;
}VersionDataPack;

typedef struct
{
    unsigned char datatype;
    unsigned short datalen;
    unsigned char data[PACK_DAT_LEN];
}PROT_DATA;

typedef struct
{
    unsigned char datatype;
    unsigned char cmd;
    unsigned char target_type;
}PROT_CMD;

typedef  struct __attribute__((packed))  monitor_update_pack
{
    unsigned char ID0;
    unsigned char ID1;
    unsigned char datasize;     // 包含 cmd之后字节数
    unsigned char cmd;
    unsigned int addr;
    unsigned char data[MAX_DATA_SIZE + 1];
}MONITOR_UPDATE_PACK;

typedef struct
{
    unsigned char MainSoftVersion[15];
    unsigned char MonitorSoftVersion[15];
    unsigned char KernelVersion[15];
    unsigned char UbootVersion[15];
    unsigned char FileSystemVersion[15];
    unsigned char FlowMaterVersion[15];
    unsigned char PowerVersion[15];
    unsigned char DBVersion[15];
    unsigned char DaemonVersion[15];
}VersionInfo;

typedef struct
{
    unsigned char MainSoftVersion[10];
    unsigned char MonitorSoftVersion[10];
    unsigned char KernelVersion[10];
    unsigned char UbootVersion[10];
    unsigned char DeviceTree[10];
    unsigned char FlowMaterVersion[10];
    unsigned char PowerVersion[10];
    unsigned char CustomUpdateVersion[10];
}UpdateVersionInfo;

struct ConfigMessage
{
    unsigned short NetworkConfigBool;
    unsigned short NetMonitorBool;

    unsigned short MachineRuleStandard;
    unsigned short MachineMode;

    //模块配置
    unsigned short AgBool;
    unsigned short Co2Bool;
    unsigned short O2Bool;
    unsigned short Spo2Bool;
    unsigned short LoopBool;
    unsigned short TouchScreenBool;
    unsigned short TouchScreenType;
    unsigned short WaveNum;

    //气源配置
    unsigned short SourceO2Bool;
    unsigned short SourceN2oBool;
    unsigned short SourceAirBool;

    //通气模式
    unsigned short VcvBool;
    unsigned short PcvBool;
    unsigned short PsvBool;
    unsigned short SimvvcBool;
    unsigned short SimvpcBool;
    unsigned short PrvcBool;
    unsigned short PmodeBool;
    unsigned short HlmBool;

    //最小潮气量
    unsigned short MinVt;

    //出厂日期
    unsigned short ManufactureYear;
    unsigned short ManufactureMonth;
    unsigned short ManufactureDay;      //决定序列号的位数

    //序列号
    unsigned short SerialMoudle;
    unsigned short SerialYear;
    unsigned short SerialMonth;
    unsigned short SerialNumOne;
    unsigned short SerialNumTwo;
    unsigned short SerialNumThree;
    unsigned short SerialNumFour;

    unsigned short EnableLogo;

    //语言支持
    unsigned short English;
    unsigned short Chiness;
    unsigned short Russian;

    unsigned short Spo2Module;
    unsigned short VersionMaster;

    unsigned short VsBool;

    unsigned char  DwinVersion[10];

    unsigned char  AnimalVersion[10];

    unsigned short PowerType;

    unsigned short FlowmeterBool;

    unsigned short AgModuleType;

    unsigned char ProductModel[16];
};

#endif // PROTOCOL_H
/*********************MiniVt*********************/
