﻿#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QHBoxLayout>
#include <QMap>
#include "UiApi.h"
#include "DebugLogManager.h"
#include "ParamLabelBase.h"

ParamLabelBase::ParamLabelBase(QWidget *parent) : QWidget(parent)
{
    //    setStyle(new BorderProxyStyle);
    InitUi();
}

void ParamLabelBase::InitUi()
{
    mValueLabelColor = COLOR_WHITE;

    mNameLabel = new LabelPro("Name", this);
    mNameLabel->setObjectName("mNameLabel");

    //创建单位标签，默认显示的单位是unit
    mUnitLabel = new LabelPro("unit", this);
    mUnitLabel->setObjectName("mUnitLabel");

    //创建当前值标签，默认显示的值是---
    mValueLabel = new LabelPro("---", this);
    mValueLabel->setObjectName("mValueLabel");

    //创建最大值标签
    mHighLimitLabel = new LabelPro(this);
    mHighLimitLabel->setObjectName("mHighLimitLabel");
    //创建最小值标签
    mLowLimitLabel = new LabelPro(this);
    mLowLimitLabel->setObjectName("mLowLimitLabel");
}


void ParamLabelBase::SetRange(QString min, QString max)
{
    if (mLowLimitLabel == NULL || mHighLimitLabel ==NULL)
        return;

    if (min == "OFF")
        ShowLimitOffIcon(*mLowLimitLabel);
    else
        mLowLimitLabel->setText(min);

    if (max == "OFF")
        ShowLimitOffIcon(*mHighLimitLabel);
    else
        mHighLimitLabel->setText(max);

    return ;
}

void ParamLabelBase::SetName(QString name)
{
    if (mNameLabel !=NULL)
    {
        mNameLabel->setText(name);
    }
}

void ParamLabelBase::SetValue(QString value)
{
    DebugAssert(mValueLabel !=NULL);
    mValueLabel->setText(value);
}

void ParamLabelBase::SetUnit(QString unit)
{
    DebugAssert(mUnitLabel !=NULL);
    mUnitLabel->setText(unit);
}

void ParamLabelBase::SetLabelMinimumWidthByValueDigit(int digit)
{
    mValueLabel->setMinimumWidth(CalculateLabelTextSize(*mValueLabel, {digit, 'X'}).width());
}

void ParamLabelBase::SetLimitVisible()
{
    mHighLimitLabel->hide();
    mLowLimitLabel->hide();
}

QSize ParamLabelBase::CalculateLabelTextSize(const QLabel &labelRef, QString text)
{
    return QFontMetrics{labelRef.font()}.boundingRect(text).size();
}

void PaintBorder(QPainter* painter, QRect rect)
{
    painter->setRenderHint(QPainter::Antialiasing);
    QPen pen;
    pen.setWidth(2);
    pen.setColor(COLOR_RED);
    painter->setPen(pen);
    painter->drawRect(rect);
}

void ParamLabelBase::ShowLimitOffIcon(QLabel& limitLabelRef)
{
    limitLabelRef.setPixmap(QPixmap(":/limitOff.png").scaled(QSize(32,32)));
}

void ParamLabelBase::mousePressEvent(QMouseEvent *)
{
    emit SignalClicked();
}

void ParamLabelBase::SetNameColor(const QColor& color)
{
    if (mNameLabel !=NULL)
        mNameLabel->SetTextFontColor(color);
}

void ParamLabelBase::SetUnitColor(const QColor& color)
{
    if (mUnitLabel !=NULL)
        mUnitLabel->SetTextFontColor(color);
}

void ParamLabelBase::SetValueColor(const QColor& color)
{
    mValueLabelColor = color;
    if (mValueLabel !=NULL)
        mValueLabel->SetTextFontColor(color);
}

void ParamLabelBase::SetScaleColor(const QColor& color)
{
    if (mLowLimitLabel != NULL)
    {
        mLowLimitLabel->SetTextFontColor(color);
    }
    if (mHighLimitLabel != NULL)
    {
        mHighLimitLabel->SetTextFontColor(color);
    }
}

void ParamLabelBase::SetLowLimitColor(const QColor &color)
{
    if (mLowLimitLabel != NULL)
        mLowLimitLabel->SetTextFontColor(color);
}

void ParamLabelBase::SetHighLimitColor(const QColor &color)
{
    if (mHighLimitLabel != NULL)
        mHighLimitLabel->SetTextFontColor(color);
}

void ParamLabelBase::SetLabelBgColor(const QColor &color)
{
    QPalette pa(this->palette());
    pa.setColor(QPalette::Window, color);
    this->setAutoFillBackground(true);
    this->setPalette(pa);
}

void ParamLabelBase::SetValueBackgroundBlinkColor(const QColor& color)
{
    mValueLabelBlinkColor = color;
}

void ParamLabelBase::SetValueBackgroundColor(const QColor &color)
{
    QPalette pa(mValueLabel->palette());
    pa.setColor(QPalette::Window, color);
    mValueLabel->setPalette(pa);
}

void ParamLabelBase::SetIsOpenValueBackgroundBlink(bool isOpen)
{
    QPalette pa(mValueLabel->palette());
    if (isOpen)
    {
        pa.setColor(QPalette::Window, mValueLabelBlinkColor);
        if (mValueLabelBlinkColor == COLOR_RED)
            pa.setColor(QPalette::WindowText, COLOR_WHITE);
        else if (mValueLabelBlinkColor == COLOR_LIGHT_YELLOW)
            pa.setColor(QPalette::WindowText, COLOR_BLACK);
        else
            pa.setColor(QPalette::WindowText, mValueLabelColor);
    }
    else
    {
        pa.setColor(QPalette::Window, Qt::transparent);
        pa.setColor(QPalette::WindowText, mValueLabelColor);
    }

    mValueLabel->setAutoFillBackground(true);
    mValueLabel->setPalette(pa);
}



LabelPro::LabelPro(QWidget *parent, Qt::WindowFlags f) : QLabel(parent, f)
{
    SetTextFontFamily(FONT_FAMILY_NINA);
}

LabelPro::LabelPro(const QString &text, QWidget *parent, Qt::WindowFlags f) : QLabel(text, parent, f)
{
    SetTextFontFamily(FONT_FAMILY_NINA);
}

void LabelPro::SetLabelBgColor(const QColor &color)
{
    QPalette pa(this->palette());
    pa.setColor(QPalette::Background, color);
    setAutoFillBackground(true);
    setPalette(pa);
}

void LabelPro::SetTextFontColor(const QColor &color)
{
    QPalette pa(palette());
    pa.setColor(QPalette::WindowText, color);
    setPalette(pa);
}

void LabelPro::SetTextFontSize(const size_t fontSize)
{
    QFont tmpFont(font());
    tmpFont.setPixelSize(fontSize);
    setFont(tmpFont);
}

void LabelPro::SetTextFontBold(bool flag)
{
    QFont tmpFont(font());
    tmpFont.setBold(flag);
    setFont(tmpFont);
}

void LabelPro::SetTextFontFamily(QString family)
{
    QFont tmpFont(font());
    tmpFont.setFamily(family);
    setFont(tmpFont);
}
