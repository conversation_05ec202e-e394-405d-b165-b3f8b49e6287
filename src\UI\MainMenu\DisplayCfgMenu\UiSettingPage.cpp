﻿#include <QHBoxLayout>

#include "UiApi.h"
#include "UiSettingPage.h"
#include "ListItemGroup.h"
#include "SwitchButton.h"
#include "ChartManager.h"
#include "PushButton.h"
#include "String/UIStrings.h"
#include "TipDialog.h"

#include "MainWindow/MonitorParamView.h"
#include "MainWindow/MainWindow.h"
#include "UnitManager.h"
#include "SystemSettingManager.h"
#include "SystemConfigManager.h"
#include "ModuleTestManager.h"

#define SWITCH_BT_SIZE 120,120
#define BOOTOM_WIDGET_HEIGHT 50
QVector<QPointF> sPawWaveData
{
    QPointF(0,4), QPointF(0.0417362,6.2), QPointF(0.0834725,7.6), QPointF(0.125209,8.8), QPointF(0.166945,9.7), QPointF(0.208681,10.6), QPointF(0.250417,11.4), QPointF(0.292154,12.1), QPointF(0.33389,12.8), QPointF(0.375626,13.4), QPointF(0.417362,14), QPointF(0.459098,14.6), QPointF(0.500835,15.1), QPointF(0.542571,15.6), QPointF(0.584307,16.1), QPointF(0.626043,16.6), QPointF(0.66778,17.1), QPointF(0.709516,15.6), QPointF(0.751252,14.7), QPointF(0.792988,14.7), QPointF(0.834725,14.7), QPointF(0.876461,14.3), QPointF(0.918197,13.1), QPointF(0.959933,11.9), QPointF(1.00167,10.8), QPointF(1.04341,9.8), QPointF(1.08514,8.8), QPointF(1.12688,7.9), QPointF(1.16861,7.1), QPointF(1.21035,6.3), QPointF(1.25209,5.6), QPointF(1.29382,4.9), QPointF(1.33556,4.3), QPointF(1.3773,3.8), QPointF(1.41903,3.4), QPointF(1.46077,3), QPointF(1.5025,2.6), QPointF(1.54424,2.4), QPointF(1.58598,2.2), QPointF(1.62771,2), QPointF(1.66945,2), QPointF(1.71119,2), QPointF(1.75292,2), QPointF(1.79466,2), QPointF(1.83639,2), QPointF(1.87813,2), QPointF(1.91987,2), QPointF(1.9616,2), QPointF(2.00334,2), QPointF(2.04508,2), QPointF(2.08681,2), QPointF(2.12855,2), QPointF(2.17028,2), QPointF(2.21202,2), QPointF(2.25376,2), QPointF(2.29549,2), QPointF(2.33723,2), QPointF(2.37896,2), QPointF(2.4207,2), QPointF(2.46244,2), QPointF(2.50417,2), QPointF(2.54591,2), QPointF(2.58765,2), QPointF(2.62938,2), QPointF(2.67112,4.9), QPointF(2.71285,7.2), QPointF(2.75459,8.4), QPointF(2.79633,9.7), QPointF(2.83806,10.9), QPointF(2.8798,11.6), QPointF(2.92154,12.3), QPointF(2.96327,13), QPointF(3.00501,13.6), QPointF(3.04674,14.2), QPointF(3.08848,14.7), QPointF(3.13022,15.3), QPointF(3.17195,15.8), QPointF(3.21369,16.3), QPointF(3.25543,16.8), QPointF(3.29716,17.3), QPointF(3.3389,14.7), QPointF(3.38063,14.7), QPointF(3.42237,14.7), QPointF(3.46411,14.7), QPointF(3.50584,14.7), QPointF(3.54758,14.3), QPointF(3.58932,13.1), QPointF(3.63105,11.5), QPointF(3.67279,10.5), QPointF(3.71452,9.4), QPointF(3.75626,8.5), QPointF(3.798,7.6), QPointF(3.83973,6.8), QPointF(3.88147,6), QPointF(3.92321,5.4), QPointF(3.96494,4.7), QPointF(4.00668,4.2), QPointF(4.04841,3.7), QPointF(4.09015,3.2), QPointF(4.13189,2.9), QPointF(4.17362,2.5), QPointF(4.21536,2.3), QPointF(4.2571,2.1), QPointF(4.29883,2), QPointF(4.34057,2), QPointF(4.3823,2), QPointF(4.42404,2), QPointF(4.46578,2), QPointF(4.50751,2), QPointF(4.54925,2), QPointF(4.59098,2), QPointF(4.63272,2), QPointF(4.67446,2), QPointF(4.71619,2), QPointF(4.75793,2), QPointF(4.79967,2), QPointF(4.8414,2), QPointF(4.88314,2), QPointF(4.92487,2), QPointF(4.96661,2), QPointF(5.00835,2), QPointF(5.05008,2), QPointF(5.09182,2), QPointF(5.13356,2), QPointF(5.17529,2), QPointF(5.21703,2), QPointF(5.25876,2), QPointF(5.3005,2), QPointF(5.34224,2), QPointF(5.38397,2), QPointF(5.42571,5.6), QPointF(5.46745,7.2), QPointF(5.50918,8.4), QPointF(5.55092,9.4), QPointF(5.59265,10.3), QPointF(5.63439,11.1), QPointF(5.67613,11.9), QPointF(5.71786,12.5), QPointF(5.7596,13.2), QPointF(5.80134,13.8), QPointF(5.84307,14.4), QPointF(5.88481,14.9), QPointF(5.92654,15.4), QPointF(5.96828,16), QPointF(6.01002,16.4), QPointF(6.05175,16.9), QPointF(6.09349,16.4), QPointF(6.13523,14.7), QPointF(6.17696,14.7), QPointF(6.2187,14.7), QPointF(6.26043,14.7), QPointF(6.30217,14.7), QPointF(6.34391,13.9), QPointF(6.38564,12.7), QPointF(6.42738,11.5), QPointF(6.46912,10.1), QPointF(6.51085,8.8), QPointF(6.55259,7.6), QPointF(6.59432,6.8), QPointF(6.63606,6), QPointF(6.6778,5.4), QPointF(6.71953,4.7), QPointF(6.76127,4.2), QPointF(6.80301,3.7), QPointF(6.84474,3.2), QPointF(6.88648,2.9), QPointF(6.92821,2.5), QPointF(6.96995,2.3), QPointF(7.01169,2.1), QPointF(7.05342,2), QPointF(7.09516,2), QPointF(7.13689,2), QPointF(7.17863,2), QPointF(7.22037,2), QPointF(7.2621,2), QPointF(7.30384,2), QPointF(7.34558,2), QPointF(7.38731,2), QPointF(7.42905,2), QPointF(7.47078,2), QPointF(7.51252,2), QPointF(7.55426,2), QPointF(7.59599,2), QPointF(7.63773,2), QPointF(7.67947,2), QPointF(7.7212,2), QPointF(7.76294,2), QPointF(7.80467,2), QPointF(7.84641,2), QPointF(7.88815,2), QPointF(7.92988,2), QPointF(7.97162,2), QPointF(8.01336,2), QPointF(8.05509,2), QPointF(8.09683,2), QPointF(8.13856,2), QPointF(8.1803,5.6), QPointF(8.22204,7.2), QPointF(8.26377,8.4), QPointF(8.30551,9.7), QPointF(8.34725,10.9), QPointF(8.38898,11.6), QPointF(8.43072,12.3), QPointF(8.47245,13), QPointF(8.51419,13.6), QPointF(8.55593,14.2), QPointF(8.59766,14.7), QPointF(8.6394,15.3), QPointF(8.68114,15.8), QPointF(8.72287,16.3), QPointF(8.76461,16.8), QPointF(8.80634,17.3), QPointF(8.84808,14.7), QPointF(8.88982,14.7), QPointF(8.93155,14.7), QPointF(8.97329,14.7), QPointF(9.01503,14.7), QPointF(9.05676,14.3), QPointF(9.0985,13.1), QPointF(9.14023,11.9), QPointF(9.18197,10.8), QPointF(9.22371,9.8), QPointF(9.26544,8.8), QPointF(9.30718,7.9), QPointF(9.34891,7.1), QPointF(9.39065,6.3), QPointF(9.43239,5.6), QPointF(9.47412,4.9), QPointF(9.51586,4.3), QPointF(9.5576,3.8), QPointF(9.59933,3.4), QPointF(9.64107,3), QPointF(9.6828,2.6), QPointF(9.72454,2.4), QPointF(9.76628,2.2), QPointF(9.80801,2), QPointF(9.84975,2), QPointF(9.89149,2), QPointF(9.93322,2), QPointF(9.97496,2), QPointF(10.0167,2), QPointF(10.0584,2), QPointF(10.1002,2), QPointF(10.1419,2), QPointF(10.1836,2), QPointF(10.2254,2), QPointF(10.2671,2), QPointF(10.3088,2), QPointF(10.3506,2), QPointF(10.3923,2), QPointF(10.4341,2), QPointF(10.4758,2), QPointF(10.5175,2), QPointF(10.5593,2), QPointF(10.601,2), QPointF(10.6427,2), QPointF(10.6845,2), QPointF(10.7262,2), QPointF(10.7679,2), QPointF(10.8097,2), QPointF(10.8514,2), QPointF(10.8932,4), QPointF(10.9349,6.2), QPointF(10.9766,7.6), QPointF(11.0184,8.8), QPointF(11.0601,9.7), QPointF(11.1018,10.6), QPointF(11.1436,11.4), QPointF(11.1853,12.1), QPointF(11.227,12.8), QPointF(11.2688,13.4), QPointF(11.3105,14), QPointF(11.3523,14.6), QPointF(11.394,15.1), QPointF(11.4357,15.6), QPointF(11.4775,16.1), QPointF(11.5192,16.6), QPointF(11.5609,17.1), QPointF(11.6027,15.6), QPointF(11.6444,14.7), QPointF(11.6861,14.7), QPointF(11.7279,14.7), QPointF(11.7696,14.7), QPointF(11.8114,14.7), QPointF(11.8531,13.5), QPointF(11.8948,12.3), QPointF(11.9366,11.2), QPointF(11.9783,10.1), QPointF(12.02,9.1), QPointF(12.0618,8.2), QPointF(12.1035,7.3), QPointF(12.1452,6.5), QPointF(12.187,5.8), QPointF(12.2287,5.1), QPointF(12.2705,4.5), QPointF(12.3122,4), QPointF(12.3539,3.5), QPointF(12.3957,3.1), QPointF(12.4374,2.7), QPointF(12.4791,2.5), QPointF(12.5209,2.2), QPointF(12.5626,2.1), QPointF(12.6043,2), QPointF(12.6461,2), QPointF(12.6878,2), QPointF(12.7295,2), QPointF(12.7713,2), QPointF(12.813,2), QPointF(12.8548,2), QPointF(12.8965,2), QPointF(12.9382,2), QPointF(12.98,2), QPointF(13.0217,2), QPointF(13.0634,2), QPointF(13.1052,2), QPointF(13.1469,2), QPointF(13.1886,2), QPointF(13.2304,2), QPointF(13.2721,2), QPointF(13.3139,2), QPointF(13.3556,2), QPointF(13.3973,2), QPointF(13.4391,2), QPointF(13.4808,2), QPointF(13.5225,2), QPointF(13.5643,2), QPointF(13.606,2), QPointF(13.6477,2), QPointF(13.6895,4), QPointF(13.7312,6.2), QPointF(13.773,7.6), QPointF(13.8147,8.8), QPointF(13.8564,10), QPointF(13.8982,10.9), QPointF(13.9399,11.6), QPointF(13.9816,12.3), QPointF(14.0234,13), QPointF(14.0651,13.6), QPointF(14.1068,14.2), QPointF(14.1486,14.7), QPointF(14.1903,15.3), QPointF(14.2321,15.8), QPointF(14.2738,16.3), QPointF(14.3155,16.8), QPointF(14.3573,17.3), QPointF(14.399,14.7), QPointF(14.4407,14.7), QPointF(14.4825,14.7), QPointF(14.5242,14.7), QPointF(14.5659,14.7), QPointF(14.6077,14.3), QPointF(14.6494,13.1), QPointF(14.6912,11.9), QPointF(14.7329,10.5), QPointF(14.7746,9.4), QPointF(14.8164,8.5), QPointF(14.8581,7.6), QPointF(14.8998,6.8), QPointF(14.9416,6), QPointF(14.9833,5.4), QPointF(15.025,4.7), QPointF(15.0668,4.2), QPointF(15.1085,3.7), QPointF(15.1503,3.2), QPointF(15.192,2.9), QPointF(15.2337,2.5), QPointF(15.2755,2.3), QPointF(15.3172,2.1), QPointF(15.3589,2), QPointF(15.4007,2), QPointF(15.4424,2), QPointF(15.4841,2), QPointF(15.5259,2), QPointF(15.5676,2), QPointF(15.6093,2), QPointF(15.6511,2), QPointF(15.6928,2), QPointF(15.7346,2), QPointF(15.7763,2), QPointF(15.818,2), QPointF(15.8598,2), QPointF(15.9015,2), QPointF(15.9432,2), QPointF(15.985,2), QPointF(16.0267,2), QPointF(16.0684,2), QPointF(16.1102,2), QPointF(16.1519,2), QPointF(16.1937,2), QPointF(16.2354,2), QPointF(16.2771,2), QPointF(16.3189,2), QPointF(16.3606,2), QPointF(16.4023,2), QPointF(16.4441,2), QPointF(16.4858,5.6), QPointF(16.5275,7.2), QPointF(16.5693,8.4), QPointF(16.611,9.4), QPointF(16.6528,10.3), QPointF(16.6945,11.1), QPointF(16.7362,11.9), QPointF(16.778,12.5), QPointF(16.8197,13.2), QPointF(16.8614,13.8), QPointF(16.9032,14.4), QPointF(16.9449,14.9), QPointF(16.9866,15.4), QPointF(17.0284,16), QPointF(17.0701,16.4), QPointF(17.1119,16.9), QPointF(17.1536,16.4), QPointF(17.1953,14.7), QPointF(17.2371,14.7), QPointF(17.2788,14.7), QPointF(17.3205,14.7), QPointF(17.3623,14.7), QPointF(17.404,13.5), QPointF(17.4457,11.9), QPointF(17.4875,10.8), QPointF(17.5292,9.8), QPointF(17.571,8.8), QPointF(17.6127,7.9), QPointF(17.6544,7.1), QPointF(17.6962,6.3), QPointF(17.7379,5.6), QPointF(17.7796,4.9), QPointF(17.8214,4.2), QPointF(17.8631,3.5), QPointF(17.9048,3.1), QPointF(17.9466,2.7), QPointF(17.9883,2.5), QPointF(18.0301,2.2), QPointF(18.0718,2.1), QPointF(18.1135,2), QPointF(18.1553,2), QPointF(18.197,2), QPointF(18.2387,2), QPointF(18.2805,2), QPointF(18.3222,2), QPointF(18.3639,2), QPointF(18.4057,2), QPointF(18.4474,2), QPointF(18.4891,2), QPointF(18.5309,2), QPointF(18.5726,2), QPointF(18.6144,2), QPointF(18.6561,2), QPointF(18.6978,2), QPointF(18.7396,2), QPointF(18.7813,2), QPointF(18.823,2), QPointF(18.8648,2), QPointF(18.9065,2), QPointF(18.9482,2), QPointF(18.99,2), QPointF(19.0317,2), QPointF(19.0735,2), QPointF(19.1152,2), QPointF(19.1569,2), QPointF(19.1987,4), QPointF(19.2404,6.2), QPointF(19.2821,7.6), QPointF(19.3239,8.8), QPointF(19.3656,9.7), QPointF(19.4073,10.6), QPointF(19.4491,11.4), QPointF(19.4908,12.1), QPointF(19.5326,12.8), QPointF(19.5743,13.4), QPointF(19.616,14), QPointF(19.6578,14.6), QPointF(19.6995,15.1), QPointF(19.7412,15.6), QPointF(19.783,16.1), QPointF(19.8247,16.6), QPointF(19.8664,17.1), QPointF(19.9082,15.6), QPointF(19.9499,14.7), QPointF(19.9917,14.7), QPointF(20.0334,14.7), QPointF(20.0751,14.7), QPointF(20.1169,14.7), QPointF(20.1586,13.5), QPointF(20.2003,12.3), QPointF(20.2421,11.2), QPointF(20.2838,10.1), QPointF(20.3255,9.1), QPointF(20.3673,8.2), QPointF(20.409,7.3), QPointF(20.4508,6.5), QPointF(20.4925,5.8), QPointF(20.5342,5.1), QPointF(20.576,4.5), QPointF(20.6177,4), QPointF(20.6594,3.5), QPointF(20.7012,3.1), QPointF(20.7429,2.7), QPointF(20.7846,2.5), QPointF(20.8264,2.2), QPointF(20.8681,2.1), QPointF(20.9098,2), QPointF(20.9516,2), QPointF(20.9933,2), QPointF(21.0351,2), QPointF(21.0768,2), QPointF(21.1185,2), QPointF(21.1603,2), QPointF(21.202,2), QPointF(21.2437,2), QPointF(21.2855,2), QPointF(21.3272,2), QPointF(21.3689,2), QPointF(21.4107,2), QPointF(21.4524,2), QPointF(21.4942,2), QPointF(21.5359,2), QPointF(21.5776,2), QPointF(21.6194,2), QPointF(21.6611,2), QPointF(21.7028,2), QPointF(21.7446,2), QPointF(21.7863,2), QPointF(21.828,2), QPointF(21.8698,2), QPointF(21.9115,2), QPointF(21.9533,4), QPointF(21.995,6.2), QPointF(22.0367,7.6), QPointF(22.0785,8.8), QPointF(22.1202,9.7), QPointF(22.1619,10.6), QPointF(22.2037,11.4), QPointF(22.2454,12.1), QPointF(22.2871,12.8), QPointF(22.3289,13.4), QPointF(22.3706,14), QPointF(22.4124,14.6), QPointF(22.4541,15.1), QPointF(22.4958,15.6), QPointF(22.5376,16.1), QPointF(22.5793,16.6), QPointF(22.621,17.1), QPointF(22.6628,15.6), QPointF(22.7045,14.7), QPointF(22.7462,14.7), QPointF(22.788,14.7), QPointF(22.8297,14.7), QPointF(22.8715,14.7), QPointF(22.9132,13.5), QPointF(22.9549,12.3), QPointF(22.9967,11.2), QPointF(23.0384,10.1), QPointF(23.0801,9.1), QPointF(23.1219,8.2), QPointF(23.1636,7.3), QPointF(23.2053,6.5), QPointF(23.2471,5.8), QPointF(23.2888,5.1), QPointF(23.3306,4.5), QPointF(23.3723,4), QPointF(23.414,3.5), QPointF(23.4558,3.1), QPointF(23.4975,2.7), QPointF(23.5392,2.5), QPointF(23.581,2.2), QPointF(23.6227,2.1), QPointF(23.6644,2), QPointF(23.7062,2), QPointF(23.7479,2), QPointF(23.7896,2), QPointF(23.8314,2), QPointF(23.8731,2), QPointF(23.9149,2), QPointF(23.9566,2), QPointF(23.9983,2), QPointF(24.0401,2), QPointF(24.0818,2), QPointF(24.1235,2), QPointF(24.1653,2), QPointF(24.207,2), QPointF(24.2487,2), QPointF(24.2905,2), QPointF(24.3322,2), QPointF(24.374,2), QPointF(24.4157,2), QPointF(24.4574,2), QPointF(24.4992,2), QPointF(24.5409,2), QPointF(24.5826,2), QPointF(24.6244,2), QPointF(24.6661,2), QPointF(24.7078,4), QPointF(24.7496,6.2), QPointF(24.7913,7.6), QPointF(24.8331,8.8), QPointF(24.8748,10), QPointF(24.9165,10.9), QPointF(24.9583,11.6), QPointF(25,12.3)};

QVector<QPointF> sFlowWaveData =
{
    QPointF(0,18), QPointF(0.0417362,18), QPointF(0.0834725,18), QPointF(0.125209,18), QPointF(0.166945,18), QPointF(0.208681,18), QPointF(0.250417,18), QPointF(0.292154,18), QPointF(0.33389,18), QPointF(0.375626,18), QPointF(0.417362,18), QPointF(0.459098,18), QPointF(0.500835,18), QPointF(0.542571,18), QPointF(0.584307,18), QPointF(0.626043,18), QPointF(0.66778,18), QPointF(0.709516,0), QPointF(0.751252,0), QPointF(0.792988,0), QPointF(0.834725,0), QPointF(0.876461,0),
    QPointF(3.67279,-25), QPointF(3.71452,-22), QPointF(3.75626,-20), QPointF(3.798,-18), QPointF(3.83973,-17), QPointF(3.88147,-16), QPointF(3.92321,-14), QPointF(3.96494,-13), QPointF(4.00668,-12), QPointF(4.04841,-11), QPointF(4.09015,-10), QPointF(4.13189,-9), QPointF(4.17362,-8), QPointF(4.21536,-8), QPointF(4.2571,-7), QPointF(4.29883,-6), QPointF(4.34057,-5), QPointF(4.3823,-5), QPointF(4.42404,-4), QPointF(4.46578,-3), QPointF(4.50751,-3), QPointF(4.54925,-2),
    QPointF(1.83639,0), QPointF(1.87813,0), QPointF(1.91987,0), QPointF(1.9616,0), QPointF(2.00334,0), QPointF(2.04508,0), QPointF(2.08681,0), QPointF(2.12855,0), QPointF(2.17028,0), QPointF(2.21202,0), QPointF(2.25376,0), QPointF(2.29549,0), QPointF(2.33723,0), QPointF(2.37896,0), QPointF(2.4207,0), QPointF(2.46244,0), QPointF(2.50417,0), QPointF(2.54591,0), QPointF(2.58765,0), QPointF(2.62938,0), QPointF(2.67112,0), QPointF(2.71285,0), QPointF(2.75459,18), QPointF(2.79633,18), QPointF(2.83806,18), QPointF(2.8798,18), QPointF(2.92154,18), QPointF(2.96327,18), QPointF(3.00501,18), QPointF(3.04674,18), QPointF(3.08848,18), QPointF(3.13022,18), QPointF(3.17195,18), QPointF(3.21369,18), QPointF(3.25543,18), QPointF(3.29716,18), QPointF(3.3389,18), QPointF(3.38063,18), QPointF(3.42237,0), QPointF(3.46411,0), QPointF(3.50584,0), QPointF(3.54758,0), QPointF(3.58932,0), QPointF(3.63105,0),
    QPointF(3.67279,-25), QPointF(3.71452,-22), QPointF(3.75626,-20), QPointF(3.798,-18), QPointF(3.83973,-17), QPointF(3.88147,-16), QPointF(3.92321,-14), QPointF(3.96494,-13), QPointF(4.00668,-12), QPointF(4.04841,-11), QPointF(4.09015,-10), QPointF(4.13189,-9), QPointF(4.17362,-8), QPointF(4.21536,-8), QPointF(4.2571,-7), QPointF(4.29883,-6), QPointF(4.34057,-5), QPointF(4.3823,-5), QPointF(4.42404,-4), QPointF(4.46578,-3), QPointF(4.50751,-3), QPointF(4.54925,-2),
    QPointF(4.59098,0), QPointF(4.63272,0), QPointF(4.67446,0), QPointF(4.71619,0), QPointF(4.75793,0), QPointF(4.79967,0), QPointF(4.8414,0), QPointF(4.88314,0), QPointF(4.92487,0), QPointF(4.96661,0), QPointF(5.00835,0), QPointF(5.05008,0), QPointF(5.09182,0), QPointF(5.13356,0), QPointF(5.17529,0), QPointF(5.21703,0), QPointF(5.25876,0), QPointF(5.3005,0), QPointF(5.34224,0), QPointF(5.38397,0), QPointF(5.42571,0), QPointF(5.46745,0), QPointF(5.50918,18), QPointF(5.55092,18), QPointF(5.59265,18), QPointF(5.63439,18), QPointF(5.67613,18), QPointF(5.71786,18), QPointF(5.7596,18), QPointF(5.80134,18), QPointF(5.84307,18), QPointF(5.88481,18), QPointF(5.92654,18), QPointF(5.96828,18), QPointF(6.01002,18), QPointF(6.05175,18), QPointF(6.09349,18), QPointF(6.13523,18), QPointF(6.17696,0), QPointF(6.2187,0), QPointF(6.26043,0), QPointF(6.30217,0), QPointF(6.34391,0),
    QPointF(3.67279,-25), QPointF(3.71452,-22), QPointF(3.75626,-20), QPointF(3.798,-18), QPointF(3.83973,-17), QPointF(3.88147,-16), QPointF(3.92321,-14), QPointF(3.96494,-13), QPointF(4.00668,-12), QPointF(4.04841,-11), QPointF(4.09015,-10), QPointF(4.13189,-9), QPointF(4.17362,-8), QPointF(4.21536,-8), QPointF(4.2571,-7), QPointF(4.29883,-6), QPointF(4.34057,-5), QPointF(4.3823,-5), QPointF(4.42404,-4), QPointF(4.46578,-3), QPointF(4.50751,-3), QPointF(4.54925,-2),
    QPointF(7.34558,0), QPointF(7.38731,0), QPointF(7.42905,0), QPointF(7.47078,0), QPointF(7.51252,0), QPointF(7.55426,0), QPointF(7.59599,0), QPointF(7.63773,0), QPointF(7.67947,0), QPointF(7.7212,0), QPointF(7.76294,0), QPointF(7.80467,0), QPointF(7.84641,0), QPointF(7.88815,0), QPointF(7.92988,0), QPointF(7.97162,0), QPointF(8.01336,0), QPointF(8.05509,0), QPointF(8.09683,0), QPointF(8.13856,0), QPointF(8.1803,0), QPointF(8.22204,3), QPointF(8.26377,18), QPointF(8.30551,18), QPointF(8.34725,18), QPointF(8.38898,18), QPointF(8.43072,18), QPointF(8.47245,18), QPointF(8.51419,18), QPointF(8.55593,18), QPointF(8.59766,18), QPointF(8.6394,18), QPointF(8.68114,18), QPointF(8.72287,18), QPointF(8.76461,18), QPointF(8.80634,18), QPointF(8.84808,18), QPointF(8.88982,0), QPointF(8.93155,0), QPointF(8.97329,0), QPointF(9.01503,0), QPointF(9.05676,0), QPointF(9.0985,0), QPointF(9.14023,-25), QPointF(9.18197,-22), QPointF(9.22371,-20), QPointF(9.26544,-18), QPointF(9.30718,-17), QPointF(9.34891,-16), QPointF(9.39065,-15), QPointF(9.43239,-13), QPointF(9.47412,-12), QPointF(9.51586,-11), QPointF(9.5576,-11), QPointF(9.59933,-10), QPointF(9.64107,-9), QPointF(9.6828,-8), QPointF(9.72454,-7), QPointF(9.76628,-7), QPointF(9.80801,-6), QPointF(9.84975,-5), QPointF(9.89149,-4), QPointF(9.93322,-4), QPointF(9.97496,-3), QPointF(10.0167,-2), QPointF(10.0584,-2), QPointF(10.1002,0), QPointF(10.1419,0), QPointF(10.1836,0), QPointF(10.2254,0), QPointF(10.2671,0), QPointF(10.3088,0), QPointF(10.3506,0), QPointF(10.3923,0), QPointF(10.4341,0), QPointF(10.4758,0), QPointF(10.5175,0), QPointF(10.5593,0), QPointF(10.601,0), QPointF(10.6427,0), QPointF(10.6845,0), QPointF(10.7262,0), QPointF(10.7679,0), QPointF(10.8097,0), QPointF(10.8514,0), QPointF(10.8932,0), QPointF(10.9349,0), QPointF(10.9766,18), QPointF(11.0184,18), QPointF(11.0601,18), QPointF(11.1018,18), QPointF(11.1436,18), QPointF(11.1853,18), QPointF(11.227,18), QPointF(11.2688,18), QPointF(11.3105,18), QPointF(11.3523,18), QPointF(11.394,18), QPointF(11.4357,18), QPointF(11.4775,18), QPointF(11.5192,18), QPointF(11.5609,18), QPointF(11.6027,18), QPointF(11.6444,18), QPointF(11.6861,0), QPointF(11.7279,0), QPointF(11.7696,0), QPointF(11.8114,0), QPointF(11.8531,0), QPointF(11.8948,-26), QPointF(11.9366,-23), QPointF(11.9783,-21), QPointF(12.02,-18), QPointF(12.0618,-17), QPointF(12.1035,-16), QPointF(12.1452,-15), QPointF(12.187,-13), QPointF(12.2287,-12), QPointF(12.2705,-11), QPointF(12.3122,-11), QPointF(12.3539,-10), QPointF(12.3957,-9), QPointF(12.4374,-8), QPointF(12.4791,-7), QPointF(12.5209,-7), QPointF(12.5626,-6), QPointF(12.6043,-5), QPointF(12.6461,-4), QPointF(12.6878,-4), QPointF(12.7295,-3), QPointF(12.7713,-2), QPointF(12.813,-2), QPointF(12.8548,0), QPointF(12.8965,0), QPointF(12.9382,0), QPointF(12.98,0), QPointF(13.0217,0), QPointF(13.0634,0), QPointF(13.1052,0), QPointF(13.1469,0), QPointF(13.1886,0), QPointF(13.2304,0), QPointF(13.2721,0), QPointF(13.3139,0), QPointF(13.3556,0), QPointF(13.3973,0), QPointF(13.4391,0), QPointF(13.4808,0), QPointF(13.5225,0), QPointF(13.5643,0), QPointF(13.606,0), QPointF(13.6477,0), QPointF(13.6895,3), QPointF(13.7312,18), QPointF(13.773,18), QPointF(13.8147,18), QPointF(13.8564,18), QPointF(13.8982,18), QPointF(13.9399,18), QPointF(13.9816,18), QPointF(14.0234,18), QPointF(14.0651,18), QPointF(14.1068,18), QPointF(14.1486,18), QPointF(14.1903,18), QPointF(14.2321,18), QPointF(14.2738,18), QPointF(14.3155,18), QPointF(14.3573,18), QPointF(14.399,0), QPointF(14.4407,0), QPointF(14.4825,0), QPointF(14.5242,0), QPointF(14.5659,0), QPointF(14.6077,0), QPointF(14.6494,-25), QPointF(14.6912,-22), QPointF(14.7329,-20), QPointF(14.7746,-18), QPointF(14.8164,-17), QPointF(14.8581,-16), QPointF(14.8998,-15), QPointF(14.9416,-13), QPointF(14.9833,-12), QPointF(15.025,-11), QPointF(15.0668,-10), QPointF(15.1085,-9), QPointF(15.1503,-9), QPointF(15.192,-8), QPointF(15.2337,-7), QPointF(15.2755,-6), QPointF(15.3172,-5), QPointF(15.3589,-5), QPointF(15.4007,-4), QPointF(15.4424,-3), QPointF(15.4841,-3), QPointF(15.5259,-2), QPointF(15.5676,0), QPointF(15.6093,0), QPointF(15.6511,0), QPointF(15.6928,0), QPointF(15.7346,0), QPointF(15.7763,0), QPointF(15.818,0), QPointF(15.8598,0), QPointF(15.9015,0), QPointF(15.9432,0), QPointF(15.985,0), QPointF(16.0267,0), QPointF(16.0684,0), QPointF(16.1102,0), QPointF(16.1519,0), QPointF(16.1937,0), QPointF(16.2354,0), QPointF(16.2771,0), QPointF(16.3189,0), QPointF(16.3606,0), QPointF(16.4023,0), QPointF(16.4441,18), QPointF(16.4858,18), QPointF(16.5275,18), QPointF(16.5693,18), QPointF(16.611,18), QPointF(16.6528,18), QPointF(16.6945,18), QPointF(16.7362,18), QPointF(16.778,18), QPointF(16.8197,18), QPointF(16.8614,18), QPointF(16.9032,18), QPointF(16.9449,18), QPointF(16.9866,18), QPointF(17.0284,18), QPointF(17.0701,18), QPointF(17.1119,18), QPointF(17.1536,0), QPointF(17.1953,0), QPointF(17.2371,0), QPointF(17.2788,0), QPointF(17.3205,0), QPointF(17.3623,-30), QPointF(17.404,-24), QPointF(17.4457,-21), QPointF(17.4875,-19), QPointF(17.5292,-18), QPointF(17.571,-17), QPointF(17.6127,-15), QPointF(17.6544,-14), QPointF(17.6962,-13), QPointF(17.7379,-12), QPointF(17.7796,-11), QPointF(17.8214,-10), QPointF(17.8631,-9), QPointF(17.9048,-9), QPointF(17.9466,-8), QPointF(17.9883,-7), QPointF(18.0301,-6), QPointF(18.0718,-6), QPointF(18.1135,-5), QPointF(18.1553,-4), QPointF(18.197,-4), QPointF(18.2387,-3), QPointF(18.2805,-2), QPointF(18.3222,-2), QPointF(18.3639,0), QPointF(18.4057,0), QPointF(18.4474,0), QPointF(18.4891,0), QPointF(18.5309,0), QPointF(18.5726,0), QPointF(18.6144,0), QPointF(18.6561,0), QPointF(18.6978,0), QPointF(18.7396,0), QPointF(18.7813,0), QPointF(18.823,0), QPointF(18.8648,0), QPointF(18.9065,0), QPointF(18.9482,0), QPointF(18.99,0), QPointF(19.0317,0), QPointF(19.0735,0), QPointF(19.1152,0), QPointF(19.1569,0), QPointF(19.1987,0), QPointF(19.2404,18), QPointF(19.2821,18), QPointF(19.3239,18), QPointF(19.3656,18), QPointF(19.4073,18), QPointF(19.4491,18), QPointF(19.4908,18), QPointF(19.5326,18), QPointF(19.5743,18), QPointF(19.616,18), QPointF(19.6578,18), QPointF(19.6995,18), QPointF(19.7412,18), QPointF(19.783,18), QPointF(19.8247,18), QPointF(19.8664,18), QPointF(19.9082,18), QPointF(19.9499,0), QPointF(19.9917,0), QPointF(20.0334,0), QPointF(20.0751,0), QPointF(20.1169,0), QPointF(20.1586,-30), QPointF(20.2003,-24), QPointF(20.2421,-21), QPointF(20.2838,-19), QPointF(20.3255,-18), QPointF(20.3673,-16), QPointF(20.409,-15), QPointF(20.4508,-14), QPointF(20.4925,-13), QPointF(20.5342,-11), QPointF(20.576,-10), QPointF(20.6177,-9), QPointF(20.6594,-9), QPointF(20.7012,-8), QPointF(20.7429,-7), QPointF(20.7846,-6), QPointF(20.8264,-6), QPointF(20.8681,-5), QPointF(20.9098,-4), QPointF(20.9516,-3), QPointF(20.9933,-3), QPointF(21.0351,-2), QPointF(21.0768,0), QPointF(21.1185,0), QPointF(21.1603,0), QPointF(21.202,0), QPointF(21.2437,0), QPointF(21.2855,0), QPointF(21.3272,0), QPointF(21.3689,0), QPointF(21.4107,0), QPointF(21.4524,0), QPointF(21.4942,0), QPointF(21.5359,0), QPointF(21.5776,0), QPointF(21.6194,0), QPointF(21.6611,0), QPointF(21.7028,0), QPointF(21.7446,0), QPointF(21.7863,0), QPointF(21.828,0), QPointF(21.8698,0), QPointF(21.9115,0), QPointF(21.9533,18), QPointF(21.995,18), QPointF(22.0367,18), QPointF(22.0785,18), QPointF(22.1202,18), QPointF(22.1619,18), QPointF(22.2037,18), QPointF(22.2454,18), QPointF(22.2871,18), QPointF(22.3289,18), QPointF(22.3706,18), QPointF(22.4124,18), QPointF(22.4541,18), QPointF(22.4958,18), QPointF(22.5376,18), QPointF(22.5793,18), QPointF(22.621,18), QPointF(22.6628,0), QPointF(22.7045,0), QPointF(22.7462,0), QPointF(22.788,0), QPointF(22.8297,0), QPointF(22.8715,-26), QPointF(22.9132,-23), QPointF(22.9549,-21), QPointF(22.9967,-19), QPointF(23.0384,-17), QPointF(23.0801,-16), QPointF(23.1219,-15), QPointF(23.1636,-14), QPointF(23.2053,-13), QPointF(23.2471,-12), QPointF(23.2888,-11), QPointF(23.3306,-10), QPointF(23.3723,-9), QPointF(23.414,-8), QPointF(23.4558,-8), QPointF(23.4975,-7), QPointF(23.5392,-6), QPointF(23.581,-5), QPointF(23.6227,-5), QPointF(23.6644,-4), QPointF(23.7062,-3), QPointF(23.7479,-3), QPointF(23.7896,-2), QPointF(23.8314,0), QPointF(23.8731,0), QPointF(23.9149,0), QPointF(23.9566,0), QPointF(23.9983,0), QPointF(24.0401,0), QPointF(24.0818,0), QPointF(24.1235,0), QPointF(24.1653,0), QPointF(24.207,0), QPointF(24.2487,0), QPointF(24.2905,0), QPointF(24.3322,0), QPointF(24.374,0), QPointF(24.4157,0), QPointF(24.4574,0), QPointF(24.4992,0), QPointF(24.5409,0), QPointF(24.5826,0), QPointF(24.6244,0), QPointF(24.6661,0), QPointF(24.7078,0), QPointF(24.7496,18), QPointF(24.7913,18), QPointF(24.8331,18), QPointF(24.8748,18), QPointF(24.9165,18), QPointF(24.9583,18), QPointF(25,18)
};

QVector<QPointF> sVolWaveData
{
    QPointF(0,0), QPointF(0.0417362,0), QPointF(0.0834725,0), QPointF(0.125209,0), QPointF(0.166945,0), QPointF(0.208681,0), QPointF(0.250417,0), QPointF(0.292154,0), QPointF(0.33389,0), QPointF(0.375626,0), QPointF(0.417362,0), QPointF(0.459098,0), QPointF(0.500835,0), QPointF(0.542571,0), QPointF(0.584307,28), QPointF(0.626043,57), QPointF(0.66778,86), QPointF(0.709516,115), QPointF(0.751252,144), QPointF(0.792988,172), QPointF(0.834725,201), QPointF(0.876461,230), QPointF(0.918197,259), QPointF(0.959933,288), QPointF(1.00167,316), QPointF(1.04341,345), QPointF(1.08514,374), QPointF(1.12688,403), QPointF(1.16861,432), QPointF(1.21035,460), QPointF(1.25209,480), QPointF(1.29382,480), QPointF(1.33556,480), QPointF(1.3773,480), QPointF(1.41903,480), QPointF(1.46077,480), QPointF(1.5025,456), QPointF(1.54424,420), QPointF(1.58598,388), QPointF(1.62771,356), QPointF(1.66945,326), QPointF(1.71119,297), QPointF(1.75292,268), QPointF(1.79466,243), QPointF(1.83639,209), QPointF(1.87813,187), QPointF(1.91987,164), QPointF(1.9616,144), QPointF(2.00334,124), QPointF(2.04508,107), QPointF(2.08681,91), QPointF(2.12855,76), QPointF(2.17028,62), QPointF(2.21202,49), QPointF(2.25376,38), QPointF(2.29549,28), QPointF(2.33723,20), QPointF(2.37896,14), QPointF(2.4207,8), QPointF(2.46244,0), QPointF(2.50417,0), QPointF(2.54591,0), QPointF(2.58765,0), QPointF(2.62938,0), QPointF(2.67112,0), QPointF(2.71285,0), QPointF(2.75459,0), QPointF(2.79633,0), QPointF(2.83806,0), QPointF(2.8798,0), QPointF(2.92154,0), QPointF(2.96327,0), QPointF(3.00501,0), QPointF(3.04674,0), QPointF(3.08848,0), QPointF(3.13022,0), QPointF(3.17195,0), QPointF(3.21369,0), QPointF(3.25543,0), QPointF(3.29716,19), QPointF(3.3389,48), QPointF(3.38063,76), QPointF(3.42237,105), QPointF(3.46411,134), QPointF(3.50584,163), QPointF(3.54758,192), QPointF(3.58932,220), QPointF(3.63105,249), QPointF(3.67279,278), QPointF(3.71452,307), QPointF(3.75626,336), QPointF(3.798,364), QPointF(3.83973,393), QPointF(3.88147,422), QPointF(3.92321,451), QPointF(3.96494,480), QPointF(4.00668,480), QPointF(4.04841,480), QPointF(4.09015,480), QPointF(4.13189,480), QPointF(4.17362,480), QPointF(4.21536,467), QPointF(4.2571,432), QPointF(4.29883,388), QPointF(4.34057,356), QPointF(4.3823,326), QPointF(4.42404,297), QPointF(4.46578,268), QPointF(4.50751,243), QPointF(4.54925,217), QPointF(4.59098,193), QPointF(4.63272,172), QPointF(4.67446,150), QPointF(4.71619,131), QPointF(4.75793,113), QPointF(4.79967,96), QPointF(4.8414,81), QPointF(4.88314,67), QPointF(4.92487,49), QPointF(4.96661,35), QPointF(5.00835,25), QPointF(5.05008,19), QPointF(5.09182,11), QPointF(5.13356,0), QPointF(5.17529,0), QPointF(5.21703,0), QPointF(5.25876,0), QPointF(5.3005,0), QPointF(5.34224,0), QPointF(5.38397,0), QPointF(5.42571,0), QPointF(5.46745,0), QPointF(5.50918,0), QPointF(5.55092,0), QPointF(5.59265,0), QPointF(5.63439,0), QPointF(5.67613,0), QPointF(5.71786,0), QPointF(5.7596,0), QPointF(5.80134,0), QPointF(5.84307,0), QPointF(5.88481,0), QPointF(5.92654,0), QPointF(5.96828,0), QPointF(6.01002,0), QPointF(6.05175,19), QPointF(6.09349,48), QPointF(6.13523,76), QPointF(6.17696,105), QPointF(6.2187,134), QPointF(6.26043,163), QPointF(6.30217,192), QPointF(6.34391,220), QPointF(6.38564,249), QPointF(6.42738,278), QPointF(6.46912,307), QPointF(6.51085,336), QPointF(6.55259,364), QPointF(6.59432,393), QPointF(6.63606,422), QPointF(6.6778,451), QPointF(6.71953,480), QPointF(6.76127,480), QPointF(6.80301,480), QPointF(6.84474,480), QPointF(6.88648,480), QPointF(6.92821,480), QPointF(6.96995,443), QPointF(7.01169,409), QPointF(7.05342,377), QPointF(7.09516,345), QPointF(7.13689,316), QPointF(7.17863,288), QPointF(7.22037,260), QPointF(7.2621,235), QPointF(7.30384,209), QPointF(7.34558,187), QPointF(7.38731,164), QPointF(7.42905,144), QPointF(7.47078,124), QPointF(7.51252,107), QPointF(7.55426,91), QPointF(7.59599,76), QPointF(7.63773,62), QPointF(7.67947,49), QPointF(7.7212,38), QPointF(7.76294,28), QPointF(7.80467,20), QPointF(7.84641,14), QPointF(7.88815,8), QPointF(7.92988,0), QPointF(7.97162,0), QPointF(8.01336,0), QPointF(8.05509,0), QPointF(8.09683,0), QPointF(8.13856,0), QPointF(8.1803,0), QPointF(8.22204,0), QPointF(8.26377,0), QPointF(8.30551,0), QPointF(8.34725,0), QPointF(8.38898,0), QPointF(8.43072,0), QPointF(8.47245,0), QPointF(8.51419,0), QPointF(8.55593,0), QPointF(8.59766,0), QPointF(8.6394,0), QPointF(8.68114,0), QPointF(8.72287,0), QPointF(8.76461,0), QPointF(8.80634,19), QPointF(8.84808,48), QPointF(8.88982,76), QPointF(8.93155,105), QPointF(8.97329,134), QPointF(9.01503,163), QPointF(9.05676,192), QPointF(9.0985,220), QPointF(9.14023,249), QPointF(9.18197,278), QPointF(9.22371,307), QPointF(9.26544,336), QPointF(9.30718,364), QPointF(9.34891,393), QPointF(9.39065,422), QPointF(9.43239,451), QPointF(9.47412,480), QPointF(9.51586,480), QPointF(9.5576,480), QPointF(9.59933,480), QPointF(9.64107,480), QPointF(9.6828,480), QPointF(9.72454,456), QPointF(9.76628,420), QPointF(9.80801,388), QPointF(9.84975,356), QPointF(9.89149,326), QPointF(9.93322,297), QPointF(9.97496,268), QPointF(10.0167,243), QPointF(10.0584,217), QPointF(10.1002,193), QPointF(10.1419,172), QPointF(10.1836,150), QPointF(10.2254,131), QPointF(10.2671,113), QPointF(10.3088,96), QPointF(10.3506,81), QPointF(10.3923,67), QPointF(10.4341,54), QPointF(10.4758,43), QPointF(10.5175,32), QPointF(10.5593,24), QPointF(10.601,16), QPointF(10.6427,9), QPointF(10.6845,0), QPointF(10.7262,0), QPointF(10.7679,0), QPointF(10.8097,0), QPointF(10.8514,0), QPointF(10.8932,0), QPointF(10.9349,0), QPointF(10.9766,0), QPointF(11.0184,0), QPointF(11.0601,0), QPointF(11.1018,0), QPointF(11.1436,0), QPointF(11.1853,0), QPointF(11.227,0), QPointF(11.2688,0), QPointF(11.3105,0), QPointF(11.3523,0), QPointF(11.394,0), QPointF(11.4357,0), QPointF(11.4775,0), QPointF(11.5192,0), QPointF(11.5609,0), QPointF(11.6027,28), QPointF(11.6444,57), QPointF(11.6861,86), QPointF(11.7279,115), QPointF(11.7696,144), QPointF(11.8114,172), QPointF(11.8531,201), QPointF(11.8948,230), QPointF(11.9366,259), QPointF(11.9783,288), QPointF(12.02,316), QPointF(12.0618,345), QPointF(12.1035,374), QPointF(12.1452,412), QPointF(12.187,441), QPointF(12.2287,470), QPointF(12.2705,480), QPointF(12.3122,480), QPointF(12.3539,480), QPointF(12.3957,480), QPointF(12.4374,480), QPointF(12.4791,480), QPointF(12.5209,443), QPointF(12.5626,409), QPointF(12.6043,377), QPointF(12.6461,345), QPointF(12.6878,316), QPointF(12.7295,288), QPointF(12.7713,260), QPointF(12.813,235), QPointF(12.8548,209), QPointF(12.8965,187), QPointF(12.9382,164), QPointF(12.98,144), QPointF(13.0217,124), QPointF(13.0634,107), QPointF(13.1052,91), QPointF(13.1469,76), QPointF(13.1886,62), QPointF(13.2304,49), QPointF(13.2721,38), QPointF(13.3139,28), QPointF(13.3556,20), QPointF(13.3973,14), QPointF(13.4391,8), QPointF(13.4808,0), QPointF(13.5225,0), QPointF(13.5643,0), QPointF(13.606,0), QPointF(13.6477,0), QPointF(13.6895,0), QPointF(13.7312,0), QPointF(13.773,0), QPointF(13.8147,0), QPointF(13.8564,0), QPointF(13.8982,0), QPointF(13.9399,0), QPointF(13.9816,0), QPointF(14.0234,0), QPointF(14.0651,0), QPointF(14.1068,0), QPointF(14.1486,0), QPointF(14.1903,0), QPointF(14.2321,0), QPointF(14.2738,0), QPointF(14.3155,0), QPointF(14.3573,19), QPointF(14.399,57), QPointF(14.4407,86), QPointF(14.4825,115), QPointF(14.5242,144), QPointF(14.5659,172), QPointF(14.6077,201), QPointF(14.6494,230), QPointF(14.6912,259), QPointF(14.7329,288), QPointF(14.7746,316), QPointF(14.8164,345), QPointF(14.8581,374), QPointF(14.8998,403), QPointF(14.9416,432), QPointF(14.9833,460), QPointF(15.025,480), QPointF(15.0668,480), QPointF(15.1085,480), QPointF(15.1503,480), QPointF(15.192,480), QPointF(15.2337,480), QPointF(15.2755,456), QPointF(15.3172,420), QPointF(15.3589,388), QPointF(15.4007,356), QPointF(15.4424,326), QPointF(15.4841,297), QPointF(15.5259,268), QPointF(15.5676,243), QPointF(15.6093,217), QPointF(15.6511,193), QPointF(15.6928,172), QPointF(15.7346,150), QPointF(15.7763,131), QPointF(15.818,113), QPointF(15.8598,96), QPointF(15.9015,81), QPointF(15.9432,67), QPointF(15.985,54), QPointF(16.0267,43), QPointF(16.0684,32), QPointF(16.1102,24), QPointF(16.1519,16), QPointF(16.1937,9), QPointF(16.2354,0), QPointF(16.2771,0), QPointF(16.3189,0), QPointF(16.3606,0), QPointF(16.4023,0), QPointF(16.4441,0), QPointF(16.4858,0), QPointF(16.5275,0), QPointF(16.5693,0), QPointF(16.611,0), QPointF(16.6528,0), QPointF(16.6945,0), QPointF(16.7362,0), QPointF(16.778,0), QPointF(16.8197,0), QPointF(16.8614,0), QPointF(16.9032,0), QPointF(16.9449,0), QPointF(16.9866,0), QPointF(17.0284,0), QPointF(17.0701,0), QPointF(17.1119,9), QPointF(17.1536,38), QPointF(17.1953,67), QPointF(17.2371,96), QPointF(17.2788,124), QPointF(17.3205,153), QPointF(17.3623,182), QPointF(17.404,220), QPointF(17.4457,249), QPointF(17.4875,278), QPointF(17.5292,307), QPointF(17.571,336), QPointF(17.6127,364), QPointF(17.6544,393), QPointF(17.6962,422), QPointF(17.7379,451), QPointF(17.7796,480), QPointF(17.8214,480), QPointF(17.8631,480), QPointF(17.9048,480), QPointF(17.9466,480), QPointF(17.9883,480), QPointF(18.0301,467), QPointF(18.0718,432), QPointF(18.1135,398), QPointF(18.1553,366), QPointF(18.197,336), QPointF(18.2387,307), QPointF(18.2805,278), QPointF(18.3222,251), QPointF(18.3639,225), QPointF(18.4057,201), QPointF(18.4474,179), QPointF(18.4891,158), QPointF(18.5309,137), QPointF(18.5726,120), QPointF(18.6144,102), QPointF(18.6561,86), QPointF(18.6978,72), QPointF(18.7396,57), QPointF(18.7813,46), QPointF(18.823,35), QPointF(18.8648,25), QPointF(18.9065,16), QPointF(18.9482,8), QPointF(18.99,0), QPointF(19.0317,0), QPointF(19.0735,0), QPointF(19.1152,0), QPointF(19.1569,0), QPointF(19.1987,0), QPointF(19.2404,0), QPointF(19.2821,0), QPointF(19.3239,0), QPointF(19.3656,0), QPointF(19.4073,0), QPointF(19.4491,0), QPointF(19.4908,0), QPointF(19.5326,0), QPointF(19.5743,0), QPointF(19.616,0), QPointF(19.6578,0), QPointF(19.6995,0), QPointF(19.7412,0), QPointF(19.783,0), QPointF(19.8247,0), QPointF(19.8664,9), QPointF(19.9082,38), QPointF(19.9499,67), QPointF(19.9917,96), QPointF(20.0334,124), QPointF(20.0751,153), QPointF(20.1169,182), QPointF(20.1586,211), QPointF(20.2003,240), QPointF(20.2421,268), QPointF(20.2838,297), QPointF(20.3255,326), QPointF(20.3673,355), QPointF(20.409,384), QPointF(20.4508,432), QPointF(20.4925,460), QPointF(20.5342,480), QPointF(20.576,480), QPointF(20.6177,480), QPointF(20.6594,480), QPointF(20.7012,480), QPointF(20.7429,480), QPointF(20.7846,456), QPointF(20.8264,420), QPointF(20.8681,388), QPointF(20.9098,356), QPointF(20.9516,326), QPointF(20.9933,297), QPointF(21.0351,268), QPointF(21.0768,243), QPointF(21.1185,217), QPointF(21.1603,193), QPointF(21.202,172), QPointF(21.2437,150), QPointF(21.2855,131), QPointF(21.3272,113), QPointF(21.3689,96), QPointF(21.4107,81), QPointF(21.4524,67), QPointF(21.4942,54), QPointF(21.5359,43), QPointF(21.5776,32), QPointF(21.6194,24), QPointF(21.6611,16), QPointF(21.7028,9), QPointF(21.7446,0), QPointF(21.7863,0), QPointF(21.828,0), QPointF(21.8698,0), QPointF(21.9115,0), QPointF(21.9533,0), QPointF(21.995,0), QPointF(22.0367,0), QPointF(22.0785,0), QPointF(22.1202,0), QPointF(22.1619,0), QPointF(22.2037,0), QPointF(22.2454,0), QPointF(22.2871,0), QPointF(22.3289,0), QPointF(22.3706,0), QPointF(22.4124,0), QPointF(22.4541,0), QPointF(22.4958,0), QPointF(22.5376,0), QPointF(22.5793,0), QPointF(22.621,0), QPointF(22.6628,28), QPointF(22.7045,57), QPointF(22.7462,86), QPointF(22.788,124), QPointF(22.8297,153), QPointF(22.8715,182), QPointF(22.9132,211), QPointF(22.9549,240), QPointF(22.9967,278), QPointF(23.0384,316), QPointF(23.0801,345), QPointF(23.1219,374), QPointF(23.1636,403), QPointF(23.2053,432), QPointF(23.2471,460), QPointF(23.2888,480), QPointF(23.3306,480), QPointF(23.3723,480), QPointF(23.414,480), QPointF(23.4558,480), QPointF(23.4975,480), QPointF(23.5392,456), QPointF(23.581,420), QPointF(23.6227,388), QPointF(23.6644,356), QPointF(23.7062,326), QPointF(23.7479,297), QPointF(23.7896,268), QPointF(23.8314,243), QPointF(23.8731,217), QPointF(23.9149,193), QPointF(23.9566,172), QPointF(23.9983,150), QPointF(24.0401,131), QPointF(24.0818,113), QPointF(24.1235,96), QPointF(24.1653,81), QPointF(24.207,62), QPointF(24.2487,49), QPointF(24.2905,38), QPointF(24.3322,28), QPointF(24.374,20), QPointF(24.4157,14), QPointF(24.4574,8), QPointF(24.4992,0), QPointF(24.5409,0), QPointF(24.5826,0), QPointF(24.6244,0), QPointF(24.6661,0), QPointF(24.7078,0), QPointF(24.7496,0), QPointF(24.7913,0), QPointF(24.8331,0), QPointF(24.8748,0), QPointF(24.9165,0), QPointF(24.9583,0), QPointF(25,0)
};

QVector<QPointF> sCo2WaveData
{
    QPointF(0,0), QPointF(0.0417362,0), QPointF(0.0834725,0), QPointF(0.125209,0), QPointF(0.166945,0), QPointF(0.208681,0), QPointF(0.250417,0), QPointF(0.292154,0), QPointF(0.33389,0), QPointF(0.375626,0), QPointF(0.417362,0), QPointF(0.459098,0), QPointF(0.500835,0), QPointF(0.542571,0), QPointF(0.584307,0), QPointF(0.626043,0), QPointF(0.66778,0), QPointF(0.709516,0),
    QPointF(0.751252,0), QPointF(0.792988,0.9), QPointF(0.834725,2.7), QPointF(0.876461,8.3), QPointF(0.918197,14.5), QPointF(0.959933,19.9), QPointF(1.00167,21.8), QPointF(1.04341,23.5), QPointF(1.08514,25.4), QPointF(1.12688,26.6), QPointF(1.16861,27.4), QPointF(1.21035,27.7), QPointF(1.25209,28.1), QPointF(1.29382,28.4), QPointF(1.33556,28.6), QPointF(1.3773,28.9), QPointF(1.41903,29), QPointF(1.46077,29.1), QPointF(1.5025,29.1), QPointF(1.54424,29.3), QPointF(1.58598,29.3), QPointF(1.62771,29.4), QPointF(1.66945,29.5), QPointF(1.71119,29.6), QPointF(1.75292,29.7), QPointF(1.79466,29.7), QPointF(1.83639,29.7), QPointF(1.87813,29.7), QPointF(1.91987,29.7), QPointF(1.9616,29.7), QPointF(2.00334,29.7), QPointF(2.04508,28), QPointF(2.08681,26.1), QPointF(2.12855,23.7), QPointF(2.17028,19.8), QPointF(2.21202,14), QPointF(2.25376,9.8), QPointF(2.29549,6.9), QPointF(2.33723,4.8), QPointF(2.37896,3.2), QPointF(2.4207,2.1), QPointF(2.46244,1.3), QPointF(2.50417,0.8), QPointF(2.54591,0.5), QPointF(2.58765,0.3), QPointF(2.62938,0.1), QPointF(2.67112,0), QPointF(2.71285,0),
    QPointF(2.75459,0), QPointF(2.79633,0), QPointF(2.83806,0), QPointF(2.8798,0), QPointF(2.92154,0), QPointF(2.96327,0), QPointF(3.00501,0), QPointF(3.04674,0), QPointF(3.08848,0), QPointF(3.13022,0), QPointF(3.17195,0), QPointF(3.21369,0), QPointF(3.25543,0), QPointF(3.29716,0), QPointF(3.3389,0),
    QPointF(3.38063,0), QPointF(3.42237,0.9), QPointF(3.46411,2.7), QPointF(3.50584,8.3), QPointF(3.54758,14.5), QPointF(3.58932,19.9), QPointF(3.63105,21.8), QPointF(3.67279,23.5), QPointF(3.71452,25.4), QPointF(3.75626,26.6), QPointF(3.798,27.4), QPointF(3.83973,27.7), QPointF(3.88147,28.1), QPointF(3.92321,28.4), QPointF(3.96494,28.6), QPointF(4.00668,28.9), QPointF(4.04841,29), QPointF(4.09015,29.1), QPointF(4.13189,29.1), QPointF(4.17362,29.3), QPointF(4.21536,29.3), QPointF(4.2571,29.4), QPointF(4.29883,29.5), QPointF(4.34057,29.6), QPointF(4.3823,29.7), QPointF(4.42404,29.7), QPointF(4.46578,29.7), QPointF(4.50751,29.7), QPointF(4.54925,29.7), QPointF(4.59098,29.7), QPointF(4.63272,29.7), QPointF(4.67446,28), QPointF(4.71619,26.1), QPointF(4.75793,23.7), QPointF(4.79967,19.8), QPointF(4.8414,14), QPointF(4.88314,9.8), QPointF(4.92487,6.9), QPointF(4.96661,4.8), QPointF(5.00835,3.2), QPointF(5.05008,2.1), QPointF(5.09182,1.3), QPointF(5.13356,0.8), QPointF(5.17529,0.5), QPointF(5.21703,0.3), QPointF(5.25876,0.1), QPointF(5.3005,0), QPointF(5.34224,0),
    QPointF(5.38397,0), QPointF(5.42571,0), QPointF(5.46745,0), QPointF(5.50918,0), QPointF(5.55092,0), QPointF(5.59265,0), QPointF(5.63439,0), QPointF(5.67613,0), QPointF(5.71786,0), QPointF(5.7596,0), QPointF(5.80134,0), QPointF(5.84307,0), QPointF(5.88481,0), QPointF(5.92654,0), QPointF(5.96828,0), QPointF(6.01002,0), QPointF(6.05175,0), QPointF(6.09349,0),
    QPointF(6.13523,0), QPointF(6.17696,0), QPointF(6.2187,0.1), QPointF(6.26043,2.7), QPointF(6.30217,8.3), QPointF(6.34391,11.4), QPointF(6.38564,17.5), QPointF(6.42738,19.9), QPointF(6.46912,23.4), QPointF(6.51085,24.5), QPointF(6.55259,25.8), QPointF(6.59432,26.9), QPointF(6.63606,27.5), QPointF(6.6778,27.9), QPointF(6.71953,28.2), QPointF(6.76127,28.6), QPointF(6.80301,28.9), QPointF(6.84474,29), QPointF(6.88648,29.1), QPointF(6.92821,29.1), QPointF(6.96995,29.3), QPointF(7.01169,29.3), QPointF(7.05342,29.4), QPointF(7.09516,29.5), QPointF(7.13689,29.5), QPointF(7.17863,29.6), QPointF(7.22037,29.7), QPointF(7.2621,29.7), QPointF(7.30384,29.7), QPointF(7.34558,29.7), QPointF(7.38731,29.7), QPointF(7.42905,29.7), QPointF(7.47078,29.2), QPointF(7.51252,27.4), QPointF(7.55426,25.5), QPointF(7.59599,22.8), QPointF(7.63773,17.8), QPointF(7.67947,12.5), QPointF(7.7212,8.7), QPointF(7.76294,5.4), QPointF(7.80467,3.7), QPointF(7.84641,2.4), QPointF(7.88815,1.5), QPointF(7.92988,1), QPointF(7.97162,0.6), QPointF(8.01336,0.3), QPointF(8.05509,0.1), QPointF(8.09683,0),
    QPointF(8.13856,0), QPointF(8.1803,0), QPointF(8.22204,0), QPointF(8.26377,0), QPointF(8.30551,0), QPointF(8.34725,0), QPointF(8.38898,0), QPointF(8.43072,0), QPointF(8.47245,0), QPointF(8.51419,0), QPointF(8.55593,0), QPointF(8.59766,0), QPointF(8.6394,0), QPointF(8.68114,0), QPointF(8.72287,0), QPointF(8.76461,0), QPointF(8.80634,0), QPointF(8.84808,0),
    QPointF(8.88982,0), QPointF(8.93155,0.9), QPointF(8.97329,2.7), QPointF(9.01503,8.3), QPointF(9.05676,14.5), QPointF(9.0985,19.9), QPointF(9.14023,21.8), QPointF(9.18197,23.5), QPointF(9.22371,25.4), QPointF(9.26544,26.6), QPointF(9.30718,27.4), QPointF(9.34891,27.7), QPointF(9.39065,28.1), QPointF(9.43239,28.4), QPointF(9.47412,28.6), QPointF(9.51586,28.9), QPointF(9.5576,29), QPointF(9.59933,29.1), QPointF(9.64107,29.1), QPointF(9.6828,29.3), QPointF(9.72454,29.3), QPointF(9.76628,29.4), QPointF(9.80801,29.5), QPointF(9.84975,29.6), QPointF(9.89149,29.7), QPointF(9.93322,29.7), QPointF(9.97496,29.7), QPointF(10.0167,29.7), QPointF(10.0584,29.7), QPointF(10.1002,29.7), QPointF(10.1419,29.7), QPointF(10.1836,28), QPointF(10.2254,26.1), QPointF(10.2671,23.7), QPointF(10.3088,19.8), QPointF(10.3506,14), QPointF(10.3923,9.8), QPointF(10.4341,6.9), QPointF(10.4758,4.8), QPointF(10.5175,3.2), QPointF(10.5593,2.1), QPointF(10.601,1.3), QPointF(10.6427,0.8), QPointF(10.6845,0.5), QPointF(10.7262,0.3), QPointF(10.7679,0.1), QPointF(10.8097,0), QPointF(10.8514,0),
    QPointF(10.8932,0), QPointF(10.9349,0), QPointF(10.9766,0), QPointF(11.0184,0), QPointF(11.0601,0), QPointF(11.1018,0), QPointF(11.1436,0), QPointF(11.1853,0), QPointF(11.227,0), QPointF(11.2688,0), QPointF(11.3105,0), QPointF(11.3523,0), QPointF(11.394,0), QPointF(11.4357,0), QPointF(11.4775,0), QPointF(11.5192,0), QPointF(11.5609,0), QPointF(11.6027,0),
    QPointF(11.6444,0.1), QPointF(11.6861,0.9), QPointF(11.7279,5.3), QPointF(11.7696,8.3), QPointF(11.8114,14.5), QPointF(11.8531,17.5), QPointF(11.8948,21.7), QPointF(11.9366,23.5), QPointF(11.9783,24.9), QPointF(12.02,26.3), QPointF(12.0618,27.1), QPointF(12.1035,27.7), QPointF(12.1452,28), QPointF(12.187,28.3), QPointF(12.2287,28.6), QPointF(12.2705,28.8), QPointF(12.3122,29), QPointF(12.3539,29.1), QPointF(12.3957,29.1), QPointF(12.4374,29.3), QPointF(12.4791,29.3), QPointF(12.5209,29.4), QPointF(12.5626,29.4), QPointF(12.6043,29.5), QPointF(12.6461,29.6), QPointF(12.6878,29.7), QPointF(12.7295,29.7), QPointF(12.7713,29.7), QPointF(12.813,29.7), QPointF(12.8548,29.7), QPointF(12.8965,29.7), QPointF(12.9382,29.7), QPointF(12.98,28.6), QPointF(13.0217,26.7), QPointF(13.0634,24.6), QPointF(13.1052,21.9), QPointF(13.1469,15.9), QPointF(13.1886,11), QPointF(13.2304,7.8), QPointF(13.2721,5.4), QPointF(13.3139,3.7), QPointF(13.3556,2.4), QPointF(13.3973,1.5), QPointF(13.4391,1), QPointF(13.4808,0.6), QPointF(13.5225,0.3), QPointF(13.5643,0.1), QPointF(13.606,0),
    QPointF(13.6477,0), QPointF(13.6895,0), QPointF(13.7312,0), QPointF(13.773,0), QPointF(13.8147,0), QPointF(13.8564,0), QPointF(13.8982,0), QPointF(13.9399,0), QPointF(13.9816,0), QPointF(14.0234,0), QPointF(14.0651,0), QPointF(14.1068,0), QPointF(14.1486,0), QPointF(14.1903,0), QPointF(14.2321,0), QPointF(14.2738,0), QPointF(14.3155,0), QPointF(14.3573,0),
    QPointF(14.399,0), QPointF(14.4407,0.1), QPointF(14.4825,2.7), QPointF(14.5242,5.3), QPointF(14.5659,11.4), QPointF(14.6077,14.5), QPointF(14.6494,19.9), QPointF(14.6912,21.8), QPointF(14.7329,23.5), QPointF(14.7746,25.4), QPointF(14.8164,26.6), QPointF(14.8581,27.4), QPointF(14.8998,27.7), QPointF(14.9416,28.1), QPointF(14.9833,28.4), QPointF(15.025,28.6), QPointF(15.0668,28.9), QPointF(15.1085,29), QPointF(15.1503,29.1), QPointF(15.192,29.1), QPointF(15.2337,29.3), QPointF(15.2755,29.3), QPointF(15.3172,29.4), QPointF(15.3589,29.5), QPointF(15.4007,29.5), QPointF(15.4424,29.6), QPointF(15.4841,29.7), QPointF(15.5259,29.7), QPointF(15.5676,29.7), QPointF(15.6093,29.7), QPointF(15.6511,29.7), QPointF(15.6928,29.7), QPointF(15.7346,29.7), QPointF(15.7763,28), QPointF(15.818,26.1), QPointF(15.8598,22.8), QPointF(15.9015,17.8), QPointF(15.9432,12.5), QPointF(15.985,8.7), QPointF(16.0267,6.1), QPointF(16.0684,3.7), QPointF(16.1102,2.1), QPointF(16.1519,1.3), QPointF(16.1937,0.8), QPointF(16.2354,0.5), QPointF(16.2771,0.3), QPointF(16.3189,0.1), QPointF(16.3606,0),
    QPointF(16.4023,0), QPointF(16.4441,0), QPointF(16.4858,0), QPointF(16.5275,0), QPointF(16.5693,0), QPointF(16.611,0), QPointF(16.6528,0), QPointF(16.6945,0), QPointF(16.7362,0), QPointF(16.778,0), QPointF(16.8197,0), QPointF(16.8614,0), QPointF(16.9032,0), QPointF(16.9449,0), QPointF(16.9866,0), QPointF(17.0284,0), QPointF(17.0701,0), QPointF(17.1119,0),
    QPointF(17.1536,0.9), QPointF(17.1953,2.7), QPointF(17.2371,8.3), QPointF(17.2788,11.4), QPointF(17.3205,17.5), QPointF(17.3623,19.9), QPointF(17.404,23.4), QPointF(17.4457,24.5), QPointF(17.4875,25.8), QPointF(17.5292,26.9), QPointF(17.571,27.5), QPointF(17.6127,27.9), QPointF(17.6544,28.2), QPointF(17.6962,28.5), QPointF(17.7379,28.7), QPointF(17.7796,29), QPointF(17.8214,29), QPointF(17.8631,29.1), QPointF(17.9048,29.2), QPointF(17.9466,29.3), QPointF(17.9883,29.3), QPointF(18.0301,29.4), QPointF(18.0718,29.5), QPointF(18.1135,29.5), QPointF(18.1553,29.6), QPointF(18.197,29.7), QPointF(18.2387,29.7), QPointF(18.2805,29.7), QPointF(18.3222,29.7), QPointF(18.3639,29.7), QPointF(18.4057,29.7), QPointF(18.4474,29.2), QPointF(18.4891,27.4), QPointF(18.5309,25.5), QPointF(18.5726,21.9), QPointF(18.6144,14), QPointF(18.6561,9.8), QPointF(18.6978,6.9), QPointF(18.7396,4.8), QPointF(18.7813,3.2), QPointF(18.823,2.1), QPointF(18.8648,1.3), QPointF(18.9065,0.8), QPointF(18.9482,0.5), QPointF(18.99,0.3), QPointF(19.0317,0.1), QPointF(19.0735,0), QPointF(19.1152,0),
    QPointF(19.1569,0), QPointF(19.1987,0), QPointF(19.2404,0), QPointF(19.2821,0), QPointF(19.3239,0), QPointF(19.3656,0), QPointF(19.4073,0), QPointF(19.4491,0), QPointF(19.4908,0), QPointF(19.5326,0), QPointF(19.5743,0), QPointF(19.616,0), QPointF(19.6578,0), QPointF(19.6995,0), QPointF(19.7412,0), QPointF(19.783,0), QPointF(19.8247,0), QPointF(19.8664,0),
    QPointF(19.9082,0.9), QPointF(19.9499,2.7), QPointF(19.9917,8.3), QPointF(20.0334,11.4), QPointF(20.0751,17.5), QPointF(20.1169,19.9), QPointF(20.1586,23.4), QPointF(20.2003,24.5), QPointF(20.2421,25.8), QPointF(20.2838,26.9), QPointF(20.3255,27.5), QPointF(20.3673,28), QPointF(20.409,28.4), QPointF(20.4508,28.6), QPointF(20.4925,28.9), QPointF(20.5342,29), QPointF(20.576,29.1), QPointF(20.6177,29.1), QPointF(20.6594,29.3), QPointF(20.7012,29.3), QPointF(20.7429,29.4), QPointF(20.7846,29.5), QPointF(20.8264,29.5), QPointF(20.8681,29.6), QPointF(20.9098,29.7), QPointF(20.9516,29.7), QPointF(20.9933,29.7), QPointF(21.0351,29.7), QPointF(21.0768,29.7), QPointF(21.1185,29.7), QPointF(21.1603,29.7), QPointF(21.202,28), QPointF(21.2437,26.1), QPointF(21.2855,23.7), QPointF(21.3272,19.8), QPointF(21.3689,14), QPointF(21.4107,9.8), QPointF(21.4524,6.9), QPointF(21.4942,4.8), QPointF(21.5359,3.2), QPointF(21.5776,2.1), QPointF(21.6194,1.3), QPointF(21.6611,0.8), QPointF(21.7028,0.5), QPointF(21.7446,0.3), QPointF(21.7863,0.1), QPointF(21.828,0), QPointF(21.8698,0),
    QPointF(21.9115,0), QPointF(21.9533,0), QPointF(21.995,0), QPointF(22.0367,0), QPointF(22.0785,0), QPointF(22.1202,0), QPointF(22.1619,0), QPointF(22.2037,0), QPointF(22.2454,0), QPointF(22.2871,0), QPointF(22.3289,0), QPointF(22.3706,0), QPointF(22.4124,0), QPointF(22.4541,0), QPointF(22.4958,0), QPointF(22.5376,0), QPointF(22.5793,0), QPointF(22.621,0),
    QPointF(22.6628,0.9), QPointF(22.7045,2.7), QPointF(22.7462,8.3), QPointF(22.788,11.4), QPointF(22.8297,17.5), QPointF(22.8715,19.9), QPointF(22.9132,23.4), QPointF(22.9549,24.5), QPointF(22.9967,25.8), QPointF(23.0384,26.9), QPointF(23.0801,27.7), QPointF(23.1219,28), QPointF(23.1636,28.3), QPointF(23.2053,28.6), QPointF(23.2471,28.8), QPointF(23.2888,29), QPointF(23.3306,29.1), QPointF(23.3723,29.1), QPointF(23.414,29.3), QPointF(23.4558,29.3), QPointF(23.4975,29.4), QPointF(23.5392,29.4), QPointF(23.581,29.5), QPointF(23.6227,29.6), QPointF(23.6644,29.7), QPointF(23.7062,29.7), QPointF(23.7479,29.7), QPointF(23.7896,29.7), QPointF(23.8314,29.7), QPointF(23.8731,29.7), QPointF(23.9149,29.7), QPointF(23.9566,27.4), QPointF(23.9983,25.5), QPointF(24.0401,22.8), QPointF(24.0818,17.8), QPointF(24.1235,12.5), QPointF(24.1653,8.7), QPointF(24.207,6.1), QPointF(24.2487,4.2), QPointF(24.2905,2.8), QPointF(24.3322,1.7), QPointF(24.374,1.1), QPointF(24.4157,0.7), QPointF(24.4574,0.4), QPointF(24.4992,0.2), QPointF(24.5409,0.1), QPointF(24.5826,0), QPointF(24.6244,0),
    QPointF(24.6661,0), QPointF(24.7078,0), QPointF(24.7496,0), QPointF(24.7913,0), QPointF(24.8331,0), QPointF(24.8748,0), QPointF(24.9165,0), QPointF(24.9583,0), QPointF(25,0)
};

QMap<DataManager::WAVE_PARAM_TYPE, QVector<QPointF>> sWaveTypeDataMap
{
    {DataManager::WAVE_PARAM_TYPE::WAVE_PAW,  sPawWaveData},
    {DataManager::WAVE_PARAM_TYPE::WAVE_FLOW, sFlowWaveData},
    {DataManager::WAVE_PARAM_TYPE::WAVE_VOL,  sVolWaveData},
    {DataManager::WAVE_PARAM_TYPE::WAVE_CO2,  sCo2WaveData},
};

UiSettingPage::UiSettingPage(QWidget *parent) : FocusWidget(parent)
{
    for (auto &item : sWaveTypeDataMap[DataManager::WAVE_CO2])
    {
        item.setY(UnitManager::GetInstance()->ConvertValue(CO2_CATEGORY,
                                                           U_MMHG,
                                                           U_PERCENT,
                                                           item.y())/100.0);
    }
    InitUi();
    InitUiText();
    InitConnect();        
}

void UiSettingPage::InitUi()
{
    InitBottomWidget();
    InitWaveSetting();
    InitParamSetting();

    QHBoxLayout *topLayout = new QHBoxLayout;
    topLayout->setSpacing(2);
    topLayout->setContentsMargins(2, 2, 2, 2);
    topLayout->addWidget(mWaveSettingItems);
    topLayout->addWidget(mParamSettingItems);

    QVBoxLayout *mainLayout = new QVBoxLayout;
    mainLayout->setSpacing(0);
    mainLayout->setContentsMargins(0, 0, 0, 0);
    mainLayout->addLayout(topLayout);
    mainLayout->addWidget(mBottomWidget);

    StyleSet::SetBackgroundColor(this,COLOR_BLACK_GRAY);
    StyleSet::SetBackgroundColor(mWaveSettingItems,COLOR_BLACK_GRAY);
    StyleSet::SetBackgroundColor(mParamSettingItems,COLOR_BLACK_GRAY);
    StyleSet::SetBackgroundColor(mBottomWidget,COLOR_LIGHT_GRAY);

    setLayout(mainLayout);
}

void UiSettingPage::InitBottomWidget()
{
    mPlimitLineSwitch = new PushButtonCombox;
    mPlimitLineSwitch->SetName(UIStrings::GetStr(ALL_STRINGS_ENUM::STR_PLIMIT_LINE));

    mDrawModeSwitch = new PushButtonCombox;
    mDrawModeSwitch->SetName(UIStrings::GetStr(ALL_STRINGS_ENUM::STR_DRAW_CURVE));

    mSweepSpeedComboBox = new PushButtonCombox;
    mSweepSpeedComboBox->SetName(UIStrings::GetStr(ALL_STRINGS_ENUM::STR_SWEEP_SPEED));

    mRestoreDefaultBtn = new PushButton(UIStrings::GetStr(ALL_STRINGS_ENUM::STR_MAINMENU_DEFAULT));
    mRestoreTip = new TipDialog(this);


    QHBoxLayout *bottomLayout = new QHBoxLayout;
    bottomLayout->setSpacing(10);
    bottomLayout->setContentsMargins(2, 0, 2, 0);
    bottomLayout->addWidget(mPlimitLineSwitch);
    bottomLayout->addWidget(mDrawModeSwitch);
    bottomLayout->addWidget(mSweepSpeedComboBox);
    bottomLayout->addStretch();
    bottomLayout->addWidget(mRestoreDefaultBtn);

    for (int i = 0; i < bottomLayout->count(); i++)
    {
        bottomLayout->setAlignment(bottomLayout->itemAt(i)->widget(), Qt::AlignBottom);
    }

    mBottomWidget = new FocusWidget;
    mBottomWidget->setLayout(bottomLayout);
    mBottomWidget->setFixedHeight(BOOTOM_WIDGET_HEIGHT);
}

void UiSettingPage::InitWaveSetting()
{
    QMap<int, QString> waveStrMap =  {};

    for (int i = DataManager::WAVE_PAW; i <= DataManager::WAVE_CO2; ++i)
    {
        if (DataManager::GetInstance()->GetWaveTypeEnable(i))
            waveStrMap.insert(i, DataManager::GetParamName((DataManager::WAVE_PARAM_TYPE)i));
    }

    QVBoxLayout *waveSettingLayout = new QVBoxLayout;
    waveSettingLayout->setSpacing(2);
    waveSettingLayout->setContentsMargins(0, 0, 0, 0);

    for (int i = 0; i < waveStrMap.size(); i++)
    {
        auto sysSettingId  = static_cast<SystemSettingManager::SYSTEM_SETTING_ENUM>(static_cast<int>(SystemSettingManager::MAIN_WAVE_1) + i);
        auto savingWaveType = static_cast<DataManager::WAVE_PARAM_TYPE>(SettingManager->GetIntSettingValue(sysSettingId));


        auto curComboBox = new WaveComboBox(this); //TBD 35MS 2023.10.20
        mWaveSettingComboBoxs.insert(sysSettingId, curComboBox);

        auto dataModel = new ValueStrIntraData;
        dataModel->SetValueAndStr(waveStrMap);
        curComboBox->setDataModel(dataModel);

        waveSettingLayout->addWidget(curComboBox);

        connect(curComboBox, &WaveComboBox::SignalValueChanged, this, [this](int value){
            auto currentBtn = qobject_cast<WaveComboBox *>(sender());
            auto sysSettingId = mWaveSettingComboBoxs.key(currentBtn);
            SettingManager->SetSettingValue(sysSettingId, value);

            if (ChartManager::GetInstance()->GetIsFreeze())
                ChartManager::GetInstance()->SetFreeze(false);
        });

        connect(SettingManager, &SystemSettingManager::SignalSettingChanged,
                this, &UiSettingPage::SlotInitWaveComboBox);

        if (!waveStrMap.contains(savingWaveType))
            savingWaveType = (DataManager::WAVE_PARAM_TYPE)waveStrMap.lastKey();
        curComboBox->setValue(savingWaveType);
        SlotInitWaveComboBox(sysSettingId);
    }

    mWaveSettingComboBoxs[(int)SystemSettingManager::MAIN_WAVE_1]->setEnabled(false);
    mWaveSettingItems = new FocusWidget;
    mWaveSettingItems->setLayout(waveSettingLayout);
}

void UiSettingPage::InitParamSetting()
{
    QVBoxLayout *paramSettingLayout = new QVBoxLayout;
    paramSettingLayout->setSpacing(5);
    paramSettingLayout->setContentsMargins(0, 0, 0, 0);

    auto pawBlockLayout = new QVBoxLayout;
    pawBlockLayout->setContentsMargins(0, 0, 0, 0);
    pawBlockLayout->setSpacing(0);

    mPawGroupComboBox0 = new ComboBox;
    pawBlockLayout->addWidget(mPawGroupComboBox0);
    mParamComboBoxMap.insert(mPawGroupComboBox0,
                             {SystemSettingManager::VALUE_UPAREA_1, new ValueStrIntraData});
    mPawGroupComboBox0->setDataModel(mParamComboBoxMap[mPawGroupComboBox0].mDataModel);

    mPawGroupComboBox1 = new ComboBox;
    pawBlockLayout->addWidget(mPawGroupComboBox1);
    mParamComboBoxMap.insert(mPawGroupComboBox1,
                             {SystemSettingManager::VALUE_UPAREA_2, new ValueStrIntraData});
    mPawGroupComboBox1->setDataModel(mParamComboBoxMap[mPawGroupComboBox1].mDataModel);

    mPawGroupComboBox2 = new ComboBox;
    pawBlockLayout->addWidget(mPawGroupComboBox2);
    mParamComboBoxMap.insert(mPawGroupComboBox2,
                             {SystemSettingManager::VALUE_UPAREA_3, new ValueStrIntraData});
    mPawGroupComboBox2->setDataModel(mParamComboBoxMap[mPawGroupComboBox2].mDataModel);
    paramSettingLayout->addLayout(pawBlockLayout);


    auto vteBlockLayout = new QVBoxLayout;
    vteBlockLayout->setContentsMargins(0, 0, 0, 0);
    vteBlockLayout->setSpacing(0);

    mVteGroupComboBox0 = new ComboBox;
    vteBlockLayout->addWidget(mVteGroupComboBox0);
    mParamComboBoxMap.insert(mVteGroupComboBox0,
                             {SystemSettingManager::VALUE_MIDAREA_1, new ValueStrIntraData});
    mVteGroupComboBox0->setDataModel(mParamComboBoxMap[mVteGroupComboBox0].mDataModel);

    mVteGroupComboBox1 = new ComboBox;
    vteBlockLayout->addWidget(mVteGroupComboBox1);
    mParamComboBoxMap.insert(mVteGroupComboBox1,
                             {SystemSettingManager::VALUE_MIDAREA_2, new ValueStrIntraData});
    mVteGroupComboBox1->setDataModel(mParamComboBoxMap[mVteGroupComboBox1].mDataModel);

    mVteGroupComboBox2 = new ComboBox;
    vteBlockLayout->addWidget(mVteGroupComboBox2);
    mParamComboBoxMap.insert(mVteGroupComboBox2,
                             {SystemSettingManager::VALUE_MIDAREA_3, new ValueStrIntraData});
    mVteGroupComboBox2->setDataModel(mParamComboBoxMap[mVteGroupComboBox2].mDataModel);

    paramSettingLayout->addLayout(vteBlockLayout);

    auto co2BlockLayout = new QVBoxLayout;
    co2BlockLayout->setContentsMargins(0, 0, 0, 0);
    co2BlockLayout->setSpacing(0);

    mCO2GroupComboxBox0 = new ComboBox;
    co2BlockLayout->addWidget(mCO2GroupComboxBox0);
    mParamComboBoxMap.insert(mCO2GroupComboxBox0,
                             {SystemSettingManager::VALUE_DOWNAREA_1, new ValueStrIntraData});
    mCO2GroupComboxBox0->setDataModel(mParamComboBoxMap[mCO2GroupComboxBox0].mDataModel);

    mCO2GroupComboxBox1 = new ComboBox;
    co2BlockLayout->addWidget(mCO2GroupComboxBox1);
    mParamComboBoxMap.insert(mCO2GroupComboxBox1,
                             {SystemSettingManager::VALUE_DOWNAREA_2, new ValueStrIntraData});
    mCO2GroupComboxBox1->setDataModel(mParamComboBoxMap[mCO2GroupComboxBox1].mDataModel);

    mCO2GroupComboxBox2 = new ComboBox;
    co2BlockLayout->addWidget(mCO2GroupComboxBox2);
    mParamComboBoxMap.insert(mCO2GroupComboxBox2,
                             {SystemSettingManager::VALUE_DOWNAREA_3, new ValueStrIntraData});
    mCO2GroupComboxBox2->setDataModel(mParamComboBoxMap[mCO2GroupComboxBox2].mDataModel);
    paramSettingLayout->addLayout(co2BlockLayout);

    mParamSettingItems = new FocusWidget;
    mParamSettingItems->setFixedWidth(170);
    mParamSettingItems->setLayout(paramSettingLayout);
}

void UiSettingPage::InitValue()
{

}

void UiSettingPage::InitConnect()
{
    connect(mRestoreDefaultBtn, &QPushButton::clicked, mRestoreTip, &TipDialog::show);
    connect(mRestoreTip, &TipDialog::accepted,this,&UiSettingPage::RestoreDefaultUi);
    QList<ComboBox *> keys = mParamComboBoxMap.keys();
    foreach (auto &&comboBox, keys)
    {
        connect(comboBox, &ComboBox::SignalBeginEdit, this, [this]{
            MainWindow::GetInstance()->GetMonitorParam()->HighlightParam(mParamComboBoxMap[(ComboBox *)sender()].mSettingId, true);//TBD 错误用法
        });
        connect(comboBox, &ComboBox::SignalEndEdit, this, [this]{
            MainWindow::GetInstance()->GetMonitorParam()->HighlightParam(mParamComboBoxMap[(ComboBox *)sender()].mSettingId, false);//TBD 错误用法
        });
        connect(comboBox, &ComboBox::SignalValueChanged, this, [this](int value){
            SettingManager->SetSettingValue(mParamComboBoxMap[(ComboBox *)sender()].mSettingId, value);
        });
    }

    connect(mPlimitLineSwitch, &PushButtonCombox::SignalValueChanged, this, [this](int value){
        foreach (auto waveComboBox, mWaveSettingComboBoxs)
        {
            if (waveComboBox->value() == DataManager::WAVE_PAW)
            {
                waveComboBox->Chart()->SetHighLimitLine(15, value ? COLOR_RED : Qt::transparent);
            }
        }
        SettingManager->SetSettingValue(SystemSettingManager::LIMITLINE_SETTING, value);
        ChartManager::GetInstance()->SetPlimitLineEnable(value);
    });


    connect(mDrawModeSwitch, &PushButtonCombox::SignalValueChanged, this, [this](bool checked) {
        SettingManager->SetSettingValue(SystemSettingManager::WAVE_DRAWMODE, checked);
        ChartManager::GetInstance()->SetDrawMode(checked);

        for (auto &&item : mWaveSettingComboBoxs)
        {
            item->Chart()->SetDrawMode(checked);
        }
    });

    connect(mSweepSpeedComboBox, &PushButtonCombox::SignalValueChanged, this, [this](int value) {
        SettingManager->SetSettingValue(SystemSettingManager::WAVE_SPEED, value);
        ChartManager::GetInstance()->SetWaveSpeed(value);
        for (int settingId = SystemSettingManager::MAIN_WAVE_1; settingId <= SystemSettingManager::MAIN_WAVE_4; ++settingId)
            SlotInitWaveComboBox(settingId);
    });
}

void UiSettingPage::RestoreDefaultUi()
{
    foreach (auto &&item, mWaveSettingComboBoxs)
    {
        auto sysSettingId = mWaveSettingComboBoxs.key(item);
        if(item->value() != SettingManager->GetDefaultValue(sysSettingId))
            item->setValue(SettingManager->GetDefaultValue(sysSettingId).toInt());
    }

    QList<ComboBox *> comboBoxList = mParamComboBoxMap.keys();
    foreach (auto &&comboBox, comboBoxList)
    {
        auto sysSettingId = mParamComboBoxMap[comboBox].mSettingId;
        if(comboBox->value() != SettingManager->GetDefaultValue(sysSettingId))
            comboBox->setValue(SettingManager->GetDefaultValue(sysSettingId).toInt());
    }
    mPlimitLineSwitch->setValue(SettingManager->GetDefaultValue(SystemSettingManager::LIMITLINE_SETTING).toInt());
    mDrawModeSwitch->setValue(SettingManager->GetDefaultValue(SystemSettingManager::WAVE_DRAWMODE).toInt());
    mSweepSpeedComboBox->setValue(SettingManager->GetDefaultValue(SystemSettingManager::WAVE_SPEED).toInt());
}

void UiSettingPage::resizeEvent(QResizeEvent *)
{
    foreach (auto combo, mParamComboBoxMap.keys())
    {
        combo->setFixedWidth(mParamSettingItems->width());
        combo->setFixedHeight((mParamSettingItems->height() - 4) / mParamComboBoxMap.size());
    }

    foreach (auto combo, mWaveSettingComboBoxs)
    {
        combo->setFixedWidth(mWaveSettingItems->width());
        combo->setFixedHeight((mWaveSettingItems->height() - 6)/ mWaveSettingComboBoxs.size());
    }
}

void UiSettingPage::InitUiText()
{
    mPlimitLineSwitch->SetName(UIStrings::GetStr(ALL_STRINGS_ENUM::STR_PLIMIT_LINE));
    mDrawModeSwitch->SetName(UIStrings::GetStr(ALL_STRINGS_ENUM::STR_DRAW_CURVE));
    mSweepSpeedComboBox->SetName(UIStrings::GetStr(ALL_STRINGS_ENUM::STR_SWEEP_SPEED));
    mRestoreTip->SetDialogText(UIStrings::GetStr(ALL_STRINGS_ENUM::STR_MAINMENU_DEFAULT) + "？");
    mRestoreDefaultBtn->setText(UIStrings::GetStr(ALL_STRINGS_ENUM::STR_MAINMENU_DEFAULT));


    auto pawStrMap = QMap<int, QString>
    {
        {VALUEDATA_PPEAK, UIStrings::GetStr(ALL_STRINGS_ENUM::STR_VALUEPARAM_PPEAK)},
    };
    mParamComboBoxMap[mPawGroupComboBox0].mDataModel->SetValueAndStr(pawStrMap);
    mPawGroupComboBox0->setValue(VALUEDATA_PPEAK);

    auto pawGroupStrMap = QMap<int, QString>
    {
        {VALUEDATA_PPLAT,UIStrings::GetStr(ALL_STRINGS_ENUM::STR_VALUEPARAM_PPLAT)},
        {VALUEDATA_PEEP, UIStrings::GetStr(ALL_STRINGS_ENUM::STR_VALUEPARAM_PEEP)},
        {VALUEDATA_PMEAN,UIStrings::GetStr(ALL_STRINGS_ENUM::STR_VALUEPARAM_PMEAN)}
    };
    mParamComboBoxMap[mPawGroupComboBox1].mDataModel->SetValueAndStr(pawGroupStrMap);
    mParamComboBoxMap[mPawGroupComboBox2].mDataModel->SetValueAndStr(pawGroupStrMap);

    mPawGroupComboBox1->setValue(SettingManager->
                                 GetIntSettingValue(SystemSettingManager::VALUE_UPAREA_2));
    mPawGroupComboBox2->setValue(SettingManager->
                                 GetIntSettingValue(SystemSettingManager::VALUE_UPAREA_3));

    auto vteGroupStrMap0 = QMap<int, QString>
    {
        {VALUEDATA_VTE, UIStrings::GetStr(ALL_STRINGS_ENUM::STR_VALUEPARAM_VTE)},
    };
    mParamComboBoxMap[mVteGroupComboBox0].mDataModel->SetValueAndStr(vteGroupStrMap0);
    mVteGroupComboBox0->setValue(VALUEDATA_VTE);

    auto vteGroupStrMap = QMap<int, QString>
    {
        {VALUEDATA_VTI, UIStrings::GetStr(ALL_STRINGS_ENUM::STR_VALUEPARAM_VTI)},
        {VALUEDATA_MV, UIStrings::GetStr(ALL_STRINGS_ENUM::STR_VALUEPARAM_MV)},
        {VALUEDATA_MVSPN, UIStrings::GetStr(ALL_STRINGS_ENUM::STR_VALUEPARAM_MVSPN)}
    };
    mParamComboBoxMap[mVteGroupComboBox1].mDataModel->SetValueAndStr(vteGroupStrMap);
    mParamComboBoxMap[mVteGroupComboBox2].mDataModel->SetValueAndStr(vteGroupStrMap);

    mVteGroupComboBox1->setValue(SettingManager->
                                 GetIntSettingValue(SystemSettingManager::VALUE_MIDAREA_2));
    mVteGroupComboBox2->setValue(SettingManager->
                                 GetIntSettingValue(SystemSettingManager::VALUE_MIDAREA_3));



    if (ModuleTestManager::GetInstance()->GetAgModuleType() == None)
    {
        auto co2GroupStrMap0 = QMap<int, QString>
        {
            {VALUEDATA_RATE, UIStrings::GetStr(ALL_STRINGS_ENUM::STR_VALUEPARAM_RATE)},
        };
        mParamComboBoxMap[mCO2GroupComboxBox0].mDataModel->SetValueAndStr(co2GroupStrMap0);
        mCO2GroupComboxBox0->setValue(VALUEDATA_ETCO2);

        auto co2GroupStrMap = QMap<int, QString>
        {
            {VALUEDATA_C, UIStrings::GetStr(ALL_STRINGS_ENUM::STR_VALUEPARAM_C)},
            {VALUEDATA_FSPN, UIStrings::GetStr(ALL_STRINGS_ENUM::STR_VALUEPARAM_FSPN)},
            {VALUEDATA_FIO2, UIStrings::GetStr(ALL_STRINGS_ENUM::STR_VALUEPARAM_FIO2)},
            {VALUEDATA_R, UIStrings::GetStr(ALL_STRINGS_ENUM::STR_VALUEPARAM_R)},
        };
        if (!ConfigManager->GetConfig<bool>(SystemConfigManager::O2_BOOL_INDEX))
        {
            co2GroupStrMap.remove(VALUEDATA_FIO2);
        }
        mParamComboBoxMap[mCO2GroupComboxBox1].mDataModel->SetValueAndStr(co2GroupStrMap);
        mParamComboBoxMap[mCO2GroupComboxBox2].mDataModel->SetValueAndStr(co2GroupStrMap);

        int savingSetting1 = SettingManager->GetIntSettingValue(SystemSettingManager::VALUE_DOWNAREA_2);
        int savingSetting2 = SettingManager->GetIntSettingValue(SystemSettingManager::VALUE_DOWNAREA_3);
        mCO2GroupComboxBox1->setValue(co2GroupStrMap.contains(savingSetting1) ? savingSetting1 : VALUEDATA_C);
        mCO2GroupComboxBox2->setValue(co2GroupStrMap.contains(savingSetting2) ? savingSetting2 : VALUEDATA_R);
    }
    else
    {
        auto co2GroupStrMap0 = QMap<int, QString>
        {
            {VALUEDATA_ETCO2, UIStrings::GetStr(ALL_STRINGS_ENUM::STR_VALUEPARAM_ETCO2)},
        };
        mParamComboBoxMap[mCO2GroupComboxBox0].mDataModel->SetValueAndStr(co2GroupStrMap0);
        mCO2GroupComboxBox0->setValue(VALUEDATA_ETCO2);

        QMap<int, QString> co2GroupStrMap =
        {
            {VALUEDATA_FICO2, UIStrings::GetStr(ALL_STRINGS_ENUM::STR_VALUEPARAM_FICO2)},
            {VALUEDATA_RATE, UIStrings::GetStr(ALL_STRINGS_ENUM::STR_VALUEPARAM_RATE)},
            {VALUEDATA_FIO2, UIStrings::GetStr(ALL_STRINGS_ENUM::STR_VALUEPARAM_FIO2)},
            {VALUEDATA_FSPN, UIStrings::GetStr(ALL_STRINGS_ENUM::STR_VALUEPARAM_FSPN)},
            {VALUEDATA_C, UIStrings::GetStr(ALL_STRINGS_ENUM::STR_VALUEPARAM_C)},
            {VALUEDATA_R, UIStrings::GetStr(ALL_STRINGS_ENUM::STR_VALUEPARAM_R)},
        };
        if (!ConfigManager->GetConfig<bool>(SystemConfigManager::O2_BOOL_INDEX))
        {
            co2GroupStrMap.remove(VALUEDATA_FIO2);
        }
        mParamComboBoxMap[mCO2GroupComboxBox1].mDataModel->SetValueAndStr(co2GroupStrMap);
        mParamComboBoxMap[mCO2GroupComboxBox2].mDataModel->SetValueAndStr(co2GroupStrMap);

        int savingSetting1 = SettingManager->GetIntSettingValue(SystemSettingManager::VALUE_DOWNAREA_2);
        int savingSetting2 = SettingManager->GetIntSettingValue(SystemSettingManager::VALUE_DOWNAREA_3);
        mCO2GroupComboxBox1->setValue(co2GroupStrMap.contains(savingSetting1) ? savingSetting1 : co2GroupStrMap.lastKey());
        mCO2GroupComboxBox2->setValue(co2GroupStrMap.contains(savingSetting2) ? savingSetting2 : co2GroupStrMap.firstKey());
    }


    auto waveSpeedModel = new ValueStrIntraData;
    QMap<int, QString> waveSpeedStrMap
    {
        {0,   UIStrings::GetStr(ALL_STRINGS_ENUM::STR_MAINMENU_WAVESET_LOW_SPEED)},
        {1,   UIStrings::GetStr(ALL_STRINGS_ENUM::STR_MAINMENU_WAVESET_MID_SPEED)},
        {2,   UIStrings::GetStr(ALL_STRINGS_ENUM::STR_MAINMENU_WAVESET_HIGH_SPEED)},
    };
    waveSpeedModel->SetValueAndStr(waveSpeedStrMap);
    mSweepSpeedComboBox->setDataModel(waveSpeedModel);

    auto drawModeDataModel = new ValueStrIntraData;
    QMap<int, QString> drawModeStrMap
    {
        {0,   UIStrings::GetStr(ALL_STRINGS_ENUM::STR_DRAW_LINE)},
        {1,   UIStrings::GetStr(ALL_STRINGS_ENUM::STR_FILL_AREA)},
    };
    drawModeDataModel->SetValueAndStr(drawModeStrMap);
    mDrawModeSwitch->setDataModel(drawModeDataModel);

    auto lineDataModel = new ValueStrIntraData;
    QMap<int, QString> stringMap
    {
        {0,   UIStrings::GetStr(ALL_STRINGS_ENUM::STR_OFF)},
        {1,   UIStrings::GetStr(ALL_STRINGS_ENUM::STR_ON)},
    };

    lineDataModel->SetValueAndStr(stringMap);
    mPlimitLineSwitch->setDataModel(lineDataModel);
    mPlimitLineSwitch->setDumbValue(SettingManager->GetIntSettingValue(SystemSettingManager::LIMITLINE_SETTING));

    mDrawModeSwitch->setValue(SettingManager->GetIntSettingValue(SystemSettingManager::WAVE_DRAWMODE));
    mSweepSpeedComboBox->setValue(SettingManager->GetIntSettingValue(SystemSettingManager::WAVE_SPEED));

    QMap<int, QString> waveStrMap =  {};
    for (int i = DataManager::WAVE_PAW; i <= DataManager::WAVE_CO2; ++i)
    {
        if (DataManager::GetInstance()->GetWaveTypeEnable(i))
            waveStrMap.insert(i, DataManager::GetParamName((DataManager::WAVE_PARAM_TYPE)i));
    }
    for (int i = 0; i < waveStrMap.size(); i++)
    {
        auto dataModel = new ValueStrIntraData;
        dataModel->SetValueAndStr(waveStrMap);
        auto sysSettingId  = static_cast<SystemSettingManager::SYSTEM_SETTING_ENUM>(static_cast<int>(SystemSettingManager::MAIN_WAVE_1) + i);
        auto savingWaveType = static_cast<DataManager::WAVE_PARAM_TYPE>(SettingManager->GetIntSettingValue(sysSettingId));
        mWaveSettingComboBoxs[sysSettingId]->setDataModel(dataModel);
        mWaveSettingComboBoxs[sysSettingId]->setValue(savingWaveType);
    }
}

void UiSettingPage::CloseDialog()
{
    mRestoreTip->close();
}

void UiSettingPage::SlotInitWaveComboBox(int settingId)
{
    if (settingId == SystemSettingManager::MAIN_WAVE_1 || settingId == SystemSettingManager::MAIN_WAVE_2 ||
            settingId == SystemSettingManager::MAIN_WAVE_3 || settingId == SystemSettingManager::MAIN_WAVE_4)
    {
        if (!mWaveSettingComboBoxs.contains(settingId))
            return;
        auto value = SettingManager->GetIntSettingValue(settingId);
        auto item = mWaveSettingComboBoxs[settingId];

        ChartManager::GetInstance()->InitChart(ChartManager::CHART_TYPE_ENUM::WAVE_CHART,
                                               value,
                                               item->Chart());

        item->Chart()->SetYUnit("");
        item->Chart()->SetXUnit("");

        if (value == DataManager::WAVE_PAW && mPlimitLineSwitch->value())
        {
            item->Chart()->SetHighLimitLine(15,  COLOR_RED);
        }

        int needCnt = 200;
        switch (ChartManager::GetInstance()->GetWaveSpeed())
        {
        case ChartManager::SPEED_LEVEL_LOW:
            needCnt = 400;
            break;
        case ChartManager::SPEED_LEVEL_MID:
            needCnt = 300;
            break;
        case ChartManager::SPEED_LEVEL_HIGH:
            needCnt = 200;
            break;
        default:
            DebugAssert(0);
            break;
        }

        QVector<QPointF> buf = sWaveTypeDataMap[(DataManager::WAVE_PARAM_TYPE)value].mid(0, needCnt - 1);
        auto scalesPair = ChartManager::GetInstance()->GetAppropriateScale(ChartManager::WAVE_CHART,
                                                                           value,
                                                                           buf);
        QVector<QPair<qreal, QColor>> dataVec;
        foreach (auto &p, buf)
        {
            dataVec.append(qMakePair(p.y(), ChartManager::GetWaveColor((DataManager::WAVE_PARAM_TYPE)value)));
        }
        item->Chart()->SetYScale(scalesPair.second);
        item->Chart()->SetXScale(scalesPair.first);
        item->Chart()->setXScaleVisable(false);
        item->Chart()->setYScaleVisable(false);
        item->Chart()->SetDrawMode(mDrawModeSwitch->value());
        item->Chart()->ClearData();
        item->Chart()->SetDataNumPerWindow(needCnt);
        item->Chart()->ReplaceData(dataVec);
    }
    foreach (auto waveComboBox, mWaveSettingComboBoxs)
    {
        if (waveComboBox->value() == DataManager::WAVE_PAW)
            waveComboBox->Chart()->SetHighLimitLine(15, mPlimitLineSwitch->value() ? COLOR_RED : Qt::transparent);
    }
}

WaveComboBox::WaveComboBox(QWidget *parent) : ComboBox(parent, false)
{
    InitUi();
}

WaveChart *WaveComboBox::Chart()
{
    return mChart;
}

void WaveComboBox::InitUi()
{
    mChart = new WaveChart(this);
    auto mainLayout = new QHBoxLayout;
    mainLayout->setContentsMargins(1, 1, 1, 1);
    mainLayout->addWidget(mChart);
    setLayout(mainLayout);
    NormalStyle();
}

void WaveComboBox::DisableStyle()
{
    ComboBox::DisableStyle();
    StyleSet::SetBackgroundColor(mChart,COLOR_GRAY);
    mChart->Redraw();
}

void WaveComboBox::EditStyle()
{
    ComboBox::EditStyle();
    StyleSet::SetBackgroundColor(mChart,COLOR_DARK_GRAY);
    mChart->Redraw();
}

void WaveComboBox::NormalStyle()
{
    ComboBox::NormalStyle();
    StyleSet::SetBackgroundColor(mChart,COLOR_BLACK);
    mChart->Redraw();
}

void WaveComboBox::FocusStyle()
{
    ComboBox::FocusStyle();
    StyleSet::SetBackgroundColor(mChart,COLOR_BLACK);
    mChart->Redraw();
}
