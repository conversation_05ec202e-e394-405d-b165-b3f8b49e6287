﻿#ifndef VERSIONINFOPAGE_H
#define VERSIONINFOPAGE_H

#include <FocusWidget.h>
#include <TableButton.h>

#include "TableWidget.h"

enum VERSION_TABLE_ITEM_ENUM
{
    MAIN_CONTORL_BOARD,
    MONITOR_BOARD,
    U_BOOT,
    LINUX_FILE_SYSTEM,
    LINUX_KERNEL,
    FLOWMETER_BOARD,
    POWER_BOARD,
    FLOWMETER_GAS_CONFIG,
    FLOWMETER_SENSOR_TYPE,
    DAEMON_VERSION,
    AG_MODULE_TYPE,
    PRODUCT_MODE,
    VERSION_TABLE_ITEM_ENUM_END
};


class VersionInfoPage : public FocusWidget
{
public:
    VersionInfoPage(QWidget* parent = NULL);
    void InitUiText();

private:
    void InitUi();
    void InitVersionModel();

private:
    TableButton *mModeSwitch{};
    ValueStrIntraData *mModeIntraData{};
    TableWidget *mVersionInfoTable{};
    QStandardItemModel *mVersionInfoModel{};
};

#endif // VERSIONINFOPAGE_H
