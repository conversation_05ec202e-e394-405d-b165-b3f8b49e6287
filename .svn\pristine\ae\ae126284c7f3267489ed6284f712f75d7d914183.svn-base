﻿#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QHBoxLayout>
#include <QSizePolicy>
#include <QMap>
#include <QtDebug>

#include "UiApi.h"
#include "ParamLabelLayoutHorizontal.h"


static constexpr unsigned int MINIMUM_WIDTH = 110;

const QMap<int, ParamLabelLayoutHorizontal::LabelScaleInfoSt> ParamLabelLayoutHorizontal::sScaleInfoMap
{
    { ParamLabelLayoutHorizontal::LABEL_SCALE_ENUM::MIN,    {35, MINIMUM_WIDTH, FONT_S, 28}},
    { ParamLabelLayoutHorizontal::LABEL_SCALE_ENUM::SMALL,  {35, MINIMUM_WIDTH, FONT_M, 30}},
    { ParamLabelLayoutHorizontal::LABEL_SCALE_ENUM::MID,    {40, MINIMUM_WIDTH, FONT_M, 40}},
    { ParamLabelLayoutHorizontal::LABEL_SCALE_ENUM::LARGE,  {48, MINIMUM_WIDTH, FONT_M, 48}},
    { ParamLabelLayoutHorizontal::LABEL_SCALE_ENUM::EXTRA,  {62, MINIMUM_WIDTH, FONT_M, 62}}
};

ParamLabelLayoutHorizontal::ParamLabelLayoutHorizontal(QWidget *parent) : ParamLabelBase(parent)
{
    InitUi();
}

void ParamLabelLayoutHorizontal::InitUi()
{
    //setStyleSheet("border: 1px solid red");
    //mNameLabel->SetTextFontBold(true);
    mHighLimitLabel->SetTextFontBold(true);
    mLowLimitLabel->SetTextFontBold(true);

    QSize limitSize = CalculateLabelTextSize(*mHighLimitLabel, "XXX.");
    mHighLimitLabel->setFixedWidth(limitSize.width());
    mLowLimitLabel->setFixedWidth(limitSize.width());

    QSize nameSize = CalculateLabelTextSize(*mNameLabel, "呼气潮气量");
    mNameLabel->setFixedWidth(nameSize.width());
    
    mNameLabel->setSizePolicy(QSizePolicy::Policy::Fixed, QSizePolicy::Policy::Minimum);
    mUnitLabel->setSizePolicy(QSizePolicy::Policy::Fixed, QSizePolicy::Policy::Minimum);
    mValueLabel->setSizePolicy(QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

    mLayoutLeft = new QVBoxLayout;
    mLayoutLeft->setContentsMargins(0, 0, 0, 0);
    mLayoutLeft->setSpacing(0);
    mLayoutLeft->addWidget(mNameLabel);
    mLayoutLeft->addWidget(mUnitLabel);
    mLayoutLeft->addStretch();
    mNameLabel->setAlignment(Qt::AlignLeft);
    mUnitLabel->setAlignment(Qt::AlignLeft);

    mLayoutRight = new QVBoxLayout;
    mLayoutRight->setContentsMargins(0, 0, 0, 0);
    mLayoutRight->setSpacing(0);
    mLayoutRight->addWidget(mHighLimitLabel);
    mLayoutRight->addStretch();
    mLayoutRight->addWidget(mLowLimitLabel);
    mHighLimitLabel->setAlignment(Qt::AlignHCenter);
    mLowLimitLabel->setAlignment(Qt::AlignHCenter);

    mLayoutMain = new QHBoxLayout;
    mLayoutMain->setContentsMargins(2, 0, 0, 0);
    mLayoutMain->setSpacing(1);
    mLayoutMain->addLayout(mLayoutLeft);
    mLayoutMain->addSpacing(4);
    mLayoutMain->addWidget(mValueLabel);
    mLayoutMain->addLayout(mLayoutRight);
    mLayoutMain->setStretchFactor(mLayoutLeft, 1);
    mLayoutMain->setStretchFactor(mValueLabel, 8);
    mLayoutMain->setStretchFactor(mLayoutRight, 1);
    mValueLabel->setAlignment(Qt::AlignRight | Qt::AlignVCenter);


    StyleSet::SetBackgroundColor(this,COLOR_BLACK);

    StyleSet::SetBackgroundColor(mValueLabel,COLOR_TRANSPARENT);
    StyleSet::SetBackgroundColor(mNameLabel,COLOR_TRANSPARENT);
    StyleSet::SetBackgroundColor(mUnitLabel,COLOR_TRANSPARENT);
    StyleSet::SetBackgroundColor(mLowLimitLabel,COLOR_TRANSPARENT);
    StyleSet::SetBackgroundColor(mHighLimitLabel,COLOR_TRANSPARENT);


    setLayout(mLayoutMain);
}


void ParamLabelLayoutHorizontal::SetLabelScale(int scale)
{
    mCurScale = scale;
    LabelScaleInfoSt st = sScaleInfoMap[scale];
    setFixedHeight(st.fixedHeight);
    setMinimumWidth(st.minimumWidth);

    mNameLabel->SetTextFontSize(st.normalFontSize);
    mUnitLabel->SetTextFontSize(st.normalFontSize);
    mHighLimitLabel->SetTextFontSize(st.normalFontSize);
    mLowLimitLabel->SetTextFontSize(st.normalFontSize);
    mValueLabel->SetTextFontSize(st.valueFontSize);
}

ParamLabelLayoutHorizontalWithAdjust::ParamLabelLayoutHorizontalWithAdjust(QWidget *parent)
        : ParamLabelLayoutHorizontal(parent)
{
    auto limitSize = CalculateLabelTextSize(*mNameLabel, "中中中中");
    mNameLabel->setFixedWidth(limitSize.width()+13);
}

void ParamLabelLayoutHorizontalWithAdjust::SetValue(QString value)
{
    int scale = 0;
    if(value.size() == 4)
        scale = LABEL_SCALE_ENUM::LARGE;
    else
        scale = LABEL_SCALE_ENUM::EXTRA;
    if(mCurScale != scale)
    {
        mCurScale = scale;
        SetLabelScale(mCurScale);
    }
    ParamLabelBase::SetValue(value);
}
