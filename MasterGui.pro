QT += core gui network svg multimedia sql charts concurrent printsupport serialport widgets
DEFINES+=QT_KEYPAD_NAVIGATION
INCLUDEPATH += $$PWD/qwtInclude
message("当前C++编译器类型: $$QMAKE_COMPILER")
win32{
    CONFIG += C++17
    DEFINES+=WIN32
    LIBS += -luser32
    contains(QMAKE_COMPILER, msvc) {
        QMAKE_CFLAGS += /utf-8
        QMAKE_CXXFLAGS += /utf-8
        DEFINES += QWT_DLL
        win32:CONFIG(release, debug|release): LIBS += -L$$PWD/qwtLib/MSVC/ -lqwt
        else:win32:CONFIG(debug, debug|release): LIBS += -L$$PWD/qwtLib/MSVC/ -lqwtd
    }
    else{
        win32:CONFIG(release, debug|release): LIBS += -L$$PWD/qwtLib/MINGW/ -lqwt
        else:win32:CONFIG(debug, debug|release): LIBS += -L$$PWD/qwtLib/MINGW/ -lqwtd
    }
}
else{
    unix:!macx:CONFIG(release, debug|release): LIBS += -L$$PWD/qwtLib/ARM/ -lqwt
    else:unix:!macx:CONFIG(debug, debug|release): LIBS += -L$$PWD/qwtLib/ARM/ -lqwt
    LIBS += -ludev
    DEFINES+=ARM
    QMAKE_CXXFLAGS += -std=c++11
    target.path = /home/<USER>/gui
    INSTALLS += target
    DEFINES+=__ARM_PCS_VFP
    LIBS +=  -lasound
}
RESOURCES += \
    qss/qss.qrc \
    images/images.qrc
DEPENDPATH += . \
     images \
     lang \
     qss \
     rcc_dir \
     src/BaseInclude \
     src \
     src/Com \
     src/message \
     src/soft_config \
     src/UI \
     src/Com/protocol \
     src/Com/Qt_serial \
     src/Com/gpio \
     src/Com/Spi \
     src/Manage/AlarmManger \
     src/Manage/AnesthesiBusiness \
     src/Manage/AnesthesiBusiness/Data \
     src/Manage/calibration \
     src/Manage \
     src/Manage/DeviceManager \
     src/Manage/DBManager \
     src/Manage/Persistence \
     src/Manage/UilManager \
     src/Modules \
     src/Modules/AbstractModule \
     src/Modules/AbstractModule/Communications \
     src/Modules/Calibration \
     src/Modules/Demo \
     src/Modules/DeviceModel \
     src/resource/font \
     src/UI/AlarmMenu \
     src/UI/CalibrationMenu \
     src/UI/Dialog \
     src/UI/EngneerMenu \
     src/UI/General \
     src/UI/GeneralLeft \
     src/UI/Graphs \
     src/UI/MainMenu \
     src/UI/ModesetSheet \
     src/UI/MainWindow/TopTip
INCLUDEPATH += . \
     src/UI/Dialog \
     src/UI \
     src/soft_config \
     src/Com/protocol \
     src/Com \
     src/Com/gpio \
     src/Com/Spi \
     src/Com/Qt_serial \
     src/BaseInclude \
     src/resource/font \
     src/Manage \
     src/Manage/AlarmManger \
     src/Manage/AnesthesiBusiness \
     src/Manage/AnesthesiBusiness/Data \
     src/Manage/calibration \
     src/Manage/DeviceManager \
     src/Manage/DBManager \
     src/Manage/Persistence \
     src/Manage/UilManager \
     src/Modules \
     src/Modules/AbstractModule \
     src/Modules/AbstractModule/Communications \
     src/Modules/Calibration \
     src/Modules/Demo \
     src/Modules/DeviceModel \
     src/UI/AlarmMenu \
     src/UI/ModesetSheet \
     src/UI/EngneerMenu \
     src/message \
     src/UI/CalibrationMenu \
     src/UI/General \
     src/UI/GeneralLeft \
     src/UI/Graphs \
     src/UI/MainMenu \
     src/UI/ControlWidget \
     src/UI/ControlWidget/Button \
     src/UI/ControlWidget/Chart \
     src/UI/ControlWidget/DataModel \
     src/UI/ControlWidget/FoucusWidget \
     src/UI/ControlWidget/ParamLabel \
     src/UI/ControlWidget/PoupupAdapter \
     src/UI/ControlWidget/Series \
     src/UI/ControlWidget/TableWidget \
     src/UI/ControlWidget/TabWidget \
     src/UI/MainWindow/TopTip


TRANSLATIONS += lang_chn.ts

DISTFILES += \
    lang/lang.csv

HEADERS += \
    src/BaseInclude/CustomStyle.h \
    src/BaseInclude/UiAttribute.h \
    src/BaseInclude/base.h \
    src/BaseInclude/queue.h \
    src/Com/Qt4SerialPort/posix_qextserialport.h \
    src/Com/Qt4SerialPort/qextserialbase.h \
    src/Com/Spi/SpiIODevice.h \
    src/Com/Spi/spiuser.h \
    src/Com/TCP/ToolCommProxy.h \
    src/Com/TCP/tcpserver.h \
    src/Com/UploadOwnStatus.h \
    src/Com/gpio/gpio.h \
    src/Com/mymutexlocker.h \
    src/Com/protocol/CRC.h \
    src/Com/protocol/protocol.h \
    src/Manage/AlarmManger/AlarmManager.h \
    src/Manage/AlarmManger/AlarmModeManage.h \
    src/Manage/AnesthesiBusiness/Data/DataLimitManager.h \
    src/Manage/AnesthesiBusiness/Data/DataManager.h \
    src/Manage/AnesthesiBusiness/FlowmeterManager.h \
    src/Manage/AnesthesiBusiness/HistoryEventManager.h \
    src/Manage/AnesthesiBusiness/HlmModeManager.h \
    src/Manage/AnesthesiBusiness/LeakTestManage.h \
    src/Manage/AnesthesiBusiness/ModuleTestManager.h \
    src/Manage/AnesthesiBusiness/RuleManager.h \
    src/Manage/AnesthesiBusiness/RunModeManage.h \
    src/Manage/AnesthesiBusiness/SettingSpinboxManager.h \
    src/Manage/AnesthesiBusiness/TrendDataManager.h \
    src/Manage/AnesthesiBusiness/VentModeSettingManager.h \
    src/Manage/DBManager/AlarmLimitInterface.h \
    src/Manage/DBManager/DBInit.h \
    src/Manage/DBManager/DBManager.h \
    src/Manage/DBManager/HistoryEventDbInterface.h \
    src/Manage/DBManager/LoopHistoryInterface.h \
    src/Manage/DBManager/SystemConfigInterface.h \
    src/Manage/DBManager/SystemSettingInterface.h \
    src/Manage/DBManager/TrendHistoryInterface.h \
    src/Manage/DebugLogManager.h \
    src/Manage/DeviceManager/DeviceManageBase.h \
    src/Manage/DeviceManager/DeviceManage_C.h \
    src/Manage/DeviceManager/DeviceManage_E.h \
    src/Manage/DeviceManager/ManageFactory.h \
    src/Manage/Persistence/ControlButtonManager.h \
    src/Manage/Persistence/SystemSettingManager.h \
    src/Manage/SystemTimeManager.h \
    src/Manage/UilManager/ChartManager.h \
    src/Manage/UilManager/ParamLabelManage.h \
    src/Manage/UilManager/TrendChartManager.h \
    src/Manage/UilManager/UnitManager.h \
    src/Manage/UpgradeManager/UpgradeManager.h \
    src/Manage/calibration/CalModuleManage.h \
    src/Manage/calibration/Co2CalManage.h \
    src/Manage/calibration/FlowZeroCalManage.h \
    src/Manage/calibration/FlowValveCalManage.h \
    src/Manage/calibration/flowmeter_cal_manage.h \
    src/Manage/calibration/o2CalManage.h \
    src/Manage/calibration/peep_valve_cal_manage.h \
    src/Modules/AbstractModule/AGModule.h \
    src/Modules/AbstractModule/CO2Module.h \
    src/Modules/AbstractModule/Communications/BaigeProtocal.h \
    src/Modules/AbstractModule/Communications/HL7.h \
    src/Modules/AbstractModule/Communications/LowerProxyBase.h \
    src/Modules/AbstractModule/Communications/NetManage.h \
    src/Modules/AbstractModule/Communications/ProtocalMsgManage.h \
    src/Modules/AbstractModule/Flowmeter.h \
    src/Modules/AbstractModule/Monitoring.h \
    src/Modules/AbstractModule/VTModel.h \
    src/Modules/Demo/CalDemoModule.h \
    src/Modules/Demo/DemoModules.h \
    src/Modules/Demo/SystemSoundManager.h \
    src/Modules/Demo/wav_format.h \
    src/Modules/DeviceModel/AlarmLed.h \
    src/Modules/DeviceModel/Battery.h \
    src/Modules/DeviceModel/BytteryUiFlush.h \
    src/Modules/DeviceModel/DeviceThread.h \
    src/Modules/DeviceModel/Inputscan.h \
    src/Modules/DeviceModel/Key.h \
    src/Modules/DeviceModel/Knob.h \
    src/Modules/DeviceModel/Lcd.h \
    src/Modules/DeviceModel/Power.h \
    src/Modules/DeviceModel/audio.h \
    src/MyApplication.h \
    src/UI/AlarmMenu/AlarmMenu.h \
    src/UI/AlarmMenu/AlarmRangePage.h \
    src/UI/AlarmMenu/CurAlarmModel.h \
    src/UI/AlarmMenu/CurrentAlarm.h \
    src/UI/AlarmMenu/RangeController.h \
    src/UI/ControlWidget/Button/CfgButton.h \
    src/UI/ControlWidget/Button/ControlButton.h \
    src/UI/ControlWidget/Button/IntraData.h \
    src/UI/ControlWidget/Button/PasswordButton.h \
    src/UI/ControlWidget/Button/PushButton.h \
    src/UI/ControlWidget/Button/RadioButton.h \
    src/UI/ControlWidget/Button/SwitchButton.h \
    src/UI/ControlWidget/Button/TableButton.h \
    src/UI/ControlWidget/Chart/AxisPlus.h \
    src/UI/ControlWidget/Chart/BaseChart.h \
    src/UI/ControlWidget/Chart/ChartCursor.h \
    src/UI/ControlWidget/Chart/LoopChart.h \
    src/UI/ControlWidget/Chart/LoopChartWithSwitcher.h \
    src/UI/ControlWidget/Chart/ScaleStrategy.h \
    src/UI/ControlWidget/Chart/TrendChart.h \
    src/UI/ControlWidget/Chart/WaveChart.h \
    src/UI/ControlWidget/Chart/WaveCurve.h \
    src/UI/ControlWidget/ComboBox.h \
    src/UI/ControlWidget/CountdownProgressBar.h \
    src/UI/ControlWidget/DataModel/WaveDataModel.h \
    src/UI/ControlWidget/DoubleProgressBar.h \
    src/UI/ControlWidget/FoldableText.h \
    src/UI/ControlWidget/FoucusWidget/FocusWidget.h \
    src/UI/ControlWidget/FoucusWidget/TabFocusWidget.h \
    src/UI/ControlWidget/LineDelegate.h \
    src/UI/ControlWidget/ListItemGroup.h \
    src/UI/ControlWidget/MyScrollDrive.h \
    src/UI/ControlWidget/ParamLabel/ParamLabelBase.h \
    src/UI/ControlWidget/ParamLabel/ParamLabelLayoutHorizontal.h \
    src/UI/ControlWidget/ParamLabel/ParamLabelLayoutVertical.h \
    src/UI/ControlWidget/PoupupAdapter/ChoicePopup.h \
    src/UI/ControlWidget/PoupupAdapter/NumberEditPopup.h \
    src/UI/ControlWidget/PoupupAdapter/PopupAdapter.h \
    src/UI/ControlWidget/PoupupAdapter/RichTextStyle.h \
    src/UI/ControlWidget/ResultLabel.h \
    src/UI/ControlWidget/ScrollBar.h \
    src/UI/ControlWidget/Series/DualModeSeries.h \
    src/UI/ControlWidget/Series/MyAreaSeries.h \
    src/UI/ControlWidget/Series/MyLineSeries.h \
    src/UI/ControlWidget/Series/MyLineSeriesItem.h \
    src/UI/ControlWidget/Series/MySeriesItemBase.h \
    src/UI/ControlWidget/SlideController.h \
    src/UI/ControlWidget/TabWidget/BasicTabBar.h \
    src/UI/ControlWidget/TabWidget/HorizontalTabWidget.h \
    src/UI/ControlWidget/TabWidget/VerticalTabWidget.h \
    src/UI/ControlWidget/TableWidget/TableWidget.h \
    src/UI/ControlWidget/Timer.h \
    src/UI/DebugView/FlowerDebugDialog.h \
    src/UI/Dialog/FlowControlDialog.h \
    src/UI/Dialog/HistoryDataDialog.h \
    src/UI/Dialog/PowerOnSelfTest/PreoperativeTestPage.h \
    src/UI/Dialog/PowerOnSelfTest/SelftestDlg.h \
    src/UI/Dialog/PowerOnSelfTest/SysSelftestPage.h \
    src/UI/Dialog/PowerOnSelfTest/SystemSelftestModel.h \
    src/UI/Dialog/PoweroffConfirmDlg.h \
    src/UI/Dialog/StandByPage/CalibrateBlock.h \
    src/UI/Dialog/StandByPage/StandByPageInfoLabel.h \
    src/UI/Dialog/StandByPage/StandbyPage.h \
    src/UI/Dialog/StandByPage/SystemTestBlock.h \
    src/UI/Dialog/TipDialog.h \
    src/UI/EngneerMenu/ConfigCodePage.h \
    src/UI/EngneerMenu/EngineerCalMenu.h \
    src/UI/EngneerMenu/EngneerMenu.h \
    src/UI/EngneerMenu/ExportData.h \
    src/UI/EngneerMenu/FactorySettingsMenu.h \
    src/UI/EngneerMenu/FlowValveCal.h \
    src/UI/EngneerMenu/FlowmeterCal.h \
    src/UI/EngneerMenu/PeepValveCal.h \
    src/UI/EngneerMenu/ProportionalValveCalPage.h \
    src/UI/EngneerMenu/VersionInfoPage.h \
    src/UI/GeneralLeft/AgView.h \
    src/UI/GeneralLeft/FlowmeterView.h \
    src/UI/GeneralLeft/FlowmeterWidget.h \
    src/UI/Graphs/AllParamBoard.h \
    src/UI/Graphs/LoopGraph.h \
    src/UI/Graphs/MainDisplayWidget.h \
    src/UI/HistoryDataDialog/HistoryEventModel.h \
    src/UI/HistoryDataDialog/HistoryEventPage.h \
    src/UI/HistoryDataDialog/LoopCompareView.h \
    src/UI/HistoryDataDialog/LoopDetailView.h \
    src/UI/HistoryDataDialog/LoopPage.h \
    src/UI/HistoryDataDialog/SmallLoopWidget.h \
    src/UI/HistoryDataDialog/TrendGraph.h \
    src/UI/HistoryDataDialog/TrendTable.h \
    src/UI/HistoryDataDialog/TrendTableModel.h \
    src/UI/HotKeyArea/HotKeyButton.h \
    src/UI/HotKeyArea/HotKeyWidget.h \
    src/UI/KeyConfig.h \
    src/UI/MainMenu/CalibrationMenu/CO2CalPage.h \
    src/UI/MainMenu/CalibrationMenu/CalibrationMenu.h \
    src/UI/MainMenu/CalibrationMenu/FlowZeroCalPage.h \
    src/UI/MainMenu/CalibrationMenu/LeakTestPage.h \
    src/UI/MainMenu/CalibrationMenu/O2CalPage.h \
    src/UI/MainMenu/CalibrationMenu/SelftestInfoTable.h \
    src/UI/MainMenu/DisplayCfgMenu/DisplayCfgMenu.h \
    src/UI/MainMenu/DisplayCfgMenu/UiSettingPage.h \
    src/UI/MainMenu/DisplayCfgMenu/VolAndBrightCtrlPage.h \
    src/UI/MainMenu/DisplayCfgMenu/WaveformsSettingPage.h \
    src/UI/MainMenu/EngineerLocker.h \
    src/UI/MainMenu/NetSettingMenu/LocalProtocalSettingPage.h \
    src/UI/MainMenu/NetSettingMenu/NetProtocalSettingPage.h \
    src/UI/MainMenu/NetSettingMenu/ProtocalSettingMenu.h \
    src/UI/MainMenu/SettingsMenu.h \
    src/UI/MainMenu/SystemSettingsMenu/ChangePasswordPage.h \
    src/UI/MainMenu/SystemSettingsMenu/NormalSetPage.h \
    src/UI/MainMenu/SystemSettingsMenu/SoftwareUpdatePage.h \
    src/UI/MainMenu/SystemSettingsMenu/SystemCfgMenu.h \
    src/UI/MainMenu/SystemSettingsMenu/SystemConfigInfoPage.h \
    src/UI/MainWindow/MainWindow.h \
    src/UI/MainWindow/MonitorParamView.h \
    src/UI/MainWindow/TopTip/AlarmArea.h \
    src/UI/MainWindow/TopTip/AlarmAudioPauseIcon.h \
    src/UI/MainWindow/TopTip/BatteryIcon.h \
    src/UI/MainWindow/TopTip/DateTimeWidget.h \
    src/UI/MainWindow/TopTip/MessageBoard.h \
    src/UI/MainWindow/TopTip/RunModeLabel.h \
    src/UI/MainWindow/TopTip/SysInfoBoard.h \
    src/UI/ModesetSheet/HLMModeSheet.h \
    src/UI/ModesetSheet/ModeSetTabWidget.h \
    src/UI/ModesetSheet/ModeSheetBase.h \
    src/UI/ModesetSheet/PCVModeSheet.h \
    src/UI/ModesetSheet/PModeSheet.h \
    src/UI/ModesetSheet/PRCVModeSheet.h \
    src/UI/ModesetSheet/PSVModeSheet.h \
    src/UI/ModesetSheet/SIMV_PCModeSheet.h \
    src/UI/ModesetSheet/SIMV_VCModeSheet.h \
    src/UI/ModesetSheet/SettingSpinbox/ExtSettingSpinbox.h \
    src/UI/ModesetSheet/SettingSpinbox/SettingSpinbox.h \
    src/UI/ModesetSheet/VCVModeSheet.h \
    src/UI/String/Chinese.h \
    src/UI/String/English.h \
    src/UI/String/French.h \
    src/UI/String/StringEnum.h \
    src/UI/String/UIStrings.h \
    src/UI/String/Ukrainian.h \
    src/UI/UiApi.h \
    src/UI/UiConfig.h \
    src/soft_config/SystemConfigManager.h \
    src/soft_config/encryption.h

SOURCES += \
    src/BaseInclude/CustomStyle.cpp \
    src/Com/Qt4SerialPort/posix_qextserialport.cpp \
    src/Com/Qt4SerialPort/qextserialbase.cpp \
    src/Com/Spi/SpiIODevice.cpp \
    src/Com/Spi/spiuser.cpp \
    src/Com/TCP/ToolCommProxy.cpp \
    src/Com/TCP/tcpserver.cpp \
    src/Com/UploadOwnStatus.cpp \
    src/Com/gpio/gpio.cpp \
    src/Com/mymutexlocker.cpp \
    src/Com/protocol/CRC.cpp \
    src/Manage/AlarmManger/AlarmManager.cpp \
    src/Manage/AlarmManger/AlarmModeManage.cpp \
    src/Manage/AnesthesiBusiness/Data/DataLimitManager.cpp \
    src/Manage/AnesthesiBusiness/Data/DataManager.cpp \
    src/Manage/AnesthesiBusiness/FlowmeterManager.cpp \
    src/Manage/AnesthesiBusiness/HistoryEventManager.cpp \
    src/Manage/AnesthesiBusiness/HlmModeManager.cpp \
    src/Manage/AnesthesiBusiness/LeakTestManage.cpp \
    src/Manage/AnesthesiBusiness/ModuleTestManager.cpp \
    src/Manage/AnesthesiBusiness/RuleManager.cpp \
    src/Manage/AnesthesiBusiness/RunModeManage.cpp \
    src/Manage/AnesthesiBusiness/SettingSpinboxManager.cpp \
    src/Manage/AnesthesiBusiness/TrendDataManager.cpp \
    src/Manage/AnesthesiBusiness/VentModeSettingManager.cpp \
    src/Manage/DBManager/AlarmLimitInterface.cpp \
    src/Manage/DBManager/DBInit.cpp \
    src/Manage/DBManager/DBManager.cpp \
    src/Manage/DBManager/HistoryEventDbInterface.cpp \
    src/Manage/DBManager/LoopHistoryInterface.cpp \
    src/Manage/DBManager/SystemConfigInterface.cpp \
    src/Manage/DBManager/SystemSettingInterface.cpp \
    src/Manage/DBManager/TrendHistoryInterface.cpp \
    src/Manage/DebugLogManager.cpp \
    src/Manage/DeviceManager/DeviceManageBase.cpp \
    src/Manage/DeviceManager/DeviceManage_C.cpp \
    src/Manage/DeviceManager/DeviceManage_E.cpp \
    src/Manage/DeviceManager/ManageFactory.cpp \
    src/Manage/Persistence/ControlButtonManager.cpp \
    src/Manage/Persistence/SystemSettingManager.cpp \
    src/Manage/SystemTimeManager.cpp \
    src/Manage/UilManager/ChartManager.cpp \
    src/Manage/UilManager/ParamLabelManage.cpp \
    src/Manage/UilManager/TrendChartManager.cpp \
    src/Manage/UilManager/UnitManager.cpp \
    src/Manage/UpgradeManager/UpgradeManager.cpp \
    src/Manage/calibration/CalModuleManage.cpp \
    src/Manage/calibration/Co2CalManage.cpp \
    src/Manage/calibration/FlowZeroCalManage.cpp \
    src/Manage/calibration/FlowValveCalManage.cpp \
    src/Manage/calibration/flowmeter_cal_manage.cpp \
    src/Manage/calibration/o2CalManage.cpp \
    src/Manage/calibration/peep_valve_cal_manage.cpp \
    src/Modules/AbstractModule/AGModule.cpp \
    src/Modules/AbstractModule/CO2Module.cpp \
    src/Modules/AbstractModule/Communications/BaigeProtocal.cpp \
    src/Modules/AbstractModule/Communications/HL7.cpp \
    src/Modules/AbstractModule/Communications/LowerProxyBase.cpp \
    src/Modules/AbstractModule/Communications/NetManage.cpp \
    src/Modules/AbstractModule/Communications/ProtocalMsgManage.cpp \
    src/Modules/AbstractModule/Flowmeter.cpp \
    src/Modules/AbstractModule/Monitoring.cpp \
    src/Modules/AbstractModule/VTModel.cpp \
    src/Modules/Demo/CalDemoModule.cpp \
    src/Modules/Demo/DemoModules.cpp \
    src/Modules/Demo/SystemSoundManager.cpp \
    src/Modules/DeviceModel/AlarmLed.cpp \
    src/Modules/DeviceModel/Battery.cpp \
    src/Modules/DeviceModel/BytteryUiFlush.cpp \
    src/Modules/DeviceModel/DeviceThread.cpp \
    src/Modules/DeviceModel/Inputscan.cpp \
    src/Modules/DeviceModel/Key.cpp \
    src/Modules/DeviceModel/Knob.cpp \
    src/Modules/DeviceModel/Lcd.cpp \
    src/Modules/DeviceModel/Power.cpp \
    src/Modules/DeviceModel/audio.cpp \
    src/MyApplication.cpp \
    src/UI/AlarmMenu/AlarmMenu.cpp \
    src/UI/AlarmMenu/AlarmRangePage.cpp \
    src/UI/AlarmMenu/CurAlarmModel.cpp \
    src/UI/AlarmMenu/CurrentAlarm.cpp \
    src/UI/AlarmMenu/RangeController.cpp \
    src/UI/ControlWidget/Button/CfgButton.cpp \
    src/UI/ControlWidget/Button/ControlButton.cpp \
    src/UI/ControlWidget/Button/IntraData.cpp \
    src/UI/ControlWidget/Button/PasswordButton.cpp \
    src/UI/ControlWidget/Button/PushButton.cpp \
    src/UI/ControlWidget/Button/RadioButton.cpp \
    src/UI/ControlWidget/Button/SwitchButton.cpp \
    src/UI/ControlWidget/Button/TableButton.cpp \
    src/UI/ControlWidget/Chart/AxisPlus.cpp \
    src/UI/ControlWidget/Chart/BaseChart.cpp \
    src/UI/ControlWidget/Chart/ChartCursor.cpp \
    src/UI/ControlWidget/Chart/LoopChart.cpp \
    src/UI/ControlWidget/Chart/LoopChartWithSwitcher.cpp \
    src/UI/ControlWidget/Chart/ScaleStrategy.cpp \
    src/UI/ControlWidget/Chart/TrendChart.cpp \
    src/UI/ControlWidget/Chart/WaveChart.cpp \
    src/UI/ControlWidget/Chart/WaveCurve.cpp \
    src/UI/ControlWidget/ComboBox.cpp \
    src/UI/ControlWidget/CountdownProgressBar.cpp \
    src/UI/ControlWidget/DataModel/WaveDataModel.cpp \
    src/UI/ControlWidget/DoubleProgressBar.cpp \
    src/UI/ControlWidget/FoldableText.cpp \
    src/UI/ControlWidget/FoucusWidget/FocusWidget.cpp \
    src/UI/ControlWidget/FoucusWidget/TabFocusWidget.cpp \
    src/UI/ControlWidget/LineDelegate.cpp \
    src/UI/ControlWidget/ListItemGroup.cpp \
    src/UI/ControlWidget/MyScrollDrive.cpp \
    src/UI/ControlWidget/ParamLabel/ParamLabelBase.cpp \
    src/UI/ControlWidget/ParamLabel/ParamLabelLayoutHorizontal.cpp \
    src/UI/ControlWidget/ParamLabel/ParamLabelLayoutVertical.cpp \
    src/UI/ControlWidget/PoupupAdapter/ChoicePopup.cpp \
    src/UI/ControlWidget/PoupupAdapter/NumberEditPopup.cpp \
    src/UI/ControlWidget/PoupupAdapter/PopupAdapter.cpp \
    src/UI/ControlWidget/PoupupAdapter/RichTextStyle.cpp \
    src/UI/ControlWidget/ResultLabel.cpp \
    src/UI/ControlWidget/ScrollBar.cpp \
    src/UI/ControlWidget/Series/DualModeSeries.cpp \
    src/UI/ControlWidget/Series/MyAreaSeries.cpp \
    src/UI/ControlWidget/Series/MyLineSeries.cpp \
    src/UI/ControlWidget/Series/MyLineSeriesItem.cpp \
    src/UI/ControlWidget/Series/MySeriesItemBase.cpp \
    src/UI/ControlWidget/SlideController.cpp \
    src/UI/ControlWidget/TabWidget/BasicTabBar.cpp \
    src/UI/ControlWidget/TabWidget/HorizontalTabWidget.cpp \
    src/UI/ControlWidget/TabWidget/VerticalTabWidget.cpp \
    src/UI/ControlWidget/TableWidget/TableWidget.cpp \
    src/UI/ControlWidget/Timer.cpp \
    src/UI/DebugView/FlowerDebugDialog.cpp \
    src/UI/Dialog/FlowControlDialog.cpp \
    src/UI/Dialog/HistoryDataDialog.cpp \
    src/UI/Dialog/PowerOnSelfTest/PreoperativeTestPage.cpp \
    src/UI/Dialog/PowerOnSelfTest/SelftestDlg.cpp \
    src/UI/Dialog/PowerOnSelfTest/SysSelftestPage.cpp \
    src/UI/Dialog/PowerOnSelfTest/SystemSelftestModel.cpp \
    src/UI/Dialog/PoweroffConfirmDlg.cpp \
    src/UI/Dialog/StandByPage/CalibrateBlock.cpp \
    src/UI/Dialog/StandByPage/StandByPageInfoLabel.cpp \
    src/UI/Dialog/StandByPage/StandbyPage.cpp \
    src/UI/Dialog/StandByPage/SystemTestBlock.cpp \
    src/UI/Dialog/TipDialog.cpp \
    src/UI/EngneerMenu/ConfigCodePage.cpp \
    src/UI/EngneerMenu/EngineerCalMenu.cpp \
    src/UI/EngneerMenu/EngneerMenu.cpp \
    src/UI/EngneerMenu/ExportData.cpp \
    src/UI/EngneerMenu/FactorySettingsMenu.cpp \
    src/UI/EngneerMenu/FlowValveCal.cpp \
    src/UI/EngneerMenu/FlowmeterCal.cpp \
    src/UI/EngneerMenu/PeepValveCal.cpp \
    src/UI/EngneerMenu/ProportionalValveCalPage.cpp \
    src/UI/EngneerMenu/VersionInfoPage.cpp \
    src/UI/GeneralLeft/AgView.cpp \
    src/UI/GeneralLeft/FlowmeterView.cpp \
    src/UI/GeneralLeft/FlowmeterWidget.cpp \
    src/UI/Graphs/AllParamBoard.cpp \
    src/UI/Graphs/LoopGraph.cpp \
    src/UI/Graphs/MainDisplayWidget.cpp \
    src/UI/HistoryDataDialog/HistoryEventModel.cpp \
    src/UI/HistoryDataDialog/HistoryEventPage.cpp \
    src/UI/HistoryDataDialog/LoopCompareView.cpp \
    src/UI/HistoryDataDialog/LoopDetailView.cpp \
    src/UI/HistoryDataDialog/LoopPage.cpp \
    src/UI/HistoryDataDialog/SmallLoopWidget.cpp \
    src/UI/HistoryDataDialog/TrendGraph.cpp \
    src/UI/HistoryDataDialog/TrendTable.cpp \
    src/UI/HistoryDataDialog/TrendTableModel.cpp \
    src/UI/HotKeyArea/HotKeyButton.cpp \
    src/UI/HotKeyArea/HotKeyWidget.cpp \
    src/UI/MainMenu/CalibrationMenu/CO2CalPage.cpp \
    src/UI/MainMenu/CalibrationMenu/CalibrationMenu.cpp \
    src/UI/MainMenu/CalibrationMenu/FlowZeroCalPage.cpp \
    src/UI/MainMenu/CalibrationMenu/LeakTestPage.cpp \
    src/UI/MainMenu/CalibrationMenu/O2CalPage.cpp \
    src/UI/MainMenu/CalibrationMenu/SelftestInfoTable.cpp \
    src/UI/MainMenu/DisplayCfgMenu/DisplayCfgMenu.cpp \
    src/UI/MainMenu/DisplayCfgMenu/UiSettingPage.cpp \
    src/UI/MainMenu/DisplayCfgMenu/VolAndBrightCtrlPage.cpp \
    src/UI/MainMenu/DisplayCfgMenu/WaveformsSettingPage.cpp \
    src/UI/MainMenu/EngineerLocker.cpp \
    src/UI/MainMenu/NetSettingMenu/LocalProtocalSettingPage.cpp \
    src/UI/MainMenu/NetSettingMenu/NetProtocalSettingPage.cpp \
    src/UI/MainMenu/NetSettingMenu/ProtocalSettingMenu.cpp \
    src/UI/MainMenu/SettingsMenu.cpp \
    src/UI/MainMenu/SystemSettingsMenu/ChangePasswordPage.cpp \
    src/UI/MainMenu/SystemSettingsMenu/NormalSetPage.cpp \
    src/UI/MainMenu/SystemSettingsMenu/SoftwareUpdatePage.cpp \
    src/UI/MainMenu/SystemSettingsMenu/SystemCfgMenu.cpp \
    src/UI/MainMenu/SystemSettingsMenu/SystemConfigInfoPage.cpp \
    src/UI/MainWindow/MainWindow.cpp \
    src/UI/MainWindow/MonitorParamView.cpp \
    src/UI/MainWindow/TopTip/AlarmArea.cpp \
    src/UI/MainWindow/TopTip/AlarmAudioPauseIcon.cpp \
    src/UI/MainWindow/TopTip/BatteryIcon.cpp \
    src/UI/MainWindow/TopTip/DateTimeWidget.cpp \
    src/UI/MainWindow/TopTip/MessageBoard.cpp \
    src/UI/MainWindow/TopTip/RunModeLabel.cpp \
    src/UI/MainWindow/TopTip/SysInfoBoard.cpp \
    src/UI/ModesetSheet/HLMModeSheet.cpp \
    src/UI/ModesetSheet/ModeSetTabWidget.cpp \
    src/UI/ModesetSheet/ModeSheetBase.cpp \
    src/UI/ModesetSheet/PCVModeSheet.cpp \
    src/UI/ModesetSheet/PModeSheet.cpp \
    src/UI/ModesetSheet/PRCVModeSheet.cpp \
    src/UI/ModesetSheet/PSVModeSheet.cpp \
    src/UI/ModesetSheet/SIMV_PCModeSheet.cpp \
    src/UI/ModesetSheet/SIMV_VCModeSheet.cpp \
    src/UI/ModesetSheet/SettingSpinbox/ExtSettingSpinbox.cpp \
    src/UI/ModesetSheet/SettingSpinbox/SettingSpinbox.cpp \
    src/UI/ModesetSheet/VCVModeSheet.cpp \
    src/UI/String/UIStrings.cpp \
    src/UI/UiApi.cpp \
    src/UI/UiConfig.cpp \
    src/main.cpp \
    src/soft_config/RSA.cpp \
    src/soft_config/SystemConfigManager.cpp \
    src/soft_config/encryption.cpp \
    src/soft_config/random.cpp \
    src/start.cpp





