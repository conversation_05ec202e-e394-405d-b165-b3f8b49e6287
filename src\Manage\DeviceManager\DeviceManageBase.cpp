﻿#include <QKeyEvent>
#include <QtConcurrent>
#include <climits>
#ifdef ARM
#include <libudev.h>
#endif
#include "mymutexlocker.h"
#include "DebugLogManager.h"
#include "ManageFactory.h"
#include "AlarmManager.h"
#include "AlarmLed.h"
#include "Lcd.h"
#include "Battery.h"
#include "Key.h"
#include "Knob.h"
#include "audio.h"
#include "Inputscan.h"
#include "KeyConfig.h"
#include "SystemSoundManager.h"
#include "SystemSettingManager.h"
#include "gpio.h"
#include "RunModeManage.h"
#include "HistoryEventManager.h"
#include "../TCP/ToolCommProxy.h"

#define DELAY_SHUTDOWN_TIME 250
//#define OPEN_LED_LOG
bool DeviceManageBase::SetLedStatu(int ledId,int type,int blinkOnM,int blinkOffMs)
{
    if(ledId>=LED_RED&&ledId<LED_TYPE_MAX)
    {
        if(type>=LED_OP_OPEN&&type<=LED_OP_BLINK)
        {
#ifdef OPEN_LED_LOG
            if(ledId==LED_RED||ledId==LED_YELLOW)
                DebugLog<<"set led id:"<<ledId<<"statu "<<type;
#endif
            mMutex.lock();
            if(!mAllLedObject.contains(ledId))
                mAllLedObject[ledId].mCurState = LED_STATU_OFF;
            mAllLedObject[ledId].mCntrolState = type;
            mAllLedObject[ledId].mBlinkOffMs = blinkOffMs;
            mAllLedObject[ledId].mBlinkOnMs = blinkOnM;
            mMutex.unlock();
            return true;
        }
    }
    return  false;

}

void DeviceManageBase::ShutdownCheck()
{
    static bool powerCheckLast = false;
    if(ToolCommProxy::GetInstance()->GetCurIsLoading())
        return;
    auto isOpenShutDown = GetPowerState();
    if(isOpenShutDown)
    {
        if(powerCheckLast != isOpenShutDown && mIsDelayShutDown)
        {
            QMetaObject::invokeMethod(
                        RunModeManage::GetInstance(),
                        "ActiveRunMode",
                        Qt::QueuedConnection,
                        Q_ARG(E_RUNMODE, E_RUNMODE::MODE_RUN_SHUTDOWN_DELAY));

            emit SignalShundownStateChanged(SHUTDOWN_STATE::ON_COUNTDOWN);
        }
        else if(powerCheckLast != isOpenShutDown && !mIsDelayShutDown)
        {
            PowerDelayOn();
            HistoryEventManager::GetInstance()->AddHistoryEvent(HistoryEventManager::HISTORY_EVENT_TYPE::OPERATE_EVENT, LOG_SHUT_DOWN);
            QTimer::singleShot(DELAY_SHUTDOWN_TIME,this,[=](){
                PowerDelayOff();
            });
        }
    }
    else
    {
        if(powerCheckLast != isOpenShutDown)
        {
            QMetaObject::invokeMethod(
                        RunModeManage::GetInstance(),
                        "InactiveRunMode",
                        Qt::QueuedConnection,
                        Q_ARG(E_RUNMODE, E_RUNMODE::MODE_RUN_SHUTDOWN_DELAY));

            emit SignalShundownStateChanged(SHUTDOWN_STATE::SHUTDOWN_NORMAL);
        }
    }
    powerCheckLast = isOpenShutDown;
}

bool DeviceManageBase::SetLcdBrightness(int value)
{
    //无需内部在音量设置成功前调用
    //SettingManager->SetSettingValue(SystemSettingManager::LCD_BRIGHTNESS_SETTING, value);
    return lcd_backlight_brightness(value);
}

void DeviceManageBase::SetLcdStatu(bool isOpen)
{
    isOpen?lcd_backlight_switch(0):lcd_backlight_switch(1);
}

bool DeviceManageBase::SetEnableAudio(bool isEnable)
{
    set_audio_mute(isEnable?1:0);
    return true;
}

void DeviceManageBase::SetVolume(int SetStep)
{
    SystemSoundManager::GetInstance()->setVolumeByLevel(SetStep);
}

int DeviceManageBase::GetVolume()
{
    return SystemSoundManager::GetInstance()->getSetVolume();
}

void DeviceManageBase::TestVolume(int SetStep)
{
    SystemSoundManager::GetInstance()->TestSound(SetStep, SystemSoundManager::TEST);
}

void DeviceManageBase::MaxVolumePlay()
{
    SystemSoundManager::GetInstance()->PlayByMaxLevel(SystemSoundManager::KNOB_PRESS);
}

void DeviceManageBase::PasuePlayKnobSound(bool isPasue)
{
    SystemSoundManager::GetInstance()->SetIsPasuePlayKnobSound(isPasue);
}

void DeviceManageBase::PasuePlayAlarm(bool isPasue)
{
    if(isPasue)
        SystemSoundManager::GetInstance()->pausePlayAlarm();
    else if(!mIsStopPlayAlarmAudio)
        SystemSoundManager::GetInstance()->recoverPlayAlarm();
}

void DeviceManageBase::StopPlayAlarm(bool isStop)
{
    mIsStopPlayAlarmAudio = isStop;
    PasuePlayAlarm(mIsStopPlayAlarmAudio);
}

void DeviceManageBase::PasuePlayTestSound(bool isPasue)
{
    SystemSoundManager::GetInstance()->SetIsPasueTest(isPasue);
}

bool DeviceManageBase::IsPlayAlarm()
{
    return SystemSoundManager::GetInstance()->IsPlayAlarmSound();
}

void DeviceManageBase::PlayAlarmAudio(short alarmPrio)
{
    switch (alarmPrio)
    {
    case ALARMPRIO_HIG:
        SystemSoundManager::GetInstance()->set_sound_level(SOUND_LEVEL_3);
        break;
    case ALARMPRIO_MID:
        SystemSoundManager::GetInstance()->set_sound_level(SOUND_LEVEL_2);
        break;
    case ALARMPRIO_LOW:
        SystemSoundManager::GetInstance()->set_sound_level(SOUND_LEVEL_1);
        break;
    default:
        SystemSoundManager::GetInstance()->set_sound_level(SOUND_INVALID_LEVEL);
        break;
    }
}
void DeviceManageBase::playKeyaudio(int type)
{
    SystemSoundManager::GetInstance()->PlayKeySound(type);
}

void DeviceManageBase::OpenDelayShutDown(bool isOpen)
{
    mIsDelayShutDown = isOpen;
    if(isOpen)
    {
        PowerDelayOn();
    }
    else
    {
        PowerDelayOff();
    }

}

QStringList DeviceManageBase::GetCurMouseEventInputStr()
{
    MyMutexLocker locker(&mCheckUSBMutex,__FUNCTION__);
    return mMouseEventInputStr;
}

void DeviceManageBase::ExcuteLedEvent()
{
    static QTimer* timer = new QTimer;
    static bool flag = true;
    if(flag)
    {
        timer->setInterval(INT_MAX);
        timer->start();
        flag=false;
    }
    mMutex.lock();
    for(QMap<int,LedObject>::iterator ite=mAllLedObject.begin();ite!=mAllLedObject.end();ite++)
    {
        LedObject tempLed = ite.value();
        switch (tempLed.mCntrolState) {
        case LED_OP_OPEN:
        {
            if(tempLed.mCurState!=LED_STATU_ON)
            {
#ifdef OPEN_LED_LOG
                DebugLog<<"set led id:"<<ite.key()<< true;
#endif
                set_led(ite.key(),true);
                tempLed.mPrevOnTimer = INT_MAX-timer->remainingTime();
                tempLed.mCurState=LED_STATU_ON;
            }
        }
            break;
        case LED_OP_CLOSE:
        {
            if(tempLed.mCurState!=LED_STATU_OFF)
            {
#ifdef OPEN_LED_LOG
                DebugLog<<"set led id:"<<ite.key()<< false;
#endif
                set_led(ite.key(),false);
                tempLed.mPrevOffTimer = INT_MAX-timer->remainingTime();
                tempLed.mCurState=LED_STATU_OFF;
            }
        }
            break;
        case LED_OP_BLINK:
        {
            if(tempLed.mPrevOffTimer==-1&&tempLed.mPrevOnTimer==-1)
            {
                if(tempLed.mCurState!=LED_STATU_ON)
                {
#ifdef OPEN_LED_LOG
                    DebugLog<<"set led id:"<<ite.key()<< true;
#endif
                    set_led(ite.key(),true);
                    tempLed.mPrevOnTimer = INT_MAX-timer->remainingTime();
                    tempLed.mCurState=LED_STATU_ON;
                }
            }
            else
            {
                if(tempLed.mCurState==LED_STATU_ON&&
                        (INT_MAX-timer->remainingTime() - tempLed.mPrevOnTimer)>=tempLed.mBlinkOnMs)
                {
#ifdef OPEN_LED_LOG
                    DebugLog<<"set led id:"<<ite.key()<< false;
#endif
                    set_led(ite.key(),false);
                    tempLed.mPrevOffTimer = INT_MAX-timer->remainingTime();
                    tempLed.mCurState=LED_STATU_OFF;
                }
                else if(tempLed.mCurState==LED_STATU_OFF&&
                        (INT_MAX-timer->remainingTime() - tempLed.mPrevOffTimer)>=tempLed.mBlinkOffMs)
                {
#ifdef OPEN_LED_LOG
                    DebugLog<<"set led id:"<<ite.key()<< true;
#endif
                    set_led(ite.key(),true);
                    tempLed.mPrevOnTimer = INT_MAX-timer->remainingTime();
                    tempLed.mCurState=LED_STATU_ON;
                }
            }
        }
            break;
        default:
            break;
        }
        mAllLedObject[ite.key()]=tempLed;
    }
    mMutex.unlock();
}

DeviceManageBase::DeviceManageBase()
{
    GpioInit();
    int lcdId =SettingManager->GetIntSettingValue(SystemSettingManager::LCD_DEVICE_NAME);
    lcd_init(QString("/sys/class/backlight/backlight.%1/brightness").arg(lcdId).toUtf8().data());
    int brightvalue = SettingManager->GetIntSettingValue(SystemSettingManager::LCD_BRIGHTNESS_SETTING);
    SetLcdBrightness(brightvalue);
    SystemSoundManager::GetInstance();
    QtConcurrent::run(this,&DeviceManageBase::CheckUSBEventThread);
    QtConcurrent::run(this,&DeviceManageBase::ResetartMonitoring);
    int volvalue = SettingManager->GetIntSettingValue(SystemSettingManager::VOLUME_SETTING);
    SetVolume(volvalue);
    PowerDelayOff();

}

DeviceManageBase::~DeviceManageBase()
{
    if(mUsbCheckThread)
    {
        mUsbCheckThread->terminate();
    }
    SystemSoundManager::GetInstance()->requestInterruption();
    SystemSoundManager::GetInstance()->quit();
    SystemSoundManager::GetInstance()->wait();
}
#ifdef ARM
int isRealMouse(struct udev_device *dev) {
    const char *devnode = udev_device_get_devnode(dev);
    const char *vendorID = udev_device_get_property_value(dev, "ID_VENDOR_ID");
    const char *modelID = udev_device_get_property_value(dev, "ID_MODEL_ID");

    return (devnode && vendorID && modelID &&
            strcmp(modelID, "0001") != 0);
}

// Function to check if a device is a USB storage device (U盘)
int isUSBStorageDevice(struct udev_device *dev) {
    const char *devnode = udev_device_get_devnode(dev);
    const char *idBus = udev_device_get_property_value(dev, "ID_BUS");
    const char *idInputMouse = udev_device_get_property_value(dev, "ID_INPUT_MOUSE");
    return (devnode && idBus && strcmp(idBus, "usb") == 0 && !idInputMouse);
}
#endif
void DeviceManageBase::CheckUSBEventThread()
{
#ifdef ARM
    mUsbCheckThread = QThread::currentThread();

    struct udev *udev;
    struct udev_enumerate *enumerate;
    struct udev_list_entry *devices, *entry;

    while(true)
    {
        msleep(200);
        udev = udev_new();
        if (!udev) {
            continue;
            return;
        }

        enumerate = udev_enumerate_new(udev);
        udev_enumerate_add_match_subsystem(enumerate, "input");
        udev_enumerate_add_match_property(enumerate, "ID_INPUT_MOUSE", "1");
        udev_enumerate_add_match_subsystem(enumerate, "block");
        udev_enumerate_add_match_property(enumerate, "DEVTYPE", "disk");
        udev_enumerate_scan_devices(enumerate);

        devices = udev_enumerate_get_list_entry(enumerate);
        bool prevIsHaveUsb=mIsHaveUsbFile;
        mIsHaveUsbFile =false;
        {
            MyMutexLocker locker(&mCheckUSBMutex,__FUNCTION__);
            if(!mMouseEventInputStr.isEmpty())
                mMouseEventInputStr.clear();
        }
        udev_list_entry_foreach(entry, devices)
        {
            const char *path = udev_list_entry_get_name(entry);
            struct udev_device *dev = udev_device_new_from_syspath(udev, path);
            const char *devnode = udev_device_get_devnode(dev);
            if (isRealMouse(dev))
            {
                if (devnode)
                {
                    const char *eventSubstring = "/dev/input/event";
                    const char *eventNumberStr = strstr(devnode, eventSubstring);
                    if (eventNumberStr)
                    {
                        int eventNumber;
                        if (sscanf(eventNumberStr + strlen(eventSubstring), "%d", &eventNumber) == 1)
                        {
                            MyMutexLocker locker(&mCheckUSBMutex,__FUNCTION__);
                            if(mMouseEventInputStr.indexOf(devnode)==-1)
                                mMouseEventInputStr<<devnode;
                        }
                    }
                }
            }
            if (isUSBStorageDevice(dev))
            {
                if (devnode)
                {
                    mIsHaveUsbFile=true;
                }
            }
            udev_device_unref(dev);
        }
        if(prevIsHaveUsb!=mIsHaveUsbFile)
        {
            emit SignalUFileEveent(mIsHaveUsbFile);
            DebugLog<<"emit SignalUFileEveent"<<mIsHaveUsbFile;
        }
        udev_enumerate_unref(enumerate);
        udev_unref(udev);
    }
#endif
}


void DeviceManageBase::ResetartMonitoring()
{
    //重启监控板
    GpioWriteBit(BANK_DOWNLOAD,GPIO_6_NR_23,1);
    GpioWriteBit(BANK_DOWNLOAD,GPIO_6_NR_22,1);
    usleep(1000*100);
    GpioWriteBit(BANK_DOWNLOAD,GPIO_6_NR_22,0);
    usleep(1000*100);
    GpioWriteBit(BANK_DOWNLOAD,GPIO_6_NR_22,1);
}


