﻿#ifndef _SOFT_SETTING_MANAGER_H_
#define _SOFT_SETTING_MANAGER_H_

#define  DEF_LANG    ENGLISH

#include <QVariant>
#include "DBManager/SystemSettingInterface.h"
#include "../Com/protocol/protocol.h"

enum SOFT_MODE
{
    DEMO_MODE = 0,
    RUN_MODE
};

enum E_LANGUAGE
{
    LANGUAGE_BEGIN = 0,
    ENGLISH= LANGUAGE_BEGIN,	///英文
    CHINESE, 	///中文
    RUSSIAN,
    LANGUAGE_MAX
};

enum E_PROTOCAL {
    NONE_PROTOCAL,
    HL7_PROTOCAL,
    BAIGE_PROTOCAL
};

enum E_BAUDRATE {
    RATE14400  = 14400,
    RATE19200  = 19200,
    RATE38400  = 38400,
    RATE56000  = 56000,
    RATE57600  = 57600,
    RATE115200 = 115200
};

enum E_DATABIT {
    BIT5 = 5,
    BIT6 = 6,
    BIT7 = 7,
    BIT8 = 8
};

enum E_STOPBIT{
    BIT1 = 1,
    BIT2 = 2,
    BIT1_5 = 3
};

enum E_PARITY {
    NONE_PARITY = 0,
    EVEN = 2,
    ODD = 3
};

enum E_LIMITLINE {
    HIDE,
    SHOW
};

class SystemSettingManager : public QObject
{
    Q_OBJECT
public:
    enum SYSTEM_SETTING_ENUM
    {
        LANGUAGE_SETTING /*= SYSTEM_SETTING_ENUM_BEGIN*/,
        SOFT_MODE_SETTING,
        VOLUME_SETTING,
        PUNIT_SETTING,      //压力单位设置
        CO2UNIT_SETTING,
        CPB_SETTING,
        TIMEFORMAT_SETTING,
        DATEFORMAT_SETTING,
        SYSTEM_TIME_Y,
        SYSTEM_TIME_M,
        SYSTEM_TIME_D,
        TRIG_SETTING,       //触发方式设置
        SERIALPROTOCAL_SETTING,
        BAUDRATE_SETTING,
        DATABIT_SETTING,
        STOPBIT_SETTING,
        PARITY_SETTING,
        IPADDRESS_SETTING,
        MASK_SETTING,
        GATEWAY_SETTING,
        NETPROTOCAL_SETTING,
        SENDINTERVAL_SETTING,
        DESTINATIONIP_SETTING,
        DESTINATIONPORT_SETTING,
        LOOP_POSITION_LEFT,
        LOOP_POSITION_RIGHT,
        TREND_GRAPH1,
        TREND_GRAPH2,
        TREND_GRAPH3,
        TREND_PERIOD,
        VALUE_UPAREA_1,     //显示设置区域右侧参数部分:上
        VALUE_UPAREA_2,
        VALUE_UPAREA_3,
        VALUE_MIDAREA_1,    //中
        VALUE_MIDAREA_2,
        VALUE_MIDAREA_3,
        VALUE_DOWNAREA_1,   //下
        VALUE_DOWNAREA_2,
        VALUE_DOWNAREA_3,
        MAIN_WAVE_1,        //显示设置区域的曲线类型
        MAIN_WAVE_2,
        MAIN_WAVE_3,
        MAIN_WAVE_4,
        WAVE_SPEED,
        WAVE_DRAWMODE,
        ASPHYXIA_TIME,
        N2O_CONSISTENCY,
        O2_CONSISTENCY,
        AGUNIT_SETTING,
        LCD_DEVICE_NAME,
        FLOW_CONTROL_TYPE,
        FLOW_CONTROL_TOTAL_VALUE,
        FLOW_CONTROL_O2_VALUE,
        FLOW_CONTROL_BANLANCE_TYPE,
        FLOW_CONTROL_BANLANCE_GAS_VALUE,
        LAST_CHK_TIME,
        LAST_CAL_TIME,
        LAST_SYS_TIME,
        LIMITLINE_SETTING,
        MASTER_SOFT_VERSION,
        LCD_BRIGHTNESS_SETTING,
        USER_PASSWORD,
        LAST_MAIN_DISPLAY_LAYOUT,
        LOOP_LOOPDETAIL_POSITION_LEFT,
        LOOP_LOOPDETAIL_POSITION_RIGHT,
        LOOP_AG,
        SEND_SERIAL_INTERVAL_SETTING,
        SYSTEM_SETTING_ENUM_END,
    };Q_ENUM(SYSTEM_SETTING_ENUM); //改

public:
    void Init();
    void RestoreFactorySetting();

    static QVariant GetDefaultValue(int settingId);

    int GetIntSettingValue(int settingId);
    double GetFloatSettingValue(int settingId);
    QString GetStringSettingValue(int settingId);

    void SetSettingValue(int settingId, int context);
    void SetSettingValue(int settingId, double context);
    void SetSettingValue(int settingId, QString context);

    static SystemSettingManager * GetInstance(void);
    static QVector<SystemSettingItemSt> sSettingDefault;

signals:
    void SignalSettingChanged(int);

protected:
    SystemSettingManager();
    ~SystemSettingManager(){}

private:
    QMap<int, SystemSettingItemSt> mCurSettingMap{};
};
#endif
#define SettingManager (SystemSettingManager::GetInstance())
