﻿#include "TrendTable.h"
#include "IntraData.h"
#include "ScrollBar.h"

#include "UiApi.h"
#include "String/UIStrings.h"

#include "UnitManager.h"
#include "SystemConfigManager.h"
#include "ModuleTestManager.h"

#define TABLEVIEW_WIDTH 729
#define TABLEVIEW_HEIGHT 414
#define BOTTOM_WIDGET_HEIGHT 50
#define ORIGINAL_DATA_ROLE (Qt::UserRole+1)
#define SORT_TYPE_ROLE (Qt::UserRole+2)
#define SORT_ALL_ROLE (Qt::UserRole+3)
#define COLUMN_WIDTH 89
#define MIN_COLUMN_COUNT 7
#define ERROR_RANGE 45
void HeaderView::paintSection(QPainter *painter, const QRect &rect, int logicalIndex) const
{
    QAbstractItemModel *mo = model();
    QVariant hData = mo->headerData(logicalIndex, Qt::Vertical, Qt::DisplayRole);
    QString dataStr = hData.toString();


    QTextDocument htmlText;
    htmlText.setHtml(dataStr);
    QAbstractTextDocumentLayout *htmlLayout = htmlText.documentLayout();
    int vTextMargin = (rect.height() - htmlLayout->documentSize().height()) / 2;

    painter->save();

    QVector<int> indexs{};
    for (int i = 0; i < count(); ++i)
    {
        if (isSectionHidden(i) == false)
        {
            indexs.append(i);
        }
    }

    if (indexs.indexOf(logicalIndex) % 2 == 0)
    {
        painter->fillRect(rect, COLOR_LIGHT_GRAY);

        painter->setPen(QPen{COLOR_GRAY, 1});
        painter->drawLine(QLineF(rect.bottomLeft(), rect.bottomRight()));
    }
    else
    {
        painter->fillRect(rect, COLOR_GRAY);
        painter->setPen(QPen{COLOR_GRAY, 1});
        painter->drawLine(QLineF(rect.topLeft(), rect.topRight()));
        painter->drawLine(QLineF(rect.bottomLeft(), rect.bottomRight()));
    }

    painter->setPen(QPen{COLOR_DARK_GRAY , 1});
    painter->drawLine(QLineF(rect.topRight(), rect.bottomRight()));
    painter->translate(rect.left(), rect.top() + vTextMargin);

    QAbstractTextDocumentLayout::PaintContext context;

    htmlLayout->draw(painter, context);
    painter->restore();
}


TrendTable::TrendTable(QWidget *parent) : FocusWidget(parent)
{
    setMouseTracking(true);

    InitUi();
    InitUiText();
    InitScroller();
    InitConnect();
    mParamTypeFilter->setValue(TrendDataModel::ALL);
}

void TrendTable::InitUiText()
{
    auto tmpMap = QMap<int, QString>();
    for (int id = TrendDataModel::ALL; id < TrendDataModel::GROUP_TYPE_MAX; ++id)
    {
        if(id==TrendDataModel::FLOWMETER_PARAM&&ConfigManager->GetConfig<int>(SystemConfigManager::MACHINE_MODEL_INDEX) == CENAR_30M)
            continue;
        else if(id==TrendDataModel::EXTERNAL_MODULE_PARAM && ModuleTestManager::GetInstance()->GetAgModuleType() == None
                && !ConfigManager->GetConfig<int>(SystemConfigManager::O2_BOOL_INDEX))
        {
            continue;
        }
        tmpMap.insert(id,TrendDataModel::GetTypeFilterTypeName((TrendDataModel::DATA_FILTER_TYPE)id));
    }
    mParamTypeDataModel->SetValueAndStr(tmpMap);

    QMap<int, QString> strMap
    {
        {TrendDataModel::INTERVAL_1_MIN,          "1" },
        {TrendDataModel::INTERVAL_5_MIN,          "5"},
        {TrendDataModel::INTERVAL_10_MIN,         "10"},
        {TrendDataModel::INTERVAL_15_MIN,         "15"},
        {TrendDataModel::INTERVAL_30_MIN,         "30"},
        {TrendDataModel::INTERVAL_60_MIN,         "60"},
        {TrendDataModel::INTERVAL_120_MIN,        "120"},
    };
    QMutableMapIterator<int, QString> i(strMap);
    while (i.hasNext())
    {
        i.next();
        i.setValue(i.value() + " " + UIStrings::GetStr(ALL_STRINGS_ENUM::STR_MINUTE));
    }
    mTimeFilterDataModel->SetValueAndStr(strMap);
}


void TrendTable::InitUi()
{
    this->installEventFilter(this);
    mSortFilterModel = new QSortFilterProxyModel(this);

    mTableModel = new TrendDataModel(this);
    mSortFilterModel->setSourceModel(mTableModel);
    mSortFilterModel->setFilterRole(Qt::UserRole);
    mSortFilterModel->setSortRole(Qt::UserRole-1);
    mSortFilterModel->setFilterKeyColumn(0);
    mSortFilterModel->setDynamicSortFilter(true);
    mSortFilterModel->setFilterRegExp(QRegExp("1", Qt::CaseSensitive, QRegExp::RegExp));

    mTableView = new QTableView(this);
    mTableView->installEventFilter(this);
    mTableView->setObjectName("mTableView");
    mTableView->setModel(mSortFilterModel);

    mVerHeaderView = new HeaderView(Qt::Vertical, this);
    mTableView->setVerticalHeader(mVerHeaderView);

    mHorizontalBar = new ScrollBar(Qt::Horizontal, this);
    mHorizontalBar->setTracking(true);

    mVerticalBar = new ScrollBar(Qt::Vertical, this);
    mVerticalBar->setTracking(true);

    mTableView->setSelectionMode(QAbstractItemView::SelectionMode::SingleSelection);
    mTableView->setSelectionBehavior(QAbstractItemView::SelectionBehavior::SelectColumns);
    mTableView->setFrameStyle(QFrame::NoFrame);
    mTableView->setFocusPolicy(Qt::NoFocus);
    mTableView->setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOn);
    mTableView->setVerticalScrollBarPolicy(Qt::ScrollBarAlwaysOn);
    mTableView->setVerticalScrollBar(mVerticalBar);
    mTableView->setHorizontalScrollBar(mHorizontalBar);
    mTableView->setEditTriggers(QAbstractItemView::NoEditTriggers);
    mTableView->verticalHeader()->setEnabled(false);
    mTableView->setAlternatingRowColors(true);
    mTableView->horizontalHeader()->setObjectName("horHeader");
    mTableView->setFixedSize(QSize(TABLEVIEW_WIDTH,TABLEVIEW_HEIGHT));
    mTableView->setFocusPolicy(Qt::NoFocus);

    mParamTypeFilter = new PushButtonCombox;
    mParamTypeDataModel = new ValueStrIntraData;
    mParamTypeFilter->setDataModel(mParamTypeDataModel);

    mZoomTimeFilter = new PushButtonCombox;
    mTimeFilterDataModel = new ValueStrIntraData;
    mZoomTimeFilter->setDataModel(mTimeFilterDataModel);


    auto bottomLayout = new QHBoxLayout;
    bottomLayout->setContentsMargins(5, 0, 5, 0);
    bottomLayout->addWidget(mZoomTimeFilter);
    bottomLayout->addWidget(mParamTypeFilter);
    bottomLayout->addStretch();

    mBottomDocker = new FocusWidget;
    mBottomDocker->setLayout(bottomLayout);
    mBottomDocker->setFixedHeight(BOTTOM_WIDGET_HEIGHT);

    auto mView = new QHBoxLayout;
    mView->setContentsMargins(0, 0, 0, 0);
    mView->setSpacing(0);
    mView->addWidget(mTableView);
    mView->addWidget(mVerticalBar);

    auto mainLayout = new QVBoxLayout;
    mainLayout->setContentsMargins(0, 0, 0, 0);
    mainLayout->setSpacing(0);
    mainLayout->addLayout(mView);
    mainLayout->addWidget(mHorizontalBar);
    mainLayout->addWidget(mBottomDocker);
    setLayout(mainLayout);
    mVerticalBar->setEnable(false);
    //mHorizontalBar->setEnable(false);
}

void TrendTable::InitConnect()
{
    connect(mTableView, &QTableView::clicked, this, [this](const QModelIndex &index){
        if(mCurSelectedCol == index.column())
        {
            mTableView->selectionModel()->clearSelection();
            mCurSelectedCol = -1;
        }
        else
            mCurSelectedCol = index.column();
    });
    connect(mParamTypeFilter, &CfgButton::SignalValueChanged, this,  &TrendTable::SlotOnParamFilterTypeChanged);
    connect(mZoomTimeFilter, &CfgButton::SignalValueChanged, this, &TrendTable::SlotOnTimeFilterTypeChanged);
    connect(mTableModel, &TrendDataModel::SignalDataChange, this,[=](){
        static bool isFirstEnter = true;
        int Value = mHorizontalBar->value();
        int maxValue = mHorizontalBar->maximum();
        int oneClWidth=mTableView->columnWidth(0);
        if(isFirstEnter)
        {
            isFirstEnter = false;
            mHorizontalBar->setValue(mHorizontalBar->maximum());
        }
        if ((Value >= (maxValue - oneClWidth - ERROR_RANGE)))
        {
            if (maxValue != 0)
            {
                mHorizontalBar->triggerAction(ScrollBar::SliderAction::SliderToMaximum);
                mHorizontalBar->setValue(mHorizontalBar->maximum());
                mIsNeedToMoveMaxValue = true;
            }
        }
        else
        {
            mIsNeedToMoveMaxValue = false;
        }
    });
}

void TrendTable::Clear()
{
    mTableModel->Clean();
}

bool TrendTable::eventFilter(QObject *obj, QEvent *ev)
{
    if(ev->type() == QEvent::KeyPress && obj == mTableView)
    {
        QApplication::sendEvent(this, ev);
    }
    if(obj==this)
    {
        if(ev->type()==QEvent::Show)
        {
            mTableModel->SetIsShow(true);
            if(mIsNeedToMoveMaxValue)
            {
                mIsNeedToMoveMaxValue = false;
                mHorizontalBar->triggerAction(ScrollBar::SliderAction::SliderToMaximum);
                mHorizontalBar->setValue(mHorizontalBar->maximum());
            }
        }
        if(ev->type()==QEvent::Hide)
            mTableModel->SetIsShow(false);
    }

    return false;
}

QString TrendTable::ValueToString(qreal value, int displayPrecision)
{
    if (value == INVALID_VALUE)
    {
        return INVALID_VALUE_STR;
    }
    else
    {
        return QString::number(value, 'f', displayPrecision);
    }
}


void TrendTable::resizeEvent(QResizeEvent *event)
{
    auto colWidth = (event->size().width() - mTableView->verticalScrollBar()->width()) / (MIN_COLUMN_COUNT + 1);
    auto rowHeight = (event->size().height() - mTableView->horizontalScrollBar()->height()) / 10;

    mVerHeaderView->setFixedWidth(colWidth);
    mTableView->verticalHeader()->setSectionResizeMode(QHeaderView::Fixed);
    mTableView->verticalHeader()->setDefaultSectionSize(rowHeight);
    mTableView->verticalHeader()->setFixedWidth(colWidth);
    mTableView->horizontalHeader()->setSectionResizeMode(QHeaderView::Fixed);
    mTableView->horizontalHeader()->setDefaultSectionSize(colWidth);
    mTableView->horizontalHeader()->setFixedHeight(rowHeight);
    mTableView->verticalScrollBar()->setSingleStep(rowHeight);
}

void TrendTable::SlotOnTimeFilterTypeChanged(int value)
{
    static double prevTimeType=value;
    int oneClWidth=mTableView->columnWidth(0);
    int prevBarValue = mHorizontalBar->value();

    mTableModel->SetColumnFilterType((TrendDataModel::TIME_FILETER_TYPE)value);
    int cnt = mTableModel->columnCount();
    if(cnt >= MIN_COLUMN_COUNT) //实现左侧恒定（接近）
    {
        double prevBeginDouble= prevBarValue*1.0/oneClWidth;
        int prevBeginCount=prevBeginDouble;
        prevBeginCount=(prevBeginDouble>prevBeginCount)?(prevBeginCount+1):prevBeginCount;
        int newBeginCount = prevBeginCount*(prevTimeType/value);
        int newValue = newBeginCount*oneClWidth;
        mHorizontalBar->setValue(newValue);
    }
    prevTimeType = value;
}



void TrendTable::SlotOnParamFilterTypeChanged(int value)
{
    mTableModel->SetRowFilterType((TrendDataModel::DATA_FILTER_TYPE)value);
    mSortFilterModel->sort(0);
}

void TrendTable::InitScroller()
{
    QScroller *pScroller = QScroller::scroller(mTableView->viewport());
    pScroller->grabGesture(mTableView->viewport(), QScroller::LeftMouseButtonGesture);
    QScrollerProperties ScrollerProperties = QScroller::scroller(mTableView->viewport())->scrollerProperties();
    ScrollerProperties.setScrollMetric(QScrollerProperties::VerticalOvershootPolicy, QScrollerProperties::OvershootAlwaysOff);
    ScrollerProperties.setScrollMetric(QScrollerProperties::HorizontalOvershootPolicy, QScrollerProperties::OvershootAlwaysOff);
    ScrollerProperties.setScrollMetric(QScrollerProperties::MaximumVelocity, 0.5);
    ScrollerProperties.setScrollMetric(QScrollerProperties::MinimumVelocity, 0.5);

    pScroller->setScrollerProperties(ScrollerProperties);
    mTableView->setVerticalScrollMode(QAbstractItemView::ScrollPerPixel);
    mTableView->setHorizontalScrollMode(QAbstractItemView::ScrollPerPixel);
}
