﻿#include "TrendDataManager.h"
#include "RunModeManage.h"
#include "VentModeSettingManager.h"
#include "SystemTimeManager.h"
#include "SystemConfigManager.h"
#include "ModuleTestManager.h"
#include "AGModule.h"

//#define OPEN_QUICK_ADD_TREND_DATA
#define QUICK_ADD_MESC 10
#define VIRTUAL_DATA_COUNT 2000
using AlarmSubPrio = int;
QMap<E_ALARM_ID, AlarmSubPrio> alarmSubPrioInTrend
{
    {ALARM_NEGATIVE_PRESS,                  0}, //气道负压
    {ALARM_PRESS_HIGH_CONTINUA,             1},//持续气道
    {ALARM_PAW_HIGH,                        2},
    {ALARM_FIO2_LOW,                        3},
    {ALARM_FLOW_SENSOR_ERR,                 4},
    {ALARM_APNEA_120,                       5},
    {ALARM_ETCO2_HIGH,                      6},
    {ALARM_FICO2_HIGH,                      7},
    {ALARM_FIN2O_HIGH,                      7},
    {ALARM_ETN2O_HIGH,                      7},
    {ALARM_O2_N2O_UNUSUAL_RATIO,            7},
    {TECHALARM_BATTERYEMPTY,                8},   //电池耗尽
    {TECHALARM_GAS_LOOP_ERR,                8},
    {TECHALARM_GASSOURCE_LOW,               8},
    {TECHALARM_GASSOURCE_O2_LOW,            8},
    {TECHALARM_MINOTORING_MODULE_ERR,       8},  //新增报警2011-10-28
    {TECHALARM_PRESSURE_SENSOR_ERR,         8},  //新增报警2011-10-28
    {TECHALARM_EXP_VALVE_ERR,               8},  //新增报警2011-10-28
    {TECHALARM_EXP_VALVE_CLOSE_OFF,         8}, //新增报警2011-10-28
    {TECHALARM_INSP_VALVE_ERR,              8},  //新增报警2011-10-28
    {TECHALARM_ADDO2_ERR,                   8},
    {TECHALARM_BYPASS_ERR,                  8},
    {TECHALARM_FAN_STOP,                    8},  //风扇故障
    {TECHALARM_AG_SW_ERR,                   8},
    {TECHALARM_AG_HW_ERR,                   8},
    {TECHALARM_AG_MOTO_ERR,                 8},


    //---MID
    {ALARM_PAW_LOW,                         0},
    {ALARM_VTE_LOW,                         1},
    {ALARM_VTE_HIGH,                        2},
    {ALARM_MV_HIGH,                         3},
    {ALARM_APNEA,                           4},
    {ALARM_MV_LOW,                          5},
    {ALARM_PRESS_LOW_CONTINUA,              6},
    {ALARM_ETCO2_LOW,                       7},


    {ALARM_FIENF_HIGH,                      8},
    {ALARM_ETENF_HIGH,                      8},
    {ALARM_FISEV_HIGH,                      8},
    {ALARM_ETSEV_HIGH,                      8},
    {ALARM_FIISO_HIGH,                      8},
    {ALARM_ETISO_HIGH,                      8},
    {ALARM_FIHAL_HIGH,                      8},
    {ALARM_ETHAL_HIGH,                      8},
    {ALARM_FIDES_HIGH,                      8},
    {ALARM_ETDES_HIGH,                      8},

    {TECHALARM_BATTERY_LOW,                         9},
    {TECHALARM_FLOWMETER_O2_SENSOR_NO_CAL_DATA,     9},
    {TECHALARM_FLOWMETER_N2O_SENSOR_NO_CAL_DATA,    9},
    {TECHALARM_FLOWMETER_AIR_SENSOR_NO_CAL_DATA,    9},
    {TECHALARM_FLOW_TOO_LOW,                  9},
    {TECHALARM_O2_PRESS_LOW,                  9},
    {TECHALARM_N2O_PRESS_LOW,                 9},
    {TECHALARM_AIR_PRESS_LOW,                 9},
    {TECHALARM_BACKUP_FLOW_CONTROL_OPEN,      9},//后备流量控制系统针阀打开
    {TECHALARM_VENT_STATE_ACGO,               9},
    {TECHALARM_ADDO2_OVERTIME,                9},
    {TECHALARM_O2_SENSOR_ERR,                 9},
    {TECHALARM_SPO2_BREAKOFF,                 9},
    {TECHALARM_AX_OUT_OF_RANGE,               9},
    {TECHALARM_CO2_OUT_OF_RANGE,              9},
    {TECHALARM_N2O_OUT_OF_RANGE,              9},
    {TECHALARM_AG_NEED_RECAIL,                9},
    {TECHALARM_AG_DATA_UNTRUSTABLE,           9},
    {TECHALARM_AG_LOST_CAIL,                  9},
    {TECHALARM_AG_REPLACE,                    9},
    {TECHALARM_CO2_REPLACE,                   9},
    {TECHALARM_AG_SAMPLING_CLOGGED,           9},
    {TECHALARM_CO2_SAMPLING_CLOGGED,          9},
    {TECHALARM_DB_FAIL,                       9},

    // LOW
    {ALARM_RATE_HIGH,                       0},
    {ALARM_RATE_LOW,                        1},

    {ALARM_N2O_FLOW_HIGH,                   2},
    {ALARM_O2_FLOW_HIGH,                    3},
    {ALARM_AIR_FLOW_HIGH,                   4},

    {ALARM_ETN2O_LOW,                       5},
    {ALARM_FIN2O_LOW,                       6},
    {ALARM_FIENF_LOW,                       7},
    {ALARM_ETENF_LOW,                       8},
    {ALARM_FIHAL_LOW,                       9},
    {ALARM_ETHAL_LOW,                       10},
    {ALARM_FISEV_LOW,                       11},
    {ALARM_ETSEV_LOW,                       12},
    {ALARM_FIDES_LOW,                       13},
    {ALARM_ETDES_LOW,                       14},
    {ALARM_FIISO_LOW,                       15},
    {ALARM_ETISO_LOW,                       16},

    {ALARM_FIO2_HIGH,                      17},
    {ALARM_PR_HIGH,                        17},
    {ALARM_PR_LOW,                         17},
    {ALARM_MV_LOW,                         17},
    {ALARM_FICO2_LOW,                      17},
    {ALARM_SPO2_HIGH,                      17},
    {ALARM_SPO2_LOW,                       17},

    {ALARM_FI_AGENT1_HIGH,               17},
    {ALARM_FI_AGENT1_LOW,                17},
    {ALARM_FI_AGENT2_HIGH,               17},
    {ALARM_FI_AGENT2_LOW,                17},

    {ALARM_ET_AGENT1_HIGH,                17},
    {ALARM_ET_AGENT1_LOW,                 17},
    {ALARM_ET_AGENT2_HIGH,                17},
    {ALARM_ET_AGENT2_LOW,                 17},
    {ALARM_FLOW_HIGH,                     17},	// 补充总流量报警 2022-02-9

    {TECHALARM_O2_FLOW_INEXACT,                                  17},
    {TECHALARM_BANLANCE_FLOW_INEXACT,                            17},
    {TECHALARM_NOAC,                                             17},
    {TECHALARM_HEATING_MODULE_ERR,                               17},  //新增报警2011-10-28
    {TECHALARM_AG_ADAPTOR_ERR,                                   17},  //新增报警2011-10-28
    {TECHALARM_AG_SAMPLING_ERR,                                  17},
    {TECHALARM_CO2_ADAPTOR_ERR,                                  17},
    {TECHALARM_CO2_SAMPLING_ERR,                                 17},
    {TECHALARM_HLM_TIME_UP,                                      17},
    {TECHALARM_TEM_OUT_OF_RANGE,                                 17},
    {TECHALARM_PRESS_OUT_OF_RANGE,                               17},
    {TECHALARM_AG_MIX_AX,                                        17},

    {PROMPT_TIMER_TIMEOUT,                                       666},
    {PROMPT_COUNTDOWN_TIMEOUT,                                   666},
    {PROMPT_PRESSURE_LIMITD,                                     666},
    {PROMPT_FLOWMETER_BREAK_OFF,                                 666},
    {PROMPT_MONIT_SENSOR_CAL_FAIL,                               666},
    {PROMPT_MONITOR_ACCURACY_LOW,                                666},
    {PROMPT_O2_FM_SENSOR_ERROR,                                  666},
    {PROMPT_N2O_FM_SENSOR_ERROR,                                 666},
    {PROMPT_AIR_FM_SENSOR_ERROR,                                 666},
    {PROMPT_FM_HARDWARE_ERROR,                                   666},
    {PROMPT_SPO2_SENSOR_UNUNITED,                                666},
    {PROMPT_AG_ERROR,                                            666},
    {PROMPT_O2_CONCENTRATION_ERROR,                              666},
    {PROMPT_TOUCH_SCREEN_HOLDBACK,                               666},
    {PROMPT_SYSTEM_TIME_ERR,                                     666},
    {PROMPT_BREATH_LEAK_HIG,                                     666},
    {PROMPT_BATTERY_ERR,                                         666},
    {PROMPT_ONLY_ONE_BATTERY,                                    666},
    {PROMPT_FLOW_TOO_LOW,                                        666},
    {PROMPT_AG_WARMING,                                          666},
    {PROMPT_CO2_WARMING,                                         666},
    {PROMPT_CO2_ERROR,                                           666},
    {PROMPT_O2_FM_SENSOR_COMMUNICATION_STOP,                     666},
    {PROMPT_BALANCE_GAS_FM_SENSOR_ERROR,                         666},
    {PROMPT_BACKUP_FLOW_CONTROL_COMMUNICATION_STOP,              666},
};

struct TrendParamInfoSt
{
    MONITOR_PARAM_TYPE mRelevantMonitorParam;
    unsigned short mDisplayPrecision;
};

static QMap<TREND_PARAM_TYPE, TrendParamInfoSt> sTrendDescriptors
{                                                                                                   /* name  */  				/* unit  */  		/* scale_num  */  /* scale_value_digit  */  /* mTrendDataDigit  */  	/* name_color  */  		/* unit_color  */  		/* data_color  */  		/* scale_value_color  */  /* axis_color  */  	  /* mWaveColor  */
    {TREND_FLOWMETER_O2,  { VALUEDATA_FLOWMETER_O2,   DIGIT_2,}},
    {TREND_FLOWMETER_AIR, { VALUEDATA_FLOWMETER_AIR,  DIGIT_2,}},
    {TREND_FLOWMETER_N2O, { VALUEDATA_FLOWMETER_N2O,  DIGIT_2,}},
    {TREND_PPEAK,         { VALUEDATA_PPEAK,          DIGIT_0,}},
    {TREND_PPLAT,         { VALUEDATA_PPLAT,          DIGIT_0,}},
    {TREND_PEEP,          { VALUEDATA_PEEP,           DIGIT_0,}},
    {TREND_PMEAN,         { VALUEDATA_PMEAN,          DIGIT_0,}},
    {TREND_VTE,           { VALUEDATA_VTE,            DIGIT_0,}},
    {TREND_VTI,           { VALUEDATA_VTI,            DIGIT_0,}},
    {TREND_MV,            { VALUEDATA_MV,             DIGIT_2,}},
    {TREND_MVSPN,         { VALUEDATA_MVSPN,          DIGIT_2,}},
    {TREND_RATE,          { VALUEDATA_RATE,           DIGIT_0,}},
    {TREND_FSPN,          { VALUEDATA_FSPN,           DIGIT_0,}},
    {TREND_R,             { VALUEDATA_R,              DIGIT_0,}},
    {TREND_C,             { VALUEDATA_C,              DIGIT_0,}},
    {TREND_FIO2,          { VALUEDATA_FIO2,           DIGIT_0,}},
    {TREND_FICO2,         { VALUEDATA_FICO2,          DIGIT_1,}},
    {TREND_ETCO2,         { VALUEDATA_ETCO2,          DIGIT_1,}},
    {TREND_FIN2O,         { VALUEDATA_FIN2O,          DIGIT_0,}},
    {TREND_ETN2O,         { VALUEDATA_ETN2O,          DIGIT_0,}},
    {TREND_FIENF,         { VALUEDATA_FIENF,          DIGIT_1,}},
    {TREND_ETENF,         { VALUEDATA_ETENF,          DIGIT_1,}},
    {TREND_FIISO,         { VALUEDATA_FIISO,          DIGIT_1,}},
    {TREND_ETISO,         { VALUEDATA_ETISO,          DIGIT_1,}},
    {TREND_FISEV,         { VALUEDATA_FISEV,          DIGIT_1,}},
    {TREND_ETSEV,         { VALUEDATA_ETSEV,          DIGIT_1,}},
    {TREND_FIHAL,         { VALUEDATA_FIHAL,          DIGIT_1,}},
    {TREND_ETHAL,         { VALUEDATA_ETHAL,          DIGIT_1,}},
    {TREND_FIDES,         { VALUEDATA_FIDES,          DIGIT_1,}},
    {TREND_ETDES,         { VALUEDATA_ETDES,          DIGIT_1,}},
};

TrendDataManager::TrendDataManager()
{
}

void TrendDataManager::RefurbishRawData()
{
    for(int trendType = 0;
        trendType < TREND_PARAM_TYPE::TREND_PARAM_TYPE_END;
        ++trendType)
    {
        auto eTrendType = static_cast<TREND_PARAM_TYPE>(trendType);
        int paramDataId = sTrendDescriptors[eTrendType].mRelevantMonitorParam;

        short tmpData;
        DataManager::GetInstance()->GetMonitorData(paramDataId, tmpData);
        if (tmpData == INVALID_VALUE)
            continue;
        mTrendRawDataBuf[eTrendType].append(tmpData);
    }
}

void TrendDataManager::RefurbishTrendData()
{
    ++mTimeCnt;
#ifdef OPEN_QUICK_ADD_TREND_DATA
    static int count =0;
    count++;
    if (count == VIRTUAL_DATA_COUNT)
    {
        mRefurbishTrendDataTimer->stop();
        mRefurbishTrendDataTimer->setInterval(3*1000); //TBD常量
        mRefurbishTrendDataTimer->start();

        return;
    }
        #else
    auto curTime = QDateTime::currentDateTime();
#endif
    TrendDataSt tmpSt{};
    tmpSt.mRelativeTimestamp = mTimeCnt * 60000;
    for(int i = 0;
        i < TREND_PARAM_TYPE::TREND_PARAM_TYPE_END;
        ++i )
    {
        TREND_PARAM_TYPE trendType = (TREND_PARAM_TYPE)i;
        int result{};
        auto mDataBuf = std::move(mTrendRawDataBuf[(TREND_PARAM_TYPE)trendType]);
        if (mDataBuf.size() != 0)
        {
            result = (int)(std::round(std::accumulate(mDataBuf.begin(), mDataBuf.end(), 0) / ((double)mDataBuf.size())));
        }
        else
        {
            result = INVALID_VALUE;
        }
        mCurDataMap[trendType] = result;

#ifdef OPEN_QUICK_ADD_TREND_DATA
        mCurDataMap[trendType] = QRandomGenerator::global()->bounded(11);
#endif
    }

    tmpSt.mRunMode = RunModeManage::GetInstance()->GetCurRunMode();
    auto idVec = AlarmManager::GetInstance()->GetSortedAlarms();
    for(auto i = idVec.rbegin(); i != idVec.rend(); i ++)
        mAlarmBuf.push_back(*i);

    if (MODE_RUN_VENT == tmpSt.mRunMode)
    {
        tmpSt.mVentMode = VentModeSettingManager::GetInstance()->GetCurMode();
    }
    else
    {
        tmpSt.mVentMode = -1;
    }
    for(int i = 0;i<mCurDataMap.size();i++)
    {
        if(tmpSt.mRunMode == MODE_RUN_SELFTEST || tmpSt.mRunMode == MODE_RUN_STANDBY)
            mCurDataMap[(TREND_PARAM_TYPE)i] = INVALID_VALUE;
        else
        {
            auto relevantParam = TrendDataManager::GetRelevantMonitorParam((TREND_PARAM_TYPE)i);
            double tmpAverData = DataManager::RevertToFloat(relevantParam, mCurDataMap[(TREND_PARAM_TYPE)i]);
            if(tmpAverData < sTrendYDefaultScales[(TREND_PARAM_TYPE)i].last().mMin || tmpAverData > sTrendYDefaultScales[(TREND_PARAM_TYPE)i].last().mMax)
                mCurDataMap[(TREND_PARAM_TYPE)i] = INVALID_VALUE;
        }
    }

    tmpSt.mFlowmeterO2  = mCurDataMap[TREND_FLOWMETER_O2];
    tmpSt.mFlowmeterAIR = mCurDataMap[TREND_FLOWMETER_AIR];
    tmpSt.mFlowmeterN2O = mCurDataMap[TREND_FLOWMETER_N2O];
    tmpSt.mPpeak = mCurDataMap[TREND_PPEAK];
    tmpSt.mPplat = mCurDataMap[TREND_PPLAT];
    tmpSt.mPeep = mCurDataMap[TREND_PEEP];
    tmpSt.mPmean = mCurDataMap[TREND_PMEAN];
    tmpSt.mVTI = mCurDataMap[TREND_VTI];
    tmpSt.mVTE = mCurDataMap[TREND_VTE];
    tmpSt.mMV = mCurDataMap[TREND_MV];
    tmpSt.mMVSpn = mCurDataMap[TREND_MVSPN];
    tmpSt.mRate = mCurDataMap[TREND_RATE];
    tmpSt.mFspn = mCurDataMap[TREND_FSPN];
    tmpSt.mR =  mCurDataMap[TREND_R];
    tmpSt.mC = mCurDataMap[TREND_C];
    tmpSt.mFiO2 = mCurDataMap[TREND_FIO2];
    tmpSt.mFiCo2 = mCurDataMap[TREND_FICO2];
    tmpSt.mEtCo2 = mCurDataMap[TREND_ETCO2];
    tmpSt.mFiN2O = mCurDataMap[TREND_FIN2O];
    tmpSt.mEtN2O = mCurDataMap[TREND_ETN2O];
    tmpSt.mFiENF = mCurDataMap[TREND_FIENF];
    tmpSt.mEtENF = mCurDataMap[TREND_ETENF];
    tmpSt.mFiISO = mCurDataMap[TREND_FIISO];
    tmpSt.mEtISO = mCurDataMap[TREND_ETISO];
    tmpSt.mFiSEV = mCurDataMap[TREND_FISEV];
    tmpSt.mEtSEV = mCurDataMap[TREND_ETSEV];
    tmpSt.mFiHAL = mCurDataMap[TREND_FIHAL];
    tmpSt.mEtHAL = mCurDataMap[TREND_ETHAL];
    tmpSt.mFiDES = mCurDataMap[TREND_FIDES];
    tmpSt.mEtDES = mCurDataMap[TREND_ETDES];
    mAlarmBuf = SortAlarm(mAlarmBuf);
    if (mAlarmBuf.size() != 0)
        mTimeWithAlarm << tmpSt.mRelativeTimestamp;
    tmpSt.mAlarmIdList = std::move(mAlarmBuf);
    mTrendDatas[tmpSt.mRelativeTimestamp] = tmpSt;
    SetBaseDataTime(QDateTime::currentDateTime());
    emit SignalDataAdded();
    DataRecordLimit();
}

void TrendDataManager::SlotOnUnitChanged(CONVERTIBLE_UNIT_CATEGORY category, UNIT_TYPE oldType, UNIT_TYPE newType)
{
    UnitInfoSt unitInfo = UnitManager::GetInstance()->GetInfoSt(newType);
    auto digit = unitInfo.digit;

    for(int trendType = TREND_PARAM_TYPE::TREND_PARAM_TYPE_START;
        trendType <  TREND_PARAM_TYPE::TREND_PARAM_TYPE_END;
        ++trendType)
    {
        auto eTrendType = (TREND_PARAM_TYPE)trendType;
        auto relevantMonitorParam = TrendDataManager::GetRelevantMonitorParam(eTrendType);

        if (DataManager::GetParamUnitCategory(relevantMonitorParam) == category)
        {
            sTrendDescriptors[eTrendType].mDisplayPrecision = digit;
        }
    }
}

void TrendDataManager::InitConnect()
{
    connect(mRefurbishRawDataTimer,   &QTimer::timeout, this,[=](){
       RefurbishRawData();
    } );
    connect(mRefurbishTrendDataTimer, &QTimer::timeout, this, [=]()
    {
     DEBUG_RUNTIME(RefurbishTrendData());
    });
    connect(UnitManager::GetInstance(), &UnitManager::SignalCategoryUnitChanged, this, &TrendDataManager::SlotOnUnitChanged);
}

void TrendDataManager::DataRecordLimit()
{
    static const int toleranceLimit = 24*60;
    static const int hardLimit = 24*60+1;
    if (mTrendDatas.size() == hardLimit)
    {
        QVector<qint64> deletedDataTimeStamp;
        for (int i = 0; i < hardLimit -  toleranceLimit; ++i)
        {
            auto timestamp = mTrendDatas.firstKey();
            deletedDataTimeStamp << timestamp;
            if (mTimeWithAlarm.contains(timestamp))
                mTimeWithAlarm.removeOne(timestamp);
            mTrendDatas.remove(mTrendDatas.firstKey());
        }

        std::sort(deletedDataTimeStamp.begin(), deletedDataTimeStamp.end());
        emit SignalDataRemoved(deletedDataTimeStamp);
    }
}

bool TrendDataManager::GetTrendParamEnable(TREND_PARAM_TYPE type)
{
    DebugAssert(mTrendParamEnableMap.contains(type));
    return mTrendParamEnableMap[type];
}

QVector<int> TrendDataManager::GetDisabledParams()
{
    RefurbishConfig();
    QVector<int> vec{};
    auto keys = mTrendParamEnableMap.keys();
    foreach (auto type, keys)
    {
        if (mTrendParamEnableMap[type] == false)
            vec << type;
    }
    return vec;
}

qint64 TrendDataManager::GetShowTime(qint64 relativeTime,QDateTime* showTime)
{
    qint64 nowTime=mBaseDataTime.toMSecsSinceEpoch() -mBaseDataTime.toMSecsSinceEpoch()%60000;
    if (mTrendDatas.contains(relativeTime))
    {
        qint64 re= nowTime - (mTrendDatas.last().mRelativeTimestamp - relativeTime);
        if(showTime)
            *showTime=QDateTime::fromMSecsSinceEpoch(re);
        return re;
    }

    else
        return -1;
}


QDateTime TrendDataManager::GetBaseDataTime()
{
    return mBaseDataTime;
}

void TrendDataManager::SetBaseDataTime(QDateTime dataTime)
{
    mBaseDataTime=dataTime;
}

QString TrendDataManager::GetTrendName(TREND_PARAM_TYPE type)
{
    return DataManager::GetParamName(sTrendDescriptors[type].mRelevantMonitorParam);
}

QString TrendDataManager::GetTrendUnit(TREND_PARAM_TYPE type)
{
    auto relevantMonitorParam = sTrendDescriptors[type].mRelevantMonitorParam;
    return DataManager::GetParamCurUnitStr(relevantMonitorParam);
}

short TrendDataManager::GetDisplayPrecision(TREND_PARAM_TYPE trendType)
{
    return sTrendDescriptors[trendType].mDisplayPrecision;
}

MONITOR_PARAM_TYPE TrendDataManager::GetRelevantMonitorParam(TREND_PARAM_TYPE trendType)
{
    return sTrendDescriptors[trendType].mRelevantMonitorParam;
}



qint64 TrendDataManager::GetPreAlarmTime(qint64 curTime)
{
    std::sort(mTimeWithAlarm.begin(), mTimeWithAlarm.end());

    auto idx = mTimeWithAlarm.indexOf(curTime);
    if (idx > 0)
    {
        return mTimeWithAlarm[idx - 1];
    }
    else
    {
        for (auto it = mTimeWithAlarm.crbegin(); it != mTimeWithAlarm.crend(); ++it)
        {
            if (*it < curTime)
                return *it;
        }
    }
    return -1;
}

qint64 TrendDataManager::GetNextAlarmTime(qint64 curTime)
{
    auto t = mTimeWithAlarm.indexOf(curTime);
    if (t + 1 < mTimeWithAlarm.size() && t != -1)
    {
        return mTimeWithAlarm[t + 1];
    }
    else
    {
        foreach (auto t, mTimeWithAlarm)
        {
            if (t > curTime)
                return t;
        }
    }
    return -1;
}

QDateTime TrendDataManager::GetNewestDataTime()
{
    if (mTrendDatas.size() != 0)
        return QDateTime::fromMSecsSinceEpoch(mTrendDatas.lastKey());
    else
        return {};
}

QDateTime TrendDataManager::GetOldestDataTime()
{
    if (mTrendDatas.isEmpty())
        return {};
    else
        return QDateTime::fromMSecsSinceEpoch(mTrendDatas.firstKey());
}


qreal TrendDataManager::GetCurTrendData(TREND_PARAM_TYPE type)
{
    return mCurDataMap[type];
}

TrendDataSt TrendDataManager::GetCurTrendDataSt()
{
    return mTrendDatas.last();
}

int TrendDataManager::GetTrendDataNum()
{
    return mTrendDatas.count();
}

QVector<E_ALARM_ID> TrendDataManager::GetAlarms(qint64 time)
{
    if (mTrendDatas.contains(time))
        return mTrendDatas[time].mAlarmIdList;
    else
        return {};
}

TrendDataSt TrendDataManager::GetTrendDataSt(QDateTime time)
{
    auto timeStamp = time.toMSecsSinceEpoch();
    return GetTrendDataSt(timeStamp);
}

TrendDataSt TrendDataManager::GetTrendDataSt(qint64 timeMsec)
{
    if (mTrendDatas.contains(timeMsec))
        return mTrendDatas[timeMsec];
    else
    {
        TrendDataSt dataSt;
        dataSt.mRelativeTimestamp  = timeMsec;
        dataSt.mRunMode = -1;
        dataSt.mVentMode = -1;

        return dataSt;
    }
}

QVector<E_ALARM_ID> TrendDataManager::SortAlarm(QVector<E_ALARM_ID> idVec)
{
    QVector<E_ALARM_ID> sortedAlarms{};
    QMap<E_ALARMPRIO, QMap<int, QVector<E_ALARM_ID>>> tmpPrioIdsMap{};

    for (auto id : idVec)
    {
        if(!alarmSubPrioInTrend.contains(id))
            continue;
        auto infoSt = AlarmManager::GetInstance()->GetAlarmInfo(id);
        tmpPrioIdsMap[infoSt.mAlarmPrio][alarmSubPrioInTrend[id]].push_front(id);
    }

    for (int i = E_ALARMPRIO::ALARMPRIO_HIG; i <= E_ALARMPRIO::ALARMPRIO_PROMPT; ++i)
    {
        for (auto data : tmpPrioIdsMap[(E_ALARMPRIO)i].values())
        {
            sortedAlarms << data;
        }
    }

    return sortedAlarms;
}

void TrendDataManager::Init()
{
    RefurbishConfig();
    InitConnect();

#ifdef OPEN_QUICK_ADD_TREND_DATA
    mRefurbishTrendDataTimer->start(QUICK_ADD_MESC);//60*1000); //TBD常量
#else
    mRefurbishTrendDataTimer->setInterval(60*1000); //TBD常量
    mRefurbishTrendDataTimer->start();

#endif
    mRefurbishRawDataTimer->setInterval(25); //TBD 常量
    mRefurbishRawDataTimer->start();

    for (int i = PRESSURE_CATEGORY; i < NONE_CATEGORY; ++i)
        SlotOnUnitChanged((CONVERTIBLE_UNIT_CATEGORY)i, U_MAX, UnitManager::GetInstance()->GetCategoryCurUnit((CONVERTIBLE_UNIT_CATEGORY)i));
}

qreal TrendDataManager::GetTrendData(qint64 time, TREND_PARAM_TYPE type)
{
    return -1;
}

QMap<qint64, TrendDataSt> TrendDataManager::GetAllTrendDatas()
{
    return mTrendDatas;
}

QMap<TREND_PARAM_TYPE, qreal> TrendDataManager::ConvertTrendDataSt(TrendDataSt dataSt)
{
    QMap<TREND_PARAM_TYPE, qreal> dataMap
    {
        {TREND_PARAM_TYPE::TREND_FLOWMETER_O2,      dataSt.mFlowmeterO2},
        {TREND_PARAM_TYPE::TREND_FLOWMETER_AIR,     dataSt.mFlowmeterAIR},
        {TREND_PARAM_TYPE::TREND_FLOWMETER_N2O,     dataSt.mFlowmeterN2O},

        {TREND_PARAM_TYPE::TREND_PPEAK,             dataSt.mPpeak},
        {TREND_PARAM_TYPE::TREND_PPLAT,             dataSt.mPplat},
        {TREND_PARAM_TYPE::TREND_PEEP,              dataSt.mPeep},
        {TREND_PARAM_TYPE::TREND_PMEAN,             dataSt.mPmean},

        {TREND_PARAM_TYPE::TREND_VTE,               dataSt.mVTE},
        {TREND_PARAM_TYPE::TREND_VTI,               dataSt.mVTI},
        {TREND_PARAM_TYPE::TREND_MV,                dataSt.mMV},
        {TREND_PARAM_TYPE::TREND_MVSPN,             dataSt.mMVSpn},
        {TREND_PARAM_TYPE::TREND_RATE,              dataSt.mRate},
        {TREND_PARAM_TYPE::TREND_FSPN,              dataSt.mFspn},
        {TREND_PARAM_TYPE::TREND_R,                 dataSt.mR},
        {TREND_PARAM_TYPE::TREND_C,                 dataSt.mC},

        {TREND_PARAM_TYPE::TREND_FIO2,              dataSt.mFiO2},
        {TREND_PARAM_TYPE::TREND_FICO2,             dataSt.mFiCo2},
        {TREND_PARAM_TYPE::TREND_ETCO2,             dataSt.mEtCo2},

        {TREND_PARAM_TYPE::TREND_FIN2O,             dataSt.mFiN2O},
        {TREND_PARAM_TYPE::TREND_ETN2O,             dataSt.mEtN2O},

        {TREND_PARAM_TYPE::TREND_FIENF,             dataSt.mFiENF},
        {TREND_PARAM_TYPE::TREND_ETENF,             dataSt.mEtENF},

        {TREND_PARAM_TYPE::TREND_FIISO,             dataSt.mFiISO},
        {TREND_PARAM_TYPE::TREND_ETISO,             dataSt.mEtISO},

        {TREND_PARAM_TYPE::TREND_FISEV,             dataSt.mFiSEV},
        {TREND_PARAM_TYPE::TREND_ETSEV,             dataSt.mEtSEV},

        {TREND_PARAM_TYPE::TREND_FIHAL,             dataSt.mFiHAL},
        {TREND_PARAM_TYPE::TREND_ETHAL,             dataSt.mEtHAL},

        {TREND_PARAM_TYPE::TREND_FIDES,             dataSt.mFiDES},
        {TREND_PARAM_TYPE::TREND_ETDES,             dataSt.mEtDES},
    };

    return dataMap;
}

TrendDataSt TrendDataManager::ConvertTrendDataMap(QMap<TREND_PARAM_TYPE, qreal> dataMap)
{
    TrendDataSt tmpSt;
    tmpSt.mFlowmeterO2  = dataMap[TREND_FLOWMETER_O2];
    tmpSt.mFlowmeterAIR = dataMap[TREND_FLOWMETER_AIR];
    tmpSt.mFlowmeterN2O = dataMap[TREND_FLOWMETER_N2O];
    tmpSt.mPpeak = dataMap[TREND_PPEAK];
    tmpSt.mPplat = dataMap[TREND_PPLAT];
    tmpSt.mPeep = dataMap[TREND_PEEP];
    tmpSt.mPmean = dataMap[TREND_PMEAN];
    tmpSt.mVTI = dataMap[TREND_VTI];
    tmpSt.mVTE = dataMap[TREND_VTE];
    tmpSt.mMV = dataMap[TREND_MV];
    tmpSt.mMVSpn = dataMap[TREND_MVSPN];
    tmpSt.mRate = dataMap[TREND_RATE];
    tmpSt.mFspn = dataMap[TREND_FSPN];
    tmpSt.mR =  dataMap[TREND_R];
    tmpSt.mC = dataMap[TREND_C];
    tmpSt.mFiO2 =  dataMap[TREND_FIO2];
    tmpSt.mFiCo2 = dataMap[TREND_FICO2];
    tmpSt.mEtCo2 = dataMap[TREND_ETCO2];
    tmpSt.mFiN2O = dataMap[TREND_FIN2O];
    tmpSt.mEtN2O = dataMap[TREND_ETN2O];
    tmpSt.mFiENF = dataMap[TREND_FIENF];
    tmpSt.mEtENF = dataMap[TREND_ETENF];
    tmpSt.mFiISO = dataMap[TREND_FIISO];
    tmpSt.mEtISO = dataMap[TREND_ETISO];
    tmpSt.mFiSEV = dataMap[TREND_FISEV];
    tmpSt.mEtSEV = dataMap[TREND_ETSEV];
    tmpSt.mFiHAL = dataMap[TREND_FIHAL];
    tmpSt.mEtHAL = dataMap[TREND_ETHAL];
    tmpSt.mFiDES = dataMap[TREND_FIDES];
    tmpSt.mEtDES = dataMap[TREND_ETDES];
    return tmpSt;
}

void TrendDataManager::RefurbishConfig()
{
    for(int trendType = 0;
        trendType < static_cast<int>(TREND_PARAM_TYPE::TREND_PARAM_TYPE_END);
        ++trendType)
    {
        mTrendParamEnableMap[(TREND_PARAM_TYPE)trendType] = true;
    }

    if (!ConfigManager->GetConfig<bool>(SystemConfigManager:: GAS_SOURCE_N2O_BOOL_INDEX))
    {
        mTrendParamEnableMap[TREND_PARAM_TYPE::TREND_FLOWMETER_N2O] = false;
    }
    if (!ConfigManager->GetConfig<bool>(SystemConfigManager:: GAS_SOURCE_AIR_BOOL_INDEX ))
    {
        mTrendParamEnableMap[TREND_PARAM_TYPE::TREND_FLOWMETER_AIR] = false;
    }
    if (!ConfigManager->GetConfig<bool>(SystemConfigManager:: GAS_SOURCE_O2_BOOL_INDEX ))
    {
        mTrendParamEnableMap[TREND_PARAM_TYPE::TREND_FLOWMETER_O2] = false;
    }

    //无AG，有CO2模块
    if (ModuleTestManager::GetInstance()->GetAgModuleType() == CO2)
    {
        for (int i = TREND_PARAM_TYPE::TREND_FIN2O; i <= TREND_PARAM_TYPE::TREND_ETDES; ++i )
            mTrendParamEnableMap[(TREND_PARAM_TYPE)i] = false;
    }
    //无AG,无CO2模块,CO2相关参数无效
    if(ModuleTestManager::GetInstance()->GetAgModuleType() == None)
    {
        for (int i = TREND_PARAM_TYPE::TREND_AG_START; i <= TREND_PARAM_TYPE::TREND_ETDES; ++i )
            mTrendParamEnableMap[(TREND_PARAM_TYPE)i] = false;
    }

    if (!ConfigManager->GetConfig<bool>(SystemConfigManager::ENABLE_FLOWMETER_MODULE))
    {
        for (int i = TREND_PARAM_TYPE::TREND_FLOWMETER_O2; i <= TREND_PARAM_TYPE::TREND_FLOWMETER_N2O; ++i )
            mTrendParamEnableMap[(TREND_PARAM_TYPE)i] = false;
    }
    if (!ConfigManager->GetConfig<bool>(SystemConfigManager::O2_BOOL_INDEX))
    {
        mTrendParamEnableMap[TREND_FIO2] = false;
    }
}

void TrendDataSt::SetInvalidValue()
{
    mRunMode = INVALID_VALUE;
    mVentMode= INVALID_VALUE;

    mFlowmeterO2= INVALID_VALUE;
    mFlowmeterAIR= INVALID_VALUE;
    mFlowmeterN2O= INVALID_VALUE;

    mPpeak= INVALID_VALUE;
    mPplat= INVALID_VALUE;
    mPeep= INVALID_VALUE;
    mPmean= INVALID_VALUE;
    mVTI= INVALID_VALUE;
    mVTE= INVALID_VALUE;
    mMV= INVALID_VALUE;
    mMVSpn= INVALID_VALUE;
    mRate= INVALID_VALUE;
    mFspn= INVALID_VALUE;
    mR= INVALID_VALUE;
    mC= INVALID_VALUE;
    mFiO2= INVALID_VALUE;

    mFiCo2= INVALID_VALUE;
    mEtCo2= INVALID_VALUE;
    mFiN2O= INVALID_VALUE;
    mEtN2O= INVALID_VALUE;
    mFiENF= INVALID_VALUE;
    mEtENF= INVALID_VALUE;
    mFiISO= INVALID_VALUE;
    mEtISO= INVALID_VALUE;
    mFiSEV= INVALID_VALUE;
    mEtSEV= INVALID_VALUE;
    mFiHAL= INVALID_VALUE;
    mEtHAL= INVALID_VALUE;
    mFiDES= INVALID_VALUE;
    mEtDES= INVALID_VALUE;
}
