﻿

#include <QLabel>
#include <QHBoxLayout>
#include <QVBoxLayout>
#include <QGridLayout>
#include<QSpacerItem>

#include "MainDisplayWidget.h"
#include "UiApi.h"
#include "UiConfig.h"
#include "WaveChart.h"
#include "LoopGraph.h"
#include "ComboBox.h"
#include "String/UIStrings.h"
#include "RunModeManage.h"
#include "VentModeSettingManager.h"
#include "SystemConfigManager.h"
#include "ModuleTestManager.h"

#define WAVE_PLOT_SPACE     (2)

#define WAVE_PAGE_LEFT_MARGIN       (0) //10
#define WAVE_PAGE_TOP_MARGIN        (0)
#define WAVE_PAGE_RIGHT_MARGIN      (0) //10
#define WAVE_PAGE_BOTTOM_MARGIN     (0)

#define PLOT_INDEX  (0)
#define CPB_INDEX   (1)

#define LAYOUT_SWITCH_WIDTH (40)
#define LAYOUT_SWITCH_HEIGHT (20)
#define WAVE_CHART_MAX_HEIGHT (190)
#define LAYOUT_SWITCH_ICON_URL (":/button/MenuIcon.png")
#define AllParamBoard_HEIGHT 382
#define LoopGraph_MAX_HEIGHT 190
QMap<MainDisplayWidget::MAIN_DISPLAY_LAYOUT_ENUM, bool> MainDisplayWidget::sLayoutEnable{};

MainDisplayWidget::MainDisplayWidget(QWidget *parent) :
    FocusWidget(parent)
{
    InitUi();
    InitConnect();
    ShowPlotInfo();

    //    for(auto ite = sLayoutEnable.begin(); ite != sLayoutEnable.end(); ite++)  //TBD 什么特殊作用？
    //    {
    //        if (ite.value())
    //        {
    //            mLayoutSwitcher->setValue(ite.key());
    //            break;
    //        }
    //    }
}

SystemSettingManager::SYSTEM_SETTING_ENUM ConvertUiEnumToSettingId(E_MAINSHOW_WAVEAREA_WAVE_INDEX en)
{
    QMap<E_MAINSHOW_WAVEAREA_WAVE_INDEX, SystemSettingManager::SYSTEM_SETTING_ENUM> enumMap
    {
        {E_MAINSHOW_WAVEAREA_WAVE_INDEX::UI_MAINSHOW_WAVEAREA_WAVE_1, SystemSettingManager::MAIN_WAVE_1},
        {E_MAINSHOW_WAVEAREA_WAVE_INDEX::UI_MAINSHOW_WAVEAREA_WAVE_2, SystemSettingManager::MAIN_WAVE_2},
        {E_MAINSHOW_WAVEAREA_WAVE_INDEX::UI_MAINSHOW_WAVEAREA_WAVE_3, SystemSettingManager::MAIN_WAVE_3},
        {E_MAINSHOW_WAVEAREA_WAVE_INDEX::UI_MAINSHOW_WAVEAREA_WAVE_4, SystemSettingManager::MAIN_WAVE_4},
    };

    return enumMap.value(en);
}

void MainDisplayWidget::InitUi()
{
    StyleSet::SetBackgroundColor(this,COLOR_BLACK_GRAY);

    sLayoutEnable = {
        {FOUR_WAVE,             true},
        {THREE_WAVE,            true},
        {ONE_WAVE_ALL_PARAMS,   true},
        {TWO_WAVE_TWO_LOOP,     true},
    };

    if (!ConfigManager->GetConfig<bool>(SystemConfigManager::LOOP_BOOL_INDEX))
    {
        sLayoutEnable[TWO_WAVE_TWO_LOOP] = false;
    }
    if (ModuleTestManager::GetInstance()->GetAgModuleType() == None)
    {
        sLayoutEnable[FOUR_WAVE] = false;
    }

    auto waveLayout = new QVBoxLayout;
    waveLayout->setContentsMargins(WAVE_PAGE_LEFT_MARGIN, WAVE_PAGE_TOP_MARGIN,
                                   WAVE_PAGE_RIGHT_MARGIN, WAVE_PAGE_BOTTOM_MARGIN);
    waveLayout->setSpacing(2);

    SystemSettingManager *settingManager = SettingManager;
    for (int i = 0; i < E_MAINSHOW_WAVEAREA_WAVE_INDEX::UI_MAINSHOW_WAVEAREA_WAVE_MAX; ++i)
    {
        mWaves[static_cast<E_MAINSHOW_WAVEAREA_WAVE_INDEX>(i)] = new WaveChart(this);
        mWaves[static_cast<E_MAINSHOW_WAVEAREA_WAVE_INDEX>(i)]->setMaximumHeight(WAVE_CHART_MAX_HEIGHT);
        waveLayout->addWidget(mWaves[static_cast<E_MAINSHOW_WAVEAREA_WAVE_INDEX>(i)]);

        if ((DataManager::WAVE_PARAM_TYPE)settingManager->GetIntSettingValue(ConvertUiEnumToSettingId((E_MAINSHOW_WAVEAREA_WAVE_INDEX)i)) > 3)
            settingManager->SetSettingValue(ConvertUiEnumToSettingId((E_MAINSHOW_WAVEAREA_WAVE_INDEX)i), 3);

        ChartManager::GetInstance()->RegisterChart(ChartManager::CHART_TYPE_ENUM::WAVE_CHART,
                                                   (DataManager::WAVE_PARAM_TYPE)settingManager->GetIntSettingValue(ConvertUiEnumToSettingId((E_MAINSHOW_WAVEAREA_WAVE_INDEX)i)),
                                                   mWaves[(E_MAINSHOW_WAVEAREA_WAVE_INDEX)i]);
    }
    mWaveWidget = new FocusWidget;
    mWaveWidget->setLayout(waveLayout);
    mLayoutSwitcher = new IconComboBox(mWaveWidget);
    mLayoutSwitcher->setPixmap(QPixmap(LAYOUT_SWITCH_ICON_URL));
    mLayoutSwitcher->setContentsMargins(2, 2, 2, 2);
    mLayoutSwitcher->setFixedSize(LAYOUT_SWITCH_WIDTH, LAYOUT_SWITCH_HEIGHT);
    mWaveWidget->setFocusProxy(mLayoutSwitcher);

    auto mainLayout = new QVBoxLayout;
    mainLayout->setContentsMargins(WAVE_PAGE_LEFT_MARGIN, WAVE_PAGE_TOP_MARGIN,
                                   WAVE_PAGE_RIGHT_MARGIN, WAVE_PAGE_BOTTOM_MARGIN);
    mainLayout->setSpacing(2);
    mainLayout->addWidget(mWaveWidget);

    mLoopGraph = new LoopGraph;
    mLoopGraph->hide();
    StyleSet::SetBackgroundColor(mLoopGraph,COLOR_BLACK);
    mLoopGraph->setMaximumHeight(LoopGraph_MAX_HEIGHT);
    mainLayout->addWidget(mLoopGraph);

    mAllParamBoard = new AllParamBoard;
    StyleSet::SetBackgroundColor(mAllParamBoard,COLOR_BLACK);
    mAllParamBoard->setFixedHeight(AllParamBoard_HEIGHT);
    mAllParamBoard->hide();

    mainLayout->addWidget(mAllParamBoard);
    mPlotContainer = new FocusWidget;
    mPlotContainer->setLayout(mainLayout);

    mCpbLabel = new QLabel("CPB");
    StyleSet::SetBackgroundColor(mCpbLabel,COLOR_BLACK);
    StyleSet::SetTextColor(mCpbLabel,COLOR_WHITE);
    StyleSet::SetTextFont(mCpbLabel,FONT_XXXL,FONT_FAMILY_NINA,true);
    mCpbLabel->setAlignment(Qt::AlignCenter);

    QVBoxLayout* tVlayout = new QVBoxLayout;
    tVlayout->setContentsMargins(0,0,0,0);
    tVlayout->addWidget(mPlotContainer);
    tVlayout->addWidget(mCpbLabel);
    setLayout(tVlayout);
    mLoopGraph->installEventFilter(mPlotContainer);
}

void MainDisplayWidget::InitConnect()
{
    connect(mLayoutSwitcher, &ComboBox::SignalValueChanged, this, [this](int value){
        if(value != SettingManager->GetIntSettingValue(SystemSettingManager::LAST_MAIN_DISPLAY_LAYOUT))
        {
            SettingManager->
                    SetSettingValue(SystemSettingManager::LAST_MAIN_DISPLAY_LAYOUT,value);
            LayoutSwitch(MAIN_DISPLAY_LAYOUT_ENUM(value));
        }
    });
    connect(SettingManager,&SystemSettingManager::SignalSettingChanged,[=](int settingId){
        if(settingId == SystemSettingManager::LAST_MAIN_DISPLAY_LAYOUT)
        {
            int WaveformsType = SettingManager->GetIntSettingValue(SystemSettingManager::LAST_MAIN_DISPLAY_LAYOUT);
            if(mLayoutSwitcher->value() != WaveformsType)
            {
                mLayoutSwitcher->setDumbValue(WaveformsType);
                LayoutSwitch(MAIN_DISPLAY_LAYOUT_ENUM(WaveformsType));
            }
        }
    });

    foreach (auto chart, findChildren<WaveChart *>())
    {
        connect(chart, &WaveChart::SignalMousePress, this, [this](const QPointF& pos){
            foreach (auto chart, findChildren<WaveChart *>())
            {
                chart->SetYCursor(pos.x());
            }
        });
    }

    connect(ChartManager::GetInstance(), &ChartManager::SignalFreezeFlagChanged, this, [this](bool isFreeze){
        if (isFreeze)
        {
            //mWaves[E_MAINSHOW_WAVEAREA_WAVE_INDEX::UI_MAINSHOW_WAVEAREA_WAVE_1]->setFocus();
        }
    });

    connect(SettingManager, &SystemSettingManager::SignalSettingChanged, this, [this](int settingId){
        if (settingId == SystemSettingManager::MAIN_WAVE_1 || settingId == SystemSettingManager::MAIN_WAVE_2 ||
                settingId == SystemSettingManager::MAIN_WAVE_3 || settingId == SystemSettingManager::MAIN_WAVE_4)
        {
            ChangeWaveChart(settingId, SettingManager->GetIntSettingValue(settingId));
        }
    });

}

void MainDisplayWidget::HideAllComponent()
{
    for (auto &&i : mWaves)
        i->hide();
    mLoopGraph->hide();
    mAllParamBoard->hide();
}


void MainDisplayWidget::ShowCPBInfo()
{
    mCpbLabel->setVisible(true);
    mPlotContainer->setVisible(false);
}

void MainDisplayWidget::ShowPlotInfo()
{
    mCpbLabel->setVisible(false);
    mPlotContainer->setVisible(true);
}

//设置对应位置显示的波形图
void MainDisplayWidget::ChangeWaveChart(int settingId, short waveType)
{
    WaveChart *concretePlot{};
    switch (settingId)
    {
    case SystemSettingManager::MAIN_WAVE_1:
        concretePlot = mWaves[E_MAINSHOW_WAVEAREA_WAVE_INDEX::UI_MAINSHOW_WAVEAREA_WAVE_1];
        break;
    case SystemSettingManager::MAIN_WAVE_2:
        concretePlot = mWaves[E_MAINSHOW_WAVEAREA_WAVE_INDEX::UI_MAINSHOW_WAVEAREA_WAVE_2];
        break;
    case SystemSettingManager::MAIN_WAVE_3:
        concretePlot = mWaves[E_MAINSHOW_WAVEAREA_WAVE_INDEX::UI_MAINSHOW_WAVEAREA_WAVE_3];
        break;
    case SystemSettingManager::MAIN_WAVE_4:
        concretePlot = mWaves[E_MAINSHOW_WAVEAREA_WAVE_INDEX::UI_MAINSHOW_WAVEAREA_WAVE_4];
        break;
    default:
        DebugAssert(0);
        break;
    }

    ChartManager::GetInstance()->RegisterChart(ChartManager::CHART_TYPE_ENUM::WAVE_CHART,
                                               (DataManager::WAVE_PARAM_TYPE)waveType, concretePlot);

    foreach (auto chart, findChildren<WaveChart *>())
    {
        chart->ClearData();
        chart->Redraw();
    }
}

void MainDisplayWidget::InitUiText()
{
    ValueStrIntraData *dataModel = new ValueStrIntraData;
    QMap<MAIN_DISPLAY_LAYOUT_ENUM, ALL_STRINGS_ENUM> strIdMap =
    {
        {FOUR_WAVE,                 ALL_STRINGS_ENUM::STR_FOUR_WAVE},
        {THREE_WAVE,                ALL_STRINGS_ENUM::STR_THREE_WAVE},
        {ONE_WAVE_ALL_PARAMS,       ALL_STRINGS_ENUM::STR_ALL_PARAMS},
        {TWO_WAVE_TWO_LOOP,         ALL_STRINGS_ENUM::STR_LOOP},
    };

    QMap<int, QString> tmpStrMap;
    for (auto it = strIdMap.begin(); it != strIdMap.end(); ++it)
    {
        if (!sLayoutEnable[it.key()])
        {
            continue;
        }
        tmpStrMap[it.key()] = UIStrings::GetStr(it.value());
    }
    dataModel->SetValueAndStr(tmpStrMap);
    mLayoutSwitcher->setDataModel(dataModel);
    mLayoutSwitcher->setDumbValue(SettingManager->GetIntSettingValue(SystemSettingManager::LAST_MAIN_DISPLAY_LAYOUT));
    if(VentModeSettingManager::GetInstance()->GetCurMode()!=VENTMODE_FLAG_HLM)
        LayoutSwitch(MAIN_DISPLAY_LAYOUT_ENUM(mLayoutSwitcher->value()));
}

void MainDisplayWidget::keyPressEvent(QKeyEvent *ev)
{
    switch(ev->key())
    {
    case KEY_ANTI_CLOSEWISE:
        foreach (auto chart, findChildren<WaveChart *>())
        {
            chart->CursorStepMove(WaveChart::POS_MINUS);
        }

        break;
    case KEY_CLOSEWISE:
        foreach (auto chart, findChildren<WaveChart *>())
        {
            chart->CursorStepMove(WaveChart::POS_PLUS);
        }
        break;
    default:
        break;
    }
//    if (!ChartManager::GetInstance()->GetIsFreeze())
//        QWidget::keyPressEvent(ev);
    FocusWidget::keyPressEvent(ev);
}

void MainDisplayWidget::resizeEvent(QResizeEvent *ev)
{
    FocusWidget::resizeEvent(ev);
    mCpbLabel->setFixedWidth(mPlotContainer->width());
    mLayoutSwitcher->move(mPlotContainer->width() - mLayoutSwitcher->width(), 0);
}

void MainDisplayWidget::LayoutSwitch(MAIN_DISPLAY_LAYOUT_ENUM en)
{
    HideAllComponent();
    mCpbLabel->setVisible(false);
    mPlotContainer->setVisible(true);
    mPlotContainer->layout()->activate();
    switch(en)
    {
    case MAIN_DISPLAY_LAYOUT_ENUM::FOUR_WAVE:
    {
        for (auto &&i : mWaves)
            i->show();
    }
        break;
    case MAIN_DISPLAY_LAYOUT_ENUM::THREE_WAVE:
    {
        for (int i = 0; i < E_MAINSHOW_WAVEAREA_WAVE_INDEX::UI_MAINSHOW_WAVEAREA_WAVE_4; i++)
        {
            mWaves[(E_MAINSHOW_WAVEAREA_WAVE_INDEX)i]->show();
        }
    }
        break;
    case MAIN_DISPLAY_LAYOUT_ENUM::ONE_WAVE_ALL_PARAMS:
    {
        mAllParamBoard->show();
        mWaves[E_MAINSHOW_WAVEAREA_WAVE_INDEX::UI_MAINSHOW_WAVEAREA_WAVE_1]->show();
    }
        break;
    case MAIN_DISPLAY_LAYOUT_ENUM::TWO_WAVE_TWO_LOOP:
    {
        for (int i = 0; i < E_MAINSHOW_WAVEAREA_WAVE_INDEX::UI_MAINSHOW_WAVEAREA_WAVE_3; i++)
        {
            mWaves[(E_MAINSHOW_WAVEAREA_WAVE_INDEX)i]->show();
        }

        mLoopGraph->show();
    }
        break;
    default:
        break;
    }
}
