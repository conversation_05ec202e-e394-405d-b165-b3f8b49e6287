#include "CO2Module.h"
#include <QSerialPort>
#include <QDebug>
#include <QTimer>
#include "DataManager.h"
#include "DebugLogManager.h"
#include "AlarmManager.h"
#include "ModuleTestManager.h"

#define STOPBIT QSerialPort::OneStop
#define PARITY QSerialPort::NoParity
#define SERIAL_NAME "ttyS5"

#define LISTEN_ORDER 0x80
#define CAL_ORDER 0x82
#define SET_ORDER 0x84
#define SILTENT_ORDER 0xC8
#define STOP_ORDER 0xC9
#define CLEAR_APNEA_ORDER 0xCC
#define RESET_ORDER 0xF8

#define SLOVE_PACKS_INTERVAL 20

static const QVector<uint8_t> sHeaderVec = {
    LISTEN_ORDER,CAL_ORDER,SET_ORDER,SILTENT_ORDER,STOP_ORDER,CLEAR_APNEA_ORDER,RESET_ORDER
};

CO2Module *CO2Module::GetInstance()
{
    static CO2Module* sInstance = new CO2Module;
    return sInstance;
}

bool CO2Module::IsCalibrationing()
{
    return mIsCaling;
}

bool CO2Module::IsWarming()
{
    return mIsWarming;
}

bool CO2Module::IsAdaptorConnected()
{
    return mIsAdaptorConnected;
}

void CO2Module::BeginCalibration()
{
    CommProxyThread::MiniTask task;
    QByteArray cmd;
    cmd.push_back(CAL_ORDER);
    cmd.push_back(0x01);
    cmd.push_back(0x7D);
    task.mData = cmd;
    mCommProxy->SendTask(task);
}

CO2Module::CO2Module(QObject *parent) : QThread(parent)
{
    QSerialPort *serialport = new QSerialPort;
    serialport->setPortName(SERIAL_NAME);
    serialport->setBaudRate(19200);
    serialport->setStopBits(STOPBIT);
    serialport->setParity(PARITY);

    mCommProxy = new CommProxyThread;
    mCommProxy->OpenSendLog(false,"CO2Module: ");
    mCommProxy->InstallReadOnePackHandler([this](QIODevice* io)-> bool { return ReadOnePackage(io);});
    mCommProxy->SetIoDevice(serialport);
    mCommProxy->OpenThread(true);

    ResetModule();

    qRegisterMetaType<CO2Module::BEGIN_CO2_CAL_RESULT>("BEGIN_CO2_CAL_RESULT");
}

bool CO2Module::ReadOnePackage(QIODevice *io)
{
    static uint8_t headByte;
    static int8_t length = -1;
    static QByteArray pack{};

    if(IsHeader(headByte))//读过包头了
    {
        if(length != -1)//读了长度了
        {
            if(pack.isEmpty())//还没读后续数据
            {
                if(io->bytesAvailable() >= length)//可以读后续数据
                {
                    pack=(io->read(length));

                    QByteArray vec;
                    vec.push_back(headByte);
                    vec.push_back(length);
                    vec.push_back(pack);

                    mRecvPackMutex.lock();
                    mRecvPackQue.enqueue(vec);
                    mRecvPackMutex.unlock();

                    headByte = 0;
                    length = -1;
                    pack.clear();
                }
            }
        }
        else//还没读长度
        {
            if(io->bytesAvailable() >= 1)//可以读长度
            {
                io->read((char*)&length,1);
                if(io->bytesAvailable() >= length)//可以读后续数据
                {
                    pack=(io->read(length));

                    QByteArray vec;
                    vec.push_back(headByte);
                    vec.push_back(length);
                    vec.push_back(pack);

                    mRecvPackMutex.lock();
                    mRecvPackQue.enqueue(vec);
                    mRecvPackMutex.unlock();

                    headByte = 0;
                    length = -1;
                    pack.clear();
                }
            }
        }
    }
    else//没读过包头
    {
        while(io->bytesAvailable() >= 1)
        {
            io->read((char*)&headByte,1);//尝试读出包头
            if(IsHeader(headByte))//读出包头
            {
                length = -1;
                pack.clear();
                break;
            }
        }
    }
    return false;
}

bool CO2Module::IsHeader(uint8_t byteValue)
{
    return sHeaderVec.contains(byteValue);
}

void CO2Module::ResetModule()
{
    CommProxyThread::MiniTask task;
    QByteArray cmd;
    cmd.push_back(RESET_ORDER);
    cmd.push_back(0x01);
    cmd.push_back(0x07);
    task.mData = cmd;
    mCommProxy->SendTask(task);
}

void CO2Module::ProceAlarm(uint8_t DB1, uint8_t DB2, uint8_t DB3, uint8_t DB4, uint8_t DB5)
{
    if((DB1 >> 1) & 0x01)
    {
        AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_CO2_REPLACE,1);

        mIsAdaptorConnected = false;
    }
    else
    {
        AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_CO2_REPLACE,0);

        mIsAdaptorConnected = true;
    }

    if(((DB2 >> 2) & 0x03) == 0x00)
    {
        mIsCaling = false;
    }
    else if(((DB2 >> 2) & 0x03) == 0x01)
    {
        mIsCaling = true;
    }

    if((DB2 & 0x03) == 0x00)
    {
        mIsWarming = false;
        AlarmManager::GetInstance()->TriggerAlarm(PROMPT_CO2_WARMING, 0);
    }
    else if((DB2 & 0x03) == 0x01)
    {
        mIsWarming = true;
        AlarmManager::GetInstance()->TriggerAlarm(PROMPT_CO2_WARMING, 1);
    }
    else if((DB2 & 0x03) == 0x02)
    {

    }
    else
    {
        mIsWarming = true;
        AlarmManager::GetInstance()->TriggerAlarm(PROMPT_CO2_WARMING, 1);
    }


    if((DB4 >> 2) & 0x01)
        AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_CO2_SAMPLING_ERR, 1);
    else
        AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_CO2_SAMPLING_ERR, 0);


    switch (DB5)
    {
    case 0x01:
        //模块温度过高
        break;
    case 0x02:
        //模块失效
        break;
    case 0x03:
        //模块未设置参数
        break;
    case 0x04:
        //null
        break;
    case 0x05:
        //模块正在校零
        break;
    case 0x06:
        //模块正在预热
        break;
    case 0x07:
        //检查采样管
        break;
    case 0x08:
        //模块需要校零
        break;
    case 0x09:
        //超出量程
        break;
    case 0x0A:
        //检查适配器
        break;
    }
}

void CO2Module::Upackage()
{
    static uint8_t tail;

    mRecvPackMutex.lock();
    while(!mRecvPackQue.empty())
    {
        if (None != ModuleTestManager::GetInstance()->GetAgModuleType())
        {
            ModuleTestManager::GetInstance()->UpdateModuleState(TEST_AG_CO2, ABLE);
        }

        QByteArray pack = mRecvPackQue.dequeue();

        tail = 0;
        for(int index = 0;index < pack.size()-1;index++)
            tail += pack[index];
        tail  = ~tail;
        tail += 1;
        tail &= 0x7F;
        if(tail != (uint8_t)pack.back())
            continue;
        switch ((uint8_t)pack[0])       //包头
        {
        case LISTEN_ORDER:
            DataManager::GetInstance()->UpdateWaveData(DataManager::WAVE_CO2, (uint8_t)pack[3]*128+(uint8_t)pack[4]-1000, false);

            switch ((uint8_t)pack[1])   //包长度
            {
            case 0x07:
                //根据DPI判断包类型
                switch ((uint8_t)pack[5])
                {
                case 0x02:
                    DataManager::GetInstance()->SaveMonitorData(VALUEDATA_ETCO2, ((uint8_t)pack[6]*128+(uint8_t)pack[7])/10);
                    break;
                case 0x04:
                    DataManager::GetInstance()->SaveMonitorData(VALUEDATA_FICO2, ((uint8_t)pack[6]*128+(uint8_t)pack[7])/10);
                    break;
                }
                break;
            case 0x0A:
                //主要是报警信息
                ProceAlarm((uint8_t)pack[6],(uint8_t)pack[7],(uint8_t)pack[8],(uint8_t)pack[9],(uint8_t)pack[10]);
                break;
            }
            break;
        case CAL_ORDER:
            switch ((uint8_t)pack[2])
            {
            case 0x00:
                mIsCaling = true;
                emit SignalBeginCalResult(CAL_BEGIN_SUCCESS);
                break;
            case 0x01:
                mIsCaling = false;
                emit SignalBeginCalResult(CAL_NOT_READY);
                break;
            case 0x02:
                mIsCaling = true;
                emit SignalBeginCalResult(CAL_ALREADY_CALING);
                break;
            }
        }
    }
    mRecvPackMutex.unlock();
}

void CO2Module::run()
{
    QTimer* mUnpackTimer = new QTimer;
    connect(mUnpackTimer,&QTimer::timeout,this,&CO2Module::Upackage,Qt::DirectConnection);
    mUnpackTimer->start(SLOVE_PACKS_INTERVAL);
    exec();
}
