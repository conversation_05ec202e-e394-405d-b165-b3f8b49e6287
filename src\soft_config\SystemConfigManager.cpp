﻿#include "SystemConfigManager.h"
#include "SettingSpinboxManager.h"
#include "random.cpp"
#include "CRC.h"
#include "DebugLogManager.h"
#include "VentModeSettingManager.h"
#include "base.h"
#include <QSettings>
#include <unistd.h>

//配置信息1st字节
#define AG_MODULE	(1 << 6)
#define CO2_MODULE	(1 << 5)
#define O2_MODULE	(1 << 4)
#define SPO2_MODULE	(1 << 3)
#define TS_MODULE	(1 << 2)
#define LOOP_MODULE	(1 << 1)
#define WAVE_COUNT	(1 << 0)

//配置信息2nd字节
#define VCV_MODE	(1 << 7)
#define PCV_MODE	(1 << 6)
#define PSV_MODE	(1 << 5)
#define SIMVVC_MODE	(1 << 4)
#define SIMVPC_MODE	(1 << 3)
#define PRVC_MODE	(1 << 2)
#define PMODE_MODE	(1 << 1)
#define HLM_MODE	(1 << 0)

//配置信息3rd字节
#define LOGO_EN		(1 << 5)
#define O2_GAS		(1 << 4)
#define N2O_GAS		(1 << 3)
#define AIR_GAS		(1 << 2)
#define NET_CONFIG	(1 << 1)
#define NET_MONITOR	(1 << 0)

//配置信息4th字节
//最上潮气量

//配置信息5th字节
//支持语言种类
#define ENGLISH_EN          (1 << 7)
#define CHINESE_EN          (1 << 6)
#define RUSSIAN_EN          (1 << 5)
#define FLOWMETER_MODULE    (1 << 4)

#define AG_MODULE_TYPE      (1 << 0)

#define SPO2_TAIDA_EN (1 << 4)


QVector<SystemConfigItemSt> SystemConfigManager::sConfigDefault
{
    {NETWORK_CONFIG_BOOL_INDEX,                    0,                                                 },
    {NETWORK_MONITOR_BOOL_INDEX,                 1,                                   },
    {MACHINE_RULE_STANDARD_INDEX,               STD_CHINA,                                                 },
    {MACHINE_MODEL_INDEX,                           CENAR_30,                                    },
    {AG_BOOL_INDEX,                                 1,                                                 },
    {CO2_BOOL_INDEX,                                1,                    },
    {O2_BOOL_INDEX,                                 1,                   },
    {LOOP_BOOL_INDEX,                               1,                                                       },
    {TOUCHSCREEN_BOOL_INDEX,                        0,                                           },
    {TOUCHSCREEN_TYPE_INDEX,                       0,                                            },
    {WAVE_NUM_INDEX,                                     3,                                           },
    {ENABLE_FLOWMETER_MODULE,                    1,                                              },
    {GAS_SOURCE_O2_BOOL_INDEX,                      1,                                           },
    {GAS_SOURCE_N2O_BOOL_INDEX,                     1,                                           },
    {GAS_SOURCE_AIR_BOOL_INDEX,                     0,                                           },
    {VCV_BOOL_INDEX,                                      1,                                                                           },
    {PCV_BOOL_INDEX,                                                                    1,                                                                           },
    {SIMVVC_BOOL_INDEX,                                                                    1,                                                                           },
    {SIMVPC_BOOL_INDEX,                                                                1,                                                                           },
    {PSV_BOOL_INDEX,                                                                 1,                                                                           },
    {PMODE_BOOL_INDEX,                                                                   0,                                                                           },
    {PRVC_BOOL_INDEX,                                        1,                                                                                               },
    {HLM_BOOL_INDEX,                                                                     1,                                                                   },
    {MIN_VT_INDEX,                                                      15,                                                                                   },
    {MANUFACTURE_YEAR_INDEX,                       2013,                                                                                                      },
    {MANUFACTURE_MONTH_INDEX,                            6,                                                                                                   },
    {MANUFACTURE_DAY_INDEX,                              6,                                                                                                   },
    {SERIAL_MODULE_INDEX,                                "C3",                                                                                                },
    {SERIAL_YEAR_INDEX,                                  13,                                                                                                  },
    {SERIAL_MONTH_INDEX,                                 6,                                                                                                                                                   },
    {SERIAL_DAY_INDEX,                                   6,                                                                                                   },
    {SERIAL_NUM_1_INDEX,                                 0,                                                                                                   },
    {SERIAL_NUM_2_INDEX,                                 0,                                                                                                   },
    {SERIAL_NUM_3_INDEX,                                 0,                                                                                                   },
    {SERIAL_NUM_4_INDEX,                                 0,                                                                                                   },
    {VENT_MODE_NUM_INDEX,                            0,                                                                                                        },
    {BIG_SCREEN_BOOL_INDEX,                           1,                                                                                                },
    {ENABLE_LOGO,                                         0x01,                                                                                               },
    {ENABLE_ENGLISH,                                    0x01,                                                                                                 },  //tbd lijin
    {ENABLE_CHINESE,                                    0x01,                                                                                                 },  //tbd lijin
    {ENABLE_RUSSIAN,                                    0x00,                                                                                                 },  //tbd lijin
    {ENABLE_UKRAINIAN,                                  0x01,                                                                                                 },
    {ENABLE_FRENCH,                                     0x01,                                                                                                 },
    {AG_TYPE_INDEX,                                      0,                                                                                                   },
};


SystemConfigManager::SystemConfigManager()
{
    mSystemConfigDbInterface = new SystemConfigInterface;
    InitConfig();
}

SystemConfigManager *SystemConfigManager::GetInstance()
{
    static SystemConfigManager sInstance;
    return &sInstance;
}

void SystemConfigManager::CheckVentMode()
{
    int ventModeNum = 0;
    for (int i = VCV_BOOL_INDEX; i < HLM_BOOL_INDEX; i++)
    {
        ventModeNum += mCurConfigVec[i].mValue.toBool();
    }
    if ( ventModeNum > 7 || ventModeNum < 0 || !mCurConfigVec[VCV_BOOL_INDEX].mValue.toBool())
    {//防止呼吸模式配置错误
        mCurConfigVec[VCV_BOOL_INDEX].mValue = 1;
        mCurConfigVec[PCV_BOOL_INDEX].mValue = 1;
        mCurConfigVec[PSV_BOOL_INDEX].mValue = 0;
        mCurConfigVec[SIMVPC_BOOL_INDEX].mValue = 0;
        mCurConfigVec[SIMVVC_BOOL_INDEX].mValue = 0;
        mCurConfigVec[PRVC_BOOL_INDEX].mValue = 0;
        mCurConfigVec[PMODE_BOOL_INDEX].mValue = 0;
        mCurConfigVec[HLM_BOOL_INDEX].mValue = 0;
    }
}

QVector<SystemConfigItemSt> SystemConfigManager::GetAllConfig()
{
    return mCurConfigVec;
}


void SystemConfigManager::RestoreFactoryConfig()
{
    for (auto &&configItem : sConfigDefault)
    {
        switch (configItem.mValue.type())
        {
        case QVariant::Int:
            SetConfigValue((SystemConfigManager::SYSTEM_CONFIG_ENUM)configItem.mConfigId, configItem.mValue.toInt());
            break;
        case QVariant::Double:
            SetConfigValue((SystemConfigManager::SYSTEM_CONFIG_ENUM)configItem.mConfigId, configItem.mValue.toDouble());
            break;
        case QVariant::String:
            SetConfigValue((SystemConfigManager::SYSTEM_CONFIG_ENUM)configItem.mConfigId, configItem.mValue.toString());
            break;
        default:
            break;
        }
    }
}

QByteArray SystemConfigManager::BgDecrypt(QByteArray bgCipher)
{
    unsigned char *p_buf = (unsigned char*)bgCipher.data();

    int dataSize = bgCipher.size();
    //还原种子和校验码原貌
    char low_4_bit = p_buf[dataSize - 1] & 0x0F;
    p_buf[dataSize - 1] &= 0xF0;
    p_buf[dataSize - 1] |= (p_buf[dataSize - 2] & 0x0F);
    p_buf[dataSize - 2] &= 0xF0;
    p_buf[dataSize - 2] |= low_4_bit;

    //还原数据原貌
    unsigned char rand_val = 0;
    unsigned char rand_seed = p_buf[dataSize - 2];
    bg_srand(rand_seed);
    for (int i = 0; i < dataSize - 2; ++i)
    {
        rand_val = (unsigned char)bg_rand();
        p_buf[i] ^= rand_val;
    }

    //校验
    Make_Table_8();
    unsigned char crc_value = Crc_CcittArray((unsigned char *)p_buf, dataSize - 1);
    if (p_buf[dataSize - 1] != crc_value)
    {
        return 0;
    }


    QByteArray plainText((char*)p_buf, dataSize - 2);

    return plainText;
}

int SystemConfigManager::VerifySn(unsigned char *machSn)
{
    char value = 0;
    unsigned short model;
    unsigned short year;
    unsigned short month;
    unsigned short flow_num_1;
    unsigned short flow_num_2;
    unsigned short flow_num_3;
    unsigned short flow_num_4;

    value = (machSn[0]>>4) & 0x0F;
    switch (value)
    {
    case 0:
        model = CENAR_30;
        break;
    case 1:
        model = CENAR_40;
        break;
    case 2:
        model = CENAR_50;
        break;
    case 3:
        model = ELUNA_60;
        break;
    case 4:
        model = ELUNA_70;
        break;
    case 5:
        model = ELUNA_80;
        break;
    case 6:
        model = OEM;
        break;
    case 7:
        model = CENAR_30M;
        break;
    default:
        return 0;
        break;
    }

    year = ((machSn[0]<<4) & 0xF0) | ((machSn[1]>>4) & 0x0F);
    month = machSn[1] & 0x0F;
    flow_num_1 = (machSn[2]>>4) & 0x0F;
    flow_num_2 = machSn[2] & 0x0F;
    flow_num_3 = (machSn[3]>>4) & 0x0F;
    flow_num_4 = machSn[3] & 0x0F;

    auto configVec = ConfigManager->GetAllConfig();
    if (model == configVec[SystemConfigManager::MACHINE_MODEL_INDEX].mValue &&
            year == configVec[SystemConfigManager::SERIAL_YEAR_INDEX].mValue &&
            month == configVec[SystemConfigManager::SERIAL_MONTH_INDEX].mValue &&
            flow_num_1 == configVec[SystemConfigManager::SERIAL_NUM_1_INDEX].mValue &&
            flow_num_2 == configVec[SystemConfigManager::SERIAL_NUM_2_INDEX].mValue &&
            flow_num_3 == configVec[SystemConfigManager::SERIAL_NUM_3_INDEX].mValue &&
            flow_num_4 == configVec[SystemConfigManager::SERIAL_NUM_4_INDEX].mValue)
    {
        return 1;
    }

    return 0;
}

void SystemConfigManager::ParseConfig(QVector<SystemConfigItemSt> &configs, unsigned char *config)
{
    //字节1st
    configs[SystemConfigManager::AG_BOOL_INDEX].mValue                              = (unsigned short)(!!(config[0] & AG_MODULE));
    configs[SystemConfigManager::CO2_BOOL_INDEX].mValue                             = (unsigned short)(!!(config[0] & CO2_MODULE));
    configs[SystemConfigManager::O2_BOOL_INDEX].mValue                              = (unsigned short)(!!(config[0] & O2_MODULE));

    configs[SystemConfigManager::TOUCHSCREEN_BOOL_INDEX].mValue                     = (unsigned short)(!!(config[0] & TS_MODULE));
    configs[SystemConfigManager::LOOP_BOOL_INDEX].mValue                            = (unsigned short)(!!(config[0] & LOOP_MODULE));
    configs[SystemConfigManager::WAVE_NUM_INDEX].mValue                             = (config[0] & WAVE_COUNT) ? 3 : 2;

    //字节2nd
    configs[SystemConfigManager::VCV_BOOL_INDEX].mValue                             = (unsigned short)(!!(config[1] & VCV_MODE));
    configs[SystemConfigManager::PCV_BOOL_INDEX].mValue                             = (unsigned short)(!!(config[1] & PCV_MODE));
    configs[SystemConfigManager::PSV_BOOL_INDEX].mValue                             = (unsigned short)(!!(config[1] & PSV_MODE));
    configs[SystemConfigManager::SIMVVC_BOOL_INDEX].mValue                          = (unsigned short)(!!(config[1] & SIMVVC_MODE));
    configs[SystemConfigManager::SIMVPC_BOOL_INDEX].mValue                          = (unsigned short)(!!(config[1] & SIMVPC_MODE));
    configs[SystemConfigManager::PRVC_BOOL_INDEX].mValue                            = (unsigned short)(!!(config[1] & PRVC_MODE));
    configs[SystemConfigManager::PMODE_BOOL_INDEX].mValue                           = (unsigned short)(!!(config[1] & PMODE_MODE));
    configs[SystemConfigManager::HLM_BOOL_INDEX].mValue                             = (unsigned short)(!!(config[1] & HLM_MODE));

    //字节3rd
    configs[SystemConfigManager::ENABLE_LOGO].mValue                                = (unsigned short)(!!(config[2] & LOGO_EN));
    configs[SystemConfigManager::GAS_SOURCE_O2_BOOL_INDEX].mValue                   = (unsigned short)(!!(config[2] & O2_GAS));
    configs[SystemConfigManager::GAS_SOURCE_N2O_BOOL_INDEX].mValue                  = (unsigned short)(!!(config[2] & N2O_GAS));
    configs[SystemConfigManager::GAS_SOURCE_AIR_BOOL_INDEX].mValue                  = (unsigned short)(!!(config[2] & AIR_GAS));
    configs[SystemConfigManager::NETWORK_CONFIG_BOOL_INDEX].mValue                  = (unsigned short)(!!(config[2] & NET_CONFIG));
    configs[SystemConfigManager::NETWORK_MONITOR_BOOL_INDEX].mValue                 = (unsigned short)(!!(config[2] & NET_MONITOR));

    //字节4th
    configs[SystemConfigManager::MIN_VT_INDEX].mValue                               =   config[3];

    //字节5th
    //configs[SystemConfigManager::ENABLE_ENGLISH].mValue                             = !!(config[4] & ENGLISH_EN);
    //configs[SystemConfigManager::ENABLE_CHINESE].mValue                             = !!(config[4] & CHINESE_EN);
    //configs[SystemConfigManager::ENABLE_RUSSIAN].mValue                           = !!(config[4] & RUSSIAN_EN);
    if (configs[MACHINE_MODEL_INDEX].mValue == ELUNA_80)
    {
        configs[SystemConfigManager::ENABLE_FLOWMETER_MODULE].mValue                = (unsigned short)(!!(config[4] & FLOWMETER_MODULE));
    }
    else
    {
        mCurConfigVec[SystemConfigManager::ENABLE_FLOWMETER_MODULE].mValue          = (unsigned short)1;
    }

    configs[SystemConfigManager::AG_TYPE_INDEX].mValue                              = (unsigned short)(!!(config[4] & AG_MODULE_TYPE));
    //    configs[SystemConfigManager::ENAB].mValue = !!(config[4] & SPO2_TAIDA_EN);
}

ConfigMessage SystemConfigManager::GetMachineConfig()
{
    ConfigMessage tmpConfig;
    memset(&tmpConfig ,0 ,sizeof(tmpConfig));

    tmpConfig.AgBool = GetConfig<bool>(SystemConfigManager::AG_BOOL_INDEX);
    tmpConfig.Co2Bool = GetConfig<bool>(SystemConfigManager::CO2_BOOL_INDEX);
    tmpConfig.O2Bool = GetConfig<bool>(SystemConfigManager::O2_BOOL_INDEX);

    tmpConfig.TouchScreenBool = GetConfig<bool>(SystemConfigManager::TOUCHSCREEN_BOOL_INDEX);
    tmpConfig.LoopBool = GetConfig<bool>(SystemConfigManager::LOOP_BOOL_INDEX);
    tmpConfig.WaveNum = GetConfig<unsigned short>(SystemConfigManager::WAVE_NUM_INDEX);

    tmpConfig.VcvBool = GetConfig<bool>(SystemConfigManager::VCV_BOOL_INDEX);
    tmpConfig.PcvBool = GetConfig<bool>(SystemConfigManager::PCV_BOOL_INDEX);
    tmpConfig.PsvBool = GetConfig<bool>(SystemConfigManager::PSV_BOOL_INDEX);
    tmpConfig.SimvvcBool = GetConfig<bool>(SystemConfigManager::SIMVVC_BOOL_INDEX);
    tmpConfig.SimvpcBool = GetConfig<bool>(SystemConfigManager::SIMVPC_BOOL_INDEX);
    tmpConfig.PrvcBool = GetConfig<bool>(SystemConfigManager::PRVC_BOOL_INDEX);
    tmpConfig.PmodeBool = GetConfig<bool>(SystemConfigManager::PMODE_BOOL_INDEX);
    tmpConfig.HlmBool = GetConfig<bool>(SystemConfigManager::HLM_BOOL_INDEX);

    tmpConfig.EnableLogo = GetConfig<bool>(SystemConfigManager::ENABLE_LOGO);
    tmpConfig.SourceO2Bool = GetConfig<bool>(SystemConfigManager::GAS_SOURCE_O2_BOOL_INDEX);
    tmpConfig.SourceN2oBool = GetConfig<bool>(SystemConfigManager::GAS_SOURCE_N2O_BOOL_INDEX);
    tmpConfig.SourceAirBool = GetConfig<bool>(SystemConfigManager::GAS_SOURCE_AIR_BOOL_INDEX);
    tmpConfig.NetworkConfigBool = GetConfig<bool>(SystemConfigManager::NETWORK_CONFIG_BOOL_INDEX);
    tmpConfig.NetMonitorBool = GetConfig<bool>(SystemConfigManager::NETWORK_MONITOR_BOOL_INDEX);

    tmpConfig.MinVt = GetConfig<unsigned short>(SystemConfigManager::MIN_VT_INDEX);

    tmpConfig.English = GetConfig<bool>(SystemConfigManager::ENABLE_ENGLISH);
    tmpConfig.Chiness = GetConfig<bool>(SystemConfigManager::ENABLE_CHINESE);
    tmpConfig.Russian = GetConfig<bool>(SystemConfigManager::ENABLE_RUSSIAN);
    if (GetConfig<int>(SystemConfigManager::MACHINE_MODEL_INDEX) == ELUNA_80)
    {
        tmpConfig.FlowmeterBool = GetConfig<bool>(SystemConfigManager::ENABLE_FLOWMETER_MODULE);
    }

    tmpConfig.AgModuleType = GetConfig<unsigned short>(SystemConfigManager::AG_TYPE_INDEX);

    tmpConfig.MachineRuleStandard = GetConfig<unsigned short>(SystemConfigManager::MACHINE_RULE_STANDARD_INDEX);
    if(GetConfig<unsigned short>(SystemConfigManager::MACHINE_MODEL_INDEX) == 6)
    {
        tmpConfig.MachineMode = OEM;
    }
    else if(GetConfig<unsigned short>(SystemConfigManager::MACHINE_MODEL_INDEX) == 7)
    {
        tmpConfig.MachineMode = CENAR_30M;
    }
    else
    {
        tmpConfig.MachineMode = GetConfig<unsigned short>(SystemConfigManager::MACHINE_MODEL_INDEX);
    }

    tmpConfig.ManufactureYear = GetConfig<unsigned short>(SystemConfigManager::MANUFACTURE_YEAR_INDEX);
    tmpConfig.ManufactureMonth = GetConfig<unsigned short>(SystemConfigManager::MANUFACTURE_MONTH_INDEX);
    if(GetConfig<unsigned short>(SystemConfigManager::SERIAL_DAY_INDEX) != 0)
    {
        tmpConfig.ManufactureDay = GetConfig<unsigned short>(SystemConfigManager::MANUFACTURE_DAY_INDEX) * 100 + GetConfig<unsigned short>(SystemConfigManager::SERIAL_DAY_INDEX);
    }
    else
    {
        tmpConfig.ManufactureDay = GetConfig<unsigned short>(SystemConfigManager::MANUFACTURE_DAY_INDEX);
    }


    QString Number = GetConfig<QString>(SystemConfigManager::SERIAL_MODULE_INDEX);
    QByteArray tmpByteArray = Number.toLatin1();
    tmpConfig.SerialMoudle = tmpByteArray.at(0) | (tmpByteArray.at(1) << 8);

    Number = GetConfig<QString>(SystemConfigManager::SERIAL_YEAR_INDEX);
    tmpByteArray = Number.toLatin1();
    if( Number.toInt() > 9 )
        tmpConfig.SerialYear = tmpByteArray.at(0) | (tmpByteArray.at(1) << 8);
    else
        tmpConfig.SerialYear = 0x30 | ((Number.toInt() + 0x30) << 8);

    Number = GetConfig<QString>(SystemConfigManager::SERIAL_MONTH_INDEX);
    tmpByteArray = Number.toLatin1();
    if(Number.toInt() > 9)
        tmpConfig.SerialMonth = tmpByteArray.at(0) | (tmpByteArray.at(1) << 8);
    else
        tmpConfig.SerialMonth = 0x30 | ((Number.toInt() + 0x30) << 8);

    Number = GetConfig<QString>(SystemConfigManager::SERIAL_NUM_1_INDEX);
    tmpByteArray = Number.toLatin1();
    tmpConfig.SerialNumOne = tmpByteArray.at(0);
    Number = GetConfig<QString>(SystemConfigManager::SERIAL_NUM_2_INDEX);
    tmpByteArray = Number.toLatin1();
    tmpConfig.SerialNumTwo = tmpByteArray.at(0);
    Number = GetConfig<QString>(SystemConfigManager::SERIAL_NUM_3_INDEX);
    tmpByteArray = Number.toLatin1();
    tmpConfig.SerialNumThree = tmpByteArray.at(0);
    Number = GetConfig<QString>(SystemConfigManager::SERIAL_NUM_4_INDEX);
    tmpByteArray = Number.toLatin1();
    tmpConfig.SerialNumFour = tmpByteArray.at(0);
    DebugLog << "SerialNumOne:" << tmpConfig.SerialNumOne;
    DebugLog << "SerialNumTwo:" << tmpConfig.SerialNumTwo;
    DebugLog << "SerialNumThree:" << tmpConfig.SerialNumThree;
    DebugLog << "SerialNumFour:" << tmpConfig.SerialNumFour;

    memset(tmpConfig.AnimalVersion, '\0', sizeof(tmpConfig.AnimalVersion));
    QByteArray byteArray = QString(DEV_SOFT_VERSION).toUtf8();
    int versionLength = byteArray.size() < sizeof(tmpConfig.AnimalVersion) ? byteArray.size() : sizeof(tmpConfig.AnimalVersion);

    memcpy(tmpConfig.AnimalVersion, byteArray.constData(), versionLength);

    QString productModel = "";
    QSettings settings("Config.ini", QSettings::IniFormat);
    QString customProductModel =  settings.value("ProductModel", "").toString();
    if(customProductModel == "")
    {
        productModel = UIStrings::GetStr((ALL_STRINGS_ENUM)ConfigManager->GetMachineModeNameId(
                                           ConfigManager->GetConfig<int>(SystemConfigManager::MACHINE_MODEL_INDEX)));
    }
    else
    {
        productModel = customProductModel;
    }

    QByteArray productModelBytes = productModel.toUtf8();
    memset(tmpConfig.ProductModel, 0, sizeof(tmpConfig.ProductModel));
    size_t copyLength = std::min<size_t>(productModelBytes.size(), sizeof(tmpConfig.ProductModel));
    memcpy(tmpConfig.ProductModel, productModelBytes.constData(), copyLength);

    return tmpConfig;
}

void SystemConfigManager::SetMachineConfig(ConfigMessage config)
{
    mCurConfigVec[SystemConfigManager::AG_BOOL_INDEX].mValue                        = config.AgBool;
    mCurConfigVec[SystemConfigManager::CO2_BOOL_INDEX].mValue                       = config.Co2Bool;
    mCurConfigVec[SystemConfigManager::O2_BOOL_INDEX].mValue                        = config.O2Bool;

    mCurConfigVec[SystemConfigManager::TOUCHSCREEN_BOOL_INDEX].mValue               = config.TouchScreenBool;
    mCurConfigVec[SystemConfigManager::LOOP_BOOL_INDEX].mValue                      = config.LoopBool;
    mCurConfigVec[SystemConfigManager::WAVE_NUM_INDEX].mValue                       = config.WaveNum;

    mCurConfigVec[SystemConfigManager::VCV_BOOL_INDEX].mValue                       = config.VcvBool;
    mCurConfigVec[SystemConfigManager::PCV_BOOL_INDEX].mValue                       = config.PcvBool;
    mCurConfigVec[SystemConfigManager::PSV_BOOL_INDEX].mValue                       = config.PsvBool;
    mCurConfigVec[SystemConfigManager::SIMVVC_BOOL_INDEX].mValue                    = config.SimvvcBool;
    mCurConfigVec[SystemConfigManager::SIMVPC_BOOL_INDEX].mValue                    = config.SimvpcBool;
    mCurConfigVec[SystemConfigManager::PRVC_BOOL_INDEX].mValue                      = config.PrvcBool;
    mCurConfigVec[SystemConfigManager::PMODE_BOOL_INDEX].mValue                     = config.PmodeBool;
    mCurConfigVec[SystemConfigManager::HLM_BOOL_INDEX].mValue                       = config.HlmBool;

    mCurConfigVec[SystemConfigManager::ENABLE_LOGO].mValue                          = config.EnableLogo;
    mCurConfigVec[SystemConfigManager::GAS_SOURCE_O2_BOOL_INDEX].mValue             = config.SourceO2Bool;
    mCurConfigVec[SystemConfigManager::GAS_SOURCE_N2O_BOOL_INDEX].mValue            = config.SourceN2oBool;
    mCurConfigVec[SystemConfigManager::GAS_SOURCE_AIR_BOOL_INDEX].mValue            = config.SourceAirBool;
    mCurConfigVec[SystemConfigManager::NETWORK_CONFIG_BOOL_INDEX].mValue            = config.NetworkConfigBool;
    mCurConfigVec[SystemConfigManager::NETWORK_MONITOR_BOOL_INDEX].mValue           = config.NetMonitorBool;

    mCurConfigVec[SystemConfigManager::MIN_VT_INDEX].mValue                         = config.MinVt;

    //mCurConfigVec[SystemConfigManager::ENABLE_ENGLISH].mValue                       = config.English;
    //mCurConfigVec[SystemConfigManager::ENABLE_CHINESE].mValue                       = config.Chiness;
    //mCurConfigVec[SystemConfigManager::ENABLE_RUSSIAN].mValue                       = config.Russian;
    if (mCurConfigVec[MACHINE_MODEL_INDEX].mValue == ELUNA_80)
    {
        mCurConfigVec[SystemConfigManager::ENABLE_FLOWMETER_MODULE].mValue              = config.FlowmeterBool;
    }
    else
    {
        mCurConfigVec[SystemConfigManager::ENABLE_FLOWMETER_MODULE].mValue              = 1;
    }

    mCurConfigVec[SystemConfigManager::AG_TYPE_INDEX].mValue                        = config.AgModuleType;

    mCurConfigVec[SystemConfigManager::MACHINE_RULE_STANDARD_INDEX].mValue          = config.MachineRuleStandard;
    if(config.MachineMode >= CENAR_30 && config.MachineMode < MACHINE_SERIAL_MAX)
    {
        //以前枚举值反了，防止对出货的机器产生影响不修改枚举顺序。
        if(config.MachineMode == 6)
        {
            mCurConfigVec[SystemConfigManager::MACHINE_MODEL_INDEX].mValue          = OEM;
        }
        else if(config.MachineMode == 7)
        {
            mCurConfigVec[SystemConfigManager::MACHINE_MODEL_INDEX].mValue          = CENAR_30M;
        }
        else
        {
            mCurConfigVec[SystemConfigManager::MACHINE_MODEL_INDEX].mValue          = config.MachineMode;
        }
    }

    mCurConfigVec[SystemConfigManager::MANUFACTURE_YEAR_INDEX].mValue               = config.ManufactureYear;
    mCurConfigVec[SystemConfigManager::MANUFACTURE_MONTH_INDEX].mValue              = config.ManufactureMonth;

    mCurConfigVec[SystemConfigManager::SERIAL_MODULE_INDEX].mValue                  = QString::fromLatin1((char *)&(config.SerialMoudle),2);
    mCurConfigVec[SystemConfigManager::SERIAL_YEAR_INDEX].mValue                    = QString::fromLatin1((char *)&(config.SerialYear),2);
    mCurConfigVec[SystemConfigManager::SERIAL_MONTH_INDEX].mValue                   = QString::fromLatin1((char *)&(config.SerialMonth),2);
    mCurConfigVec[SystemConfigManager::SERIAL_NUM_1_INDEX].mValue                   = QString::fromLatin1((char *)&(config.SerialNumOne),1);
    mCurConfigVec[SystemConfigManager::SERIAL_NUM_2_INDEX].mValue                   = QString::fromLatin1((char *)&(config.SerialNumTwo),1);
    mCurConfigVec[SystemConfigManager::SERIAL_NUM_3_INDEX].mValue                   = QString::fromLatin1((char *)&(config.SerialNumThree),1);
    mCurConfigVec[SystemConfigManager::SERIAL_NUM_4_INDEX].mValue                   = QString::fromLatin1((char *)&(config.SerialNumFour),1);

    if(config.ManufactureDay > 100)
    {
        mCurConfigVec[SystemConfigManager::MANUFACTURE_DAY_INDEX].mValue = config.ManufactureDay / 100;
        mCurConfigVec[SystemConfigManager::SERIAL_DAY_INDEX].mValue = config.ManufactureDay % 100;
    }
    else
    {
        mCurConfigVec[SystemConfigManager::MANUFACTURE_DAY_INDEX].mValue = config.ManufactureDay;
        mCurConfigVec[SystemConfigManager::SERIAL_DAY_INDEX].mValue = 0;
    }

    QByteArray productModelBytes(reinterpret_cast<const char*>(config.ProductModel), sizeof(config.ProductModel));
    QString productModel = QString::fromUtf8(productModelBytes).trimmed();
    QSettings settings("Config.ini", QSettings::IniFormat);
    settings.setValue("ProductModel", productModel);
    settings.sync();
#ifdef ARM
    sync();
#endif

    UpdateConfigs(mCurConfigVec);
}

bool SystemConfigManager::UpdateByConfigCode(QString code)
{
    if (3*(ENC_LENGTH) != code.size())
    {
        return 0;
    }

    //rsa解密
    auto bgCipher = RsaDecrypt(code.toLatin1());
    //解密，分离出mach_sn[], config[]
    auto bgPlaintext = BgDecrypt(bgCipher);
    auto sn = bgPlaintext.mid(0, SN_LENGTH);
    auto config = bgPlaintext.mid(SN_LENGTH, CONFIG_LENGTH);

    if (!VerifySn((unsigned char*)sn.data()))
    {
        return 0;
    }

    ParseConfig(mCurConfigVec, (unsigned char *)config.data());
    UpdateConfigs(mCurConfigVec);
    return true;
}

bool SystemConfigManager::IsFullTouch()
{
    if(GetConfig<int>(SystemConfigManager::MACHINE_MODEL_INDEX)==ELUNA_70||
            GetConfig<int>(SystemConfigManager::MACHINE_MODEL_INDEX)==ELUNA_80)
        return true;
    return false;
}

bool SystemConfigManager::IsFullElecFlomter()
{
    return (GetConfig<int>(SystemConfigManager::MACHINE_MODEL_INDEX) == ELUNA_80);
}

bool SystemConfigManager::IsDoubleBattery()
{
    return IsFullTouch();
}

QVariant SystemConfigManager::ReadDefaultValue(SYSTEM_CONFIG_ENUM settingId)
{
    return sConfigDefault[settingId].mValue;
}

int SystemConfigManager::GetMachineModeNameId(int machineModelEnum)
{
    switch(machineModelEnum)
    {
    case CENAR_30:
        return STR_CENAR_30;
        break;
    case CENAR_40:
        return STR_CENAR_40;
        break;
    case CENAR_50:
        return STR_CENAR_50;
        break;
    case ELUNA_60:
        return STR_ELUNA_60;
        break;
    case ELUNA_70:
        return STR_ELUNA_70;
        break;
    case ELUNA_80:
        return STR_ELUNA_80;
        break;
    case CENAR_30M:
        return STR_CENAR_30M;
        break;
    case OEM:
        return STR_OEM;
        break;
    default:
        return STR_NULL;
        break;
    }
}

void SystemConfigManager::InitConfig()
{
    for (int i = 0; i < SYSTEM_CONFIG_ENUM_END; i++)
    {
        mCurConfigVec << sConfigDefault[(SYSTEM_CONFIG_ENUM)i];
    }
    auto configs = mSystemConfigDbInterface->GetAllConfig();
    if(configs.size() < sConfigDefault.size())
    {
        for(int i = configs.size(); i < sConfigDefault.size(); ++i)
        {
            mSystemConfigDbInterface->InsertConfig(sConfigDefault[i].mConfigId,sConfigDefault[i].mValue);
        }
    }
    UpdateConfigs(configs);
}

void SystemConfigManager::SetConfigValue(SYSTEM_CONFIG_ENUM configId, QVariant context)
{
    mCurConfigVec[configId].mValue = context;
    mSystemConfigDbInterface->SetSystemConfig(configId, context);
}

void SystemConfigManager::UpdateConfigs(const QVector<SystemConfigItemSt> &configs)
{
    for (auto &&config : configs)
    {
        if (config.mConfigId >= SYSTEM_CONFIG_ENUM_END || config.mConfigId == -1)
        {
            continue;
        }
        SetConfigValue((SYSTEM_CONFIG_ENUM)config.mConfigId, config.mValue);
    }

    CheckVentMode();
    if (mCurConfigVec[MACHINE_MODEL_INDEX].mValue == CENAR_30M)
    {
        mCurConfigVec[ENABLE_FLOWMETER_MODULE].mValue = false;
    }
}

QVariant SystemConfigManager::ReadConfigValue(int id)
{
//    if(id==MACHINE_MODEL_INDEX)
//        return ELUNA_80;
    return mCurConfigVec[id].mValue;
}

