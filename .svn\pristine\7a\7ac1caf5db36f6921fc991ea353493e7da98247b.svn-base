//2025-06-04 14:34:24
//Generated by BAIGE
#pragma once
#include <QStringList>
#include "StringEnum.h"

namespace Chinese
{
    QStringList UiStr
    {

		u8R"()", //STR_NULL
		u8R"(VCV)", //STR_LABEL_VCV
		u8R"(PCV)", //STR_LABEL_PCV
		u8R"(PSV)", //STR_LABEL_PSV
		u8R"(SIMV-VC)", //STR_LABEL_SIMVVCV
		u8R"(SIMV-PC)", //STR_LABEL_SIMVPCV
		u8R"(PRVC)", //STR_LABEL_PRVC
		u8R"(P-mode)", //STR_LABEL_PMODE
		u8R"(HLM)", //STR_LABEL_HLM
		u8R"(CPB)", //STR_LABEL_CPB
		u8R"(MECHANICAL)", //STR_MODE_MECHANICAL
		u8R"(ACGO)", //STR_MODE_ACGO
		u8R"(Standby)", //STR_MODE_STANDBY
		u8R"(Manual)", //STR_MODE_MANUAL
		u8R"(Rate)", //STR_VALUEPARAM_RATE
		u8R"(fspn)", //STR_VALUEPARAM_FSPN
		u8R"(I:E)", //STR_VALUEPARAM_IE
		u8R"(VTe)", //STR_VALUEPARAM_VTE
		u8R"(VTi)", //STR_VALUEPARAM_VTI
		u8R"(MV)", //STR_VALUEPARAM_MV
		u8R"(MVspn)", //STR_VALUEPARAM_MVSPN
		u8R"(Ppeak)", //STR_VALUEPARAM_PPEAK
		u8R"(Pplat)", //STR_VALUEPARAM_PPLAT
		u8R"(PEEP)", //STR_VALUEPARAM_PEEP
		u8R"(Pmean)", //STR_VALUEPARAM_PMEAN
		u8R"(Pmin)", //STR_VALUEPARAM_PMIN
		u8R"(R)", //STR_VALUEPARAM_R
		u8R"(C)", //STR_VALUEPARAM_C
		u8R"(FiO2)", //STR_VALUEPARAM_FIO2
		u8R"(EtCO2)", //STR_VALUEPARAM_ETCO2
		u8R"(FiCO2)", //STR_VALUEPARAM_FICO2
		u8R"(PR)", //STR_VALUEPARAM_PR
		u8R"(AA)", //STR_VALUEPARAM_AG_NULL
		u8R"(FiAA)", //STR_VALUEPARAM_AG_FINULL
		u8R"(FiN2O)", //STR_VALUEPARAM_FIN2O
		u8R"(FiHAL)", //STR_VALUEPARAM_FIHAL
		u8R"(FiENF)", //STR_VALUEPARAM_FIENF
		u8R"(FiSEV)", //STR_VALUEPARAM_FISEV
		u8R"(FiISO)", //STR_VALUEPARAM_FIISO
		u8R"(FiDES)", //STR_VALUEPARAM_FIDES
		u8R"(EtAA)", //STR_VALUEPARAM_AG_ETNULL
		u8R"(EtN2O)", //STR_VALUEPARAM_ETN2O
		u8R"(EtHAL)", //STR_VALUEPARAM_ETHAL
		u8R"(EtENF)", //STR_VALUEPARAM_ETENF
		u8R"(EtSEV)", //STR_VALUEPARAM_ETSEV
		u8R"(EtISO)", //STR_VALUEPARAM_ETISO
		u8R"(EtDES)", //STR_VALUEPARAM_ETDES
		u8R"(Apnea)", //STR_VALUEPARAM_ASPHY
		u8R"(VT)", //STR_SETTINGPARAM_VT
		u8R"(Rate)", //STR_SETTINGPARAM_RATE
		u8R"(I:E)", //STR_SETTINGPARAM_IE
		u8R"(Tip:Ti)", //STR_SETTINGPARAM_TITP
		u8R"(Pinsp)", //STR_SETTINGPARAM_PINSP
		u8R"(CPAP)", //STR_SETTINGPARAM_CPAP
		u8R"(Plimit)", //STR_SETTINGPARAM_PLIMIT
		u8R"(PEEP)", //STR_SETTINGPARAM_PEEP
		u8R"(Ftrig)", //STR_SETTINGPARAM_FTRIG
		u8R"(Ptrig)", //STR_SETTINGPARAM_PTRIG
		u8R"(Tinsp)", //STR_SETTINGPARAM_TINSP
		u8R"(Slope)", //STR_SETTINGPARAM_SLOPE
		u8R"(Psupp)", //STR_SETTINGPARAM_PSUPP
		u8R"(In End Level)", //STR_SETTINGPARAM_INENDLEVEL
		u8R"(VTe)", //STR_SETTINGLIMIT_VTE
		u8R"(Paw)", //STR_SETTINGLIMIT_PAW
		u8R"(FiO2)", //STR_SETTINGLIMIT_FIO2
		u8R"(Rate)", //STR_SETTINGLIMIT_RATE
		u8R"(MV)", //STR_SETTINGLIMIT_MV
		u8R"(1/(Min.L))", //STR_UNIT_1PERMINL
		u8R"(s)", //STR_UNIT_S
		u8R"(cmH2O)", //STR_UNIT_CMH2O
		u8R"(cmH2O/(L/s))", //STR_UNIT_CMH2OLS
		u8R"(mL/cmH2O)", //STR_UNIT_MLCMH2O
		u8R"(mmHg)", //STR_UNIT_MMHG
		u8R"(%)", //STR_UNIT_PERCENTAGE
		u8R"(L)", //STR_UNIT_L
		u8R"(mL)", //STR_UNIT_ML
		u8R"(L/min)", //STR_UNIT_LPERMIN
		u8R"(min)", //STR_UNIT_MIN
		u8R"(bpm)", //STR_UNIT_BPM
		u8R"(kg)", //STR_UNIT_KG
		u8R"(lb)", //STR_UNIT_LB
		u8R"(m)", //STR_UNIT_M
		u8R"(ft)", //STR_UNIT_FT
		u8R"(hPa)", //STR_UNIT_HPA
		u8R"(kPa)", //STR_UNIT_KPA
		u8R"(mBar)", //STR_UNIT_MBAR
		u8R"(---)", //STR_DASH
		u8R"(Insp.)", //STR_INSP
		u8R"(Exp.)", //STR_EXP
		u8R"(MAC)", //STR_MAC
		u8R"(Paw)", //STR_GRAPHNAME_PAW
		u8R"(Flow)", //STR_GRAPHNAME_FLOW
		u8R"(Volume)", //STR_GRAPHNAME_VOL
		u8R"(CO2)", //STR_GRAPHNAME_CO2
		u8R"(SPO2)", //STR_GRAPHNAME_SPO2
		u8R"(C2 / C)", //STR_GRAPHNAME_C20C
		u8R"(V - F)", //STR_GRAPHNAME_VF
		u8R"(P - V)", //STR_GRAPHNAME_PV
		u8R"(F - P)", //STR_GRAPHNAME_FP
		u8R"(Vol-CO2)", //STR_GRAPHNAME_VCO2
		u8R"(O2)", //STR_GASNAME_O2
		u8R"(N2O)", //STR_GASNAME_N2O
		u8R"(CO2)", //STR_GASNAME_CO2
		u8R"(Air)", //STR_GASNAME_AIR
		u8R"(Pinsp)", //STR_CPAP
		u8R"(Phy-alarm)", //STR_BIO_ALARM
		u8R"(Set-time)", //STR_SET_TIME
		u8R"(VENTURA)", //STR_OEM
		u8R"(ELUNA-60)", //STR_ELUNA_60
		u8R"(ELUNA-70)", //STR_ELUNA_70
		u8R"(ELUNA-80)", //STR_ELUNA_80
		u8R"(CENAR-30)", //STR_CENAR_30
		u8R"(CENAR-40)", //STR_CENAR_40
		u8R"(CENAR-50)", //STR_CENAR_50
		u8R"(PSV Level)", //STR_PSV_LEVEL
		u8R"(backup)", //STR_BACKUP
		u8R"(SN-)", //STR_SN_SHORT_CUT
		u8R"()", //STR_BLANK
		u8R"(回退)", //STR_BACK
		u8R"(确认)", //STR_ENTER
		u8R"(泄漏检测不能进行，由于:
1.手动模式打开；或
2.ACGO开关打开；或
3.流量计未完全关闭。)", //STR_DISABLE_TEST
		u8R"(请按如下方式进行检测)", //STR_LABEL_BST_HEAD
		u8R"(1、将波纹管上的Y 形三通插到泄漏测试塞；
2、确保手动/机控开关设置在机控位置；
3、快速充氧，确保折叠囊顶部与风箱顶部紧贴；
4、确保流量计关闭；
5、按下‘开始’按钮进行测试。)", //STR_LABEL_BST_INIT
		u8R"(检测)", //STR_TEST
		u8R"(正在进行泄漏检测，
请等待一分钟)", //STR_LABEL_BST_TESTING
		u8R"(泄漏检测失败，请重新连接。)", //STR_LABEL_BST_FAIL
		u8R"(顺应性)", //STR_COMPLIANCE
		u8R"(泄漏检测)", //STR_LEAK
		u8R"(失败)", //STR_FAIL
		u8R"(合格)", //STR_QULIFIED
		u8R"(不合格)", //STR_UNQULIFIED
		u8R"(最近一次检测)", //STR_RecentSTT
		u8R"(最近一次校准)", //STR_RECENT_CAL
		u8R"(总流量)", //STR_TOTAL
		u8R"(退出)", //STR_EXIT
		u8R"(确定)", //STR_CONFIRM
		u8R"(取消)", //STR_CANCEL
		u8R"(开始)", //STR_START
		u8R"(重置)", //STR_RESET
		u8R"(波形图)", //STR_LABEL_WAVEFORMS
		u8R"(环图)", //STR_LABEL_SPIROMETRY
		u8R"(趋势图)", //STR_LABEL_TREND
		u8R"(所有参数)", //STR_LABEL_VALUES
		u8R"(备份环图)", //STR_LOOP_BUTTON
		u8R"(取消备份)", //STR_LOOP_BUTTONRELEASE
		u8R"(流量计)", //STR_LABEL_FLOWMETER
		u8R"(报警！)", //STR_ALARM
		u8R"(消息)", //STR_MESSAGE
		u8R"(主菜单)", //STR_MAINMENU
		u8R"(校零)", //STR_MAINMENU_ZERO
		u8R"(恢复默认)", //STR_MAINMENU_DEFAULT
		u8R"(校 准)", //STR_CALIBRATION
		u8R"(在手动模式或者ACGO开关打开时，校准无效。)", //STR_CAL_DIABLE
		u8R"(校准)", //STR_MAINMENU_CAL
		u8R"(校准成功)", //STR_MAINMENU_CALDONE
		u8R"(校准失败)", //STR_MAINMENU_CALFAIL
		u8R"(请按提示进行校准)", //STR_MAINMENU_CAL_TIP
		u8R"(流量计校准)", //STR_MAINMENU_FLOWCAL
		u8R"(1、确保流量计校零在待机模式下进行；
2、将流量计旋钮调到最小值；
3、点击‘开始’按钮。)", //STR_MAINMENU_FLOWCAL_TXT2
		u8R"(点击'校准’)", //STR_MAINMENU_FLOWCAL_TXT3
		u8R"(校零正在进行中，请等待1-2分钟。)", //STR_MAINMENU_FLOWCAL_TXT4
		u8R"(氧校准)", //STR_MAINMENU_O2CAL
		u8R"(21%氧校准)", //STR_MAINMENU_21PO2CAL
		u8R"(100%氧校准)", //STR_MAINMENU_100PO2CAL
		u8R"(校准结束)", //STR_MAINMENU_CAL_FIN
		u8R"(1、确保氧浓度校准在待机模式下进行；
2、氧气气源关闭；
3、打开堵头和吸气单向阀盖（如图）；
4、点击‘开始’按键进行21%氧浓度校准。)", //STR_MAINMENU_O2CAL_TXT3
		u8R"(氧浓度校准正在进行中，请等待若干分钟...)", //STR_MAINMENU_CAL_IN_PROCESS
		u8R"(21%氧浓度校准已经完成，请重新接好回路，拧上堵头，并选择是否进行100%氧浓度校准)", //STR_MAINMENU_CAL_21_DONE
		u8R"(100%氧浓度校准已经完成，请恢复回路连接)", //STR_MAINMENU_CAL_100_DONE
		u8R"(氧浓度21%校准失败，请按图恢复回路连接)", //STR_MAINMENU_CAL_21_FAIL
		u8R"(氧浓度100%校准失败，请按图恢复回路连接)", //STR_MAINMENU_CAL_100_FAIL
		u8R"(氧浓度校准已经结束，请检测回路是否已经连接完整)", //STR_MAINMENU_CAL_FINISH
		u8R"(AG/CO2)", //STR_MAINMENU_AGCO2CAL
		u8R"(1、确保校零在待机模式下进行；
2、保持二氧化碳和环境浓度一致。)", //STR_MAINMENU_AGCO2CAL_TXT1_TITLE
		u8R"(AG/CO2模块预热中)", //STR_MAINMENU_CAL_STATUS_DISABLE
		u8R"(校零进行中。。。)", //STR_MAINMENU_CAL_STATUS_IN_PROG
		u8R"(校零成功)", //STR_MAINMENU_CAL_STATUS_ZERODONE
		u8R"(校零失败)", //STR_MAINMENU_CAL_STATUS_FAILED
		u8R"(最近一次校零)", //STR_LATEST_ZERO_CAL_TIME
		u8R"(传感器校准)", //STR_MAINMENU_SENSORCAL
		u8R"(压力传感器)", //STR_MAINMENU_SENSORCAL_TXT3
		u8R"(系统设置)", //STR_MAINMENU_SYSSETTING
		u8R"(常规设置)", //STR_MAINMENU_EQUIPSET
		u8R"(系统音量)", //STR_MAINMENU_EQUIPSET_TXT2
		u8R"(开)", //STR_ON
		u8R"(关)", //STR_OFF
		u8R"(压力单位)", //STR_PAW_UNIT
		u8R"(CO2 单位)", //STR_CO2_UNIT
		u8R"(时间)", //STR_MAINMENU_TIMEDATE
		u8R"(日期)", //STR_MAINMENU_TIMEDATE_DATE
		u8R"(时间)", //STR_MAINMENU_TIMEDATE_TIME
		u8R"(时间(AM))", //STR_MAINMENU_TIMEDATE_12HOUR_AM
		u8R"(时间(PM))", //STR_MAINMENU_TIMEDATE_12HOUR_PM
		u8R"(日期格式)", //STR_DATE_FORMAT
		u8R"(时间格式)", //STR_TIME_FORMAT
		u8R"(年-月-日)", //STR_MAINMENU_YMD
		u8R"(月-日-年)", //STR_MAINMENU_MDY
		u8R"(日-月-年)", //STR_MAINMENU_DMY
		u8R"(12小时制)", //STR_TIME_FORMAT_12H
		u8R"(24小时制)", //STR_TIME_FORMAT_24H
		u8R"(更改触发方式)", //STR_MAINMENU_TRIGSET
		u8R"(IP设置)", //STR_MAINMENU_IP
		u8R"(触发方式)", //STR_MAINMENU_TRIGSET_TXT1
		u8R"(显示设置)", //STR_MAINMENU_DISPSETTING
		u8R"(高)", //STR_MAINMENU_WAVESET_HIGH_SPEED
		u8R"(中)", //STR_MAINMENU_WAVESET_MID_SPEED
		u8R"(低)", //STR_MAINMENU_WAVESET_LOW_SPEED
		u8R"(环图)", //STR_MAINMENU_LOOPSET
		u8R"(监测值)", //STR_MAINMENU_VALUE
		u8R"(趋势图)", //STR_MAINMENU_TRENDSET
		u8R"(趋势时间)", //STR_MAINMENU_TRENDSET_TXT4
		u8R"(系统信息)", //STR_MAINMENU_SYSINFO
		u8R"(版本)", //STR_MAINMENU_VER
		u8R"(名称)", //STR_MAINMENU_VER_TITLE1
		u8R"(系统软件)", //STR_MAINMENU_VER_TITLE1_TXT1
		u8R"(版本)", //STR_MAINMENU_VER_TITLE2
		u8R"(日期)", //STR_MAINMENU_VER_TITLE3
		u8R"(配置)", //STR_MAINMENU_CONFIG
		u8R"(机器ID)", //STR_MAINMENU_CONFIG_TXT1
		u8R"(系统自检)", //STR_MAINMENU_SELFTEST
		u8R"(O2 Flow Sensor Test)", //STR_MAINMENU_SELFTEST_TXT1
		u8R"(空气流量传感器测试)", //STR_MAINMENU_SELFTEST_TXT2
		u8R"(呼吸流量传感器测试)", //STR_MAINMENU_SELFTEST_TXT3
		u8R"(压力传感器测试)", //STR_MAINMENU_SELFTEST_TXT4
		u8R"(呼气阀测试)", //STR_MAINMENU_SELFTEST_TXT5
		u8R"(安全阀测试)", //STR_MAINMENU_SELFTEST_TXT6
		u8R"(O2 模块测试)", //STR_MAINMENU_SELFTEST_TXT7
		u8R"(泄漏量(mL/min))", //STR_MAINMENU_SELFTEST_TXT8
		u8R"(顺应性(mL/cmH2O))", //STR_MAINMENU_SELFTEST_TXT9
		u8R"(Circuit Resistance(cmH2O/L/s))", //STR_MAINMENU_SELFTEST_TXT10
		u8R"(结束)", //STR_FINISH
		u8R"(工程师设置)", //STR_MAINMENU_EGRSETTING
		u8R"(工程师设置)", //STR_EGNEER_SETTING
		u8R"(操作日志)", //STR_OPER_LOG
		u8R"(工程师定标)", //STR_EG_CAL
		u8R"(配置信息)", //STR_PRODUCT_CFG
		u8R"(流速定标)", //STR_FLOW_CAL
		u8R"(压力定标)", //STR_PRESS_CAL
		u8R"(待机)", //STR_STANDBY
		u8R"(是否确定进入待机状态？)", //STR_STANDBY_STRING1
		u8R"(时间)", //STR_ALARMSET_TIME
		u8R"(报警信息)", //STR_ALARMSET_ALARMITEM
		u8R"(级别)", //STR_ALARMSET_PRIORITY
		u8R"(下限)", //STR_ALARMSET_LOW
		u8R"(上限)", //STR_ALARMSET_HIGH
		u8R"(报警设置)", //STR_ALARMSET
		u8R"(报警限)", //STR_ALARMSET_ALARMLIMIT
		u8R"(报警日志)", //STR_ALARMSET_ALARMLOG
		u8R"(吸入氧浓度高！！)", //STR_ALARMINFO_HIGH_FIO2
		u8R"(吸入氧浓度低！！！)", //STR_ALARMINFO_LOW_FIO2
		u8R"(PR高！！)", //STR_ALARMINFO_HIGH_PR
		u8R"(PR低！！)", //STR_ALARMINFO_LOW_PR
		u8R"(分钟通气量高！！)", //STR_ALARMINFO_HIGH_MV
		u8R"(分钟通气量低！！)", //STR_ALARMINFO_LOW_MV
		u8R"(呼吸频率高！)", //STR_ALARMINFO_HIGH_RATE
		u8R"(呼吸频率低！)", //STR_ALARMINFO_LOW_RATE
		u8R"(呼出潮气量高！！)", //STR_ALARMINFO_HIGH_VTE
		u8R"(呼出潮气量低！！)", //STR_ALARMINFO_LOW_VTE
		u8R"(Paw过低！！)", //STR_ALARMINFO_LOW_PAW
		u8R"(Paw过高！！！)", //STR_ALARMINFO_HIGH_PAW
		u8R"(压力达到限制)", //STR_PRESSURE_REACH_LIMIT
		u8R"(吸入N2O浓度高！！！)", //STR_ALARMINFO_HIGH_FIN2O
		u8R"(吸入N2O浓度低！)", //STR_ALARMINFO_LOW_FIN2O
		u8R"(呼出N2O浓度高！！！)", //STR_ALARMINFO_HIGH_ETN2O
		u8R"(呼出N2O浓度低！)", //STR_ALARMINFO_LOW_ETN2O
		u8R"(吸入二氧化碳浓度高！！！)", //STR_ALARMINFO_HIGH_FICO2
		u8R"(吸入二氧化碳浓度低！)", //STR_ALARMINFO_LOW_FICO2
		u8R"(呼末二氧化碳浓度高！！！)", //STR_ALARMINFO_HIGH_ETCO2
		u8R"(呼末二氧化碳浓度低！！)", //STR_ALARMINFO_LOW_ETCO2
		u8R"(High SPO2!!)", //STR_ALARMINFO_HIGH_SPO2
		u8R"(Low SPO2!!!)", //STR_ALARMINFO_LOW_SPO2
		u8R"(吸入氟烷浓度高！！)", //STR_ALARMINFO_HIGH_FIHAL
		u8R"(吸入氟烷浓度低！)", //STR_ALARMINFO_LOW_FIHAL
		u8R"(吸入安氟烷浓度高！！)", //STR_ALARMINFO_HIGH_FIENF
		u8R"(吸入安氟烷浓度低！)", //STR_ALARMINFO_LOW_FIENF
		u8R"(吸入七氟烷浓度高！！)", //STR_ALARMINFO_HIGH_FISEV
		u8R"(吸入七氟烷浓度低！)", //STR_ALARMINFO_LOW_FISEV
		u8R"(吸入异氟烷浓度高！！)", //STR_ALARMINFO_HIGH_FIISO
		u8R"(吸入异氟烷浓度低！)", //STR_ALARMINFO_LOW_FIISO
		u8R"(吸入地氟烷浓度高！！)", //STR_ALARMINFO_HIGH_FIDES
		u8R"(吸入地氟烷浓度低！)", //STR_ALARMINFO_LOW_FIDES
		u8R"(呼出氟烷浓度高！！)", //STR_ALARMINFO_HIGH_ETHAL
		u8R"(呼出氟烷浓度低！)", //STR_ALARMINFO_LOW_ETHAL
		u8R"(呼出安氟烷浓度高！！)", //STR_ALARMINFO_HIGH_ETENF
		u8R"(呼出安氟烷浓度低！)", //STR_ALARMINFO_LOW_ETENF
		u8R"(呼出七氟烷浓度高！！)", //STR_ALARMINFO_HIGH_ETSEV
		u8R"(呼出七氟烷浓度低！)", //STR_ALARMINFO_LOW_ETSEV
		u8R"(呼出异氟烷浓度高！！)", //STR_ALARMINFO_HIGH_ETISO
		u8R"(呼出异氟烷浓度低！)", //STR_ALARMINFO_LOW_ETISO
		u8R"(呼出地氟烷浓度高！！)", //STR_ALARMINFO_HIGH_ETDES
		u8R"(呼出地氟烷浓度低！)", //STR_ALARMINFO_LOW_ETDES
		u8R"(O2流速过高！)", //STR_ALARMINFO_O2_FLOW_HIGH
		u8R"(N2O流速过高！)", //STR_ALARMINFO_N2O_FLOW_HIGH
		u8R"(Air流速过高！)", //STR_ALARMINFO_AIR_FLOW_HIGH
		u8R"(总流速过高！)", //STR_ALARMINFO_FLOW_HIGH
		u8R"(氧笑比例错误！！！)", //STR_ALARMINFO_O2_N2O_UNUSUAL_RATIO
		u8R"(电池电量低！！)", //STR_TECHALARM_BATTERYLOW
		u8R"(电池耗尽，系统即将关机！！！)", //STR_TECHALARM_BATTERYEMPTY
		u8R"(交流电未接！)", //STR_TECHALARM_PLUGOUT
		u8R"(电池未连接！)", //STR_TECHALARM_POWERERR
		u8R"(ACGO打开！！)", //STR_TECHALARM_ACGO
		u8R"(持续气道压力过高！！！)", //STR_ALARMINFO_GASHIGH
		u8R"(持续气道压力过低！！)", //STR_ALARMINFO_GASLOW
		u8R"(回路未安装到位！！！)", //STR_TECHALARM_GASLOOPERR
		u8R"(驱动气体压力低！！！)", //STR_TECHALARM_GASSOURCELOW
		u8R"(氧气气源压力低！！！)", //STR_TECHALARM_O2_GASSOURCELOW
		u8R"(气道负压！！！)", //STR_ALARM_NEGPRESS
		u8R"(快速充氧超时！！)", //STR_TECHALARM_ADDO2TIMEOUT
		u8R"(快速充氧错误！！！)", //STR_TECHALARM_ADDO2ERR
		u8R"(钠石灰罐安装未到位！！！)", //STR_TECHALARM_BYPASSERR
		u8R"(呼吸窒息！！)", //STR_TECHALARM_APNEAALARM
		u8R"(呼吸窒息大于120s！！！)", //STR_TECHALARM_APNEA_120_ALARM
		u8R"(流量传感器故障！！！)", //STR_FLOW_SENSOR_ERR_ALARM
		u8R"(电源温度过高！！！)", //STR_TECHALARM_FAN_STOP
		u8R"(氧传感器未连接！！)", //STR_TECHALARM_O2_SENSOR_ERR
		u8R"(监控模块通信停止！！！)", //STR_TECHALARM_MINOTORING_MODULE_ERR
		u8R"(压力传感器故障！！！)", //STR_TECHALARM_PRESSURE_SENSOR_ERR
		u8R"(呼气阀失效！！！)", //STR_TECHALARM_EXP_VALVE_ERR
		u8R"(呼气阀封死！！！)", //STR_TECHALARM_EXP_VALVE_CLOSE_OFF
		u8R"(吸气阀故障！！！)", //STR_TECHALARM_INSP_VALVE_ERR
		u8R"(加热模块失效！)", //STR_TECHALARM_HEATING_MODULE_ERR
		u8R"(AG 适配未安装！)", //STR_TECHALARM_AG_ADAPTOR_ERR
		u8R"(预设时间结束！)", //STR_TECHALARM_TIME_UP
		u8R"(SPO2 sensor distconnect!!)", //STR_TECHALARM_SPO2_BREAKOFF
		u8R"(AX 浓度超出范围！！)", //STR_TECHALARM_AX_OUT_OF_RANGE
		u8R"(CO2 浓度超出范围！！)", //STR_TECHALARM_CO2_OUT_OF_RANGE
		u8R"(N2O 浓度超出范围！！)", //STR_TECHALARM_N2O_OUT_OF_RANGE
		u8R"(AG 温度超出范围！！)", //STR_TECHALARM_TMP_OUT_OF_RANGE
		u8R"(AG 压力超出范围！！)", //STR_TECHALARM_PRESS_OUT_OF_RANGE
		u8R"(AG 需重新校准！！)", //STR_TECHALARM_AG_NEED_RECAIL
		u8R"(有混合麻醉剂！)", //STR_TECHALARM_AG_AGENT_MIX
		u8R"(AG 数据不准确！！)", //STR_TECHALARM_AG_UNSPECIFIED_ACCURACY
		u8R"(AG 软件错误！！！)", //STR_TECHALARM_AG_SW_ERR
		u8R"(AG 硬件错误！！！)", //STR_TECHALARM_AG_HW_ERR
		u8R"(AG 电机错误！！！)", //STR_TECHALARM_MOTO_ERR
		u8R"(AG 丢失校准数据！！)", //STR_TECHALARM_AG_LOST_CAIL
		u8R"(需要更换AG适配器！！)", //STR_TECHALARM_AG_REPLACE
		u8R"(手动开关)", //STR_TIP_MANUAL
		u8R"(高)", //STR_HIGH
		u8R"(中)", //STR_MID
		u8R"(低)", //STR_LOW
		u8R"(定时器)", //STR_TIMER1
		u8R"(定时时间)", //STR_TIMER2
		u8R"(点击归零)", //STR_TIMER3
		u8R"(打开)", //STR_OPEN
		u8R"(关闭)", //STR_CLOSE
		u8R"(部分生理报警关闭)", //STR_BIO_ALARM_CLOSE
		u8R"(参数达到限制)", //STR_PARAM_LIMITED
		u8R"(系统自检)", //STR_SYSTEM_SELFTEST
		u8R"(重试)", //STR_RETRY
		u8R"(忽略)", //STR_IGNORE
		u8R"(监控模块检测)", //STR_SELFTEST_MONITORING
		u8R"(交流电检测)", //STR_SELFTEST_AC
		u8R"(电池检测)", //STR_SELFTEST_BATTERY
		u8R"(系统时间检测)", //STR_SELFTEST_SYS_TIME
		u8R"(流量计模块检测)", //STR_SELFTEST_FLOWMETER
		u8R"(AG/CO2 模块)", //STR_SELFTEST_AG_CO2
		u8R"(O2 传感器)", //STR_SELFTEST_O2
		u8R"(SPO2 模块)", //STR_SELFTEST_SPO2
		u8R"(触摸屏模块检测)", //STR_SELFTEST_TOUCHSCREEN
		u8R"(驱动气体压力检测)", //STR_SELFTEST_GAS_SOURCE
		u8R"(数据库载入)", //STR_SELFTEST_DATABASE
		u8R"(呼气阀测试)", //STR_SELFTEST_EXHALATION_VALUE
		u8R"(吸气阀测试)", //STR_SELFTEST_INHALATION_VALUE
		u8R"(与流量计通讯停止)", //STR_FLOWMETER_STOP
		u8R"(监控传感器校零失败)", //STR_MONIT_SENSOR_CAL_FAIL
		u8R"(监测精度不高)", //STR_MONITOR_ACCURACY_LOW
		u8R"(O2流量传感器故障)", //STR_O2_FM_SENSOR_ERROR
		u8R"(N2O流量传感器故障)", //STR_N2O_FM_SENSOR_ERROR
		u8R"(Air流量传感器故障)", //STR_AIR_FM_SENSOR_ERROR
		u8R"(流量计硬件错误)", //STR_FM_HARDWARE_ERROR
		u8R"(未连接 SPO2 模块)", //STR_SPO2_SENSOR_UNUNITED
		u8R"(未连接 AG 模块)", //STR_AG_ERROR
		u8R"(未连接 O2 模块)", //STR_O2_CONCENTRATION_ERROR
		u8R"(触摸屏被压住)", //STR_TOUCH_SCREEN_HOLDBACK
		u8R"(系统时间错误)", //STR_SYS_TIME_ERR
		u8R"(回路泄漏过大)", //STR_BREATH_LEAK_HIGH
		u8R"(电池故障)", //STR_BATTERYERR
		u8R"(结果)", //STR_RESULT
		u8R"(通过)", //STR_PASS
		u8R"(操作日志事件)", //STR_OPER_EVENT
		u8R"(详细信息)", //STR_DETAIL_INFO
		u8R"(系统启动)", //STR_SYS_START
		u8R"(运行模式)", //STR_RUNMODE_CHAGNE
		u8R"(通气模式改变)", //STR_VENTMODE_CHAGNE
		u8R"(通气模式参数改变)", //STR_VENT_PARAM_CHANGE
		u8R"(报警参数上限设置改变)", //STR_HIGH_ALARM_LIMIT_CHANGE
		u8R"(报警参数下限设置改变)", //STR_LOW_ALARM_LIMIT_CHANGE
		u8R"(HLM模式参数改变)", //STR_HLM_PARAM_CHANGE
		u8R"(产品型号)", //STR_PRODUCT_MODE
		u8R"(产品序列号)", //STR_SN
		u8R"(出厂日期)", //STR_P_DATE
		u8R"(软件版本)", //STR_S_VERION
		u8R"(跳过)", //STR_SKIP
		u8R"(点击按钮开始定标)", //STR_CAL_START
		u8R"(流速定标进行中。。。)", //STR_CAL_IN_FLOW_PROC
		u8R"(流速定标失败)", //STR_CAL_FLOW_FAIL
		u8R"(流速定标成功)", //STR_CAL_FLOW_DONE
		u8R"(压力定标进行中。。。)", //STR_CAL_IN_PEEP_PROC
		u8R"(压力定标成功。)", //STR_CAL_PEEP_DONE
		u8R"(压力定标失败。)", //STR_CAL_PEEP_FAIL
		u8R"(读取定标记录)", //STR_EXPRT_DATA
		u8R"(读取定标记录)", //STR_READ_CAL
		u8R"(点击按钮开始导出数据。)", //STR_EXPRT_START
		u8R"(流量计定标)", //STR_FLOWMETER_CAL
		u8R"(输入0，定标开始)", //STR_FLOWMETER_CAL_START
		u8R"(定标数据)", //STR_FM_CAL_DATA
		u8R"(定标数据失败，请重试该点)", //STR_FM_CAL_FAIL
		u8R"(当前点定标完成，请输入下一个点)", //STR_FM_CAL_DONE_NEXT
		u8R"(定标已经结束，请选择
‘确认’或‘取消’)", //STR_FM_CAL_FINISH
		u8R"(流量计定标进行中。。。)", //STR_FM_CAL_WAIT
		u8R"(流量计定标成功)", //STR_FM_CAL_SUCCEED
		u8R"(IP地址)", //STR_IPADDR
		u8R"(掩码)", //STR_NETMASK
		u8R"(网关)", //STR_GATEWAY
		u8R"(MAC)", //STR_MACADDR
		u8R"(确认)", //STR_CONFIRM_NET
		u8R"(.)", //STR_DOT
		u8R"(KEYCODE)", //STR_KEYCODE
		u8R"(UPDATE)", //STR_FIRMUPDATE
		u8R"(软件模式选择)", //STR_SOFT_MODE
		u8R"(提示:系统重启后生效！)", //STR_SOFT_MODE_SWITCHED_TIP
		u8R"(Demo)", //STR_SOFT_DEMO_MODE
		u8R"(真机)", //STR_SOFT_RUN_MODE
		u8R"(定标)", //STR_CALI
		u8R"(定标程序运行中  点击按钮取消)", //STR_CALI_PROC
		u8R"(软件配置)", //STR_SOFT_CFG_TITLE
		u8R"(配置码错误)", //STR_SOFT_CFG_INPUT_TIP
		u8R"(配置失败)", //STR_SOFT_CFG_FAIL
		u8R"(配置成功)", //STR_SOFT_CFG_SUCCEED
		u8R"(亮度调节)", //STR_MAINMENU_EQUIPSET_LCD
		u8R"(氧气流量异常！)", //STR_O2_FLOW_INEXACT
		u8R"(平衡气体流量异常！)", //STR_BANLANCE_FLOW_INEXACT
		u8R"(新鲜气体未打开！！)", //STR_FLOW_TOO_LOW
		u8R"(氧气供应压力不足！)", //STR_O2_PRESS_LOW
		u8R"(笑气供应压力不足！！)", //STR_N2O_PRESS_LOW
		u8R"(空气供应压力不足！！)", //STR_AIR_PRESS_LOW
		u8R"(O2 传感器未读取到校准数据！！)", //STR_FLOWMETER_O2_SENSOR_ERROR
		u8R"(N2O 传感器未读取到校准数据！！)", //STR_FLOWMETER_N2O_SENSOR_ERROR
		u8R"(Air 传感器未读取到校准数据！！)", //STR_FLOWMETER_AIR_SENSOR_ERROR
		u8R"(后备流量控制系统针阀打开！！)", //STR_BACKUP_FLOW_CONTROL_OPEN
		u8R"(历史数据)", //STR_HISTORY_DATA
		u8R"(设置)", //STR_SETTINGS
		u8R"(冻结)", //STR_FREEZING
		u8R"(报警静音)", //STR_ALARM_MUTE
		u8R"(环图详情)", //STR_LOOPINFO
		u8R"(最近一次系统自检)", //STR_LATEST_SELFTEST
		u8R"(上一步)", //STR_PRE_STEP
		u8R"(下一步)", //STR_NEXT_STEP
		u8R"(完成)", //STR_ACCOMPLISH
		u8R"(暂停)", //STR_STOP
		u8R"(继续)", //STR_CONTINUE
		u8R"(即将跳过系统自检，请确认)", //STR_SELFTEST_SKIP_TIP
		u8R"(泄漏测试)", //STR_MANUAL_LEAK_CHECK
		u8R"(1. 确定系统处于待机状态，并且确保手动/机控开关设置在手动位置
2. 将皮囊接到呼吸回路系统的手动呼吸皮囊接口
3. 调节APL 阀控制旋钮，使得APL 阀处于70 cmH2O 位置
4. 将O2 流量设置为0.15 L/min
5. 将波纹管上的Y型三通插到手动通气皮囊接口的泄漏测试塞上，堵住Y型三通的出气口
6. 按下快速充氧按钮，使气道压力表值上升至约30 cmH2O
7. 松开快速充氧按钮，观察气道压力表)", //STR_MANUAL_LEAK_CHECK_TIP
		u8R"(气路检查)", //STR_GAS_CIRCUIT_CHECK
		u8R"(1. 确保呼吸回路系统连接正确，呼吸管路完好无损，呼吸回路系统已经被锁紧
2. 检查BY-PASS是否安装到位
3. 检查CO2吸收罐内的钠石灰颜色，如颜色变化明显，请立即更换钠石灰)", //STR_GAS_CIRCUIT_CHECK_TIP
		u8R"(流量计检查)", //STR_FLOWMETER_CHECK
		u8R"(1. 确保供气系统的连接正确，压力正常，流量计旋钮在关闭时（最小值时），显示的读数为零
2. 确保玻璃单管流量计内壁没有脏污)", //STR_FLOWMETER_CHECK_TIP
		u8R"(麻醉气体蒸发器检查)", //STR_ANESTHETIC_GAS_EVAPORATOR_CHECK
		u8R"(1. 确保麻醉气体蒸发器在正确锁定位置
2. 确保麻醉气体蒸发器调整至0
3. 确保麻醉气体蒸发器填药在正常水平
4. 确保麻醉气体蒸发器安全填药锁定)", //STR_ANESTHETIC_GAS_EVAPORATOR_CHECK_TIP
		u8R"(计时器)", //STR_TIMER
		u8R"(倒计时)", //STR_COUNTDOWN
		u8R"(呼吸机报警限)", //STR_VENTILATOR_ALARM_LIMIT
		u8R"(麻醉气体报警限)", //STR_ANESTHETIC_GAS_ALARM_LIMIT
		u8R"(当前报警)", //STR_CUR_ALARM
		u8R"(四波形)", //STR_FOUR_WAVE
		u8R"(三波形)", //STR_THREE_WAVE
		u8R"(环图)", //STR_LOOP
		u8R"(流量控制菜单)", //STR_FLOW_CONTROL_MENU
		u8R"(快捷设置)", //STR_QUICK_SET
		u8R"(控制模式)", //STR_CONTROL_MODE
		u8R"(总流量控制)", //STR_TOTAL_FLOW_CONTROL
		u8R"(单管流量控制)", //STR_SINGLE_PIPE_FLOW_CONTROL
		u8R"(平衡气体)", //STR_BALANCE_FLOW
		u8R"(趋势图)", //STR_TREND_GRAPH
		u8R"(分钟)", //STR_MINUTE
		u8R"(上一事件)", //STR_PRE_EVENT
		u8R"(下一事件)", //STR_NEXT_EVENT
		u8R"(趋势表)", //STR_TREND_TABLE
		u8R"(所有参数)", //STR_ALL_PARAMS
		u8R"(压力参数)", //STR_PRESSURE_PARAMS
		u8R"(容积参数)", //STR_VOLUME_PARAMS
		u8R"(时间参数)", //STR_TIME_PARAMS
		u8R"(流量计参数)", //STR_FLOWMETER_PARAMS
		u8R"(AG监测参数)", //STR_AG_PARAMS
		u8R"(环图详细)", //STR_LOOP_DETAIL
		u8R"(保存环图)", //STR_SAVE_LOOP
		u8R"(删除环图)", //STR_DELETE_LOOP
		u8R"(切换参考环)", //STR_SWITCH_REFER_LOOP
		u8R"(环图对比)", //STR_COMPARE_LOOP
		u8R"(容积-流速)", //STR_LOOP_VF
		u8R"(压力-容积)", //STR_LOOP_PV
		u8R"(流速-压力)", //STR_LOOP_FP
		u8R"(确认为基准环?)", //STR_CONFIRM_REF_LOOP_TIP
		u8R"(确认删除该环图？)", //STR_CONFIRM_DELETE_LOOP_TIP
		u8R"(确认为对比环？)", //STR_CONFIRM_COMPARE_LOOP_TIP
		u8R"(历史事件)", //STR_HISTORY_EVENT
		u8R"(日期)", //STR_DATE
		u8R"(所有事件)", //STR_ALL_EVENT
		u8R"(高级报警)", //STR_HIGH_LEVEL_ALARM
		u8R"(中级报警)", //STR_MID_LEVEL_ALARM
		u8R"(低级报警)", //STR_LOW_LEVEL_ALARM
		u8R"(提示)", //STR_PROMPT
		u8R"(操作)", //STR_OPERATE
		u8R"(成功)", //STR_SUCCESS
		u8R"(音量/亮度)", //STR_VOL_BRIGHT
		u8R"(界面设置)", //STR_UI_SETTING
		u8R"(厂家维护)", //STR_FACTORY_MAINTENANCE
		u8R"(语言)", //STR_LANGUAGE
		u8R"(Russian)", //STR_LANGUAGE_RUS
		u8R"(Chinese)", //STR_LANGUAGE_CHN
		u8R"(English)", //STR_LANGUAGE_ENG
		u8R"(Ukrainian)", //STR_LANGUAGE_UKR
		u8R"(French)", //STR_LANGUAGE_FRE
		u8R"(协议)", //STR_PROTOCAL
		u8R"(协议设置)", //STR_PROTOCAL_SETTINGS
		u8R"(串口协议)", //STR_SERIAL_PROTOCAL
		u8R"(无)", //STR_NONE
		u8R"(波特率)", //STR_BAUD_RATE
		u8R"(数据位)", //STR_DATA_BITS
		u8R"(校验)", //STR_VERIFY
		u8R"(奇)", //STR_ODD
		u8R"(偶)", //STR_EVEN
		u8R"(停止位)", //STR_STOP_BIT
		u8R"(网络协议)", //STR_NETWORK_PROTOCAL
		u8R"(子网掩码)", //STR_SUBNET_MASK
		u8R"(连接状态)", //STR_LINK_STATE
		u8R"(已连接)", //STR_CONNECTED
		u8R"(未连接)", //STR_DISCONNECTED
		u8R"(发送间隔)", //STR_SEND_INTERVAL
		u8R"(目标IP)", //STR_TARGET_IP
		u8R"(目标端口)", //STR_TARGET_PORT
		u8R"(确定连接吗？)", //STR_CONFIRM_CONNECT_TIP
		u8R"(确认断开连接吗？)", //STR_CONFIRM_DISCONNECT_TIP
		u8R"(系统信息)", //STR_SYSTEM_INFO
		u8R"(配置信息)", //STR_CONFIG_INFO
		u8R"(已配置)", //STR_CONFIGURED
		u8R"(Plimit指示线)", //STR_PLIMIT_LINE
		u8R"(显示)", //STR_SHOW
		u8R"(隐藏)", //STR_HIDE
		u8R"(波形绘制)", //STR_DRAW_CURVE
		u8R"(描线)", //STR_DRAW_LINE
		u8R"(填充)", //STR_FILL_AREA
		u8R"(波形速度)", //STR_SWEEP_SPEED
		u8R"(流量计校零)", //STR_FLOWMETER_ZERO_CAL
		u8R"(开始通气)", //STR_START_VENTILATION
		u8R"(返回)", //STR_RETURN
		u8R"(切换基准环)", //STR_SWITCH_BASE_LOOP
		u8R"(仅检测到一块电池)", //STR_ONLY_ONE_BATTER
		u8R"(数据库损坏！！)", //STR_DB_FAIL
		u8R"(AG模块正在预热)", //STR_AG_WARMING
		u8R"(CO2模块正在预热)", //STR_CO2_WARMING
		u8R"(计时器已达上限)", //STR_TIMER_MAX
		u8R"(倒计时结束)", //STR_COUNTDOWN_END
		u8R"(校准只有在待机模式下可用)", //STR_CALIB_USE_ONLY_STANBY
		u8R"(进入维护)", //STR_ENTER_MAINTENANCE
		u8R"(维护只有在待机模式下可用)", //STR_MAINTENANCE_USE_ONLY_STANBY
		u8R"(出厂设置)", //STR_P_SETTINGS
		u8R"(恢复出厂设置)", //STR_RESTORE_FACTORY_SETTING
		u8R"(故障)", //STR_BREAKDOWN
		u8R"(正常)", //STR_NORMAL
		u8R"(结束删除)", //STR_END_DELETE
		u8R"(结束切换)", //STR_END_SWITCH
		u8R"(请按提示进行校零)", //STR_ZERO_CAL_TIP
		u8R"(更改呼吸模式)", //STR_CHANGE_VENT_MODE
		u8R"(超出上限)", //STR_OUT_OF_UPPER_LIMIT
		u8R"(超出下限)", //STR_OUT_OF_LOWER_LIMIT
		u8R"(输入值有误)", //STR_FORMAT_ERROR
		u8R"(无效值)", //STR_INVALID_VALUE
		u8R"(氧浓度校准取消，请按图恢复回路连接)", //STR_O2_CAL_CANCEL
		u8R"(待机)", //STR_HOTKEY_STANDBY
		u8R"(Shutdown delay)", //STR_SHUTDOWN_DELAY
		u8R"(外接模块)", //STR_EXTERNAL_MODULE
		u8R"(呼吸力学)", //STR_BREATHING_MECHANICS_PARAM
		u8R"(通气功能)", //STR_VENTILATORY_FUNCTION_PARAM
		u8R"(更改密码)", //STR_CHANGE_PASSWORD
		u8R"(输入新密码)", //STR_ENTER_NEW_PASSWORD
		u8R"(新密码确认)", //STR_NEW_PASSWORD_CONFIRM
		u8R"(确认更改)", //STR_CONFIRM_CHANGE
		u8R"(密码位数不超过6位，所有的密码字符需为数字。)", //STR_PASSWORD_TIP
		u8R"(密码格式输入错误，请检查)", //STR_PASSWORD_INPUT_FAULT_TIP
		u8R"(确认更改密码？)", //STR_PASSWORD_CHANGE_TIP
		u8R"(主控板)", //STR_MAIN_CONTROL_BOARD
		u8R"(监控板)", //STR_MONITOR_BOARD
		u8R"(U-Boot)", //STR_U_BOOT
		u8R"(Linux 文件系统)", //STR_LINUX_FILE_SYSTEM
		u8R"(Linux 内核)", //STR_LINUX_KERNEL
		u8R"(流量计板)", //STR_FLOWMETER_BOARD
		u8R"(电源板)", //STR_POWER_BOARD
		u8R"(Vt650)", //STR_VT_650
		u8R"(VtPlus)", //STR_VT_PLUS
		u8R"(VtMini)", //STR_VT_MINI
		u8R"(Vt型号选择)", //STR_VT_MODEL_CHOICE
		u8R"(试听)", //STR_AUDITION
		u8R"(输入您的密码)", //STR_ENTER_PASSWORD
		u8R"(触摸板测试)", //STR_TOUCH_PAD
		u8R"(触摸屏)", //STR_TOUCH_SCREEN
		u8R"(新鲜气体未打开)", //STR_FLOW_TOO_LOW_PROMPT
		u8R"(更改)", //STR_CHANGE
		u8R"(开始通气，当前通气模式)", //STR_LOG_ENTER_VENT_MODE
		u8R"(延迟关机)", //STR_LOG_SHUTDOWN_DELAY
		u8R"(取消延迟关机)", //STR_LOG_CANCEL_SHUTDOWN_DELAY
		u8R"(机械通气)", //STR_VENT
		u8R"(报警限恢复默认)", //STR_ALARM_LIMIT_RESTORE_DEFAULT
		u8R"(更改流量计控制模式)", //STR_CHANGE_CONTROL_MODE
		u8R"(更改平衡气体)", //STR_CHANGE_BALANCE_MODE
		u8R"(更改气体参数值)", //STR_CHANGE_FLOW_VALUE
		u8R"(更新至)", //STR_UPGRADE
		u8R"(CENAR-30M)", //STR_CENAR_30M
		u8R"(AG 模块)", //STR_SELFTEST_AG
		u8R"(CO2 模块)", //STR_SELFTEST_CO2
		u8R"(更改呼吸模式参数)", //STR_CHANGE_VENT_MODE_PARAM
		u8R"(修改日期)", //STR_CHANGE_SYSTEM_DATE
		u8R"(修改时间)", //STR_CHANGE_SYSTEM_TIME
		u8R"(密码错误)", //STR_PASSWORD_ERROR_TIP
		u8R"(两次密码不一致)", //STR_PASSWORD_DONOT_MATCH
		u8R"(切换压力单位)", //STR_SWITCH_PAW_UNIT
		u8R"(切换CO2单位)", //STR_SWITCH_CO2_UNIT
		u8R"(上午)", //STR_TIME_AM
		u8R"(下午)", //STR_TIME_PM
		u8R"(呼吸机报警限恢复默认)", //STR_VENTILATOR_LIMIT_RESTORE_DEFAULT
		u8R"(麻醉气体报警限恢复默认)", //STR_ANESTHETIC_GAS_LIMIT_RESTORE_DEFAULT
		u8R"(定时器设置不能超过)", //STR_TIMER_LIMIT
		u8R"(超出报警限)", //STR_INPUT_ALARM_LIMIT
		u8R"(配置码)", //STR_CFG_CODE
		u8R"(流量计PV阀定标)", //STR_PV_CAL
		u8R"(关机剩余时间)", //STR_SHUTDOWN_TIME
		u8R"(如需取消关机，请重新打开系统开关)", //STR_SHUTDOWN_TIP
		u8R"(未配置)", //STR_NOT_CONFIG
		u8R"(进入)", //STR_IN
		u8R"(退出)", //STR_OUT
		u8R"(未连接CO2模块)", //STR_CO2_ERROR
		u8R"(增大流量)", //STR_ADD_FLOW
		u8R"(减小流量)", //STR_REDUCE_FLOW
		u8R"(发送)", //STR_SEND
		u8R"(已恢复出厂设置，请重新启动机器)", //STR_RESTORE_TIP
		u8R"(请将IP地址修改为************以进行流速定标，点击确定将修改IP地址)", //STR_FLOW_TIP
		u8R"(AG模块正在校零，请等待)", //STR_AG_ZEROING_TIP
		u8R"(手动或ACGO模式下校准无效)", //STR_MENUAL_ACGO_STATE_TIP
		u8R"(O2 流量传感器通讯停止)", //STR_O2_FM_SENSOR_COMMUNICATION_STOP
		u8R"(平衡气体流量传感器故障)", //STR_BALANCE_GAS_FM_SENSOR_ERROR
		u8R"(后备流量控制系统传感器通讯停止)", //STR_BACKUP_FLOW_CONTROL_COMMUNICATION_STOP
		u8R"(CO2 适配未安装！)", //STR_TECHALARM_CO2_ADAPTOR_ERR
		u8R"(1. 确保供气系统的连接正确，压力正常，确保后备流量计旋钮在关闭时（最小值时），浮标位于最低点
2. 确保后备流量计玻璃管内壁没有脏污)", //STR_FLOWMETER_CHECK_TIP_FOR_80
		u8R"(手动或ACGO模式下无法进行泄漏测试)", //STR_MENUAL_ACGO_LEAK_STATE_TIP
		u8R"(1. 确保供气系统的连接正确，压力正常，流量计旋钮在关闭时（最小值时），浮子位于最低点
2. 确保玻璃单管流量计内壁没有脏污)", //STR_FLOWMETER_CHECK_TIP_FOR_C30M
		u8R"(AG模块校零)", //STR_AG_ZERO_CAL
		u8R"(CO2模块校零)", //STR_CO2_ZERO_CAL
		u8R"(CO2模块正在校零，请等待)", //STR_CO2_ZEROING_TIP
		u8R"(超出)", //STR_OVER
		u8R"(报警高限)", //STR_ALARM_HIGH_LIMIT
		u8R"(报警低限)", //STR_ALARM_LOW_LIMIT
		u8R"(单管)", //STR_DIRECT_FLOW
		u8R"(请输入定标数值!)", //STR_EMPTY_INPUT_TIP
		u8R"(请输入有效的定标数值!)", //STR_INVALID_INPUT_TIP
		u8R"(C)", //STR_SERIAL_C
		u8R"(E)", //STR_SERIAL_E
		u8R"(21%氧校准)", //STR_21PO2CAL
		u8R"(100%氧校准)", //STR_100PO2CAL
		u8R"(波形图设置)", //STR_WAVEFORMS_SETTINGS
		u8R"(关机)", //STR_SHUT_DOWN
		u8R"(术前检查)", //STR_PREOPERATIVE_CHECK
		u8R"(回路中CO2浓度过高)", //STR_CO2_CANNOT_ZERO_TIP
		u8R"(气源配置)", //STR_FLOWMETER_GAS_CONFIG
		u8R"(流量计板卡类型)", //STR_FLOWMETER_SENSOR_TYPE
		u8R"(软件更新)", //STR_SOFTWARE_UPDATE
		u8R"(更新开关)", //STR_UPDATE_SWITCH
		u8R"(更新类型)", //STR_UPDATE_TYPE
		u8R"(原版本)", //STR_SRC_VERSION
		u8R"(目标版本)", //STR_TARGET_VERSION
		u8R"(更新进度)", //STR_PROGRESS
		u8R"(软件需要重启才能完成更新，稍后自动重启！)", //STR_UPDATE_FINISH_TIP
		u8R"(立即重启)", //STR_RESTART_NOW
		u8R"(软件在传输时通信异常。)", //STR_UPDATE_COM_ERROR_TIP
		u8R"(软件在传输时校验未通过，正在请求重发。)", //STR_UPDATE_CRC_ERROR_TIP
		u8R"(正在进行在线配置...)", //STR_MACHINE_CONFIGURING
		u8R"(设备树)", //STR_DEVICE_TREE
		u8R"(自定义更新)", //STR_CUSTOM_UPDATE
		u8R"(图标png)", //STR_LOGO_PNG
		u8R"(图标bin)", //STR_LOGO_BIN
		u8R"(守护进程版本号)", //STR_DAEMON_VERSION
		u8R"(完成在线配置)", //STR_UPDATE_CONFIG
		u8R"(完成自定义产品型号)", //STR_UPDATE_PRODUCT_MODEL
		u8R"(自定义程序)", //STR_UPDATE_CUSTOM_STEP
		u8R"(完成软件启动Logo升级)", //STR_UPDATE_LOGO
		u8R"(完成Uboot启动Logo升级)", //STR_UPDATE_LOGO_BIN
		u8R"(AG模块类型)", //STR_AG_MODULE_TYPE
		u8R"(主流)", //STR_MAIN_STREAM
		u8R"(旁流)", //STR_SIDE_STREAM
		u8R"(检查AG采样线！)", //STR_TECHALARM_AG_SAMPLING_ERR
		u8R"(检查CO2采样线！)", //STR_TECHALARM_CO2_SAMPLING_ERR
		u8R"(需要更换CO2适配器！！)", //STR_TECHALARM_CO2_REPLACE
		u8R"(AG 采样线堵塞！！)", //STR_TECHALARM_AG_SAMPLING_CLOGGED
		u8R"(CO2 采样线堵塞！！)", //STR_TECHALARM_CO2_SAMPLING_CLOGGED
		u8R"(O2流量不能低于0.2L/min)", //STR_FLOWMETER_O2_FLOW_TIPS
		u8R"(O2浓度不能低于25%)", //STR_FLOWMETER_O2_CONCENTRATION_TIPS

    };
}

