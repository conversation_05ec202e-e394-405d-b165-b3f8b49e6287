﻿#include "Timer.h"
#include "UiAttribute.h"
#include "CfgButton.h"
#include "AlarmManager.h"
#include "UiApi.h"
#include "../MyApplication.h"
#include "MainWindow/MainWindow.h"

#define PROPUB_DEFAULT_SIZE 360,250
#define MAX_UP_TIME_VALUE (999 * 60 + 59)
#define TAB_SIZE 120,35
#define SEMICOLON_WIDHT 10
#define UPTIME_LABEL_HEIGHT 54
#define CLOSE_BT_SIZE 32,32
#define AUTO_EXIT_TIME (30 * 1000)
#define TIMER_LABEL_HEIGHT 17

#define MIN_REGEX "^[0-9]\\d{2}$"
#define MSEC_REGEX "^[0-9]\\d{1}$"

Timer::Timer(QWidget* parent):QFrame(parent)
{
    SystemTimeManager::GetInstance()->RegistTimerWidget(this);
    InitValue();
    InitUI();
    InitUiText();
    InitConnect();
}

void Timer::SlotUpTextStr()
{
    mTimeLabel->setText(mPopup->curTimerText());
    static int OldOperation=mPopup->GetCurState();;
    int Operation = mPopup->GetCurState();
    if(OldOperation!=Operation)
    {
        emit SignalStateChanged(Operation);
        OldOperation = Operation;
    }
}

void Timer::SlotTimerModeChange(int val)
{
    switch(val)
    {
    case TimerPopup::INIT:
    {
        StyleSet::SetTextColor(mTimeLabel,COLOR_DARK_GRAY);
        if(mPopup->getCurTabIndex() == TimerPopup::UP_TIMER_PAGE)
            mLabel->setText(UIStrings::GetStr(ALL_STRINGS_ENUM::STR_TIMER));
        else
            mLabel->setText(UIStrings::GetStr(ALL_STRINGS_ENUM::STR_COUNTDOWN));
    }
        break;
    case TimerPopup::UP_RUNNING:
    {
        StyleSet::SetTextColor(mTimeLabel,COLOR_GREEN);
        mLabel->setText(UIStrings::GetStr(ALL_STRINGS_ENUM::STR_TIMER));
    }
        break;
    case TimerPopup::DOWN_RUNNING:
    {
        StyleSet::SetTextColor(mTimeLabel,COLOR_GREEN);
        mLabel->setText(UIStrings::GetStr(ALL_STRINGS_ENUM::STR_COUNTDOWN));
    }
        break;
    case TimerPopup::UP_PAUSE:
    {
        StyleSet::SetTextColor(mTimeLabel,COLOR_LIGHT_YELLOW);
        mLabel->setText(UIStrings::GetStr(ALL_STRINGS_ENUM::STR_TIMER));
    }
        break;
    case TimerPopup::DOWN_PAUSE:
    {
        StyleSet::SetTextColor(mTimeLabel,COLOR_LIGHT_YELLOW);
        mLabel->setText(UIStrings::GetStr(ALL_STRINGS_ENUM::STR_COUNTDOWN));
    }
        break;
    case TimerPopup::UP_FINISH:
    {
        StyleSet::SetTextColor(mTimeLabel,COLOR_WHITE);
        mLabel->setText(UIStrings::GetStr(ALL_STRINGS_ENUM::STR_TIMER));
    }
        break;
    case TimerPopup::DOWN_FINISH:
    {
        StyleSet::SetTextColor(mTimeLabel,COLOR_WHITE);
        mLabel->setText(UIStrings::GetStr(ALL_STRINGS_ENUM::STR_COUNTDOWN));
    }
        break;
    }
}

void Timer::mousePressEvent(QMouseEvent *event)
{
    if(!mPopup->isActiveWindow())
    {
        mPopup->setParent(MainWindow::GetInstance());
        mPopup->installEventFilter(MainWindow::GetInstance());
        int xG=mapToGlobal(QPoint((width() - mPopup->width()) / 2, height())).x();
        int yG=mapToGlobal(QPoint((width() - mPopup->width()) / 2,height())).y();
        mPopup->setWindowFlags(Qt::Dialog | Qt::FramelessWindowHint);
        mPopup->setWindowModality(Qt::WindowModality::WindowModal);
        mPopup->show();
        mPopup->setFocus();
        mPopup->move(xG - 45,yG);
    }
    QWidget::mousePressEvent(event);
}

void Timer::InitUiText()
{
    if(mPopup->getCurTabIndex() == TimerPopup::UP_TIMER_PAGE)
        mLabel->setText(UIStrings::GetStr(ALL_STRINGS_ENUM::STR_TIMER));
    else
        mLabel->setText(UIStrings::GetStr(ALL_STRINGS_ENUM::STR_COUNTDOWN));
    mPopup->InitUiText();
}

int Timer::GetCurState()
{
    return mPopup->GetCurState();
}

void Timer::CloseTimePopup()
{
    mPopup->close();
}

bool Timer::IsTimePopupVisable()
{
    return mPopup->isVisible();
}

bool Timer::IsTimePopup(QObject *temp) const
{
    return temp == mPopup;
}

void Timer::resetTimer()
{
    mPopup->ResetTimer();
}

void Timer::InitValue()
{
    mPopup = new TimerPopup();
    mTimeLabel = new QLabel(this);
    mLabel = new QLabel(this);
}

void Timer::InitConnect()
{
    connect(mPopup,&TimerPopup::SignalTimeStrChange,this,&Timer::SlotUpTextStr);
    connect(mPopup, &TimerPopup::SignalTimerModeChange, this, &Timer::SlotTimerModeChange);
}

void Timer::InitUI()
{
    StyleSet::SetBackgroundColor(this,COLOR_BLACK);
    StyleSet::SetBorder(this, COLOR_BLACK_GRAY, 5);

    mTimeLabel->setAlignment(Qt::AlignCenter);
    StyleSet::SetTextFont(mTimeLabel,FONT_XL);
    StyleSet::SetTextColor(mTimeLabel,COLOR_DARK_GRAY);
    StyleSet::SetBackgroundColor(mTimeLabel,COLOR_TRANSPARENT);

    mLabel->setAlignment(Qt::AlignCenter);
    StyleSet::SetBackgroundColor(mLabel,COLOR_BLACK_GRAY);
    StyleSet::SetTextColor(mLabel,COLOR_LIGHT_GRAY);
    StyleSet::SetTextFont(mLabel,FONT_S);
    mLabel->setFixedHeight(TIMER_LABEL_HEIGHT);

    QVBoxLayout* layout = new QVBoxLayout(this);
    layout->addWidget(mTimeLabel, Qt::AlignCenter);
    layout->addWidget(mLabel, Qt::AlignBottom | Qt::AlignHCenter);
    layout->setSpacing(1);
    layout->setContentsMargins(0, 0, 0, 0);

    SlotUpTextStr();
}

void Timer::resizeEvent(QResizeEvent *event)
{
    mLabel->setFixedWidth(event->size().width());
}

TimerPopup::~TimerPopup()
{
}

TimerPopup::TimerPopup(QWidget *parent):FocusWidget(parent)
{
    InitUI();
    InitUiText();
    InitConnect();
}

int TimerPopup::GetCurState()
{
    return mCurModel;
}

void TimerPopup::InitConnect()
{
    connect(MyApp, &MyApplication::SignalMouseEvent, this,&TimerPopup::SlotRestartAutoOutEditTimer,Qt::QueuedConnection);
    connect(MyApp, &MyApplication::SignalKeyEvent, this, &TimerPopup::SlotRestartAutoOutEditTimer,Qt::QueuedConnection);
    connect(MyApp, &MyApplication::SignalTouchEvent, this, &TimerPopup::SlotRestartAutoOutEditTimer,Qt::QueuedConnection);
    connect(RunModeManage::GetInstance(), &RunModeManage::SignalModeChanged, this, [=](E_RUNMODE pre , E_RUNMODE now){
        if(mCurModel == UP_FINISH)
            AlarmManager::GetInstance()->TriggerAlarm(PROMPT_TIMER_TIMEOUT, 1);
        if(mCurModel == DOWN_FINISH)
            AlarmManager::GetInstance()->TriggerAlarm(PROMPT_COUNTDOWN_TIMEOUT, 1);
        if(pre == MODE_RUN_SELFTEST)
            InState(INIT);
    });

    connect(mOperationBtn, &QPushButton::clicked, this, [this](){
        if(mCurOperationModel==START_TIMER)
        {
            if(mTabWidget->currentIndex()==UP_TIMER_PAGE)
                InState(UP_RUNNING);
            else
            {
                if (!mPrevDownTimerValue)
                    InState(DOWN_FINISH);
                else
                    InState(DOWN_RUNNING);
            }
        }
        else if(mCurOperationModel==PAUSE_TIMER)
        {
            if(mTabWidget->currentIndex()==UP_TIMER_PAGE)
                InState(UP_PAUSE);
            else
                InState(DOWN_PAUSE);
        }
        else if(mCurOperationModel==CONTINUE_TIMER)
        {
            if(mTabWidget->currentIndex()==UP_TIMER_PAGE)
                InState(UP_RUNNING);
            else
                InState(DOWN_RUNNING);
        }
        UpCurTimerText();
    }) ;

    connect(mReset,&QPushButton::clicked,this,[=](){
        if(mTabWidget->currentIndex()==UP_TIMER_PAGE)
        {
            if (mCurModel <= UP_FINISH)
                InState(INIT);
        }
        else
        {
            if (mCurModel >= DOWN_RUNNING)
                InState(INIT);
        }
        UpCurTimerText();
    }) ;

    connect(mClose,&QPushButton::clicked,this,&TimerPopup::close);

    connect(&mBaseTimer,&QTimer::timeout,this,[=]()
    {
        QDateTime curTime = QDateTime::currentDateTime();
        int trueTikeCount = static_cast<int>(std::round((mBeginTime.msecsTo(curTime))/1000.0));
        switch(mCurModel)
        {
        case DOWN_RUNNING:
            mCurTimerTikerDown = mLastBeginTiker+trueTikeCount;
            break;
        case UP_RUNNING:
            mCurTimerTikerUp = mLastBeginTiker+trueTikeCount;
            break;
        default:
            break;
        }
        UpCurTimerText();
    });

    connect(mTabWidget,&QTabWidget::currentChanged,this,[=](){
        UpOperationModel();
        if(mCurModel == INIT)
        {
            UpCurTimerText();
            InState(INIT);
        }
    });

    connect(&mAutoExitEditTime, &QTimer::timeout, this, [&]{
        hide();
        mAutoExitEditTime.stop();
    });

    connect(mDownMinutes, &CfgButton::SignalValueChanged, mDownMinutes, [this](int val){
        mDownMinutes->setText(QString("%1").arg(val, 2, 10, QChar('0')));
        mPrevDownTimerValue = val*60 + mDownMsec->value();
        if(mCurModel == DOWN_FINISH)
            InState(INIT);
        if(mCurModel == INIT)
            UpCurTimerText();
    });

    connect(mDownMsec, &CfgButton::SignalValueChanged, mDownMsec, [this](int val){
        mDownMsec->setText(QString("%1").arg(val, 2, 10, QChar('0')));
        mPrevDownTimerValue = mDownMinutes->value()*60 + val;
        if(mCurModel == DOWN_FINISH)
            InState(INIT);
        if(mCurModel == INIT)
            UpCurTimerText();
    });

    connect(mDownMinutes, &CfgButton::SignalValueChangedTemporary, mDownMinutes, [this](int val){
        mDownMinutes->setText(QString("%1").arg(val, 2, 10, QChar('0')));
    });

    connect(mDownMsec, &CfgButton::SignalValueChangedTemporary, mDownMinutes, [this](int val){
        mDownMsec->setText(QString("%1").arg(val, 2, 10, QChar('0')));
    });

    mDownMinutes->setValue(mPrevDownTimerValue/60);
    mDownMsec->setValue(mPrevDownTimerValue%60);
    mBaseTimer.setTimerType(Qt::PreciseTimer);
    mBaseTimer.setInterval(1000);

}

void TimerPopup::InitUI()
{
    setMouseTracking(true);
    setStyleSheet(GetQssFileStr("Timer.qss"));
    StyleSet::SetBackgroundColor(this,COLOR_GRAY);
    mAutoExitEditTime.setSingleShot(true);
    installEventFilter(this);

    mUpTimer =new FocusWidget();
    mUpTimeMinutes = new QLabel();
    mUpTimeMinutes->setFixedHeight(UPTIME_LABEL_HEIGHT);
    StyleSet::SetTextColor(mUpTimeMinutes,COLOR_DARK_GRAY);
    StyleSet::SetTextFont(mUpTimeMinutes,FONT_XL);
    StyleSet::SetBackgroundColor(mUpTimeMinutes,COLOR_WHITE_GRAY);
    mUpTimeMinutes->setText("00");

    mUpTimeSeconds = new QLabel();
    mUpTimeSeconds->setFixedHeight(UPTIME_LABEL_HEIGHT);
    StyleSet::SetTextColor(mUpTimeSeconds,COLOR_DARK_GRAY);
    StyleSet::SetTextFont(mUpTimeSeconds,FONT_XL);
    StyleSet::SetBackgroundColor(mUpTimeSeconds,COLOR_WHITE_GRAY);
    mUpTimeSeconds->setText("00");

    auto layout1 = new QHBoxLayout(mUpTimer);
    mUpSemicolon = new QLabel();
    StyleSet::SetBackgroundColor(mUpSemicolon,COLOR_TRANSPARENT);
    StyleSet::SetTextColor(mUpSemicolon,COLOR_DARK_GRAY);
    StyleSet::SetTextFont(mUpSemicolon,FONT_L);
    mUpSemicolon->setText(":");
    mUpSemicolon->setFixedWidth(SEMICOLON_WIDHT);
    mUpSemicolon->setAlignment(Qt::AlignHCenter | Qt::AlignVCenter);


    mUpTimeMinutes->setAlignment(Qt::AlignCenter);
    mUpTimeSeconds->setAlignment(Qt::AlignCenter);

    layout1->addWidget(mUpTimeMinutes);
    layout1->addWidget(mUpSemicolon);
    layout1->addWidget(mUpTimeSeconds);
    mDownTimer = new FocusWidget();

    mDownMinutes = new CfgButton(mDownTimer, true);
    StyleSet::SetTextFont(mDownMinutes,FONT_XL);
    mDownMinutes->setObjectName("MinutesEdit");
    mDownMinutes->setFixedHeight(UPTIME_LABEL_HEIGHT);
    mDownMinutes->setDataModel(new commonInnerData);
    mDownMinutes->SetRange(0, 999);
    mDownMinutes->SetPopupType(PopupAdapter::NUMBER_POPUB);
    //mDownMinutes->setIsCloseRefershPopup(false);
    mDownMinutes->SetPopupInputRegex(QString(MIN_REGEX));
    mDownMinutes->SetErrorValueTipStr(IntraData::OUT_MAX,
                                      UIStrings::GetStr(ALL_STRINGS_ENUM::STR_TIMER_LIMIT) + " 999min");

    mDownMsec = new CfgButton(mDownTimer, true);
    StyleSet::SetTextFont(mDownMsec,FONT_XL);
    mDownMsec->setFixedHeight(UPTIME_LABEL_HEIGHT);
    mDownMsec->setObjectName("MescEdit");
    commonInnerData *mSecData = new commonInnerData;
    mDownMsec->setDataModel(mSecData);
    mDownMsec->SetRange(0,59);
    mDownMsec->SetPopupType(PopupAdapter::NUMBER_POPUB);
    //mDownMsec->setIsCloseRefershPopup(false);
    mDownMsec->SetPopupInputRegex(QString(MSEC_REGEX));
    mDownMsec->SetErrorValueTipStr(IntraData::OUT_MAX,
                                      UIStrings::GetStr(ALL_STRINGS_ENUM::STR_TIMER_LIMIT) + " 59s");

    auto layout = new QHBoxLayout(mDownTimer);
    mDownSemicolon = new QLabel(mDownTimer);
    StyleSet::SetBackgroundColor(mDownSemicolon,COLOR_TRANSPARENT);
    StyleSet::SetTextColor(mDownSemicolon,COLOR_DARK_GRAY);
    StyleSet::SetTextFont(mDownSemicolon,FONT_L);
    mDownSemicolon->setText(":");
    mDownSemicolon->setFixedWidth(SEMICOLON_WIDHT);
    mDownSemicolon->setAlignment(Qt::AlignHCenter | Qt::AlignVCenter);

    layout->addWidget(mDownMinutes);
    layout->addWidget(mDownSemicolon);
    layout->addWidget(mDownMsec);

    mClose = new CloseButton(this);
    mClose->setFixedSize(CLOSE_BT_SIZE);

    mOperationBtn = new PushButton(this);
    mOperationBtn->setObjectName("OperationButton");
    mReset = new PushButton(this);
    mReset->setObjectName("ResetButton");

    mTabWidget= new VerticalTabWidget(this);
    mTabWidget->SetTabBarSize(QSize(TAB_SIZE));
    mTabWidget->addTab(mUpTimer, "");
    mTabWidget->addTab(mDownTimer, "");

    mTopWidget = new FocusWidget(this);

    mTopWidget->setFixedHeight(mClose->height()+4);
    StyleSet::SetBackgroundColor(mTopWidget,COLOR_DARK_GRAY);

    QHBoxLayout* hlayoutTop = new QHBoxLayout(mTopWidget);
    hlayoutTop->setContentsMargins(0,2,2,2);
    hlayoutTop->addWidget(mClose,0,Qt::AlignRight|Qt::AlignVCenter);

    QHBoxLayout* hlayout = new QHBoxLayout;
    hlayout->addStretch(1);
    hlayout->addWidget(mOperationBtn);
    hlayout->addWidget(mReset);


    QVBoxLayout* vlayout =new QVBoxLayout();
    vlayout->addWidget(mTabWidget);
    vlayout->addLayout(hlayout);
    vlayout->addSpacing(2);
    vlayout->setContentsMargins(4, 0, 4, 4);

    QVBoxLayout* vlayoutAll =new QVBoxLayout(this);
    vlayoutAll->setContentsMargins(0,0,0,0);
    vlayoutAll->addWidget(mTopWidget);
    vlayoutAll->addLayout(vlayout);

    setFixedSize(PROPUB_DEFAULT_SIZE);
    OpenFocusCycle(true);
    OpenFocusInAutoSetFirst(true);
    setLayout(vlayoutAll);
    UpCurTimerText();
}

void TimerPopup::UpOperationModel()
{
    if(mTabWidget->currentIndex()==UP_TIMER_PAGE)
    {
        switch(mCurModel)
        {
        case UP_RUNNING:
            mCurOperationModel = PAUSE_TIMER;
            SetOperationBtnText(ALL_STRINGS_ENUM::STR_STOP);
            break;
        case UP_PAUSE:
            mCurOperationModel=CONTINUE_TIMER;
            SetOperationBtnText(ALL_STRINGS_ENUM::STR_CONTINUE);
            break;
        default:
            mCurOperationModel=START_TIMER;
            SetOperationBtnText(ALL_STRINGS_ENUM::STR_START);
            break;
        }
    }
    else
    {
        switch(mCurModel)
        {
        case DOWN_RUNNING:
            mCurOperationModel=PAUSE_TIMER;
            SetOperationBtnText(ALL_STRINGS_ENUM::STR_STOP);
            break;
        case DOWN_PAUSE:
            mCurOperationModel=CONTINUE_TIMER;
            SetOperationBtnText(ALL_STRINGS_ENUM::STR_CONTINUE);
            break;
        default:
            mCurOperationModel=START_TIMER;
            SetOperationBtnText(ALL_STRINGS_ENUM::STR_START);
            break;
        }
    }
}

void TimerPopup::UpCurTimerText()
{
    if(mCurModel==INIT)
    {
        mUpTimeMinutes->setText(QString("%1").arg(0,2,10,QChar('0')));
        mUpTimeSeconds->setText(QString("%1").arg(0,2,10,QChar('0')));

        mDownMinutes->setDumbValue(mPrevDownTimerValue/60);
        mDownMinutes->setText(QString("%1").arg(mDownMinutes->value(), 2, 10, QChar('0')));
        mDownMsec->setDumbValue(mPrevDownTimerValue%60);
        mDownMsec->setText(QString("%1").arg(mDownMsec->value(), 2, 10, QChar('0')));

        if(mTabWidget->currentIndex() == UP_TIMER_PAGE)
            mCurTimerText=QString("%1:%2").arg(0,2,10,QChar('0')).arg(0,2,10,QChar('0'));
        else
            mCurTimerText=QString("%1:%2").arg(mDownMinutes->value(),2,10,QChar('0')).arg(mDownMsec->value(),2,10,QChar('0'));
        emit SignalTimeStrChange();
    }
    if(mCurModel==DOWN_RUNNING)
    {
        if(mCurTimerTikerDown <= mPrevDownTimerValue)
        {
            mDownMinutes->setDumbValue((mPrevDownTimerValue - mCurTimerTikerDown) / 60);
            mDownMinutes->setText(QString("%1").arg(mDownMinutes->value(), 2, 10, QChar('0')));
            mDownMsec->setDumbValue((mPrevDownTimerValue-mCurTimerTikerDown) % 60);
            mDownMsec->setText(QString("%1").arg(mDownMsec->value(), 2, 10, QChar('0')));

            mCurTimerText=QString("%1:%2").arg(mDownMinutes->value(),2,10,QChar('0')).arg(mDownMsec->value(),2,10,QChar('0'));
            emit SignalTimeStrChange();
        }
        if(mCurTimerTikerDown == mPrevDownTimerValue)
            InState(DOWN_FINISH);
    }
    if(mCurModel==UP_RUNNING)
    {
        if(mCurTimerTikerUp <= MAX_UP_TIME_VALUE)
        {
            mUpTimeMinutes->setText(QString("%1").arg(mCurTimerTikerUp / 60,2,10,QChar('0')));
            mUpTimeSeconds->setText(QString("%1").arg(mCurTimerTikerUp % 60,2,10,QChar('0')));

            mCurTimerText=QString("%1:%2").arg(mCurTimerTikerUp / 60,2,10,QChar('0')).arg(mCurTimerTikerUp % 60,2,10,QChar('0'));
            emit SignalTimeStrChange();
        }
        if(mCurTimerTikerUp == MAX_UP_TIME_VALUE)
            InState(UP_FINISH);
    }
    if(mCurModel==DOWN_FINISH)
    {
        mDownMinutes->setDumbValue(mPrevDownTimerValue/60);
        mDownMinutes->setText(QString("%1").arg(mDownMinutes->value(), 2, 10, QChar('0')));
        mDownMsec->setDumbValue(mPrevDownTimerValue%60);
        mDownMsec->setText(QString("%1").arg(mDownMsec->value(), 2, 10, QChar('0')));
    }
    if(mCurModel==UP_FINISH)
    {
        mUpTimeMinutes->setText(QString("%1").arg(0,2,10,QChar('0')));
        mUpTimeSeconds->setText(QString("%1").arg(0,2,10,QChar('0')));
    }
}

void TimerPopup::SetOperationBtnText(ALL_STRINGS_ENUM strId)
{
    mOperationBtnTextId = strId;
    mOperationBtn->setText(UIStrings::GetStr(mOperationBtnTextId));
}

void TimerPopup::InState(TIMER_STATE nextStatu)
{
    mBaseTimer.stop();
    switch((TIMER_STATE)mCurModel)
    {
    case INIT:
        break;
    case UP_RUNNING:
        if(nextStatu != UP_PAUSE)
            mCurTimerTikerUp=0;
        if(nextStatu == DOWN_RUNNING)
        {
            mUpTimeMinutes->setText(QString("%1").arg(0,2,10,QChar('0')));
            mUpTimeSeconds->setText(QString("%1").arg(0,2,10,QChar('0')));
        }
        break;
    case UP_PAUSE:
        if(nextStatu != UP_RUNNING)
            mCurTimerTikerUp=0;
        if(nextStatu == DOWN_RUNNING)
        {
            mUpTimeMinutes->setText(QString("%1").arg(0,2,10,QChar('0')));
            mUpTimeSeconds->setText(QString("%1").arg(0,2,10,QChar('0')));
        }
        break;
    case UP_FINISH:
        mCurTimerTikerUp=0;
        break;
    case DOWN_RUNNING:
        if(nextStatu != DOWN_PAUSE)
            mCurTimerTikerDown=0;
        if(nextStatu == UP_RUNNING)
        {
            mDownMinutes->setDumbValue(mPrevDownTimerValue/60);
            mDownMinutes->setText(QString("%1").arg(mDownMinutes->value(), 2, 10, QChar('0')));
            mDownMsec->setDumbValue(mPrevDownTimerValue%60);
            mDownMsec->setText(QString("%1").arg(mDownMsec->value(), 2, 10, QChar('0')));
        }
        break;
    case DOWN_PAUSE:
        if(nextStatu != DOWN_RUNNING)
            mCurTimerTikerDown=0;
        if(nextStatu == UP_RUNNING)
        {
            mDownMinutes->setDumbValue(mPrevDownTimerValue/60);
            mDownMinutes->setText(QString("%1").arg(mDownMinutes->value(), 2, 10, QChar('0')));
            mDownMsec->setDumbValue(mPrevDownTimerValue%60);
            mDownMsec->setText(QString("%1").arg(mDownMsec->value(), 2, 10, QChar('0')));
        }
        break;
    case DOWN_FINISH:
        mCurTimerTikerDown=0;
        break;
    }
    mDownMsec->setEnableEdit(true);
    mDownMinutes->setEnableEdit(true);
    switch (nextStatu)
    {
    case INIT:
        mCurModel=INIT;
        AlarmManager::GetInstance()->TriggerAlarm(PROMPT_TIMER_TIMEOUT, 0);
        AlarmManager::GetInstance()->TriggerAlarm(PROMPT_COUNTDOWN_TIMEOUT, 0);
        break;
    case UP_RUNNING:
        mCurModel=UP_RUNNING;
        AlarmManager::GetInstance()->TriggerAlarm(PROMPT_TIMER_TIMEOUT, 0);
        AlarmManager::GetInstance()->TriggerAlarm(PROMPT_COUNTDOWN_TIMEOUT, 0);
        mBaseTimer.start();
        mBeginTime = QDateTime::currentDateTime();
        mLastBeginTiker = mCurTimerTikerUp;
        break;
    case UP_PAUSE:
        mCurModel=UP_PAUSE;
        break;
    case UP_FINISH:
        mCurModel=UP_FINISH;
        AlarmManager::GetInstance()->TriggerAlarm(PROMPT_TIMER_TIMEOUT, 1);
        break;
    case DOWN_RUNNING:
        mCurModel=DOWN_RUNNING;
        AlarmManager::GetInstance()->TriggerAlarm(PROMPT_TIMER_TIMEOUT, 0);
        AlarmManager::GetInstance()->TriggerAlarm(PROMPT_COUNTDOWN_TIMEOUT, 0);
        mBaseTimer.start();
        mBeginTime = QDateTime::currentDateTime();
        mLastBeginTiker = mCurTimerTikerDown;
        mDownMsec->setEnableEdit(false);
        mDownMinutes->setEnableEdit(false);
        break;
    case DOWN_PAUSE:
        mCurModel=DOWN_PAUSE;
        mDownMsec->setEnableEdit(false);
        mDownMinutes->setEnableEdit(false);
        break;
    case DOWN_FINISH:
        mCurModel=DOWN_FINISH;
        AlarmManager::GetInstance()->TriggerAlarm(PROMPT_COUNTDOWN_TIMEOUT, 1);
        break;
    }
    UpOperationModel();
    emit SignalTimerModeChange(mCurModel);
    UpCurTimerText();
}

QString TimerPopup::curTimerText() const
{
    return mCurTimerText;
}

int TimerPopup::getCurTabIndex()
{
    return mTabWidget->currentIndex();
}

void TimerPopup::InitUiText()
{
    SetOperationBtnText(mOperationBtnTextId);
    mReset->setText(UIStrings::GetStr(ALL_STRINGS_ENUM::STR_RESET));

    mTabWidget->setTabText(mTabWidget->indexOf(mUpTimer),
                           UIStrings::GetStr(ALL_STRINGS_ENUM::STR_TIMER));
    mTabWidget->setTabText(mTabWidget->indexOf(mDownTimer),
                           UIStrings::GetStr(ALL_STRINGS_ENUM::STR_COUNTDOWN));
    mDownMinutes->SetErrorValueTipStr(IntraData::OUT_MAX,
                                      UIStrings::GetStr(ALL_STRINGS_ENUM::STR_TIMER_LIMIT) + " 999min");
    mDownMsec->SetErrorValueTipStr(IntraData::OUT_MAX,
                                      UIStrings::GetStr(ALL_STRINGS_ENUM::STR_TIMER_LIMIT) + " 59s");
}

void TimerPopup::SlotRestartAutoOutEditTimer()
{
    if(mAutoExitEditTime.isActive())
        mAutoExitEditTime.start(AUTO_EXIT_TIME);
}


void TimerPopup::ResetTimer()
{
    InState(INIT);
}

bool TimerPopup::eventFilter(QObject *obj, QEvent *ev)
{
    if (obj == this)
    {
        if(ev->type() == QEvent::MouseButtonRelease)
        {
            auto mouseEvent = dynamic_cast<QMouseEvent *>(ev);
            if (!rect().contains(mouseEvent->pos()))
                this->close();
        }
        else if(ev->type() == QEvent::Show)
        {
            mAutoExitEditTime.start(AUTO_EXIT_TIME);
        }
        else if(ev->type() == QEvent::Close)
        {
            mAutoExitEditTime.stop();
        }
    }
    return FocusWidget::eventFilter(obj, ev);
}
