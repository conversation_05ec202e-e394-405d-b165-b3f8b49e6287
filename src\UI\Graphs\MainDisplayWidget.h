﻿#ifndef BGMainDisplayWidget_H
#define BGMainDisplayWidget_H

#include <QWidget>
#include <QStackedLayout>
#include <QFrame>
#include <QLabel>

#include "KeyConfig.h"
#include "UiConfig.h"
#include "FocusWidget.h"
#include "SystemSettingManager.h"
#include "AllParamBoard.h"


class WaveChart;
class LoopGraph;
class IconComboBox;


class MainDisplayWidget : public FocusWidget
{
    Q_OBJECT
public:
    enum MAIN_DISPLAY_LAYOUT_ENUM
    {
        FOUR_WAVE,
        THREE_WAVE,
        ONE_WAVE_ALL_PARAMS,
        TWO_WAVE_TWO_LOOP
    };

    explicit MainDisplayWidget(QWidget *parent = NULL);
    void ShowCPBInfo();
    void ShowPlotInfo();
    void ChangeWaveChart(int pos, short waveType);
    void InitUiText();
    static QMap<MainDisplayWidget::MAIN_DISPLAY_LAYOUT_ENUM, bool> GetLayoutEnable(){return sLayoutEnable;}

protected:
    void keyPressEvent(QKeyEvent *ev);
    void resizeEvent(QResizeEvent *);

private:
    void LayoutSwitch(MAIN_DISPLAY_LAYOUT_ENUM);

    void InitUi();
    void InitConnect();
    void HideAllComponent();

private:
    FocusWidget* mWaveWidget{};
    IconComboBox *mLayoutSwitcher{};
    QMap<E_MAINSHOW_WAVEAREA_WAVE_INDEX, WaveChart *> mWaves{};
    LoopGraph *mLoopGraph{};
    AllParamBoard *mAllParamBoard{};
    FocusWidget *mPlotContainer{};

    QLabel *mCpbLabel{};

    static QMap<MainDisplayWidget::MAIN_DISPLAY_LAYOUT_ENUM, bool> sLayoutEnable;
};

#endif // BGMainDisplayWidget_H
