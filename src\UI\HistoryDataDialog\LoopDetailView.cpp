﻿#include "UiApi.h"
#include "LoopDetailView.h"
#include "PushButton.h"
#include "ComboBox.h"
#include "ParamLabelManage.h"
#include "RunModeManage.h"
#include "VentModeSettingManager.h"
#include "SystemSettingManager.h"
#define COL_CNT 3
#define COL_WIDTH 50
#define DEFAULT_CHAT_HEIGHT 320
#define BOTTOM_WIDGET_HEIGHT 50
#define HISTORY_LOOP_MAX_NUM 5

QVector<MONITOR_PARAM_TYPE> sIdVec =
{
    VALUEDATA_MV,
    VALUEDATA_VTE,
    VALUEDATA_RATE,
    VALUEDATA_IE,
    VALUEDATA_PEEP,
    VALUEDATA_PPEAK,
    VALUEDATA_PPLAT,
    VALUEDATA_PMEAN,
    VALUEDATA_C,
    VALUEDATA_R,
};

LoopDetailView::LoopDetailView(QWidget *parent) : FocusWidget(parent)
{
    //    setStyle(new BorderProxyStyle);
    InitUi();
    InitUiText();
    InitConnect();
}

void LoopDetailView::InitUiText()
{
    mTimeStr = UIStrings::GetStr(ALL_STRINGS_ENUM::STR_MAINMENU_TIMEDATE) + ": ";
    mTimeName->setText(mTimeStr);

    mLoopCompareBtn->setText(UIStrings::GetStr(ALL_STRINGS_ENUM::STR_COMPARE_LOOP));
    mSaveLoopBtn->setText(UIStrings::GetStr(ALL_STRINGS_ENUM::STR_SAVE_LOOP));

    mSwitchRefLoopComboBox->SetName(UIStrings::GetStr(ALL_STRINGS_ENUM::STR_SWITCH_REFER_LOOP));
    QVector<LoopHistorySt> backups = ChartManager::GetInstance()->GetBackups(LOOP_VF);
    QMap<int, QString> strMap{{-1, UIStrings::GetStr(ALL_STRINGS_ENUM::STR_NONE)}};
    if (!backups.empty())
    {
        mSwitchRefLoopComboBox->setEnabled(true);
        if(backups.size() > HISTORY_LOOP_MAX_NUM)
        {
            int truePos = backups.size() - HISTORY_LOOP_MAX_NUM;
            for (int i = truePos; i < backups.size(); ++i)
            {
                strMap.insert(i - truePos, SystemTimeManager::GetDateTimeStr(QDateTime::fromMSecsSinceEpoch(backups[i].mTimestamp), "\n"));
            }
        }
        else
        {
            for (int i = 0; i < backups.size(); ++i)
            {
                strMap.insert(i, SystemTimeManager::GetDateTimeStr(QDateTime::fromMSecsSinceEpoch(backups[i].mTimestamp), "\n"));
            }
        }
    }
    else
    {
        mSwitchRefLoopComboBox->setEnabled(false);
    }

    mSwitchRefLoopComboBoxDataModel->SetValueAndStr(strMap);
    mSwitchRefLoopComboBox->setDataModel(mSwitchRefLoopComboBoxDataModel);

    foreach (auto id, sIdVec)
    {
        ParamLabelManage::GetInstance()->RefurbishParamByLanguage(id, mParamLabels[id]);
    }
}

void LoopDetailView::InitParamTableHeader()
{
    QStringList headerList = {"Time"};
    foreach (auto id, sIdVec)
        headerList.append(UIStrings::GetStr((ALL_STRINGS_ENUM)ParamLabelManage::GetInstance()->GetParamNameId(id)));
}

void LoopDetailView::FillParams(LoopHistorySt hisSt)
{
    if (hisSt.mTimestamp == 0)
    {
        foreach (auto lb, mParamLabels)
        {
            lb->SetValue("---");
        }
        return;
    }

    auto dateTime = QDateTime::fromMSecsSinceEpoch(hisSt.mTimestamp);
    mTimeLabel->setText(SystemTimeManager::GetDateTimeStr(dateTime));

    auto convertFunc = UnitManager::GetInstance()->ConvertFunctor(PRESSURE_CATEGORY,
                                                                  DataManager::GetDefaultUnit(DataManager::WAVE_PAW),
                                                                  UnitManager::GetInstance()->GetCurPressureUnitType());

    mParamLabels[VALUEDATA_MV]->SetValue(hisSt.mMv != INVALID_VALUE ?
                QString::number(hisSt.mMv / 100.0, 'f', 2) : UIStrings::GetStr(STR_DASH));
    mParamLabels[VALUEDATA_VTE]->SetValue(hisSt.mVte != INVALID_VALUE ?
                QString::number(hisSt.mVte ) : UIStrings::GetStr(STR_DASH));
    mParamLabels[VALUEDATA_RATE]->SetValue(hisSt.mVte != INVALID_VALUE ?
                QString::number(hisSt.mRate ) : UIStrings::GetStr(STR_DASH));
    mParamLabels[VALUEDATA_IE]->SetValue(DataManager::ConvertIeValueToStr(hisSt.mIE));
    mParamLabels[VALUEDATA_PEEP]->SetValue(hisSt.mPeep != INVALID_VALUE ?
                QString::number(convertFunc(hisSt.mPeep)) : UIStrings::GetStr(STR_DASH));
    mParamLabels[VALUEDATA_PPEAK]->SetValue(hisSt.mPpeak != INVALID_VALUE ?
                QString::number(convertFunc(hisSt.mPpeak)) : UIStrings::GetStr(STR_DASH));
    mParamLabels[VALUEDATA_PPLAT]->SetValue(hisSt.mPplat != INVALID_VALUE ?
                QString::number(convertFunc(hisSt.mPplat)) : UIStrings::GetStr(STR_DASH));
    mParamLabels[VALUEDATA_PMEAN]->SetValue(hisSt.mPmean != INVALID_VALUE ?
                QString::number(convertFunc(hisSt.mPmean)) : UIStrings::GetStr(STR_DASH));
    mParamLabels[VALUEDATA_C]->SetValue(hisSt.mC != INVALID_VALUE ?
                QString::number(hisSt.mC) : UIStrings::GetStr(STR_DASH));
    mParamLabels[VALUEDATA_R]->SetValue(hisSt.mR != INVALID_VALUE ?
                QString::number(hisSt.mR) : UIStrings::GetStr(STR_DASH));

    QDateTime::fromMSecsSinceEpoch(hisSt.mTimestamp).toString("hh:mm:ss");
}

void LoopDetailView::InitBottomWidget()
{
    mSaveLoopBtn = new PushButton;
    mLoopCompareBtn = new PushButton;

    mSwitchRefLoopComboBox = new PushButtonCombox(this);
    mSwitchRefLoopComboBox->setEnabled(false);
    mSwitchRefLoopComboBox->SetName(UIStrings::GetStr(ALL_STRINGS_ENUM::STR_SWITCH_REFER_LOOP));

    mSwitchRefLoopComboBoxDataModel = new ValueStrIntraData;
    mSwitchRefLoopComboBoxDataModel->SetValueAndStr({{0, UIStrings::GetStr(ALL_STRINGS_ENUM::STR_NONE)}});
    mSwitchRefLoopComboBox->setDataModel(mSwitchRefLoopComboBoxDataModel);

    auto bottomLayout = new QHBoxLayout;
    mLoopCompareBtn->setVisible(false);
    bottomLayout->setContentsMargins(5, 0, 5, 0);
    bottomLayout->addWidget(mSaveLoopBtn);
    bottomLayout->addWidget(mSwitchRefLoopComboBox);
    bottomLayout->addStretch();
    bottomLayout->addWidget(mLoopCompareBtn);


    mBottomDocker = new FocusWidget;
    mBottomDocker->setFixedHeight(BOTTOM_WIDGET_HEIGHT);
    mBottomDocker->setLayout(bottomLayout);
}


void LoopDetailView::InitUi()
{
    LOOP_TYPE leftType = (LOOP_TYPE)(SettingManager->GetIntSettingValue(SystemSettingManager::LOOP_LOOPDETAIL_POSITION_LEFT));
    mBigLoop = new LoopChartWithSwitcher(leftType);
    ChartManager::GetInstance()->RegisterChart(ChartManager::CHART_TYPE_ENUM::LOOP_CHART, leftType, mBigLoop);

    LOOP_TYPE rightType = (LOOP_TYPE)(SettingManager->GetIntSettingValue(SystemSettingManager::LOOP_LOOPDETAIL_POSITION_RIGHT));
    mRightLoop = new LoopChartWithSwitcher(rightType);
    ChartManager::GetInstance()->RegisterChart(ChartManager::CHART_TYPE_ENUM::LOOP_CHART, rightType, mRightLoop);

    mBigLoop->setFixedHeight(DEFAULT_CHAT_HEIGHT);

    mRightLoop->setFixedHeight(DEFAULT_CHAT_HEIGHT);

    QVBoxLayout *paramLayoutV = new QVBoxLayout;
    foreach (auto id, sIdVec)
    {
        mParamLabels.insert(id, new ParamLabelLayoutHorizontal);//Tbd 3ms 2023.10.18
        mParamLabels[id]->SetLabelScale(ParamLabelLayoutHorizontal::LABEL_SCALE_ENUM::MIN);
        ParamLabelManage::GetInstance()->ConfigParam(id, mParamLabels[id]);

        mParamLabels[id]->SetLimitVisible();
        paramLayoutV->addWidget(mParamLabels[id]);
        paramLayoutV->setAlignment(mParamLabels[id], Qt::AlignRight);
    }
    auto midArea = new FocusWidget;
//    auto area = new QWidget;
//    auto layout = new QHBoxLayout;
//    layout->setContentsMargins(5, 5, 5, 5);
//    layout->setSpacing(2);
//    layout->addWidget(mRightLoop);
//    area->setLayout(layout);
//    area->show();
    auto midLayout = new QHBoxLayout;
    midLayout->setContentsMargins(5, 5, 5, 5);
    midLayout->setSpacing(2);
    midLayout->addWidget(mBigLoop);
    midLayout->addWidget(mRightLoop);
    midLayout->addLayout(paramLayoutV);
    midLayout->setAlignment(mBigLoop, Qt::AlignVCenter | Qt::AlignLeft);
    midLayout->setAlignment(paramLayoutV, Qt::AlignRight);
    midArea->setLayout(midLayout);

    mTimeStr = UIStrings::GetStr(ALL_STRINGS_ENUM::STR_MAINMENU_TIMEDATE) + ": ";
    mTimeName = new LabelPro;
    mTimeName->setText(mTimeStr);
    mTimeName->SetTextFontSize(16);
    mTimeName->SetTextFontColor(Qt::white);

    mTimeLabel = new LabelPro;
    mTimeLabel->setText("---");
    mTimeLabel->SetTextFontSize(16);
    mTimeLabel->SetTextFontColor(Qt::white);

    auto TimeLayout = new QHBoxLayout;
    TimeLayout->setContentsMargins(0,0,0,0);
    TimeLayout->addWidget(mTimeName);
    TimeLayout->addWidget(mTimeLabel);
    TimeLayout->addStretch();

    auto midBottomLayout = new QVBoxLayout;
    midBottomLayout->setContentsMargins(5, 0, 5, 0);
    midBottomLayout->addSpacing(5);
    midBottomLayout->addLayout(TimeLayout);
    midBottomLayout->addWidget(midArea);
    midBottomLayout->addSpacing(5);

    auto plotArea = new FocusWidget;
    StyleSet::SetBackgroundColor(plotArea,COLOR_BLACK);
    plotArea->setObjectName("plotArea");
    plotArea->setLayout(midBottomLayout);


    InitBottomWidget();
    auto mainLayout = new QVBoxLayout;
    mainLayout->setSpacing(0);
    mainLayout->setContentsMargins(0, 0, 0, 0);
    mainLayout->addWidget(plotArea);
    mainLayout->addWidget(mBottomDocker);
    setLayout(mainLayout);
    mBottomDocker->installEventFilter(this);
    mBottomDocker->AddFoucusSonWidget(mSaveLoopBtn);
    mBottomDocker->AddFoucusSonWidget(mSwitchRefLoopComboBox);
}

void LoopDetailView::SlotOnLoopBackupChanged()
{
    QVector<LoopHistorySt> backups = ChartManager::GetInstance()->GetBackups(LOOP_VF);

    QMap<int, QString> strMap{{-1, UIStrings::GetStr(ALL_STRINGS_ENUM::STR_NONE)}};
    if (!backups.empty())
    {
        if(backups.size() > HISTORY_LOOP_MAX_NUM)
        {
            int truePos = backups.size() - HISTORY_LOOP_MAX_NUM;
            for (int i = truePos; i < backups.size(); ++i)
            {
                strMap.insert(i - truePos, SystemTimeManager::GetDateTimeStr(QDateTime::fromMSecsSinceEpoch(backups[i].mTimestamp), "\n"));
            }
        }
        else
        {
            for (int i = 0; i < backups.size(); ++i)
            {
                strMap.insert(i, SystemTimeManager::GetDateTimeStr(QDateTime::fromMSecsSinceEpoch(backups[i].mTimestamp), "\n"));
            }
        }

        FillParams(backups.last());
        mSwitchRefLoopComboBox->setEnabled(true);
        if (backups.size() >= 2)
        {
            mLoopCompareBtn->setEnabled(true);
        }
    }
    else
    {
        mTimeLabel->setText("---");
        FillParams({});
        mSwitchRefLoopComboBox->setEnabled(false);
    }

    mSwitchRefLoopComboBoxDataModel->SetValueAndStr(strMap);
    mSwitchRefLoopComboBox->setDataModel(mSwitchRefLoopComboBoxDataModel);
    mSwitchRefLoopComboBox->setValue(strMap.size() - 1);
    mSwitchRefLoopComboBox->SlotPopupNeedTextData();
}

void LoopDetailView::SlotOnDateTimeFormatChanged()
{
    QVector<LoopHistorySt> backups = ChartManager::GetInstance()->GetBackups(LOOP_VF);

    QMap<int, QString> strMap{{-1, UIStrings::GetStr(ALL_STRINGS_ENUM::STR_NONE)}};
    if (!backups.empty())
    {
        if(backups.size() > HISTORY_LOOP_MAX_NUM)
        {
            int truePos = backups.size() - HISTORY_LOOP_MAX_NUM;
            for (int i = truePos; i < backups.size(); ++i)
            {
                strMap.insert(i - truePos, SystemTimeManager::GetDateTimeStr(QDateTime::fromMSecsSinceEpoch(backups[i].mTimestamp), "\n"));
            }
        }
        else
        {
            for (int i = 0; i < backups.size(); ++i)
            {
                strMap.insert(i, SystemTimeManager::GetDateTimeStr(QDateTime::fromMSecsSinceEpoch(backups[i].mTimestamp), "\n"));
            }
        }

        auto dateTime = QDateTime::fromMSecsSinceEpoch(backups.at(mSwitchRefLoopComboBox->value()).mTimestamp);
        mTimeLabel->setText(SystemTimeManager::GetDateTimeStr(dateTime));
    }
    else
    {
        mTimeLabel->setText("---");
    }

    mSwitchRefLoopComboBoxDataModel->SetValueAndStr(strMap);
}

void LoopDetailView::SlotOnUnitChanged(CONVERTIBLE_UNIT_CATEGORY category, UNIT_TYPE, UNIT_TYPE)
{
    if(category == CONVERTIBLE_UNIT_CATEGORY::PRESSURE_CATEGORY)
    {
        foreach(auto &dataId, sIdVec)
        {
            auto paramInfo = ParamLabelManage::GetInstance()->GetParamInfoSt(dataId);
            if (UnitManager::GetInstance()->IsPressureUnitStr(paramInfo.mUnitStrId))
            {
                mParamLabels[dataId]->SetUnit(UIStrings::GetStr(paramInfo.mUnitStrId));
            }
        }
        int nowValue = mSwitchRefLoopComboBox->value();
        if (nowValue == -1) return;
        QVector<LoopHistorySt> backups = ChartManager::GetInstance()->GetBackups(LOOP_VF);
        int sizeBackips = backups.size();
        if(sizeBackips == 0) return;
        if(sizeBackips > HISTORY_LOOP_MAX_NUM)
        {
            nowValue += (sizeBackips - HISTORY_LOOP_MAX_NUM);
        }
        FillParams(backups[nowValue]);
    }
}


void LoopDetailView::InitConnect()
{
    connect(mLoopCompareBtn, &QPushButton::clicked, this, &LoopDetailView::SlotLoopCompareBtnClicked);
    connect(mSaveLoopBtn, &PushButton::clicked, ChartManager::GetInstance(), &ChartManager::AddBackUp);

    connect(ChartManager::GetInstance(), &ChartManager::SignalLoopBackupChanged, this, &LoopDetailView::SlotOnLoopBackupChanged);

    connect(mSwitchRefLoopComboBox, &CfgButton::SignalValueChanged, this, [this](int value){
        if (value == -1) //TBD 应为0时为选择无备份环图
        {
            ChartManager::GetInstance()->HideLoopBackup();
            mTimeLabel->setText("---");
            FillParams({});
            return;
        }
        QVector<LoopHistorySt> backups = ChartManager::GetInstance()->GetBackups(LOOP_VF);
        int sizeBackips = backups.size();
        if (sizeBackips == 0)
            return;

        if(sizeBackips > HISTORY_LOOP_MAX_NUM)
        {
            value += (sizeBackips - HISTORY_LOOP_MAX_NUM);
        }
        ChartManager::GetInstance()->DrawLoopBackups(backups[value].mTimestamp);
        FillParams(backups[value]);
    });

    connect(SystemTimeManager::GetInstance(), &SystemTimeManager::SignalDateFormatChanged,
            this, &LoopDetailView::SlotOnDateTimeFormatChanged);
    connect(SystemTimeManager::GetInstance(), &SystemTimeManager::SignalTimeFormatChanged,
            this, &LoopDetailView::SlotOnDateTimeFormatChanged);

    connect(SettingManager,&SystemSettingManager::SignalSettingChanged,[=](int settingId){
        if(settingId == SystemSettingManager::LOOP_LOOPDETAIL_POSITION_LEFT)
        {
            LOOP_TYPE newType = (LOOP_TYPE)(SettingManager->GetIntSettingValue(SystemSettingManager::LOOP_LOOPDETAIL_POSITION_LEFT));
            if(mBigLoop->GetType() != newType)
                mBigLoop->SetLoopType(newType);
        }
    });
    connect(mBigLoop,&LoopChartWithSwitcher::SignalLoopTypeChanged,[=](LOOP_TYPE newType){
        SettingManager->SetSettingValue(SystemSettingManager::LOOP_LOOPDETAIL_POSITION_LEFT,newType);
    });

    connect(SettingManager,&SystemSettingManager::SignalSettingChanged,[=](int settingId){
        if(settingId == SystemSettingManager::LOOP_LOOPDETAIL_POSITION_RIGHT)
        {
            LOOP_TYPE newType = (LOOP_TYPE)(SettingManager->GetIntSettingValue(SystemSettingManager::LOOP_LOOPDETAIL_POSITION_RIGHT));
            if(mRightLoop->GetType() != newType)
                mRightLoop->SetLoopType(newType);
        }
    });
    connect(mRightLoop,&LoopChartWithSwitcher::SignalLoopTypeChanged,[=](LOOP_TYPE newType){
        SettingManager->SetSettingValue(SystemSettingManager::LOOP_LOOPDETAIL_POSITION_RIGHT,newType);
    });

    connect(UnitManager::GetInstance(), &UnitManager::SignalCategoryUnitChanged, this, &LoopDetailView::SlotOnUnitChanged);

    connect(RunModeManage::GetInstance(), &RunModeManage::SignalModeChanged, this, [=](int preMode, int nowMode)
    {
        if(preMode == MODE_RUN_STANDBY && nowMode != preMode)
        {
            ChartManager::GetInstance()->DeleteAllBackup();
        }
        E_RUNMODE mode = (E_RUNMODE)nowMode;
        if (mode == MODE_RUN_STANDBY || mode == MODE_RUN_ACGO ||
                (mode == MODE_RUN_VENT && VentModeSettingManager::GetInstance()->GetCurMode()==VENTMODE_FLAG_HLM)
                )
        {
            mSaveLoopBtn->setEnabled(false);
            if(!mSwitchRefLoopComboBox->isEnabled())
                mBottomDocker->setEnabled(false);
            StyleSet::SetTextColor(mSaveLoopBtn,COLOR_GRAY);
        }
        else
        {
            mSaveLoopBtn->setEnabled(true);
            mBottomDocker->setEnabled(true);
        }
    });
}

void LoopDetailView::SlotLoopCompareBtnClicked()
{
    emit SignalShowLoopCompare();
}

void LoopDetailView::resizeEvent(QResizeEvent *event)
{
    FocusWidget::resizeEvent(event);
    mBigLoop->setFixedWidth(event->size().width() / 3 + 40);
    mRightLoop->setFixedWidth(event->size().width() / 3 + 40);
}

void LoopDetailView::showEvent(QShowEvent *ev)
{
    if (RunModeManage::GetInstance()->IsModeActive(MODE_RUN_STANDBY) ||
            RunModeManage::GetInstance()->IsModeActive(MODE_RUN_ACGO) ||
            (RunModeManage::GetInstance()->IsModeActive(MODE_RUN_VENT) && VentModeSettingManager::GetInstance()->GetCurMode()==VENTMODE_FLAG_HLM))
    {
        mSaveLoopBtn->setEnabled(false);
        if(!mSwitchRefLoopComboBox->isEnabled())
            mBottomDocker->setEnabled(false);
        StyleSet::SetTextColor(mSaveLoopBtn,COLOR_GRAY);
    }
    else
    {
        mSaveLoopBtn->setEnabled(true);
        mBottomDocker->setEnabled(true);
    }
    FocusWidget::showEvent(ev);
}
