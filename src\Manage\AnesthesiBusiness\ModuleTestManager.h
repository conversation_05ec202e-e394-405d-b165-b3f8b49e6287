﻿#ifndef _MODULETESTMANAGER_H_
#define _MODULETESTMANAGER_H_

#include <QObject>
#include <QDateTime>
#include "String/UIStrings.h"

enum TEST_MODULE_ID
{
    TEST_MODULE_BEGIN = 0,
    TEST_MONITOR = TEST_MODULE_BEGIN,
    TEST_BATTERY,
    TEST_AC,
    TEST_GAS_SOURCE,
    TEST_EXHALATION_VALUE,
    TEST_INHALATION_VALUE,
    TEST_DATABASE,
    TEST_TOUCH_PAD,
    TEST_SYSTEM_TIME,
    TEST_FLOWMETER,
    TEST_AG_CO2,
    TEST_O2,
    TEST_MODULE_MAX
};

enum E_MODULE_STATE
{
    NO_EXIST = 0,
    DISABLE,
    ABLE,
    CHECKING
};

enum AG_MODULE_TYPE
{
    AG = 0,
    CO2 = 75,
    None = -1
};

struct AG_CO2_DisableTextIdSt
{
    ALL_STRINGS_ENUM Ununited;
    ALL_STRINGS_ENUM Adapter;
    ALL_STRINGS_ENUM Warming;
    ALL_STRINGS_ENUM CannotZero;
};

class ModuleTestManager : public QObject
{
    Q_OBJECT
public:
    struct ModuleTestInfoSt
    {
        ALL_STRINGS_ENUM mItemNameId;
        bool mTimeoutCheck;	 //超时检查开启/关闭 0：关闭； 1：开启
        unsigned short mModuleState; //模块状态 able:有效 disable:无效
        unsigned short mInvalidTime; //无效时间 在多少时间内没有状态更新视为无效
        unsigned short need_counts;  //需要检查的次数，如果经过need_count检查次数后依然无效，则状态无效
        unsigned short wait_time;	//剩余的有效时间
    };

public:
    static ModuleTestManager* GetInstance()
    {
        static ModuleTestManager sInstance;
        return &sInstance;
    }

    unsigned short StartModuleTest();
    int GetModuleState(unsigned short moduleId);
    ALL_STRINGS_ENUM GetModuleName(unsigned short moduleId);
    unsigned short UpdateModuleState(unsigned short moduleId, unsigned short state);
    void ModuleStateWatching(void);
    unsigned short set_wait_time(unsigned short moduleId, unsigned short sec);
    QVector<TEST_MODULE_ID> GetTestableItemsId();
    TEST_MODULE_ID GetNextTestableItemid(TEST_MODULE_ID id);
    unsigned int GetTestCount();
    void reset_wait_time(unsigned short moduleId);
    bool IsModuleDisable(TEST_MODULE_ID id);
    bool IsModuleAble(TEST_MODULE_ID id);

    int GetAgModuleType(){return mAgModuleType;}
    ALL_STRINGS_ENUM GetAgDisableStateTextId();
    void SetAgIsHaveAdapter(bool IsHaveAdapter){mAgIsHaveAdapter = IsHaveAdapter;}
    void SetAgIsWarming(bool IsWarming){mAgIsWarming = IsWarming;}
    void SetAgIsCanZero(bool IsCanZero){mAgIsCanZero = IsCanZero;}
    void ResetAgParaValue();
    void CloseAgAllAlarm();

    void RefurbishAgConfig();
    void ChangeAgConfigByType(AG_MODULE_TYPE agType);

signals:
    void SignalModuleStateChanged(unsigned short moduleId, unsigned short state);

protected:
    void notify_module_state();

private:
    ModuleTestManager();
    ModuleTestManager(const ModuleTestManager&);
    ModuleTestManager& operator=(const ModuleTestManager&);
    ~ModuleTestManager() = default;

    void ConfigRefurbish();
    static void register_internal_timer(void* ptmr, void* parg);

private:
    static QMap<int, ModuleTestInfoSt> sModuleTestInfoStVec;
    int mAgModuleType = None;
    bool  mAgIsWarming = false;
    bool  mAgIsHaveAdapter = false;
    bool  mAgIsCanZero = false;
};

#endif
