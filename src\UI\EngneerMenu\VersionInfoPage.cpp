#include <QVBoxLayout>
#include <QHeaderView>

#include "base.h"
#include "VersionInfoPage.h"
#include "SystemSettingManager.h"
#include "String/UIStrings.h"
#include "UpgradeManager/UpgradeManager.h"
#include "SystemConfigManager.h"

#define TABLE_COL 2
#define TABLE_ROW VERSION_TABLE_ITEM_ENUM_END
#define UBOOT_SIGN "BaiGeUbootVersion="
#define UBOOT_SIGN_END   "~#"
#define KERNAL_SIGN_BEGIN "BaigeKernalVersion"
#define KERNAL_SIGN_END "~#"
#define FILESYS_SIGN_BEGIN "#~"
#define FILESYS_SIGN_END   "~#"
#define FILESYS_VER_INDEX 2
#define KERNAL_VER_INDEX 1


struct InfoStruct
{
    ALL_STRINGS_ENUM mNameStrId;
    QString mVersionStr;
};

QMap<VERSION_TABLE_ITEM_ENUM, InfoStruct> versionMap
{
    {MAIN_CONTORL_BOARD,       {STR_MAIN_CONTROL_BOARD,         DEV_SOFT_VERSION}},
    {MONITOR_BOARD,            {STR_MONITOR_BOARD,              "---"}},
    {U_BOOT,                   {STR_U_BOOT,                     "---"}},
    {LINUX_FILE_SYSTEM,        {STR_LINUX_FILE_SYSTEM,          "---"}},
    {LINUX_KERNEL,             {STR_LINUX_KERNEL,               "---"}},
    {POWER_BOARD,              {STR_POWER_BOARD,                "---"}},
    {FLOWMETER_BOARD,          {STR_FLOWMETER_BOARD,            "---"}},
    {FLOWMETER_GAS_CONFIG,     {STR_FLOWMETER_GAS_CONFIG,       "---"}},
    {FLOWMETER_SENSOR_TYPE,    {STR_FLOWMETER_SENSOR_TYPE,      "---"}},
    {DAEMON_VERSION,           {STR_DAEMON_VERSION,             "---"}},
    {AG_MODULE_TYPE,           {STR_AG_MODULE_TYPE,             "---"}},
    {PRODUCT_MODE,             {STR_PRODUCT_MODE,               "---"}},
};

VersionInfoPage::VersionInfoPage(QWidget* parent):FocusWidget(parent)
{
    InitVersionModel();
    InitUi();
}

void VersionInfoPage::InitUiText()
{
    mModeSwitch->SetName(UIStrings::GetStr(ALL_STRINGS_ENUM::STR_RUNMODE_CHAGNE));
    mModeIntraData->SetValueAndStr(
                {
                    {DEMO_MODE, UIStrings::GetStr(ALL_STRINGS_ENUM::STR_SOFT_DEMO_MODE)},
                    {RUN_MODE, UIStrings::GetStr(ALL_STRINGS_ENUM::STR_SOFT_RUN_MODE)}
                });
    mModeSwitch->setDataModel(mModeIntraData);

    mVersionInfoTable->SetTableName(UIStrings::GetStr(ALL_STRINGS_ENUM::STR_SYSTEM_INFO));

    for (int i = 0; i < TABLE_ROW; ++i)
    {
        mVersionInfoModel->item(i, 0)->setText(UIStrings::GetStr(versionMap[(VERSION_TABLE_ITEM_ENUM)i].mNameStrId));
    }
}

void VersionInfoPage::InitUi()
{
    mVersionInfoModel = new QStandardItemModel(this);
    mVersionInfoModel->setRowCount(TABLE_ROW);
    mVersionInfoModel->setColumnCount(TABLE_COL);

    for (int i = 0; i < TABLE_ROW; ++i)
    {
        for (int j = 0; j < TABLE_COL; j++)
        {
            auto newItem = new QStandardItem;
            QFont tempFont;
            tempFont.setPixelSize(15);
            newItem->setFont(tempFont);
            newItem->setFlags(Qt::ItemIsEnabled);
            if (j == 0)
            {
                auto str = UIStrings::GetStr(versionMap[(VERSION_TABLE_ITEM_ENUM)i].mNameStrId);
                newItem->setText(str);
            }
            if (j == 1)
                newItem->setText(versionMap[(VERSION_TABLE_ITEM_ENUM)i].mVersionStr);
            mVersionInfoModel->setItem(i, j, newItem);
        }
        mVersionInfoModel->item(i, 1)->setTextAlignment(Qt::AlignVCenter | Qt::AlignRight);
    }

    mVersionInfoTable = new TableWidget(this);
    mVersionInfoTable->SetTableName(UIStrings::GetStr(ALL_STRINGS_ENUM::STR_SYSTEM_INFO));
    mVersionInfoTable->GetTableView()->setModel(mVersionInfoModel);
    mVersionInfoTable->GetTableView()->verticalHeader()->setSectionResizeMode(QHeaderView::Stretch);
    mVersionInfoTable->GetTableView()->horizontalHeader()->setSectionResizeMode(QHeaderView::Stretch);
    mVersionInfoTable->GetTableView()->setContentsMargins(40, 0, 0, 0);


    mModeSwitch  = new TableButton(this);
    mModeSwitch->SetName(UIStrings::GetStr(ALL_STRINGS_ENUM::STR_RUNMODE_CHAGNE));
    mModeSwitch->SetButtonType(TableButton::COMBOBOX_BUTTON);
    mModeIntraData = new ValueStrIntraData;
    mModeIntraData->SetValueAndStr(
                {
                    {DEMO_MODE, UIStrings::GetStr(ALL_STRINGS_ENUM::STR_SOFT_DEMO_MODE)},
                    {RUN_MODE, UIStrings::GetStr(ALL_STRINGS_ENUM::STR_SOFT_RUN_MODE)}
                });
    mModeSwitch->setDataModel(mModeIntraData);
    mModeSwitch->setValue(SettingManager->GetIntSettingValue(SystemSettingManager::SOFT_MODE_SETTING));

    connect(mModeSwitch, &TableButton::SignalValueChanged, this, [this]{
        SettingManager->SetSettingValue((int)SystemSettingManager::SOFT_MODE_SETTING, mModeSwitch->value());
    });
    connect(SettingManager,&SystemSettingManager::SignalSettingChanged,[=](int settingId){
        if(settingId == SystemSettingManager::SOFT_MODE_SETTING)
        {
            SOFT_MODE newMode = (SOFT_MODE)SettingManager->GetIntSettingValue(SystemSettingManager::SOFT_MODE_SETTING);
            if(mModeSwitch->value() != newMode)
                mModeSwitch->setValue(newMode);
        }
    });


    QVBoxLayout *mainLayout = new QVBoxLayout;
    mainLayout->setContentsMargins(0,0,0,0);
    mainLayout->addWidget(mVersionInfoTable);
    mainLayout->addWidget(mModeSwitch);
    setLayout(mainLayout);

    AddFoucusSonWidget(mModeSwitch);
    CloseAutoInitFocusSonWidget();
}

void VersionInfoPage::InitVersionModel()
{
#ifdef ARM
    QProcess process;
    QObject::connect(&process, &QProcess::errorOccurred, [&process](QProcess::ProcessError error) {
        DebugLog << "获取信息进程错误退出状态: " << error << process.errorString();
    });

    //读自定义的linux内核版本
    QString command = "cat /proc/version";
    process.start(command);
    process.waitForFinished();
    QString result = process.readAll(); 
    {
        QString kernalSignBegin = KERNAL_SIGN_BEGIN;
        QString kernalSignEnd = KERNAL_SIGN_END;
        int begin = result.indexOf(kernalSignBegin);
        int end   = result.indexOf(kernalSignEnd,begin) - 1;
        result = result.mid(begin,end-begin+1);
        QStringList finalStrList=result.split(" ");
        if(finalStrList.size()<KERNAL_VER_INDEX+1)
            versionMap[LINUX_KERNEL].mVersionStr = "---";
        else
            versionMap[LINUX_KERNEL].mVersionStr=finalStrList[KERNAL_VER_INDEX];

        UpgradeManager::GetInstance()->SetMachineVersion(LINUX_KERNEL, versionMap[LINUX_KERNEL].mVersionStr);
    }

    //读uBoot版本
    {
        command="cat /proc/cmdline";
        process.start(command);
        process.waitForFinished();
        result=process.readAll();
        QString tmpUbootVersion = "";

        int begin = result.indexOf(UBOOT_SIGN);
        if (begin != -1)
        {
            begin += strlen(UBOOT_SIGN);
            int end = result.indexOf(UBOOT_SIGN_END, begin);

            if (end != -1)
            {
                tmpUbootVersion = result.mid(begin, end - begin);
            }
        }

        if(tmpUbootVersion.isEmpty())
        {
            command="/home/<USER>/fw_printenv";
            process.start(command);
            process.waitForFinished();
            result=process.readAll();
            QString uBootSign = UBOOT_SIGN;
            int begin = result.indexOf(uBootSign);
            int end   = result.indexOf("\n",begin);
            tmpUbootVersion = result.mid(begin+uBootSign.length(),end-begin-uBootSign.length());
        }

        versionMap[U_BOOT].mVersionStr = tmpUbootVersion;

        UpgradeManager::GetInstance()->SetMachineVersion(U_BOOT, versionMap[U_BOOT].mVersionStr);
    }


    //读文件系统版本
    {
        command="cat /etc/profile";
        process.start(command);
        process.waitForFinished();
        result=process.readAll();
        int begin = result.indexOf(FILESYS_SIGN_BEGIN);
        int end = result.indexOf(FILESYS_SIGN_END,begin) + 1;
        result=result.mid(begin,end-begin+1);
        QStringList finalStrList=result.split(" ");
        if(finalStrList.size()<FILESYS_VER_INDEX+1)
            versionMap[LINUX_FILE_SYSTEM].mVersionStr = "---";
        else
            versionMap[LINUX_FILE_SYSTEM].mVersionStr=finalStrList[FILESYS_VER_INDEX];

        UpgradeManager::GetInstance()->SetMachineVersion(LINUX_FILE_SYSTEM, versionMap[LINUX_FILE_SYSTEM].mVersionStr);
    }


    versionMap[MONITOR_BOARD].mVersionStr = UpgradeManager::GetInstance()->GetMachineVersion(MONITOR_BOARD);
    versionMap[FLOWMETER_BOARD].mVersionStr = UpgradeManager::GetInstance()->GetMachineVersion(FLOWMETER_BOARD);
    versionMap[POWER_BOARD].mVersionStr = UpgradeManager::GetInstance()->GetMachineVersion(POWER_BOARD);
    versionMap[FLOWMETER_GAS_CONFIG].mVersionStr = UpgradeManager::GetInstance()->GetMachineVersion(FLOWMETER_GAS_CONFIG);
    versionMap[FLOWMETER_SENSOR_TYPE].mVersionStr = UpgradeManager::GetInstance()->GetMachineVersion(FLOWMETER_SENSOR_TYPE);
    versionMap[DAEMON_VERSION].mVersionStr = UpgradeManager::GetInstance()->GetMachineVersion(DAEMON_VERSION);
    if(ConfigManager->GetConfig<int>(SystemConfigManager::AG_TYPE_INDEX))
    {
        versionMap[AG_MODULE_TYPE].mVersionStr = UIStrings::GetStr(STR_SIDE_STREAM);
    }
    else
    {
        versionMap[AG_MODULE_TYPE].mVersionStr = UIStrings::GetStr(STR_MAIN_STREAM);
    }
    QString modelName = UIStrings::GetStr((ALL_STRINGS_ENUM)ConfigManager->GetMachineModeNameId(
                                       ConfigManager->GetConfig<int>(SystemConfigManager::MACHINE_MODEL_INDEX)));
    versionMap[PRODUCT_MODE].mVersionStr = modelName;

    connect(UpgradeManager::GetInstance(), &UpgradeManager::SignalSoftwareVersionChanged, this, [this](int type, QString version)
    {
        versionMap[(VERSION_TABLE_ITEM_ENUM)type].mVersionStr = version;
        mVersionInfoModel->item((VERSION_TABLE_ITEM_ENUM)type, 1)->setText(version);
    });
#endif
}
