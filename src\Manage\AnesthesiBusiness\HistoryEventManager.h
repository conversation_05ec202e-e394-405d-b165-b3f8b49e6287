﻿#ifndef HISTORYEVENTMANAGER_H
#define HISTORYEVENTMANAGER_H

#include <QTimer>
#include "TrendChart.h"
#include "ChartManager.h"
#include "AlarmManager.h"

struct HistoryEventSt
{
    int mTimestamp=-1;
    int mEventType;
    int mEventId;
    int mParameter1;
    int mParameter2;
    int mParameter3;
    int mParameter4;
    int mParameter5;
};
Q_DECLARE_METATYPE(HistoryEventSt);

struct OperateLogStrSt
{
    QString mTimeStr;
    QString mEventNameStr;
    QString mEventContentStr;
};

enum OPERATE_EVENT_TYPE
{
    LOG_BEGINE = 0,
    LOG_SYS_START_ID = LOG_BEGINE,
    LOG_RUNMODE_ID,
    LOG_VENTMODE_ID,
    LOG_VENTMODE_PARAM_ID,
    LOG_ALARM_LMT_HIGH_ID,
    LOG_ALARM_LMT_LOW_ID,
    LOG_HLM_ID,
    LOG_CAL_ID,
    LOG_SYS_SELF_TEST_ID,
    LOG_ALARM_LIMIT_RESTORE_DEFAULT,
    LOG_FLOWMETER_CHANGE,
    LOG_UPGRADE,
    LOG_UPDATE_DATE_ID,
    LOG_UPDATE_TIME_ID,
    LOG_SWITCH_PAW_UNIT,
    LOG_SWITCH_CO2_UNIT,
    LOG_VENTILATOR_LIMIT_RESTORE_DEFAULT,
    LOG_ANESTHETIC_GAS_LIMIT_RESTORE_DEFAULT,
    LOG_RESET_FACTORY_SETTINGS,
    LOG_SHUT_DOWN,
    LOG_ONLINE_UPGRADE,
    LOG_END
};

struct EventInfoSt
{
    OPERATE_EVENT_TYPE mId;
    ALL_STRINGS_ENUM mEventNameId;
    std::function<OperateLogStrSt(const HistoryEventSt &)> ConvertFunc;
};

class HistoryEventManager : public QObject
{
    Q_OBJECT
public:
    enum HISTORY_EVENT_TYPE
    {
        ALARM_EVENT,
        OPERATE_EVENT
    };

    static HistoryEventManager * GetInstance()
    {
        static HistoryEventManager sInstance;
        return &sInstance;
    }

    void AddHistoryEvent(HISTORY_EVENT_TYPE type,
                         int eventId,
                         int param1 = INVALID_VALUE,
                         int param2 = INVALID_VALUE,
                         int param3 = INVALID_VALUE,
                         int param4 = INVALID_VALUE,
                         int param5 = INVALID_VALUE);

    const QVector<HistoryEventSt>& GetAllHistoryEvent();
    void ClearAllData();
    QString TranslateOperateLogToStr(const HistoryEventSt& paramSt);
    QVector<HistoryEventSt> GetLogByType(OPERATE_EVENT_TYPE);
    void RegisterOperateLogConvertFunc(OPERATE_EVENT_TYPE id, std::function<QString(HistoryEventSt)>);

signals:
    void SignalEventAdded(HistoryEventSt);

private slots:
    void SlotOnUnitChanged(CONVERTIBLE_UNIT_CATEGORY, UNIT_TYPE oldType, UNIT_TYPE newType);

private:
    HistoryEventManager();
    ~HistoryEventManager() = default;

    void InitConnect();
    void DataRecordLimit();

private:
    QVector<HistoryEventSt> mHistoryEventStList{};
    QHash<OPERATE_EVENT_TYPE, std::function<QString(const HistoryEventSt&)>> mOperateLogConvertFuncMap;
};

#endif // HISTORYEVENTMANAGER_H
