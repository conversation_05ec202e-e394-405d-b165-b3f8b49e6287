﻿#include <QHBoxLayout>
#include <QVBoxLayout>
#include <QtConcurrent>
#include "qwt_scale_widget.h"

#include "TrendChart.h"
#include "UiApi.h"
#include "ParamLabel/ParamLabelBase.h"
#include "qwt_symbol.h"
#include "qwt_plot_layout.h"
#include "SystemTimeManager.h"

#define OTHER_TIMEAXIS_RATE 150
#define OTHER_YSCALE_RATE 15.0
#define LEFT_WIDHT 105
#define CURVE_WIDTH 2
TrendChart::TrendChart(QWidget *parent)
    : BaseQwtChart(parent)
{
    InitUi();
    InitConnect();
}

void TrendChart::AddData(QDateTime time, qreal data)
{
    AddData(time.toMSecsSinceEpoch(),data);
}


void TrendChart::AddData(qint64 time, qreal data)
{
    qreal prevValue= INVALID_VALUE;
    if(!mData.isEmpty())
        prevValue=mData.last();
    DebugAssert(mData.find(time)==mData.end());
    mData.insert(time,data);
    if(data!=INVALID_VALUE)
    {
        if(prevValue!=INVALID_VALUE)
        {
            mCurCurveData.push_back(QPointF(time,data));
        }
        else
        {
            mCurves.push_back(new QwtPlotCurve());
            mCurves.last()->setRenderHint(QwtPlotItem::RenderAntialiased);
            mCurves.last()->setStyle(QwtPlotCurve::Lines);
            mCurves.last()->setPen(*mPen);
            mCurves.last()->setSymbol(GetSymbol());

            mCurCurveData.clear();
            mCurCurveData.push_back(QPointF(time,data));
            mCurves.last()->attach(mPlot);
        }
        mCurves.last()->setSamples(mCurCurveData);
    }
}

void TrendChart::Replace(QMap<qint64,qreal> &&datas)
{
    mData = std::move(datas);
}

void TrendChart::SetCurveColor(const QColor &color)
{
    mPen->setColor(color);
}

void TrendChart::SetScaleColor(const QColor &color)
{
    mScaleColor = color;
    mXAxisDraw->setColor(color);
    mYAxisDraw->setColor(color);
    mPlot->axisWidget(QwtPlot::yLeft)->setPalette(color);
    mPlot->axisWidget(QwtPlot::xBottom)->setPalette(color);
}

void TrendChart::SetAxisColor(const QColor &color)
{
    mPlot->axisWidget(QwtPlot::xBottom)->setPalette(color);
    mPlot->axisWidget(QwtPlot::yLeft)->setPalette(color);
    mGrid->setMajorPen(color,1, Qt::DotLine );
}

void TrendChart::SetDataLabelColor(const QColor &color)
{
    mYNameLabel->SetTextFontColor(color);
    mValueLabel->SetTextFontColor(color);
}

void TrendChart::SetTimeFormat(const QString &format)
{
    mXAxisDraw->setDateFormat(QwtDate::Minute,format);
}

void TrendChart::SetTimeRange(QDateTime timeStart, QDateTime timeEnd)
{
    qint64 paramStartTimeTick = timeStart.toMSecsSinceEpoch();
    qint64 paramEndTimeTick = timeEnd.toMSecsSinceEpoch();

    QDateTime start=timeStart.addMSecs(-((paramEndTimeTick-paramStartTimeTick)/OTHER_TIMEAXIS_RATE));
    QDateTime end=timeEnd.addMSecs((paramEndTimeTick-paramStartTimeTick)/OTHER_TIMEAXIS_RATE);

    if(start!=mStart||end!=mEnd)
    {
        qint64 startTimeTick= start.toMSecsSinceEpoch();
        qint64 endTimeTick = end.toMSecsSinceEpoch();

        mStart = start;
        mEnd = end;

        mPlot->setAxisScale(QwtPlot::xBottom,startTimeTick,endTimeTick);
        QwtScaleDiv scaleDiv((double)startTimeTick, (double)endTimeTick);

        QList<double> scaleList{};
        QList<double> bakScaleList{};
        bakScaleList<<(double)startTimeTick;
        scaleList<<(double)paramStartTimeTick;
        for (int i = 0; i < 5; ++i)
        {
            auto a = timeStart.msecsTo(timeEnd) * i / 5;
            scaleList << (double)paramStartTimeTick + a;
        }
        scaleList << (double)paramEndTimeTick;
        bakScaleList << (double)endTimeTick;
        scaleDiv.setTicks(QwtScaleDiv::MajorTick, scaleList);
        scaleDiv.setTicks(QwtScaleDiv::NoTick, bakScaleList);
        mXAxisDraw->setScaleDiv(scaleDiv);
        mXAxisDraw->invalidateScaleCache();
        mPlot->setAxisScaleDiv(QwtPlot::xBottom, scaleDiv);
        Redraw();
    }

}

bool TrendChart::ContainsInChartPlotArea(QPoint p)
{
    auto mp = mPlot->mapFromGlobal(p);
    auto left = mPlot->canvas()->geometry().left();
    auto right = mPlot->canvas()->geometry().right();
    if (mp.x() >= left && mp.x() <= right)
    {
        return true;
    }
    else
        return false;
}

void TrendChart::SetDataDigit(unsigned short digit)
{
    mDataDigit = digit;
}

void TrendChart::SetScales(qreal min, qreal max, int ticksNum)
{
    mPlot->setAxisScale(QwtPlot::yLeft,min,max);
    mPlot->setAxisMaxMajor(QwtPlot::yLeft,ticksNum);
}

void TrendChart::SetYScale(const ScaleSt &yAxisScale)
{
    qreal start=yAxisScale.mMin-(yAxisScale.mMax-yAxisScale.mMin)/OTHER_YSCALE_RATE;
    qreal end=yAxisScale.mMax+(yAxisScale.mMax-yAxisScale.mMin)/OTHER_YSCALE_RATE;

    if(mYStart!=start||mYEnd!=end)
    {
        mYStart=start;
        mYEnd=end;
        mPlot->setAxisScale(QwtPlot::yLeft,start,end);
        QwtScaleDiv scaleDiv((double)start, (double)end);

        QList<double> scaleList{};
        QList<double> bakScaleList{};


        bakScaleList<<start;
        scaleList<<yAxisScale.mMin;

        for (int i = 0; i < yAxisScale.mDetailScale.size(); ++i)
        {
            scaleList<<yAxisScale.mDetailScale.at(i);
        }

        scaleList<<yAxisScale.mMax;
        bakScaleList <<end;

        scaleDiv.setTicks(QwtScaleDiv::MajorTick, scaleList);
        scaleDiv.setTicks(QwtScaleDiv::NoTick, bakScaleList);
        mYAxisDraw->setScaleDiv(scaleDiv);
        mPlot->setAxisScaleDiv(QwtPlot::yLeft, scaleDiv);
        Redraw();
    }
}

void TrendChart::ClearData()
{
    mData.clear();
}

void TrendChart::SetChosenTimePoint(QDateTime dateTime)
{
    SetChosenTimePoint(dateTime.toMSecsSinceEpoch());
}

void TrendChart::SetChosenTimePoint(qint64 pos)
{
    if(mData.find(pos)!=mData.end())
    {
        if(mData.value(pos)==INVALID_VALUE)
            mValueLabel->setText("---");
        else
            mValueLabel->setText(QString::number(mData.value(pos), 'f', mDataDigit));
    }
}

void TrendChart::SetDateAxisVisible(bool isVisible)
{
    if (isVisible)
        mXAxisDraw->setColor(mScaleColor);
    else
        mXAxisDraw->setColor(Qt::transparent);

    mPlot->update();
}

int TrendChart::ConvertTimePosToGlobalXPos(QDateTime timePos)
{
    return ConvertTimePosToGlobalXPos(timePos.toMSecsSinceEpoch());
}

int TrendChart::ConvertTimePosToGlobalXPos(qint64 timePosTick)
{
    const QwtScaleMap xMap = mPlot->canvasMap(QwtPlot::xBottom);
    double xPixelPos = xMap.transform(timePosTick);
    QPointF tmpP = mPlot->canvas()->mapToGlobal(QPoint(xPixelPos, 0));
    if((tmpP.x()-this->mapToGlobal(QPoint(0,0)).x())>=464) // TBD 推测常量464为Plot中间坐标相对TrendChart左坐标的x偏移值
        tmpP.setX(tmpP.x()+1);
    return tmpP.x();
}

const QMap<qint64, qreal> & TrendChart::GetAllData()
{
    return mData;
}


void TrendChart::InitUi()
{
    mCurveSymbol = new QwtSymbol(QwtSymbol::Rect);
    mCurveSymbol->setPen(Qt::red);
    mCurveSymbol->setBrush(Qt::red);
    mCurveSymbol->setSize(6);

    mPen = new QPen;
    mPen->setWidth(CURVE_WIDTH);

    mXAxisDraw = new CustomDateScaleDraw;
    mYAxisDraw = new CustomScaleDraw;

    (mPlot->setAxisScaleDraw(QwtPlot::xBottom,mXAxisDraw)); //TBD 7ms 2023.10.17
    (mPlot->setAxisScaleDraw(QwtPlot::yLeft,mYAxisDraw)); //TBD 3ms 2023.10.17
    (mYAxisDraw->setMinimumExtent(50));
    (mPlot->plotLayout()->setAlignCanvasToScales(true));
    (SetTimeFormat(SystemTimeManager::TimeFormatStr()));

    QFont axisLabelFont;
    axisLabelFont.setFamily(FONT_FAMILY_NINA);
    axisLabelFont.setPixelSize(FONT_S);
    mPlot->setAxisFont(QwtPlot::xBottom,axisLabelFont);
    mPlot->setAxisFont(QwtPlot::yLeft,axisLabelFont);

    (mGrid =new QwtPlotGrid);
    mGrid->enableX( false );
    mGrid->enableY( true );
    mGrid->enableXMin(false);
    mGrid->enableYMin(false);
    mGrid->setMajorPen(COLOR_DARK_GRAY, 1, Qt::DotLine );
    mGrid->attach(mPlot);
    mValueLabel = new LabelPro("---", this);
    mValueLabel->SetTextFontSize(FONT_L);
    mValueLabel->SetTextFontBold(true);

    QFontMetrics fm(mValueLabel->font());
    mValueLabel->setFixedWidth(fm.tightBoundingRect("XXXXX").width());

    mYUnitLabel->SetTextFontSize(FONT_S);
    mYNameLabel->SetTextFontSize(FONT_L);

    auto leftLayout = new QVBoxLayout;
    leftLayout->setSpacing(0);
    leftLayout->setContentsMargins(0, 0, 0, 0);
    leftLayout->addWidget(mYNameLabel);
    leftLayout->addWidget(mYUnitLabel);
    leftLayout->addWidget(mValueLabel);
    leftLayout->setAlignment(mValueLabel, Qt::AlignRight);
    leftLayout->addStretch();

    mYNameLabel->setAlignment(Qt::AlignRight);
    mYUnitLabel->setAlignment(Qt::AlignRight);
    mValueLabel->setAlignment(Qt::AlignRight);

    auto leftWidget = new QWidget;
    leftWidget->setFixedWidth(LEFT_WIDHT);
    leftWidget->setLayout(leftLayout);


    QHBoxLayout *mainLayout = new QHBoxLayout;
    mainLayout->setContentsMargins(0, 0, 0, 0);
    mainLayout->setSpacing(10);

    mainLayout->addWidget(leftWidget);
    mainLayout->addWidget(mPlot);
    setLayout(mainLayout);
}

void TrendChart::InitConnect()
{
    installEventFilter(this);
    mPlot->installEventFilter(this);
    mPlot->canvas()->installEventFilter(this);
}


void TrendChart::UpdateCurves()
{
    int curveId=0,size=0;
    QVector<QPointF> tempData;
    for(auto i=mData.begin();i!=mData.end();i++)
    {
        if(i.value()!=INVALID_VALUE)
        {
            QPointF tempPoint(i.key(),i.value());
            tempData.push_back(tempPoint);
            size++;
            if(size==(int)(mCurves.value(curveId)->dataSize()))
            {
                mCurves.value(curveId)->setSamples(tempData);
                if(curveId == mCurves.size()-1)
                    mCurCurveData = std::move(tempData);
                tempData.clear();
                curveId++;
                size=0;
            }
            if(curveId==mCurves.size())
                break;
        }
    }
}

void TrendChart::RemoveFrontPoints(QVector<qint64>& Times)
{
    for(auto value:Times)
    {
        mData.remove(value);
    }


    qint64 lastTimeStamp = Times.last();
    int frontIndex = 0;
    for(int i=0;i<mCurves.size();i++)
    {
        QwtSeriesData<QPointF> *tempDatas=mCurves[i]->data();
        DebugAssert(tempDatas->size());
        if(tempDatas->sample(0).x()>lastTimeStamp)
        {
            frontIndex = i;
            break;
        }
        else
        {
            if(tempDatas->sample(tempDatas->size()-1).x()<=lastTimeStamp)
                mCurves[i]->detach();
            else
            {
                QVector<QPointF> tempPoints;
                for(int i=0;i<(int)tempDatas->size();i++)
                {
                    if(tempDatas->sample(i).x()>lastTimeStamp)
                        tempPoints.append(tempDatas->sample(i));
                }
                mCurves[i]->setSamples(tempPoints);
                frontIndex = i;
                break;
            }
        }
    }
    frontIndex++;
    while(--frontIndex)
    {
        QwtPlotCurve* tempCurve = mCurves.front();
        mCurves.removeFirst();
        delete tempCurve;
    }

}

void TrendChart::RedrawXAxisLabels()
{
    mXAxisDraw->invalidateScaleCache();
    Redraw();
}

QPointF TrendChart::mapToValue(const QPointF &srcPoint)
{
    const QwtScaleMap xMap = mPlot->canvasMap(QwtPlot::xBottom);
    const QwtScaleMap yMap = mPlot->canvasMap(QwtPlot::yLeft);

    double xValue = xMap.invTransform(srcPoint.x());
    double yValue = yMap.invTransform(srcPoint.y());
    QPointF mappedPos(xValue,yValue);
    return mappedPos;
}

QPointF TrendChart::mapToPosition(const QPointF &srcPoint)
{
    const QwtScaleMap xMap = mPlot->canvasMap(QwtPlot::xBottom);
    const QwtScaleMap yMap = mPlot->canvasMap(QwtPlot::yLeft);

    double xValue = qRound(xMap.transform(srcPoint.x()));
    double yValue = qRound(yMap.transform(srcPoint.y()));
    QPointF mappedPos(xValue,yValue);
    return mappedPos;
}

QwtSymbol* TrendChart::GetSymbol()
{
    QwtSymbol* tempSymbol = new QwtSymbol(QwtSymbol::Rect);
    tempSymbol->setPen(QPen(mCurveSymbol->pen()));
    tempSymbol->setBrush(QBrush(mCurveSymbol->brush()));
    tempSymbol->setSize(QSize(mCurveSymbol->size()));
    return tempSymbol;
}

void TrendChart::SetSymbolSize(int size)
{
    mCurveSymbol->setSize(size);
    foreach(auto curve,mCurves)
        curve->setSymbol(GetSymbol());
}

bool TrendChart::eventFilter(QObject *obj, QEvent *event)
{
    static QPoint startPoint{};
    switch (static_cast<int>(event->type()))
    {
    case QEvent::MouseButtonPress:
    {
        QMouseEvent *mouseEvent = static_cast<QMouseEvent *>(event);
        startPoint = mouseEvent->globalPos();
        return true;
    }
    case QEvent::MouseButtonRelease:
    {
        QMouseEvent *mouseEvent = static_cast<QMouseEvent *>(event);

        if (mouseEvent->globalPos() == startPoint)
        {
            auto pos = mouseEvent->globalPos();
            auto mp = mPlot->mapFromGlobal(pos);
            mp = mPlot->canvas()->mapFromParent(mp);

            const QwtScaleMap xMap = mPlot->canvasMap(QwtPlot::xBottom);
            const QwtScaleMap yMap = mPlot->canvasMap(QwtPlot::yLeft);
            double xValue = xMap.invTransform(mp.x());
            double yValue = yMap.invTransform(mp.y());
            QPointF mappedPos(xValue,yValue);

            auto timePos = (qint64)(mappedPos.x());
            auto secs = timePos % 60000;
            auto trimedTimePos = qreal(timePos  - secs);

            DebugLog << QDateTime::fromMSecsSinceEpoch(trimedTimePos) << QDateTime::fromMSecsSinceEpoch(timePos) << " mappedpos"
                     << mp << mappedPos;

            QPointF resultPf;
            qint64 timeStamp{};
            if (secs <= 30000)
            {
                resultPf = mapToPosition(QPointF(trimedTimePos, 1));
                timeStamp = trimedTimePos;
            }
            else
            {
                resultPf = mapToPosition({trimedTimePos + 60000, 1});
                timeStamp = trimedTimePos + 60000;
            }
            if (ContainsInChartPlotArea(mouseEvent->globalPos()))
                emit SignalClicked(timeStamp);
        }
        return true;
    }
    default:
        break;
    }
    return BaseQwtChart::eventFilter(obj, event);
}

void CustomDateScaleDraw::drawLabel(QPainter *painter, double val) const
{
    painter->save();
    QPen pen;
    pen.setColor(mColor);
    painter->setPen(pen);
    QwtDateScaleDraw::drawLabel(painter, val);
    painter->restore();
}

QwtText CustomDateScaleDraw::label(double timeStamp) const
{
    auto format = dateFormat(QwtDate::IntervalType::Minute);
    auto showTime = TrendDataManager::GetInstance()->GetShowTime(timeStamp);
    QString dateStr = " ";
    if (showTime != -1)
        dateStr = QDateTime::fromMSecsSinceEpoch(showTime).toString(format);
    return dateStr;
}

void CustomDateScaleDraw::drawBackbone(QPainter *painter) const
{
    painter->save();
    painter->setPen(QColor(203, 203, 203));
    QwtScaleDraw::drawBackbone(painter);
    painter->restore();
}

void CustomDateScaleDraw::setColor(const QColor &newColor)
{
    mColor = newColor;
}

void CustomScaleDraw::drawLabel(QPainter *painter, double val) const
{
    QPen pen;
    pen.setColor(mColor);
    painter->setPen(pen);
    QwtScaleDraw::drawLabel(painter, val);
}

void CustomScaleDraw::drawBackbone(QPainter *painter) const
{
    painter->save();
    painter->setPen(QColor(203, 203, 203));
    QwtScaleDraw::drawBackbone(painter);
    painter->restore();
}

void CustomScaleDraw::setColor(const QColor &newColor)
{
    mColor = newColor;
}
