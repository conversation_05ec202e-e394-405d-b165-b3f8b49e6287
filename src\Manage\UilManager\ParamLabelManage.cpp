﻿#include <mutex>
#include "UiApi.h"
#include "UnitManager.h"
#include "AlarmManager.h"
#include "DataLimitManager.h"
#include "ParamLabelManage.h"
#include "SystemTimeManager.h"
#include "AGModule.h"
#include "ParamLabel/ParamLabelBase.h"
#include "DataManager.h"
#include "IntraData.h"
#include "String/UIStrings.h"
#include "SystemSettingManager.h"

#define QCOLOR_CACUL(qcolorV) BG_RGB_HEX                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         (qcolorV.rgb())

#define HIGHLIGHT_PARAM_BG_COLOR COLOR_T_LIGHT_GRAY

extern double ExcuteCO2MMHGtoPCT(double srcvalue);

QVector<PARAM_DISCRIPTOR> sParamCtrlDesVec
{		/*name_id*/				/*mUnitStrId*/				/*digit*/    /*value_id*/  	/*maxDigitWidth*/	/*name_color*/      /*value_color*/     /*unit_color*/      /*limit_color*/
    //c1
    {	STR_VALUEPARAM_PPEAK,	STR_UNIT_CMH2O,			DIGIT_0,  		VALUEDATA_PPEAK,        3,          RGB_TO_QCOLOR(BG_RGB(255,124,42)),      BG_RGB(255,124,42),     BG_RGB(131,129,131),    BG_RGB(200,80,40)},
    {	STR_VALUEPARAM_PPLAT,	STR_UNIT_CMH2O,			DIGIT_0,  		VALUEDATA_PPLAT,        3,          RGB_TO_QCOLOR(BG_RGB(255,124,42)),      BG_RGB(255,124,42),     BG_RGB(131,129,131),    BG_RGB(200,80,40)},
    {	STR_VALUEPARAM_PEEP,	STR_UNIT_CMH2O,			DIGIT_0,  		VALUEDATA_PEEP,         2,          RGB_TO_QCOLOR(BG_RGB(255,124,42)),      BG_RGB(255,124,42),     BG_RGB(131,129,131),    BG_RGB(200,80,40)},
    {	STR_VALUEPARAM_PMEAN,	STR_UNIT_CMH2O,			DIGIT_0,  		VALUEDATA_PMEAN,        3,          RGB_TO_QCOLOR(BG_RGB(255,124,42)),      BG_RGB(255,124,42),     BG_RGB(131,129,131),    BG_RGB(200,80,40)},
    {	STR_VALUEPARAM_FIO2,	STR_UNIT_PERCENTAGE, 	DIGIT_0,  		VALUEDATA_FIO2,         3,          RGB_TO_QCOLOR(BG_RGB(81, 169, 199)), 	BG_RGB(81, 169, 199),   BG_RGB(131,129,131),    BG_RGB(41, 88, 103)},
    {	STR_VALUEPARAM_R,		STR_UNIT_CMH2OLS, 		DIGIT_0,  		VALUEDATA_R,            3,          RGB_TO_QCOLOR(BG_RGB(0, 128, 128)),      BG_RGB(0, 128, 128),     BG_RGB(131,129,131),    BG_RGB(200,80,40)},
    {	STR_VALUEPARAM_C,		STR_UNIT_MLCMH2O, 		DIGIT_0,  		VALUEDATA_C,            3,          RGB_TO_QCOLOR(BG_RGB(0, 128, 128)),      BG_RGB(0, 128, 128),     BG_RGB(131,129,131),    BG_RGB(200,80,40)},
    {	STR_GRAPHNAME_C20C,		STR_NULL,		  		DIGIT_2,  		VALUEDATA_C20C,         4,          RGB_TO_QCOLOR(BG_RGB(83, 239, 106)),	BG_RGB(83, 239, 106),   BG_RGB(131,129,131),    BG_RGB(83, 239, 106)},
    //c2
    {	STR_VALUEPARAM_VTI,		STR_UNIT_ML,			DIGIT_0,  		VALUEDATA_VTI,          4,          RGB_TO_QCOLOR(BG_RGB(48,152,237)),      BG_RGB(48,152,237),     BG_RGB(131,129,131),    BG_RGB(20, 70, 150)},
    {	STR_VALUEPARAM_VTE,		STR_UNIT_ML,			DIGIT_0,  		VALUEDATA_VTE,          4,          RGB_TO_QCOLOR(BG_RGB(48,152,237)),      BG_RGB(48,152,237),     BG_RGB(131,129,131),    BG_RGB(20, 70, 150)},
    {	STR_VALUEPARAM_MV,		STR_UNIT_LPERMIN,		DIGIT_2,  		VALUEDATA_MV,           5,          RGB_TO_QCOLOR(BG_RGB(48,152,237)),      BG_RGB(48,152,237),     BG_RGB(131,129,131),    BG_RGB(20, 70, 150)},
    {	STR_VALUEPARAM_RATE,	STR_UNIT_BPM,			DIGIT_0,  		VALUEDATA_RATE,         3,          RGB_TO_QCOLOR(BG_RGB(83, 239, 106)), 	BG_RGB(83, 239, 106),   BG_RGB(131,129,131),    BG_RGB(43, 130, 50)},
    {	STR_VALUEPARAM_MVSPN,	STR_UNIT_LPERMIN,		DIGIT_2,  		VALUEDATA_MVSPN,        5,          RGB_TO_QCOLOR(BG_RGB(48,152,237)),      BG_RGB(48,152,237),     BG_RGB(131,129,131),    BG_RGB(20, 70, 150)},
    {	STR_VALUEPARAM_FSPN,	STR_UNIT_BPM,			DIGIT_0,  		VALUEDATA_FSPN,         3,          RGB_TO_QCOLOR(BG_RGB(83, 239, 106)), 	BG_RGB(83, 239, 106),   BG_RGB(131,129,131),    BG_RGB(43, 130, 50)},
    {	STR_VALUEPARAM_IE,		STR_NULL,				DIGIT_0,  		VALUEDATA_IE,           6,          RGB_TO_QCOLOR(BG_RGB(83, 239, 106)), 	BG_RGB(83, 239, 106),   BG_RGB(131,129,131),    BG_RGB(43, 130, 50)},//--tbd
    //c3
    {	STR_VALUEPARAM_FICO2,	STR_UNIT_PERCENTAGE,	DIGIT_1,        VALUEDATA_FICO2,        4,          RGB_TO_QCOLOR(BG_RGB(255, 255, 255)),   BG_RGB(255, 255, 255),  BG_RGB(131,129,131),    BG_RGB(53, 53, 53)},
    {	STR_VALUEPARAM_ETCO2,	STR_UNIT_PERCENTAGE,	DIGIT_1,        VALUEDATA_ETCO2,        4,          RGB_TO_QCOLOR(BG_RGB(255, 255, 255)),	BG_RGB(255, 255, 255),	BG_RGB(131,129,131),    BG_RGB(53, 53, 53)},
    {	STR_NULL,               STR_UNIT_PERCENTAGE,	DIGIT_0,		VALUEDATA_SPO2,         4,          RGB_TO_QCOLOR(BG_RGB(83, 239, 106)),	BG_RGB(83, 239, 106),	BG_RGB(131,129,131),    BG_RGB(83, 239, 106)},
    {	STR_VALUEPARAM_PR,		STR_NULL,				DIGIT_0,    	VALUEDATA_PR,           4,          RGB_TO_QCOLOR(BG_RGB(83, 239, 106)),	BG_RGB(83, 239, 106),	BG_RGB(131,129,131),    BG_RGB(83, 239, 106)},
    {	STR_VALUEPARAM_FIN2O,	STR_UNIT_PERCENTAGE,	DIGIT_0,		VALUEDATA_FIN2O,        3,          RGB_TO_QCOLOR(BG_RGB(142,142,146)),     BG_RGB(142,142,146),    BG_RGB(131,129,131),    BG_RGB(172,174,172)},
    {	STR_VALUEPARAM_ETN2O,	STR_UNIT_PERCENTAGE,	DIGIT_0,		VALUEDATA_ETN2O,        3,          RGB_TO_QCOLOR(BG_RGB(142,142,146)),     BG_RGB(142,142,146),    BG_RGB(131,129,131),    BG_RGB(172,174,172)},
    {	STR_VALUEPARAM_AG_FINULL,STR_UNIT_PERCENTAGE,	DIGIT_1,		VALUEDATA_FI_AA1,       4,          RGB_TO_QCOLOR(BG_RGB(131,129,131)),     BG_RGB(131,129,131),    BG_RGB(131,129,131),    BG_RGB(131,129,131)},
    {	STR_VALUEPARAM_AG_ETNULL,STR_UNIT_PERCENTAGE,	DIGIT_1,		VALUEDATA_ET_AA1,       4,          RGB_TO_QCOLOR(BG_RGB(131,129,131)),     BG_RGB(131,129,131),    BG_RGB(131,129,131),    BG_RGB(131,129,131)},
    {	STR_VALUEPARAM_AG_FINULL,STR_UNIT_PERCENTAGE,	DIGIT_1,		VALUEDATA_FI_AA2,       4,          RGB_TO_QCOLOR(BG_RGB(131,129,131)),     BG_RGB(131,129,131),    BG_RGB(131,129,131),    BG_RGB(131,129,131)},
    {	STR_VALUEPARAM_AG_ETNULL,STR_UNIT_PERCENTAGE,	DIGIT_1,		VALUEDATA_ET_AA2,       4,          RGB_TO_QCOLOR(BG_RGB(131,129,131)),     BG_RGB(131,129,131),    BG_RGB(131,129,131),    BG_RGB(131,129,131)},
    {	STR_VALUEPARAM_FIHAL,	STR_UNIT_PERCENTAGE,	DIGIT_1,		VALUEDATA_FIHAL,        4,          RGB_TO_QCOLOR(BG_RGB(255,0,0)),         BG_RGB(255,0,0),        BG_RGB(131,129,131),    BG_RGB(255,0,0)},
    {	STR_VALUEPARAM_ETHAL,	STR_UNIT_PERCENTAGE,	DIGIT_1,		VALUEDATA_ETHAL,        4,          RGB_TO_QCOLOR(BG_RGB(255,0,0)),         BG_RGB(255,0,0),        BG_RGB(131,129,131),    BG_RGB(255,0,0)},
    {	STR_VALUEPARAM_FIENF,	STR_UNIT_PERCENTAGE,	DIGIT_1,		VALUEDATA_FIENF,        4,          RGB_TO_QCOLOR(BG_RGB(255,121,0)),		BG_RGB(255,121,0),      BG_RGB(131,129,131),    BG_RGB(255,121,0)},
    {	STR_VALUEPARAM_ETENF,	STR_UNIT_PERCENTAGE,	DIGIT_1,		VALUEDATA_ETENF,        4,          RGB_TO_QCOLOR(BG_RGB(255,121,0)),		BG_RGB(255,121,0),      BG_RGB(131,129,131),    BG_RGB(255,121,0)},
    {	STR_VALUEPARAM_FISEV,	STR_UNIT_PERCENTAGE,	DIGIT_1,		VALUEDATA_FISEV,        4,          RGB_TO_QCOLOR(BG_RGB(217,239,0)),		BG_RGB(217,239,0),      BG_RGB(131,129,131),    BG_RGB(217,239,0)},
    {	STR_VALUEPARAM_ETSEV,	STR_UNIT_PERCENTAGE,	DIGIT_1,		VALUEDATA_ETSEV,        4,          RGB_TO_QCOLOR(BG_RGB(217,239,0)),		BG_RGB(217,239,0),      BG_RGB(131,129,131),    BG_RGB(217,239,0)},
    {	STR_VALUEPARAM_FIISO,	STR_UNIT_PERCENTAGE,	DIGIT_1,		VALUEDATA_FIISO,        4,          RGB_TO_QCOLOR(BG_RGB(226,130,210)),     BG_RGB(226,130,210),    BG_RGB(131,129,131),    BG_RGB(226,130,210)},
    {	STR_VALUEPARAM_ETISO,	STR_UNIT_PERCENTAGE,	DIGIT_1,		VALUEDATA_ETISO,        4,          RGB_TO_QCOLOR(BG_RGB(226,130,210)),     BG_RGB(226,130,210),    BG_RGB(131,129,131),    BG_RGB(226,130,210)},
    {	STR_VALUEPARAM_FIDES,	STR_UNIT_PERCENTAGE,	DIGIT_1,		VALUEDATA_FIDES,        4,          RGB_TO_QCOLOR(BG_RGB(0,255,255)),		BG_RGB(0,255,255),      BG_RGB(131,129,131),    BG_RGB(0,255,255)},
    {	STR_VALUEPARAM_ETDES,	STR_UNIT_PERCENTAGE,	DIGIT_1,		VALUEDATA_ETDES,        4,          RGB_TO_QCOLOR(BG_RGB(0,255,255)),		BG_RGB(0,255,255),      BG_RGB(131,129,131),    BG_RGB(0,255,255)},

    {	STR_MAC,				STR_NULL,			DIGIT_2,			VALUEDATA_MAC,          4,          RGB_TO_QCOLOR(BG_RGB(48,152,237)),      BG_RGB(48,152,237),     BG_RGB(131,129,131),    BG_RGB(200,80,40)},

    {	STR_GASNAME_O2,     STR_UNIT_LPERMIN,	DIGIT_1,                VALUEDATA_FLOWMETER_O2,         4,      RGB_TO_QCOLOR(BG_RGB(48,152,237)),      BG_RGB(48,152,237),     BG_RGB(131,129,131),    BG_RGB(20, 70, 150)},
    {	STR_GASNAME_N2O,	STR_UNIT_LPERMIN,	DIGIT_1,                VALUEDATA_FLOWMETER_AIR,        4,      RGB_TO_QCOLOR(BG_RGB(48,152,237)),      BG_RGB(48,152,237),     BG_RGB(131,129,131),    BG_RGB(20, 70, 150)},
    {	STR_GASNAME_CO2,	STR_UNIT_LPERMIN,	DIGIT_1,                VALUEDATA_FLOWMETER_N2O,        4,      RGB_TO_QCOLOR(BG_RGB(48,152,237)),      BG_RGB(48,152,237),     BG_RGB(131,129,131),    BG_RGB(20, 70, 150)},

    {	STR_NULL, STR_NULL, 0,  VALUEDATA_MAX, 0, {}, NULL, NULL, NULL}
};

QHash<ParamLabelBase *, MONITOR_PARAM_TYPE> ParamLabelManage::sAllParamHash{};

ParamLabelManage::ParamLabelManage()
{//tbd
    connect(AGModule::GetInstance(), &AGModule::SignalAgentTypeChanged, this, &ParamLabelManage::RefurbishAgentName);
    connect(UnitManager::GetInstance(), &UnitManager::SignalCategoryUnitChanged, this, &ParamLabelManage::SlotOnUnitChanged);
    connect(UIStrings::GetInstance(), &UIStrings::SignalLanguageChanged, this, [this]{
        foreach (auto pLabel, sAllParamHash.keys())
        {
            auto value_id = (MONITOR_PARAM_TYPE)sAllParamHash[pLabel];
            RefurbishParamByLanguage(value_id, pLabel);
        }
    });

    SetParamAGUnitScheme(UnitManager::GetInstance()->GetCategoryCurUnit(CONVERTIBLE_UNIT_CATEGORY::CO2_CATEGORY));
    OnPressureUnitChanged(UnitManager::GetInstance()->GetCurPressureUnitType());
}

PARAM_DISCRIPTOR ParamLabelManage::GetParamInfoSt(MONITOR_PARAM_TYPE valueId)
{
    for (int i = 0; i < VALUEDATA_MAX; i++)
    {
        if (valueId == sParamCtrlDesVec[i].value_id)
        {
            return sParamCtrlDesVec[i];
        }
        else if (valueId == VALUEDATA_TOP_PAW)
        {
            return sParamCtrlDesVec[0];
        }
    }

    DebugLog << valueId << __FUNCTION__;
    Q_ASSERT_X(0, QString::number(valueId).toStdString().data(), "valueId");
    return {};
}

unsigned int ParamLabelManage::GetParamNameId(MONITOR_PARAM_TYPE valuedata_id)
{
    for (auto &item : sParamCtrlDesVec)
    {
        if (item.value_id == valuedata_id)
            return item.name_id;
    }

    return STR_NULL;
}

int ParamLabelManage::RegisterParamView(MONITOR_PARAM_TYPE value_id, ParamLabelBase *paramLabel)
{
    if (nullptr == paramLabel)
    {
        DebugAssert(0);
        return -1;
    }

    sAllParamHash[paramLabel] = value_id;
    ConfigParam (value_id, paramLabel);
    RefurbishLabelRange(paramLabel);
    RefurbishValue(true);

    connect(paramLabel, &ParamLabelBase::SignalClicked, this, [this]{
        DebugLog  << "paramlabel clicked";
        auto paramLabel =  static_cast<ParamLabelBase *>(sender());
        auto valueId = sAllParamHash[paramLabel];

        short value;
        DataManager::GetInstance()->GetMonitorData(valueId , value);
        VALUE_TYPE flag = DataLimitManager::GetInstance()->ValueType(valueId, value);
        if (flag == VALUE_TYPE::NORMAL_TYPE)
            return;

        switch (valueId) {
        case VALUEDATA_FICO2:
        case VALUEDATA_ETCO2:
        case VALUEDATA_FIN2O:
        case VALUEDATA_ETN2O:
        case VALUEDATA_FI_AA1:
        case VALUEDATA_ET_AA1:
        case VALUEDATA_FI_AA2:
        case VALUEDATA_ET_AA2:
        case VALUEDATA_ETDES:
        case VALUEDATA_ETENF:
        case VALUEDATA_ETISO:
        case VALUEDATA_ETHAL:
        case VALUEDATA_ETSEV:
        case VALUEDATA_FIDES:
        case VALUEDATA_FIENF:
        case VALUEDATA_FIISO:
        case VALUEDATA_FIHAL:
        case VALUEDATA_FISEV:
        case VALUEDATA_MAC:
            AlarmManager::GetInstance()->ShowAlarmMenu(ALARM_LIMITED, AlarmRangePage::GAS_LIMITED);
            break;
        default:
            AlarmManager::GetInstance()->ShowAlarmMenu(ALARM_LIMITED, AlarmRangePage::MACHINE_LIMITED);
            break;
        }
    });

    static std::once_flag flag;
    std::call_once(flag, [](){
        SystemTimeManager::GetInstance()->RegisterTimer(1000, ParamLabelManage::refurbish_all_param_view_value);
    });

    return 0;
}


void ParamLabelManage::RefurbishLabelRange(ParamLabelBase *label)
{
    MONITOR_PARAM_TYPE realdataId = sAllParamHash[label];
    DataLimitManager* p_limit_mag = DataLimitManager::GetInstance();
    int highLimit = p_limit_mag->GetHighLimit((MONITOR_PARAM_TYPE)realdataId);
    unsigned short highLimitDigit = p_limit_mag->GetLimitDigit((MONITOR_PARAM_TYPE)realdataId);
    int lowLimit = p_limit_mag->GetLowLimit((MONITOR_PARAM_TYPE)realdataId);
    unsigned short lowLimitDigit = p_limit_mag->GetLimitDigit((MONITOR_PARAM_TYPE)realdataId);

    QString lowStr, highStr;

    if (INVALID_VALUE == highLimit)
    {//在上限关闭时high == INVALID_VALUE，什么都不显示
        highStr = ("");
    }
    else
    {
        double tempValue = highLimit;
        tempValue /= qPow(10, highLimitDigit);
        highStr = (QString::number(tempValue, 'f', highLimitDigit));
    }

    if (INVALID_VALUE == lowLimit)
    {//在上限关闭时high == INVALID_VALUE，什么都不显示
        lowStr = ("");
    }
    else
    {
        double tempValue = lowLimit;
        tempValue /= qPow(10, lowLimitDigit);
        lowStr = (QString::number(tempValue, 'f', lowLimitDigit));
    }

    label->SetRange(lowStr, highStr);
}

void ParamLabelManage::HighlightParam(ParamLabelBase *label, bool onOff)
{
    if (onOff)
    {
        label->SetLabelBgColor(HIGHLIGHT_PARAM_BG_COLOR);
    }
    else
    {
        label->SetLabelBgColor(Qt::transparent);
    }
}

void ParamLabelManage::RefurbishParamByLanguage(MONITOR_PARAM_TYPE value_id, ParamLabelBase* p_param_label)
{
    if (nullptr == p_param_label)
    {
        DebugAssert(0);
        return;
    }

    PARAM_DISCRIPTOR* p_cur_disc = sParamCtrlDesVec.data();
    while (p_cur_disc && p_cur_disc->value_id != VALUEDATA_MAX)
    {
        if (p_cur_disc->value_id == value_id)
        {
            p_param_label->SetName(UIStrings::GetStr(p_cur_disc->name_id));
            if (value_id == VALUEDATA_FICO2 || value_id == VALUEDATA_ETCO2)
            {
                auto agUnit = SettingManager->GetIntSettingValue(SystemSettingManager::AGUNIT_SETTING);
                auto agUnitStrId = UnitManager::GetStrId((UNIT_TYPE)agUnit);
                p_param_label->SetUnit(UIStrings::GetStr(agUnitStrId));
            }
            else
            {
                p_param_label->SetUnit(UIStrings::GetStr(p_cur_disc->mUnitStrId));
            }
            return;
        }
        p_cur_disc ++;
    }
}

void ParamLabelManage::SlotOnUnitChanged(CONVERTIBLE_UNIT_CATEGORY category, UNIT_TYPE oldType, UNIT_TYPE newType)
{
    if (category == CONVERTIBLE_UNIT_CATEGORY::PRESSURE_CATEGORY)
    {
        OnPressureUnitChanged(newType);
    }
    else
    {
        SetParamAGUnitScheme(newType);
    }
}

//设置参数控件各个属性
void ParamLabelManage::ConfigParam (MONITOR_PARAM_TYPE value_id, ParamLabelBase* p_param_label)
{
    if (nullptr == p_param_label)
    {
        DebugAssert(0);
        return;
    }

    PARAM_DISCRIPTOR* p_cur_disc = sParamCtrlDesVec.data();
    while (p_cur_disc && p_cur_disc->value_id != VALUEDATA_MAX)
    {
        if (p_cur_disc->value_id == value_id)
        {
            p_param_label->SetName(UIStrings::GetStr(p_cur_disc->name_id));
            if (value_id == VALUEDATA_FICO2 || value_id == VALUEDATA_ETCO2)
            {
                auto agUnit = SettingManager->GetIntSettingValue(SystemSettingManager::AGUNIT_SETTING);
                auto agUnitStrId = UnitManager::GetStrId((UNIT_TYPE)agUnit);
                p_param_label->SetUnit(UIStrings::GetStr(agUnitStrId));
            }
            else
            {
                p_param_label->SetUnit(UIStrings::GetStr(p_cur_disc->mUnitStrId));
            }

            p_param_label->SetNameColor(p_cur_disc->mNameColor);
            p_param_label->SetValueColor(RGB_TO_QCOLOR(p_cur_disc->value_color));
            p_param_label->SetUnitColor(RGB_TO_QCOLOR(p_cur_disc->unit_color));
            p_param_label->SetScaleColor(RGB_TO_QCOLOR(p_cur_disc->limit_color));
            return;
        }
        p_cur_disc ++;
    }
}

//通过名称获取参数类型
MONITOR_PARAM_TYPE ParamLabelManage::match_strid_to_value(unsigned int str_id)
{
    PARAM_DISCRIPTOR* p_cur_disc = sParamCtrlDesVec.data();
    while (p_cur_disc && p_cur_disc->value_id != VALUEDATA_MAX)
    {
        if (p_cur_disc->name_id == str_id)
        {
            return p_cur_disc->value_id;
        }
        else
        {
            p_cur_disc++;
        }
    }
    return VALUEDATA_MAX;
}

void ParamLabelManage::OnPressureUnitChanged(unsigned short unitType)
{
    bool changed = false;
    for (PARAM_DISCRIPTOR &item : sParamCtrlDesVec)
    {
        if (UnitManager::GetInstance()->IsPressureUnitStr(item.mUnitStrId))
        {
            UnitInfoSt unitInfo = UnitManager::GetInstance()->GetCurPressureUnitInfo();
            item.mUnitStrId = (ALL_STRINGS_ENUM)unitInfo.mUnitStrId;
            item.digit = unitInfo.digit;

            changed = true;
        }
    }

    foreach (auto pLabel, sAllParamHash.keys())
    {
        auto value_id = (MONITOR_PARAM_TYPE)sAllParamHash[pLabel];
        short value = 0;
        DataManager::GetInstance()->GetMonitorData(value_id , value);
        RefurbishLabelValue(pLabel, value);
    }

    if (changed)
    {//改变压力单位
        RefurbishUnit(true);
    }
}

void ParamLabelManage::RefurbishUnit(bool isOnlyPress)
{
    UnitManager *p_unit_manage = UnitManager::GetInstance();
    foreach (auto pLabel, sAllParamHash.keys())
    {
        auto pd = GetParamInfoSt(sAllParamHash[pLabel]);
        if (!isOnlyPress||p_unit_manage->IsPressureUnitStr(pd.mUnitStrId))
        {
            unsigned short realdataId = sAllParamHash[pLabel];
            for (int j = 0; j <= VALUEDATA_MAC; ++j)
            {//在描述表中更新压力单位
                if (realdataId == sParamCtrlDesVec[j].value_id)
                {
                    QString unitStr = UIStrings::GetStr(sParamCtrlDesVec[j].mUnitStrId);
                    pLabel->SetUnit(unitStr);
                    RefurbishLabelRange(pLabel);
                }
            }
        }
    }
}


//定时触发的刷新函数
void ParamLabelManage::refurbish_all_param_view_value(void* ptmr, void* parg)
{
    Q_UNUSED(ptmr);
    Q_UNUSED(parg);

    static int s_flash_beat;//闪烁节奏
    RefurbishValue(s_flash_beat);
    s_flash_beat = 1 - s_flash_beat;
}

MONITOR_PARAM_TYPE ParamLabelManage::GetRealIdByLabelPtr(ParamLabelBase *pLabel)
{
    if(sAllParamHash.constFind(pLabel) != sAllParamHash.constEnd())
        return sAllParamHash[pLabel];
    return VALUEDATA_MAX;
}


void ParamLabelManage::RefurbishLabelValue(ParamLabelBase *pLabel, short value)
{
    if (VALUEDATA_IE == sAllParamHash[pLabel])
    {
        pLabel->SetValue(DataManager::ConvertIeValueToStr(value));
    }
    else
    {
        //一般显示处理
        if (INVALID_VALUE == value)
        {
            pLabel->SetValue(UIStrings::GetStr(ALL_STRINGS_ENUM::STR_DASH));
        }
        else
        {

            MONITOR_PARAM_TYPE paramType = sAllParamHash[pLabel];
            double tempValue = DataManager::RevertToFloat(paramType, value);
            auto category = DataManager::GetParamUnitCategory(paramType);
            if (category != CONVERTIBLE_UNIT_CATEGORY::NONE_CATEGORY)
            {
                tempValue = UnitManager::GetInstance()->ConvertValue(category,
                                                                     DataManager::GetDefaultUnit(paramType),
                                                                     UnitManager::GetInstance()->GetCategoryCurUnit(category),
                                                                     tempValue);
            }
            auto pd = GetParamInfoSt(paramType);
            //tempValue = (int)tempValue; TBD SVN 2531 ??
            pLabel->SetValue(QString::number(tempValue, 'f', pd.digit));
        }
    }
}

//刷新各个参数，值过期背景显示黑色，超出设置范围背景为红色或黄色
void ParamLabelManage::RefurbishValue(int light_or_dark)
{
    QMap<E_ALARMPRIO, QPair<QColor, QColor>> highlightSchemes
    {
        {E_ALARMPRIO::ALARMPRIO_HIG, {COLOR_WHITE, COLOR_RED}},
        {E_ALARMPRIO::ALARMPRIO_MID, {COLOR_BLACK, COLOR_LIGHT_YELLOW}}
    };

    short value = 0;
    MONITOR_PARAM_TYPE value_id;
    unsigned int out_range_type = 0;
    QColor flash_color{Qt::transparent};

    DataLimitManager* limitManager = DataLimitManager::GetInstance();

    QList<ParamLabelBase*>  allP=sAllParamHash.keys();

    for(auto ite=sAllParamHash.begin();ite!=sAllParamHash.end();ite++)
    {
        auto pLabel=ite.key();
        value_id =  ite.value();
        DataManager::GetInstance()->GetMonitorData(value_id , value);

        RefurbishLabelValue(pLabel, value);

        flash_color = Qt::transparent;
        if (value != INVALID_VALUE)
        {
            out_range_type = DataLimitManager::GetInstance()->ValueType(value_id, value);
            if (out_range_type)
            {//发生报警，设置报警闪烁颜色
                auto alarmLevel = limitManager->GetAlarmLevel(value_id, out_range_type);
                switch(alarmLevel)
                {
                case ALARMPRIO_HIG:
                    flash_color = QColor(highlightSchemes[ALARMPRIO_HIG].second);
                    break;
                case ALARMPRIO_DISABLE:
                    pLabel->SetScaleColor(RGB_TO_QCOLOR(GetParamInfoSt(value_id).limit_color));
                    break;
                default:
                    flash_color = QColor(highlightSchemes[ALARMPRIO_MID].second);
                    break;
                }

                if (alarmLevel != ALARMPRIO_DISABLE)
                {
                    if (out_range_type == INVALID_HIG_TYPE)
                    {
                        pLabel->SetHighLimitColor(GetParamInfoSt(value_id).mNameColor);
                        pLabel->SetLowLimitColor(RGB_TO_QCOLOR(GetParamInfoSt(value_id).limit_color));
                    }
                    else if (out_range_type == INVALID_LOW_TYPE)
                    {
                        pLabel->SetLowLimitColor(GetParamInfoSt(value_id).mNameColor);
                        pLabel->SetHighLimitColor(RGB_TO_QCOLOR(GetParamInfoSt(value_id).limit_color));
                    }
                }
            }
            else
            {
                pLabel->SetScaleColor(RGB_TO_QCOLOR(GetParamInfoSt(value_id).limit_color));
            }
        }
        else
        {//未发生报警
            pLabel->SetScaleColor(RGB_TO_QCOLOR(GetParamInfoSt(value_id).limit_color));
        }

        pLabel->SetValueBackgroundBlinkColor(flash_color);
        pLabel->SetIsOpenValueBackgroundBlink(light_or_dark);
    }

}

//刷新wParam所指定参数的限值
void ParamLabelManage::refurbish_alarm_limit(unsigned int wParam)
{
    auto it = sAllParamHash.begin();
    while (it != sAllParamHash.end())
    {
        if (it.key() && wParam == it.value())
        {
            RefurbishLabelRange(it.key());
        }
        ++it;
    }
}

//刷新所有参数控件的限值
void ParamLabelManage::refurbish_all_alarm_limit(unsigned int wParam, unsigned int lParam)
{
    Q_UNUSED(wParam);
    Q_UNUSED(lParam);

    auto it = sAllParamHash.begin();
    while (it != sAllParamHash.end())
    {
        if (it.key())
        {
            RefurbishLabelRange(it.key());
        }
        ++it;
    }
}

//void ParamLabelManage::refurbish_param_ctrl_limit(ParamLabel *ctrl, unsigned int param_id)
//{
//        unsigned short data_type = (MONITOR_PARAM_TYPE)wParam;
//        short low_limit = DataLimitManager::GetInstance()->GetLowLimit(data_type);
//        short base_low_limit = DataLimitManager::GetInstance()->GetLowLowest(data_type);
//        unsigned short min_off = DataLimitManager::GetInstance()->GetMinOffFlag(data_type);
//        if (low_limit == base_low_limit && min_off)
//        {
//            ctrl->SetLowLimit();
//        }
//        ctrl->SetLowLimit(DataLimitManager::GetInstance()->GetLowLimit((MONITOR_PARAM_TYPE)wParam),
//            DataLimitManager::GetInstance()->GetLimitDigit((MONITOR_PARAM_TYPE)wParam));

//        ctrl->SetHighLimit(DataLimitManager::GetInstance()->GetHighLimit((MONITOR_PARAM_TYPE)wParam),
//            DataLimitManager::GetInstance()->GetLimitDigit((MONITOR_PARAM_TYPE)wParam));
//}

//
void ParamLabelManage::RefurbishAgentName(unsigned int Ax1Type)
{
    auto idPair = AGModule::GetInstance()->AgTypeToParamType(Ax1Type);

    MONITOR_PARAM_TYPE valuedata_fi_id = idPair.first;
    MONITOR_PARAM_TYPE valuedata_et_id = idPair.second;

    auto keys = sAllParamHash.keys();
    foreach (auto &&pLabel, keys)
    {
        short data_id = sAllParamHash[pLabel];

        //fi
        if (VALUEDATA_FI_AA1 <= data_id &&
                data_id <= VALUEDATA_FIDES &&
                valuedata_fi_id != data_id)
        {
            if (!sAllParamHash.contains(pLabel)) sAllParamHash[pLabel] = valuedata_fi_id;
            ConfigParam (valuedata_fi_id, pLabel);
        }

        //et
        if (VALUEDATA_ET_AA1 <= data_id &&
                data_id <= VALUEDATA_ETDES  &&
                valuedata_et_id != data_id)
        {
            if (!sAllParamHash.contains(pLabel)) sAllParamHash[pLabel] = valuedata_fi_id;
            ConfigParam (valuedata_et_id, pLabel);
        }
    }
}

void ParamLabelManage::SetParamAGUnitScheme(unsigned short unitType)
{
    for (auto &item : sParamCtrlDesVec)
    {
        if (VALUEDATA_MAX == item.value_id)
            break;
        if(item.value_id == VALUEDATA_FICO2 || item.value_id == VALUEDATA_ETCO2)
        {
            if(unitType == U_PERCENT)
            {
                item.mUnitStrId = STR_UNIT_PERCENTAGE;
                item.digit = DIGIT_1;
            }
            else if(unitType == U_MMHG)
            {
                item.mUnitStrId = STR_UNIT_MMHG;
                item.digit = DIGIT_0;
            }
        }
    }

    foreach (auto pLabel, sAllParamHash.keys())
    {
        auto value_id = (MONITOR_PARAM_TYPE)sAllParamHash[pLabel];
        short value = 0;
        DataManager::GetInstance()->GetMonitorData(value_id , value);
        RefurbishLabelValue(pLabel, value);
    }

    RefurbishUnit(false);
}


