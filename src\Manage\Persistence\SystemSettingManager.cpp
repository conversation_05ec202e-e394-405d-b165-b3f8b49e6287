﻿#include "DebugLogManager.h"


#include "UnitManager.h"
#include "VentModeSettingManager.h"
#include "SystemSettingManager.h"
#include "ChartManager.h"
#include "WaveChart.h"
#include "UiApi.h"
#include "UiConfig.h"
#include "DataManager.h"
#include "SystemTimeManager.h"
#include "ModuleTestManager.h"
#include "SettingSpinboxManager.h"
#include "TrendDataManager.h"
#include "MainDisplayWidget.h"
#include "String/UIStrings.h"
#include "DataLimitManager.h"
#include "HistoryEventManager.h"
#include "base.h"

#define UBOOT_SIGN "BaiGeUbootVersion="
#define KERNAL_SIGN_BEGIN "BaigeKernalVersion"
#define KERNAL_SIGN_END "~#"
#define FILESYS_SIGN_BEGIN "#~"
#define FILESYS_SIGN_END   "~#"
#define FILESYS_VER_INDEX 2
#define KERNAL_VER_INDEX 1

QVector<SystemSettingItemSt> SystemSettingManager::sSettingDefault
{
    /* mSettingId  */                     /* context  */                                   /*.mValueType*/
    {LANGUAGE_SETTING,                       DEF_LANG                                                     },
    {SOFT_MODE_SETTING,                     RUN_MODE                                                     },
    {VOLUME_SETTING,                            3                                                                             },
    {PUNIT_SETTING,                               U_CMH2O                                                             },
    {CO2UNIT_SETTING,                               U_PERCENT                                                            },
    {CPB_SETTING,                                    STR_OFF                                                          },
    {TIMEFORMAT_SETTING,                   TIMEMODE_24_H                              },
    {DATEFORMAT_SETTING,                  DATE_FORMAT_YMD                         },
    {SYSTEM_TIME_Y ,                            0                              },
    {SYSTEM_TIME_M,                            0                                        },
    {SYSTEM_TIME_D,                             0                                      },
    {TRIG_SETTING,                                TRIG_FLAG_FTRIG                       },
    {SERIALPROTOCAL_SETTING,                                NONE_PROTOCAL                       },
    {BAUDRATE_SETTING,                                RATE57600},
    {DATABIT_SETTING,                                 BIT8},
    {STOPBIT_SETTING,                                 BIT1},
    {PARITY_SETTING,                                                NONE_PARITY},
    {IPADDRESS_SETTING,                      "************"},
    {MASK_SETTING,                                    "*************"},
    {GATEWAY_SETTING,                         "***********"},
    {NETPROTOCAL_SETTING,                     NONE_PROTOCAL},
    {SENDINTERVAL_SETTING,                    1000},
    {DESTINATIONIP_SETTING,                  "***********"},
    {DESTINATIONPORT_SETTING,               6666},
    {LOOP_POSITION_LEFT,                                  LOOP_PV                                    },
    {LOOP_POSITION_RIGHT,                     LOOP_VF                                     },
    {TREND_GRAPH1,                                    TREND_PPEAK                          },
    {TREND_GRAPH2,                                  TREND_PPLAT                             },
    {TREND_GRAPH3,                                  TREND_PEEP                             },
    {TREND_PERIOD,                                   360                                         },
    {VALUE_UPAREA_1,                                VALUEDATA_PPEAK,                  },
    {VALUE_UPAREA_2,                                VALUEDATA_PPLAT,                   },
    {VALUE_UPAREA_3,                                VALUEDATA_PEEP,                   },
    {VALUE_MIDAREA_1,                               VALUEDATA_VTE,                     },
    {VALUE_MIDAREA_2,                               VALUEDATA_VTI,                        },
    {VALUE_MIDAREA_3,                               VALUEDATA_MV,                    },
    {VALUE_DOWNAREA_1,                              VALUEDATA_ETCO2,                       },
    {VALUE_DOWNAREA_2,                              VALUEDATA_FICO2,                      },
    {VALUE_DOWNAREA_3,                              VALUEDATA_RATE,                                 },
    {MAIN_WAVE_1,                                        DataManager::WAVE_PAW,   },
    {MAIN_WAVE_2,                                        DataManager::WAVE_FLOW,    },
    {MAIN_WAVE_3,                                        DataManager::WAVE_VOL,    },
    {MAIN_WAVE_4,                                        DataManager::WAVE_CO2,    },
    {WAVE_SPEED,                                                    	ChartManager::SPEED_LEVEL_LOW,},
    {WAVE_DRAWMODE,                                           SCAN_MODE,                                               },
    {ASPHYXIA_TIME ,                                                	20,                                                        },
    {N2O_CONSISTENCY,                                         0,                                                                   },
    {O2_CONSISTENCY,                                                      0,                                                       },
    {AGUNIT_SETTING,                                                        U_MMHG,},
    {LCD_DEVICE_NAME,                                                          25,                                                    },
    {FLOW_CONTROL_TYPE,                                                       0.0,                                                      },
    {FLOW_CONTROL_TOTAL_VALUE,                                    0.0,                                                                  },
    {FLOW_CONTROL_O2_VALUE,                                        100,                                     },
    {FLOW_CONTROL_BANLANCE_TYPE,                              0,                                     },
    {FLOW_CONTROL_BANLANCE_GAS_VALUE,                    0,                                    },
    {LAST_CHK_TIME,                                                             -1,                                               },
    {LAST_CAL_TIME,                                                         -1,                                               },
    {LAST_SYS_TIME,                                                         -1,                                               },
    {LIMITLINE_SETTING,     SHOW},
    {MASTER_SOFT_VERSION,                                           "V02.01.43"                                               },
    {LCD_BRIGHTNESS_SETTING,                                5},
    {USER_PASSWORD, ""},
    {LAST_MAIN_DISPLAY_LAYOUT, MainDisplayWidget::THREE_WAVE},
    {LOOP_LOOPDETAIL_POSITION_LEFT,LOOP_PV},
    {LOOP_LOOPDETAIL_POSITION_RIGHT,LOOP_VF},
    {LOOP_AG,LOOP_VF},
    {SEND_SERIAL_INTERVAL_SETTING, 1000}
};


SystemSettingManager::SystemSettingManager()
{

}

void SystemSettingManager::RestoreFactorySetting()
{
    DataLimitManager::GetInstance()->SetAllLimitDefault();
    SettingSpinboxManager::GetInstance()->InitAllRegisteredSpn();    
    for (auto &&settingItem : sSettingDefault)
    {
        if(settingItem.mSettingId == LANGUAGE_SETTING)
        {
            continue;
        }
        switch (settingItem.mValue.type())
        {
        case QVariant::Int:
            SetSettingValue((SystemSettingManager::SYSTEM_SETTING_ENUM)settingItem.mSettingId, settingItem.mValue.toInt());
            break;
        case QVariant::Double:
            SetSettingValue((SystemSettingManager::SYSTEM_SETTING_ENUM)settingItem.mSettingId, settingItem.mValue.toDouble());
            break;
        case QVariant::String:
            SetSettingValue((SystemSettingManager::SYSTEM_SETTING_ENUM)settingItem.mSettingId, settingItem.mValue.toString());
            break;
        default:
            break;
        }
    }
    HistoryEventManager::GetInstance()->ClearAllData();
    HistoryEventManager::GetInstance()->AddHistoryEvent(HistoryEventManager::OPERATE_EVENT,LOG_RESET_FACTORY_SETTINGS);
}

QVariant SystemSettingManager::GetDefaultValue(int settingId)
{
    return sSettingDefault[settingId].mValue;
}


void SystemSettingManager::Init()
{
    for (int i = 0; i < SYSTEM_SETTING_ENUM_END; i++)
    {
        mCurSettingMap.insert(i, sSettingDefault[(SYSTEM_SETTING_ENUM)i]);
    }
    auto settings = SystemSettingInterface::GetAllSetting();
    if(settings.size() < sSettingDefault.size())
    {
        for(int i = settings.size(); i < sSettingDefault.size(); ++i)
        {
            SystemSettingInterface::InsertSetting(sSettingDefault[i].mSettingId,sSettingDefault[i].mValue);
        }
    }
    for (auto &setting : settings)
    {
        mCurSettingMap[setting.mSettingId].mValue = setting.mValue;
    }
}


int SystemSettingManager::GetIntSettingValue(int settingId)
{
#ifdef WIN32
    if(settingId==SOFT_MODE_SETTING)
        return DEMO_MODE;
#endif
    DebugAssert(mCurSettingMap.contains(settingId));
    return mCurSettingMap[settingId].mValue.value<int>();
}

double SystemSettingManager::GetFloatSettingValue(int settingId)
{
    DebugAssert(mCurSettingMap.contains(settingId));
    return mCurSettingMap[settingId].mValue.toDouble();
}

QString SystemSettingManager::GetStringSettingValue(int settingId)
{
    DebugAssert(mCurSettingMap.contains(settingId));
    return mCurSettingMap[settingId].mValue.value<QString>();
}

void SystemSettingManager::SetSettingValue(int settingId, int context)
{
    DebugAssert(mCurSettingMap.contains(settingId));
    {
        if(mCurSettingMap[settingId].mValue!=context)
        {
            mCurSettingMap[settingId].mValue = context;
            SystemSettingInterface::SetSystemSetting(settingId, context);
            emit SignalSettingChanged(settingId);
        }
    }
}

void SystemSettingManager::SetSettingValue(int settingId, double context)
{
    DebugAssert(mCurSettingMap.contains(settingId));
    if (mCurSettingMap[settingId].mValue != context)
    {
        mCurSettingMap[settingId].mValue = context;
        SystemSettingInterface::SetSystemSetting(settingId, context);

        emit SignalSettingChanged(settingId);
    }
}

void SystemSettingManager::SetSettingValue(int settingId, QString context)
{
    DebugAssert(mCurSettingMap.contains(settingId));
    if (mCurSettingMap[settingId].mValue != context)
    {
        mCurSettingMap[settingId].mValue = context;
        SystemSettingInterface::SetSystemSetting(settingId, context);

        emit SignalSettingChanged(settingId);
    }
}

SystemSettingManager *SystemSettingManager::GetInstance()
{
    static SystemSettingManager sSystemSettingManager;
    return &sSystemSettingManager;
}

