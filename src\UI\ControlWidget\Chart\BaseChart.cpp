﻿#include <qwt_plot_canvas.h>
#include <qwt_legend.h>
#include <qwt_plot.h>
#include <qwt_plot_curve.h>
#include <qwt_painter.h>
#include <qwt_scale_draw.h>
#include <qwt_scale_widget.h>
#include <qwt_plot_marker.h>
#include <qwt_plot_grid.h>
#include <QTime>
#include "BaseChart.h"
#include "ParamLabel/ParamLabelBase.h"
#include "UiApi.h"

//#define OPEN_FPS_CACL

#ifdef OPEN_FPS_CACL
QString StaticFpsLog;
#endif
BaseQChart::BaseQChart(QWidget *parent) : BaseChart(parent)
{
    //    setStyle(new BorderProxyStyle);



    mChart = new MyChart;
    mChart->setMargins(QMargins{0, 0, 0, 0});
    mChart->layout()->setContentsMargins(0, 0, 0, 0);
    mChart->setContentsMargins(0, 0, 0, 0);
    mChart->setBackgroundRoundness(0);
    mChart->legend()->hide();  //隐藏图例
    mChart->legend()->detachFromChart();
    mChart->setBackgroundBrush(QBrush(COLOR_BLACK));

    mChartView = new MyChartView(this);
    mChart->setParent(mChartView);
    mChartView->setChart(mChart);
    mChartView->setContentsMargins(0, 0, 0, 0);
    mChartView->setFrameStyle(QFrame::NoFrame);
   // mChartView->setRenderHint(QPainter::Antialiasing, true); TBD 抗锯齿关闭有其他影响吗
    mChartView->setStyleSheet("background-color: rgb(14, 14, 14)/*黑*/");//TBD
    mChartView->setOptimizationFlag(QGraphicsView::DontAdjustForAntialiasing, true);
    mChartView->setAttribute(Qt::WA_TransparentForMouseEvents, true);

    setStyleSheet("background-color: rgb(14, 14, 14)/*黑*/");//TBD

}

BaseQChart::~BaseQChart() {}


void BaseQChart::Redraw()
{
    mChartView->update();
}

void BaseQChart::SetXScale(const ScaleSt &)
{}

void BaseQChart::SetYScale(const ScaleSt &)
{}



MyChartView::MyChartView(QWidget *parent):QChartView(parent)
{

}

void MyChartView::paintEvent(QPaintEvent *ev)
{
    if(ev->rect().width()==rect().width())
    {
        emit SignalNeedUpFull();
    }
    QChartView::paintEvent(ev);
    return ;
}

MyChart::MyChart(QGraphicsItem *parent, Qt::WindowFlags wFlags):QChart(parent,wFlags)
{

}

void MyChart::RequestUpdate(QRect upRect)
{
    if(!upRect.isValid())
        upRect=plotArea().toRect();
    if(mParetnView)
        mParetnView->update(upRect);
}

void MyChart::setParent(MyChartView *parent)
{
    mParetnView=parent;
    QChart::setParent(parent);
}

MyChart::~MyChart()
{

}
QObject* fristPlot =NULL;
CustomAxisPlot::CustomAxisPlot(QWidget *parent,bool isOpenCustom) : QwtPlot(parent),mIsOpenCustom(isOpenCustom)
{
    if(!fristPlot)
        fristPlot =this;
    if(mIsOpenCustom)
    {
        axisWidget(QwtPlot::xBottom)->setVisible(false);
        axisWidget(QwtPlot::yLeft)->setVisible(false);

        enableAxis(QwtPlot::xBottom, false);
        enableAxis(QwtPlot::yLeft, false);
    }
}

void CustomAxisPlot::setIsOpenCustomAxis(bool newIsOpenCustom)
{
    mIsOpenCustom = newIsOpenCustom;
    if(mIsOpenCustom)
    {
        axisWidget(QwtPlot::xBottom)->setVisible(false);
        axisWidget(QwtPlot::yLeft)->setVisible(false);

        enableAxis(QwtPlot::xBottom, false);
        enableAxis(QwtPlot::yLeft, false);
    }
}

void CustomAxisPlot::SetXAxisVisbale(bool isVisable)
{
    mXCaleVisable = isVisable;
    axisWidget(QwtPlot::xBottom)->setVisible(false);
    enableAxis(QwtPlot::xBottom, false);
}

void CustomAxisPlot::SetYAxisVisbale(bool isVisable)
{
    axisWidget(QwtPlot::yLeft)->setVisible(false);
    enableAxis(QwtPlot::yLeft, false);
    mYCaleVisable = isVisable;
}

void CustomAxisPlot::AddBlackDrawRect(const QRect &rect)
{
    mBlackRect =rect;
}

void CustomAxisPlot::drawCanvas(QPainter *painter) {
    QTime time = QTime::currentTime();
    if(mIsOpenCustom)
    {
        painter->save();
        if(painter->hasClipping())
            painter->setClipping(false);
        if(mXCaleVisable)
            drawCustomXAxis(painter);
        if(mYCaleVisable)
            drawCustomYAxis(painter);
        painter->restore();
    }
    QwtPlot::drawCanvas(painter);
    if(mBlackRect.isValid())
        painter->fillRect(mBlackRect,COLOR_BLACK);

#ifdef OPEN_FPS_CACL
    static int counter;
    static QTime timeStamp;
    static QObject* first =this;
    if(this==first)
    {
        //DebugLog<<"qwt paint time "<<time.msecsTo(QTime::currentTime());
        time = QTime::currentTime();
        if ( !timeStamp.isValid() )
        {
            timeStamp.start();
            counter = 0;
        }
        else
        {
            counter++;

            const double elapsed = timeStamp.elapsed() / 1000.0;
            if ( elapsed >= 1 )
            {
                QString fps;
                fps.setNum( qRound( counter / elapsed ) );
                fps += " Fps";

                StaticFpsLog = fps;

                counter = 0;
                timeStamp.start();
            }
        }
    }

#endif
}

void CustomAxisPlot::setAxisScaleInfo(int AxisDir, const ScaleSt &scale)
{
//    if(fristPlot==this)
//    {
//        int a;
//        a++;
//    }
    switch (AxisDir) {
    case QwtPlot::xBottom:
    {
        mXScale=scale;
        setAxisScale(QwtPlot::xBottom,mXScale.mMin,mXScale.mMax);
        replot();
        break;
    }
    case QwtPlot::yLeft:
    {
        mYScale=scale;
        setAxisScale(QwtPlot::yLeft,mYScale.mMin,mYScale.mMax);
        for(int i=0;i<scale.mDetailScale.count();i++)
        {
            if(mGlayout.length()<=i)
            {
                QwtPlotMarker* PlotMarker = new QwtPlotMarker;
                PlotMarker->setLinePen(mYColor,1,Qt::DashLine);
                PlotMarker->setLineStyle(QwtPlotMarker::HLine);
                PlotMarker->attach(this);
                PlotMarker->setZ(0);
                mGlayout<<PlotMarker;
            }
            if(scale.mDetailScale[i]!=0)
            {
                mGlayout[i]->setVisible(true);
                mGlayout[i]->setYValue(scale.mDetailScale[i]);
            }
            else
                mGlayout[i]->setVisible(false);
        }
        replot();
        break;
    }
    default:
        break;
    }

}

const ScaleSt &CustomAxisPlot::GetXAxisScaleInfo()
{
    return  mXScale;
}

const ScaleSt &CustomAxisPlot::GetYAxisScaleInfo()
{
    return  mYScale;
}

void CustomAxisPlot::setXAxisFont(const QFont &font)
{
    mXFont=font;
}

void CustomAxisPlot::setYAxisFont(const QFont &font)
{
    mYFont=font;
}

void CustomAxisPlot::setXAxisColor(const QColor &color)
{
    mXColor = color;
}

void CustomAxisPlot::setYAxisColor(const QColor &color)
{
    for(auto ite:mGlayout)
    {
        QPen tPen = ite->linePen();
        tPen.setColor(color);
        ite->setLinePen(tPen);
    }
    mYColor = color;
}


void CustomAxisPlot::drawCustomXAxis(QPainter *painter) {
    QwtPainter qwtPainter;
    painter->setPen(mXColor);
    // 自定义X轴的位置和范围
    if(mXScale.mMin!=INVALID_VALUE&&mXScale.mMax!=INVALID_VALUE)
    {

        qreal xMin = mXScale.mMin;
        qreal xMax = mXScale.mMax;

        // 获取X轴的绘图范围
        const QwtScaleMap xMap = canvasMap(xBottom);
        double x1 =   xMap.transform(xMin)-2; //TBD
        double x2 =xMap.transform(xMax); //TBD

        const QwtScaleMap yMap = canvasMap(yLeft);
        double y1 =   yMap.transform(mXScale.mScalePos);
        double y2 =y1;

        painter->setFont(mXFont);

        // 绘制X轴线
        qwtPainter.drawLine(painter, x1, y1, x2, y2);

        // 绘制X轴刻度和标签
        if(mXScale.mDetailScale.count()!=0)
        {
            mXScale.mDetailScale.removeOne(0);
            for(auto ite : mXScale.mDetailScale)
            {
                double xpos = xMap.transform(ite);
                QString label = QString::number(ite,'f',mXScale.mDisplayPrecison);
                drawXTickAndLabel(painter,xpos,y1,label);
            }
        }
        else
        {
            int tickCount = mXScale.mTickCount; // 刻度的数量
            double tickStep = (xMax - xMin) / (tickCount - 1);
            for (int i = 1; i < tickCount; i++)
            {
                int x = xMap.transform(xMin + i * tickStep);
                QString label = QString::number(xMin + i * tickStep,'f',mXScale.mDisplayPrecison);
                drawXTickAndLabel(painter, x,y1,label);
            }
        }
    }
}


void CustomAxisPlot::drawXTickAndLabel(QPainter *painter, int xpos, int ypos, const QString &label)
{
    painter->drawLine(xpos, ypos, xpos , ypos+5);
    int labelWidth = painter->fontMetrics().width(label);
    painter->drawText(xpos-labelWidth/2,ypos+15, label);
}



void CustomAxisPlot::drawCustomYAxis(QPainter *painter) {
    QwtPainter qwtPainter;
    painter->setPen(mYColor);
    // 自定义Y轴的位置和范围
    if(mYScale.mMin!=INVALID_VALUE&&mYScale.mMax!=INVALID_VALUE)
    {
        qreal yMin = mYScale.mMin;
        qreal yMax = mYScale.mMax;

        // 获取Y轴的绘图范围
        const QwtScaleMap xMap = canvasMap(xBottom);
        double x1 = xMap.transform(mYScale.mScalePos)-2; //TBD
        double x2 = x1 ;

        const QwtScaleMap yMap = canvasMap(yLeft);
        double y1 = yMap.transform(yMin);
        double y2 = yMap.transform(yMax);
        // 设置Y轴的标签字体
        painter->setFont(mYFont);

        // 绘制Y轴线
        qwtPainter.drawLine(painter, x1, y1, x2, y2);

        if(mYScale.mDetailScale.count()!=0)
        {
            for(auto ite : mYScale.mDetailScale)
            {
                double ypos = yMap.transform(ite);
                QString label = QString::number(ite,'f',mYScale.mDisplayPrecison);
                drawYTickAndLabel(painter,x1,ypos,label);
            }
        }
        else
        {
            int tickCount = mYScale.mTickCount; // 刻度的数量
            double tickStep = (yMax - yMin) / (tickCount - 1);
            for (int i = 0; i < tickCount; i++)
            {
                int y = yMap.transform(yMin + i * tickStep);
                QString label = QString::number(yMin + i * tickStep,'f',mYScale.mDisplayPrecison);
                drawYTickAndLabel(painter,x1,y,label);
            }
        }
    }

}

void CustomAxisPlot::drawYTickAndLabel(QPainter *painter, int xpos, int ypos, const QString &label)
{
    painter->drawLine(xpos, ypos, xpos-5 , ypos);
    int labelWidth = painter->fontMetrics().width(label);
    int labelHeight =  painter->fontMetrics().height();
    painter->drawText(xpos-labelWidth-5,ypos+labelHeight/2, label);
}

BaseChart::BaseChart(QWidget *parent):QFrame(parent)
{


    mXNameLabel = new LabelPro;
    mXUnitLabel = new LabelPro;
    mYNameLabel = new LabelPro;
    mYUnitLabel = new LabelPro;

    mYNameLabel->SetTextFontSize(FONT_M);
    mYUnitLabel->SetTextFontSize(FONT_S);


    mXUnitLabel->setAttribute(Qt::WA_TranslucentBackground);
    mXNameLabel->setAttribute(Qt::WA_TranslucentBackground);
    mYUnitLabel->setAttribute(Qt::WA_TranslucentBackground);
    mYNameLabel->setAttribute(Qt::WA_TranslucentBackground);
}

BaseChart::~BaseChart()
{

}


void BaseChart::SetXName(QString name)
{
    mXNameLabel->setText(name);
}

void BaseChart::SetXUnit(QString unitStr)
{
    mXUnitLabel->setText(unitStr);
}

void BaseChart::SetYName(QString name)
{
    mYNameLabel->setText(name);
}

void BaseChart::SetYUnit(QString unitStr)
{
    mYUnitLabel->setText(unitStr);
}

void BaseChart::SetNameColor(const QColor& color)
{
    mNameColor = color;

    mYNameLabel->SetTextFontColor(color);
    mXNameLabel->SetTextFontColor(color);
}

void BaseChart::SetUnitColor(const QColor& color)
{
    mUnitColor = color;
    QPalette pa;
    pa.setColor(QPalette::WindowText, color);
    mYUnitLabel->setPalette(pa);
    mXUnitLabel->setPalette(pa);
}

void BaseChart::SetCurveColor(const QColor& color)
{
    mCurveColor = color;
}

void BaseChart::SetScaleColor(const QColor& color)
{
    mScaleColor = color;
}

void BaseChart::SetScales(const ScaleSt &xAxisScale, const ScaleSt &yAxisScale)
{
    SetXScale(xAxisScale);
    SetYScale(yAxisScale);
}

void BaseChart::SetDataDigits(short digits)
{

}
int BaseChart::FindNearestPoint(QPointF point, QVector<QPointF> pointVec)
{
    int minIndex = 0;
    qreal minLength = std::numeric_limits<int>::max();

    QVector2D p(point);

    for (int i = 0; i < pointVec.count(); i++)
    {
        QVector2D pointInLine(pointVec[i]);
        qreal tmpLength = p.distanceToPoint(pointInLine);
        if (tmpLength < minLength)
        {
            minLength = tmpLength;
            minIndex = i;
        }
    }

    return minIndex;
}
BaseQwtChart::BaseQwtChart(QWidget *parent):BaseChart(parent)
{
    mPlot = new CustomAxisPlot(this); //TBD 30ms 2023.10.17
    mPlot->setFocusPolicy(Qt::NoFocus);    
    QwtPlotCanvas *canvas = new QwtPlotCanvas();
    canvas->setFocusPolicy(Qt::NoFocus);
    canvas->setCursor(Qt::ArrowCursor);
    //StyleSet::SetBackgroundColor(canvas,Qt::transparent); TBD QwtPlotCanvas类Styleset方法设置背景色无效？
    canvas->setStyleSheet("QwtPlotCanvas{background-color: transparent};");
    canvas->setPaintAttribute(QwtPlotCanvas::BackingStore,false);
    canvas->setFrameStyle( QFrame::Box | QFrame::Plain |QFrame::NoFrame );
    canvas->setLineWidth(0);
    mPlot->setCanvas(canvas);


    StyleSet::SetBackgroundColor(mPlot,COLOR_TRANSPARENT);
    StyleSet::SetBackgroundColor(this,COLOR_BLACK);
}

BaseQwtChart::~BaseQwtChart()
{

}

void BaseQwtChart::Redraw()
{
    mPlot->replot();
}

void BaseQwtChart::SetXScale(const ScaleSt &)
{

}

void BaseQwtChart::SetYScale(const ScaleSt &)
{

}

void BaseQwtChart::setXScaleVisable(bool isVisable)
{
    mPlot->SetXAxisVisbale(isVisable);
    mPlot->setXAxisColor(COLOR_TRANSPARENT);
}

void BaseQwtChart::setYScaleVisable(bool isVisable)
{
    mPlot->SetYAxisVisbale(isVisable);
    mPlot->setYAxisColor(COLOR_TRANSPARENT);
}

