#include "DebugLogManager.h"
#include "Flowmeter.h"
#include "ModuleTestManager.h"
#include "calibration/flowmeter_cal_manage.h"
#include "calibration/FlowZeroCalManage.h"
#include "AlarmManager.h"
#include "SystemConfigManager.h"
#include "DataManager.h"
#include "RunModeManage.h"
#include "SystemSettingManager.h"
#include "FlowmeterManager.h"
#include "../Com/TCP/ToolCommProxy.h"
#include "CRC.h"
#include "UpgradeManager/UpgradeManager.h"

#ifdef __linux__
#define SERIALNAME "/dev/ttymxc2"
#else
#define SERIALNAME "com11"
#endif


#define BAUDRATE (9600)
#define STOPBIT QSerialPort::OneStop
#define PARITY QSerialPort::EvenParity

#define UPGRADE_TIMEOUT             (1000 *300)
#define FLOWMETER_PATH              "/usr/local/Update/Flowmeter.hex"
#define QUERY_INTERVAL_MS 1000
#define MAX_QUERY_RETRIES 20

Flowmeter* Flowmeter::mObject {};
Flowmeter::Flowmeter(QObject *parent) : LowerProxyBase(parent)
{
    mPackDir = 0x13;

    mUpgradeTimeoutTimer = new QTimer;
    mUpgradeTimeoutTimer->setSingleShot(true);
    mUpgradeTimeoutTimer->setInterval(UPGRADE_TIMEOUT);
    connect(mUpgradeTimeoutTimer, &QTimer::timeout, this, [this]()
    {
        DebugLog << "触发流量计300s更新超时";
        for(int i = 0; i < 10; i++)
        {
            SendIsOpenReportMsg(true);
        }
        UpgradeFinish();
        DebugLog << "让流量计恢复上报并且结束此次更新";
    });

    mQueryTimer = new QTimer(this);
    mQueryTimer->setInterval(QUERY_INTERVAL_MS);
    connect(mQueryTimer, &QTimer::timeout, this, &Flowmeter::SlotProcessQuery);
}

Flowmeter *Flowmeter::GetInstance()
{
    if(mObject ==NULL)
        mObject = new Flowmeter;
    return  mObject;
}

void Flowmeter::FlowmeterCal(unsigned char CalType, unsigned char CalValue, unsigned short value)
{
    switch(CalType)
    {
    case FM_CAL_O2:
        CalType = FLOWMETER_O2_CALIBRATION_ID;
        break;
    case FM_CAL_AIR:
        CalType = FLOWMETER_AIR_CALIBRATION_ID;
        break;
    case FM_CAL_N2O:
        CalType = FLOWMETER_N2O_CALIBRATION_ID;
        break;
    case FM_CAL_BACKUP:
        CalType = FLOWMETER_BACKUP_CALIBRATION_ID;
    default:
        break;
    }
    FlowmeterCalPack flow_cmd;
    flow_cmd.FlowCommandID = CalType;
    flow_cmd.FlowCommandValue = CalValue;
    flow_cmd.FlowValue = value;

    DebugLog<<"Flowmeter Cal:Send MsgType-"<<FLOWMETER_CAL_COMMAND_PACK<<"GasType-"<<CalType<<"State-"<<CalValue<<"FlowValue-"<<value;
    SendData(FLOWMETER_CAL_COMMAND_PACK,(char*)&flow_cmd,sizeof(flow_cmd));
}

void Flowmeter::FlowmeterValueSet(short o2Control, int assistType, short assistValue)
{
    FloweControlPack sendPack;
    sendPack.controlO2 = o2Control;
    sendPack.controlAssistType = assistType;
    sendPack.controlAssist = assistValue;
    SendData(FLOWMETER_CONTROL_PACK,(char*)&sendPack,sizeof(sendPack));
}

void Flowmeter::FlowmeterProportionalCal(int cmd)
{
    SendData(PROPORTIONAL_VALVE_CAL_COMMAND_PACK,(char*)&cmd,sizeof(cmd));
}

void Flowmeter::AddFlowmeterFlowValue(int asssistType)
{
    FlowemeterSimpleControlPack sendPack;
    sendPack.controlType = PV_CONTROL_INC_FLOW;
    sendPack.gasType = asssistType;
    SendData(PROPORTIONAL_VALVE_CONTROL_PACK,(char*)&sendPack,sizeof(sendPack));
}

void Flowmeter::DecFlowmeterFlowValue(int asssistType)
{
    FlowemeterSimpleControlPack sendPack;
    sendPack.controlType = PV_CONTROL_DEC_FLOW;
    sendPack.gasType = asssistType;
    SendData(PROPORTIONAL_VALVE_CONTROL_PACK,(char*)&sendPack,sizeof(sendPack));
}

void Flowmeter::StartFlowmeterCalZero()
{
    FlowmeterCalPack flow_cmd;
    flow_cmd.FlowCommandID = FLOWMETER_CAL_ZERO_ID;
    flow_cmd.FlowCommandValue = FLOW_METER_CAL_START;
    SendData(FLOWMETER_CAL_COMMAND_PACK,(char*)&flow_cmd,sizeof(flow_cmd));
}

void Flowmeter::CancelFlowmeterCalZero()
{
    FlowmeterCalPack flow_cmd;
    flow_cmd.FlowCommandID = FLOWMETER_CAL_ZERO_ID;
    flow_cmd.FlowCommandValue = FLOW_METER_CAL_CANCEL;
    SendData(FLOWMETER_CAL_COMMAND_PACK,(char*)&flow_cmd,sizeof(flow_cmd));
}

void Flowmeter::InitSerial()
{
    mSerialPort.setPortName(SERIALNAME);
    mSerialPort.setBaudRate(BAUDRATE);
    mSerialPort.setStopBits(STOPBIT);
    mSerialPort.setParity(PARITY);

    mPackDir = 0x13;

    if(!SetCommIODevice(&mSerialPort))
        DebugLog<<"Flowmeter serial connect error";
}

void Flowmeter::CloseSerial()
{
    if(mSerialPort.isOpen())
        mSerialPort.close();
}

void Flowmeter::CloseAllTechAlarm()
{
    AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_FLOW_TOO_LOW, 0);
    AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_BACKUP_FLOW_CONTROL_OPEN, 0);
    AlarmManager::GetInstance()->TriggerAlarm(PROMPT_BACKUP_FLOW_CONTROL_COMMUNICATION_STOP, 0);
    AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_AIR_PRESS_LOW, 0);
    AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_N2O_PRESS_LOW, 0);
    AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_O2_PRESS_LOW, 0);
    AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_O2_FLOW_INEXACT, 0);
    AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_BANLANCE_FLOW_INEXACT, 0);
    AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_FLOWMETER_O2_SENSOR_NO_CAL_DATA, 0);
    AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_FLOWMETER_N2O_SENSOR_NO_CAL_DATA, 0);
    AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_FLOWMETER_AIR_SENSOR_NO_CAL_DATA, 0);
    AlarmManager::GetInstance()->TriggerAlarm(PROMPT_O2_FM_SENSOR_COMMUNICATION_STOP, 0);
    AlarmManager::GetInstance()->TriggerAlarm(PROMPT_BALANCE_GAS_FM_SENSOR_ERROR, 0);
    AlarmManager::GetInstance()->TriggerAlarm(PROMPT_O2_FM_SENSOR_ERROR, 0);
    AlarmManager::GetInstance()->TriggerAlarm(PROMPT_N2O_FM_SENSOR_ERROR, 0);
    AlarmManager::GetInstance()->TriggerAlarm(PROMPT_AIR_FM_SENSOR_ERROR, 0);

}

bool Flowmeter::SendQueryVersion()
{
    QByteArray sendData;
    sendData.push_back((char)0);
    return SendData(FLOWMETER_QUERY_VERSION_ID,sendData);
}

bool Flowmeter::QueryGasType()
{
    QByteArray sendData;
    return SendData(FLOWMETER_QUERY_GAS_TYPE_ID,sendData);
}

bool Flowmeter::QuerySensorType()
{
    QByteArray sendData;
    return SendData(FLOWMETER_QUERY_SENSOR_TYPE_ID,sendData);
}

void Flowmeter::StartUpgrade()
{
    if(QFile::exists(FLOWMETER_PATH))
    {
        for(int i = 0; i < 10; i++)
        {
            SendIsOpenReportMsg(false);
        }
        DebugLog << "让流量计停止上报信息";

        QTimer::singleShot(1000, this, [this]()
        {
            mUpgradeTimeoutTimer->start();
            mSendedFileSize = 0;
            mCurUpgrading = true;
            mRecvData.clear();
            QByteArray tmpBuffer = mCommIODevice->readAll();

            mUpdateFile.setFileName(FLOWMETER_PATH);
            mUpdateFile.open(QIODevice::ReadOnly);
            SendUpdateHeadPack();
            DebugLog << "给流量计发送升级请求包";
        });
    }
}

void Flowmeter::SlotReadData()
{
    if(!mCurUpgrading)
    {
        LowerProxyBase::SlotReadData();
    }
    else
    {
        DebugLog << "收到数据尝试解包";
        ProcessUpdateRecv();
    }
}

void Flowmeter::UnpackData(int msgType, QByteArray data)
{
    if (mQueryState != QUERY_STATE_IDLE && mQueryState != QUERY_STATE_FINISHED && mQueryState != QUERY_STATE_FAILED)
    {
        switch (msgType)
        {
        case FLOWMETER_QUERY_VERSION_ID:
            if (mQueryState == QUERY_STATE_VERSION)
            {
                mQueriedVersion = QString::fromUtf8(data);
                UnpackVersion(data);
                mQueryState = QUERY_STATE_GAS_TYPE;
                mQueryRetryCount = 0;
            }
            break;
        case FLOWMETER_QUERY_GAS_TYPE_ID:
            if (mQueryState == QUERY_STATE_GAS_TYPE)
            {
                QString text = "";
                if (!data.isEmpty())
                {
                    unsigned char result = static_cast<unsigned char>(data[0]);
                    switch (result)
                    {
                        case 0: text = "氧笑空"; break;
                        case 1: text = "氧空"; break;
                        case 2: text = "氧笑"; break;
                        case 3: text = "单氧"; break;
                        default: text = "未知"; break;
                    }
                }
                mQueriedGasType = text;
                UnpackGasSourceType(text);
                mQueryState = QUERY_STATE_SENSOR_TYPE;
                mQueryRetryCount = 0;
            }
            break;
        case FLOWMETER_QUERY_SENSOR_TYPE_ID:
            if (mQueryState == QUERY_STATE_SENSOR_TYPE)
            {
                QString text = "";
                 if (!data.isEmpty())
                {
                     unsigned char result = static_cast<unsigned char>(data[0]);
                     switch (result)
                     {
                         case 0: text = "数字"; break;
                         case 1: text = "模拟"; break;
                         default: text = "未知"; break;
                     }
                }
                mQueriedSensorType = text;
                DebugLog << "Received Sensor Type:" << mQueriedSensorType;
                UnpackSensorType(text);
                DebugLog << "Initialization query finished successfully!";
                mQueryState = QUERY_STATE_FINISHED;
                mQueryRetryCount = 0;
                mQueryTimer->stop();
            }
            break;
        default:
            break;
        }
    }

    FlowmeterValuePack flowpack;
    FlowmeterCalPack* pflowcom;
    unsigned char calResult;
    QString text = "";

    switch (msgType)
    {
    case FLOWMETER_CAL_COMMAND_PACK:
        pflowcom = (FlowmeterCalPack*)data.data();
        if(FLOWMETER_CAL_ZERO_ID == pflowcom->FlowCommandID)
        {
            FlowZeroCalManage::GetInstance()->SaveCalResult((FLOWMETER_CAL_CMD)pflowcom->FlowCommandValue);
        }
        else
        {
            flowmeter_cal_manage::GetInstance()->ReceiveCalPack(pflowcom->FlowCommandValue);
        }
        break;
    case FLOWMETER_MONIT_PACK:
        if (mQueriedVersion == "V01.00.03")
        {
            flowpack.flow_o2 = CHAR2_TO_INT16(data[0], data[1]);
            flowpack.Back_up_flow_o2 = CHAR2_TO_INT16(data[2], data[3]);
            flowpack.flow_n2o = CHAR2_TO_INT16(data[4], data[5]);
            flowpack.flow_air = CHAR2_TO_INT16(data[6], data[7]);
            if(data.size() >= 10)
                flowpack.alarm_status = CHAR2_TO_INT16(data[8], data[9]);
        }
        else
        {
            flowpack.flow_o2 = CHAR2_TO_INT16(data[0], data[1]);
            if(data.size() >= 10)
                flowpack.Back_up_flow_o2 = CHAR2_TO_INT16(data[8], data[9]);
            flowpack.flow_n2o = CHAR2_TO_INT16(data[2], data[3]);
            flowpack.flow_air = CHAR2_TO_INT16(data[4], data[5]);
            flowpack.alarm_status = CHAR2_TO_INT16(data[6], data[7]);
        }

        DataManager::GetInstance()->SaveMonitorData(VALUEDATA_FLOWMETER_O2, flowpack.flow_o2);
        DataManager::GetInstance()->SaveMonitorData(VALUEDATA_FLOWMETER_AIR, flowpack.flow_air);
        DataManager::GetInstance()->SaveMonitorData(VALUEDATA_FLOWMETER_N2O, flowpack.flow_n2o);       
        ModuleTestManager::GetInstance()->UpdateModuleState(TEST_FLOWMETER, ABLE);

        ProcessAlamStatus(flowpack.alarm_status);
        if (ConfigManager->IsFullElecFlomter())
        {
            int realO2Flow = 0;
            realO2Flow = flowpack.flow_o2 - flowpack.Back_up_flow_o2;
            if(O2_SENSOR_ALARM_STATUS(flowpack.alarm_status))
            {
                AlarmManager::GetInstance()->TriggerAlarm(PROMPT_O2_FM_SENSOR_ERROR, 1);
            }
            else
            {
                AlarmManager::GetInstance()->TriggerAlarm(PROMPT_O2_FM_SENSOR_ERROR, 0);
            }

            if(N2O_SENSOR_ALARM_STATUS(flowpack.alarm_status) || AIR_SENSOR_ALARM_STATUS(flowpack.alarm_status))
            {
                AlarmManager::GetInstance()->TriggerAlarm(PROMPT_BALANCE_GAS_FM_SENSOR_ERROR, 1);
            }
            else
            {
                AlarmManager::GetInstance()->TriggerAlarm(PROMPT_BALANCE_GAS_FM_SENSOR_ERROR, 0);
            }
        }
        break;
    case PROPORTIONAL_VALVE_CAL_COMMAND_PACK:
        calResult = data[0];
        flowmeter_cal_manage::GetInstance()->ProcessPvCalResult((PV_CAL_RESULT)calResult);
        break;
    default:
        break;
    }
}

void Flowmeter::ProcessAlamStatus(int almStatus)
{
    if (ConfigManager->GetConfig<bool>(SystemConfigManager:: GAS_SOURCE_O2_BOOL_INDEX ) )
    {
        AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_FLOWMETER_O2_SENSOR_NO_CAL_DATA, O2_ALARM_STATUS(almStatus));
    }
    if (ConfigManager->GetConfig<bool>(SystemConfigManager:: GAS_SOURCE_N2O_BOOL_INDEX ) )
    {
        AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_FLOWMETER_N2O_SENSOR_NO_CAL_DATA, N2O_ALARM_STATUS(almStatus));
    }
    if (ConfigManager->GetConfig<bool>(SystemConfigManager:: GAS_SOURCE_AIR_BOOL_INDEX ) )
    {
        AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_FLOWMETER_AIR_SENSOR_NO_CAL_DATA, AIR_ALARM_STATUS(almStatus));
    }

    ModuleTestManager::GetInstance()->UpdateModuleState(TEST_FLOWMETER, ABLE);

    short o2Value = 0, airValue = 0, n2oValue = 0;
    DataManager::GetInstance()->GetMonitorData(VALUEDATA_FLOWMETER_O2, o2Value);
    DataManager::GetInstance()->GetMonitorData(VALUEDATA_FLOWMETER_AIR, airValue);
    DataManager::GetInstance()->GetMonitorData(VALUEDATA_FLOWMETER_N2O, n2oValue);

    int balanceType = SettingManager->GetIntSettingValue(SystemSettingManager::FLOW_CONTROL_BANLANCE_TYPE);

    if (ConfigManager->IsFullElecFlomter())
    {
        AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_O2_FLOW_INEXACT, O2_INEXAT_ALARM_STATUS(almStatus));
        AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_BANLANCE_FLOW_INEXACT, BANLANCE_INEXAT_ALARM_STATUS(almStatus));
        AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_FLOW_TOO_LOW, FLOW_TO_LOW_ALARM_STATUS(almStatus));
        AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_O2_PRESS_LOW, O2_PRESS_ALARM_STATUS(almStatus));

        if (balanceType == BALANCE_GAS_TYPE::AIR_BALANCE)
        {
            AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_N2O_PRESS_LOW, 0);
            AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_AIR_PRESS_LOW, AIR_PRESS_ALARM_STATUS(almStatus));
        }
        else if(balanceType == BALANCE_GAS_TYPE::N2O_BALANCE)
        {
            AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_AIR_PRESS_LOW, 0);
            AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_N2O_PRESS_LOW, N2O_PRESS_ALARM_STATUS(almStatus));
        }
        else
        {
            AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_AIR_PRESS_LOW, 0);
            AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_N2O_PRESS_LOW, 0);
        }

        AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_BACKUP_FLOW_CONTROL_OPEN, BACKUP_FLOW_CONTROL_OPEN(almStatus));
        AlarmManager::GetInstance()->TriggerAlarm(PROMPT_BACKUP_FLOW_CONTROL_COMMUNICATION_STOP, BACKUP_FLOW_COMMUNICATION_STOP(almStatus));
    }
    else
    {
        AlarmManager::GetInstance()->TriggerAlarm(PROMPT_O2_FM_SENSOR_ERROR, O2_SENSOR_ALARM_STATUS(almStatus));
        AlarmManager::GetInstance()->TriggerAlarm(PROMPT_N2O_FM_SENSOR_ERROR, N2O_SENSOR_ALARM_STATUS(almStatus));
        AlarmManager::GetInstance()->TriggerAlarm(PROMPT_AIR_FM_SENSOR_ERROR, AIR_SENSOR_ALARM_STATUS(almStatus));
    }

    return ;
}

void Flowmeter::UnpackVersion(QByteArray data)
{
    if ( !data.isEmpty() )
    {
        UpgradeManager::GetInstance()->SetMachineVersion(FLOWMETER_BOARD, data);
    }
}

void Flowmeter::UnpackGasSourceType(QString typeText)
{
    if (!typeText.isEmpty())
    {
        UpgradeManager::GetInstance()->SetMachineVersion(FLOWMETER_GAS_CONFIG, typeText);;
    }
}

void Flowmeter::UnpackSensorType(QString typeText)
{
    if (!typeText.isEmpty())
    {
        UpgradeManager::GetInstance()->SetMachineVersion(FLOWMETER_SENSOR_TYPE, typeText);
    }
}

bool Flowmeter::SendIsOpenReportMsg(bool isOpen)
{
    QByteArray sendData;
    if(isOpen)
    {
        sendData.push_back((char)1);
    }
    else
    {
        sendData.push_back((char)0);
    }

    return SendData(FLOWMETER_REPORT_MSG_SWITCH_ID, sendData);
}

bool Flowmeter::SendUpdateHeadPack()
{
    UpdateHeadPack HeadPcak = {HEAD_PACK, "BaiGe update", TARGET_FLOWMETER, (unsigned int)0};
    if(mCommIODevice->write(QByteArray::fromRawData(reinterpret_cast<const char *>(&HeadPcak) ,sizeof(UpdateHeadPack))) == -1)
    {
        return false;
    }
    else
    {
        return true;
    }
}

void Flowmeter::SendUpdatePartFileData()
{
    UpdateData Data;

    if(PACK_DAT_LEN + mSendedFileSize < mUpdateFile.size()){
        memset((char *)&Data.mData ,0 ,PACK_DAT_LEN);
        mUpdateFile.seek(mSendedFileSize);
        mUpdateFile.read((char *)&Data.mData ,PACK_DAT_LEN);
        mSendedFileSize += PACK_DAT_LEN;
        Data.mSize = PACK_DAT_LEN;
        SendUpdateFileData(Data);
        int progress = (double)mSendedFileSize / (double)mUpdateFile.size() * 100 - 1;
        UpgradeManager::GetInstance()->SetUpdateFileProgress(E_UPDATE_FLOWMETER, progress);
    }
    else if(mSendedFileSize >= mUpdateFile.size())
    {
        SendUpdateCmd(CMD_SEND_DATA_END);
    }
    else if(PACK_DAT_LEN + mSendedFileSize >= mUpdateFile.size())
    {
        memset((char *)&Data.mData ,0 ,PACK_DAT_LEN);
        mUpdateFile.seek(mSendedFileSize);
        mUpdateFile.read((char *)&Data.mData ,mUpdateFile.size() - mSendedFileSize);
        Data.mSize = mUpdateFile.size() - mSendedFileSize;
        mSendedFileSize += mUpdateFile.size() - mSendedFileSize;
        SendUpdateFileData(Data);
        int progress = (double)mSendedFileSize / (double)mUpdateFile.size() * 100 - 1;
        UpgradeManager::GetInstance()->SetUpdateFileProgress(E_UPDATE_FLOWMETER, progress);
    }

    mLastSendData = Data;
}

void Flowmeter::ResendUpdatePartFileData()
{
    SendUpdateFileData(mLastSendData);
}

bool Flowmeter::SendUpdateFileData(UpdateData data)
{
    UpdateDateCrcPack DataPack = {DATA_PACK ,PACK_DAT_LEN};
    DataPack.mDataLen = data.mSize;
    memcpy(reinterpret_cast<char *>(&DataPack.mData) ,reinterpret_cast<char *>(&data.mData) ,data.mSize);
    DataPack.mCrc16Value = Crc16Calculate(DataPack.mData, DataPack.mDataLen);
    return mCommIODevice->write(QByteArray::fromRawData(reinterpret_cast<const char *>(&DataPack),sizeof(UpdateDateCrcPack)));
}

bool Flowmeter::SendUpdateCmd(unsigned char cmd)
{
    UpdateCmdPack CmdPack = {CMD_PACK, cmd, TARGET_FLOWMETER};
    return mCommIODevice->write(QByteArray::fromRawData(reinterpret_cast<const char *>(&CmdPack) ,sizeof(UpdateCmdPack)));
}

void Flowmeter::ProcessUpdateRecv()
{
    QByteArray tmpBuffer = mCommIODevice->readAll();
    mRecvData.append(tmpBuffer);

    DebugLog << "收到的指令数据:" << mRecvData;
    while (true)
    {
        bool continueAnalyze = false;

        DebugLog << "收到的指令数据大小:" << mRecvData.size();
        if (mRecvData.size() >= sizeof(UpdateCmdPack))
        {
            UpdateCmdPack* cmdPack = reinterpret_cast<UpdateCmdPack*>(mRecvData.data());
            if (cmdPack->mPackType == CMD_PACK && cmdPack->mTargrtType == TARGET_FLOWMETER)
            {
                DebugLog << "指令解析成功";
                ProcessRecvCmd(cmdPack->mCmd);

                continueAnalyze = true;
                mRecvData.remove(0, sizeof(UpdateCmdPack));
            }
        }

        if(mRecvData.size() >= 1)
        {
            unsigned char header = static_cast<unsigned char>(mRecvData[0]);
            if(header != CMD_PACK)
            {
                mRecvData.clear();
            }
        }

        if (!continueAnalyze)
        {
            break;
        }
    }
}

void Flowmeter::ProcessRecvCmd(unsigned char cmd)
{
    switch (cmd)
    {
    case CMD_READY:
        DebugLog << "收到流量计更新准备完成数据包";
        SendUpdatePartFileData();
        break;
    case CMD_REQUEST_DATA:
        DebugLog << "收到流量计请求更新数据包";
        SendUpdatePartFileData();
        break;
    case CMD_RECIVE_FINISH:
        DebugLog << "收到流量计接收完成数据包";
        SendUpdateCmd(CMD_SEND_ALLFILE_END);
        mUpgradeTimeoutTimer->stop();
        UpgradeFinish();
        break;
    case CMD_RESEND_PART:
        DebugLog << "收到流量计请求重发数据包";
        ResendUpdatePartFileData();
        break;
    default:
        break;
    }
}

void Flowmeter::UpgradeFinish()
{
    mCurUpgrading = false;
    mSendedFileSize = 0;
    mRecvData.clear();
    mUpdateFile.close();

    if(QFile::exists(FLOWMETER_PATH))
        QFile::remove(FLOWMETER_PATH);

    UpgradeManager::GetInstance()->SetUpdateFileProgress(E_UPDATE_FLOWMETER, 100);
}

void Flowmeter::StartInitQuery()
{
    if (mQueryState != QUERY_STATE_IDLE && mQueryState != QUERY_STATE_FINISHED && mQueryState != QUERY_STATE_FAILED)
    {
        return;
    }

    mQueryState = QUERY_STATE_VERSION;
    mQueryRetryCount = 0;
    mQueriedVersion = "";
    mQueriedGasType = "";
    mQueriedSensorType = "";
    mQueryTimer->start();
}

QString Flowmeter::GetQueriedVersion() const
{
    return mQueriedVersion;
}

QString Flowmeter::GetQueriedGasType() const
{
    return mQueriedGasType;
}

QString Flowmeter::GetQueriedSensorType() const
{
    return mQueriedSensorType;
}

void Flowmeter::SlotProcessQuery()
{
    if (mQueryState == QUERY_STATE_IDLE || mQueryState == QUERY_STATE_FINISHED || mQueryState == QUERY_STATE_FAILED)
    {
        mQueryTimer->stop();
        return;
    }

    if (mQueryRetryCount >= MAX_QUERY_RETRIES)
    {
        DebugLog << "Max retries reached for state:" << mQueryState << ". Stopping query.";
        mQueryState = QUERY_STATE_FAILED;
        mQueryTimer->stop();
        return;
    }

    mQueryRetryCount++;
    DebugLog << "Processing query state:" << mQueryState << ", Retry count:" << mQueryRetryCount;

    bool success = false;
    switch (mQueryState)
    {
        case QUERY_STATE_VERSION:
            success = SendQueryVersion();
            break;
        case QUERY_STATE_GAS_TYPE:
            success = QueryGasType();
            break;
        case QUERY_STATE_SENSOR_TYPE:
            success = QuerySensorType();
            break;
        default:
            DebugLog << "Error: Invalid query state in SlotProcessQuery.";
            mQueryState = QUERY_STATE_FAILED;
            mQueryTimer->stop();
            break;
    }

    if (!success)
    {
        DebugLog << "Failed to send query command for state:" << mQueryState;
    }
}
