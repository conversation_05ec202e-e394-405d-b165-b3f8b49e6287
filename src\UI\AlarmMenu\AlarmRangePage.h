﻿#ifndef ALARMRANGEPAGE_H
#define ALARMRANGEPAGE_H

#include <QLabel>
#include <QPushButton>
#include <QGridLayout>
#include <QSpacerItem>

#include "FocusWidget.h"
#include "VerticalTabWidget.h"
class RangeController;
class VtRange;
class MVRange;
class ApneaRange;
class TipDialog;



class AlarmRangePage : public FocusWidget
{
    Q_OBJECT
public:
    enum ALARMRANGEPAGE_INDEX
    {
        MACHINE_LIMITED = 0,
        GAS_LIMITED,
        END,
    };

public:
    explicit AlarmRangePage(QWidget *parent = NULL);
    void RefurbishAgentType(unsigned short type);
    void InitUiText();
    void ShowTab(int idx);
    void CloseDialog();

private slots:
    void onSetDefault();
    void SlotMachineRestore();
    void SlotGasRestore();
    void onLowLimitChanged(int limit);
    void onHighLimitChanged(int limit);

private:
    void resizeEvent ( QResizeEvent * event );
    void InitUi();
    void UpUi();
    void initRangeController(RangeController *ctrl, short id, bool lowEnable = true, bool highEnable = true);
    void InitConnect();
    void translateHeader(QLabel *highLabel, QLabel *lowLabel);
    unsigned short getControlId(RangeController *ctrl);
    void CloseOldAgentAlarm(unsigned short FiAnaesDatatype, unsigned short EtAnaesDatatype);

private:
    TipDialog *mRestoreTip{};
    VerticalTabWidget *mTabWidget {};
    FocusWidget *mMachineLimited {};
    FocusWidget *mGasLimited {};

    VtRange *m_vteRange;
    ApneaRange *m_apneaRange;
    RangeController *mMvRange;
    RangeController *mRateRange;
    RangeController *mPrRange;
    RangeController *mPawRange;
    RangeController *m_fio2Range;
    RangeController *m_fico2Range;
    RangeController *m_etco2Range;
    RangeController *mFiN2oRange;
    RangeController *mEtN2oRange;
    RangeController *mFiAnaesRange;
    RangeController *mEtAnaesRange;

    QList<RangeController *> mAllRangeController{};

    QPushButton *mMachineRestore {};
    QPushButton *mGasRestore {};

    short mAsphyxia;
    unsigned short mFiAnaesDatatype;
    unsigned short mEtAnaesDatatype;
};

#endif // ALARMRANGEPAGE_H
