﻿#include "NormalSetPage.h"
#include "SystemTimeManager.h"
#include "SystemSettingManager.h"
#include "SystemConfigManager.h"
#include "MainWindow/MainWindow.h"
#include "ListItemGroup.h"
#include "UnitManager.h"
#include "String/UIStrings.h"
#include "VentModeSettingManager.h"
#include "HistoryEventManager.h"
#include "SettingSpinboxManager.h"
#include "VentModeSettingManager.h"
#include "RunModeManage.h"
#include "ModuleTestManager.h"

#define TIME_OUT_TIME (1000-QDateTime::currentDateTime().time().msec())
#define YEAR_ATFIRST_FORMAT "\\d{4}-([1-9]|\\d{2})-([1-9]|\\d{2})"
#define YEAR_ATLAST_FORMAT "([1-9]|\\d{2})-([1-9]|\\d{2})-\\d{4}"
#define H12_TIME_FORMAT "(0[1-9]|[0-9]|1[0-2]):([0-9]|\\d{2}):([0-9]|\\d{2})"
#define H24_TIME_FORMAT "([0-9]|\\d{2}):([0-9]|\\d{2}):([0-9]|\\d{2})"
#define BORDER_AT_AMPM 11
#define Y_M_D_FORMAT "yyyy-MM-dd"
#define M_D_Y_FORMAT "MM-dd-yyyy"
#define D_M_Y_FORMAT "dd-MM-yyyy"
NormalSetPage::NormalSetPage(QWidget *parent) : FocusWidget(parent)
{
    InitUI();
    InitConnect();
}

void NormalSetPage::InitUiText()
{
    mLanguage->SetName(UIStrings::GetStr(ALL_STRINGS_ENUM::STR_LANGUAGE));
    auto langIntraData = new ValueStrIntraData;

    QMap<int, QString> strMap{};
    for (auto langId : UIStrings::GetInstance()->GetAllUsableLang())
    {
        strMap.insert((int)langId, UIStrings::GetInstance()->GetLangName(langId));
    }
    langIntraData->SetValueAndStr(strMap);
    mLanguage->setDataModel(langIntraData);
    mLanguage->setValue(SettingManager->GetIntSettingValue(SystemSettingManager::LANGUAGE_SETTING));

    //------------------------------------------------------------------------------------------------------------------------------------------

    mDate->SetName(UIStrings::GetStr(ALL_STRINGS_ENUM::STR_MAINMENU_TIMEDATE_DATE));
    mDate->InitUiText();
    mTime->SetName(UIStrings::GetStr(ALL_STRINGS_ENUM::STR_MAINMENU_TIMEDATE_TIME));
    mTime->InitUiText();


    sDateFormat->SetName(UIStrings::GetStr(ALL_STRINGS_ENUM::STR_DATE_FORMAT));
    ValueStrIntraData *DateFormat = new ValueStrIntraData;
    DateFormat->SetValueAndStr({
                                   {DATE_FORMAT_YMD, UIStrings::GetStr(ALL_STRINGS_ENUM::STR_MAINMENU_YMD)},
                                   {DATE_FORMAT_MDY, UIStrings::GetStr(ALL_STRINGS_ENUM::STR_MAINMENU_MDY)},
                                   {DATE_FORMAT_DMY, UIStrings::GetStr(ALL_STRINGS_ENUM::STR_MAINMENU_DMY)}});
    sDateFormat->setDataModel(DateFormat);
    sDateFormat->setValue(SettingManager->GetIntSettingValue(SystemSettingManager::DATEFORMAT_SETTING));


    sTimeFormat->SetName(UIStrings::GetStr(ALL_STRINGS_ENUM::STR_TIME_FORMAT));
    ValueStrIntraData *TimeFormat = new ValueStrIntraData;
    TimeFormat->SetValueAndStr(
                {
                    {TIMEMODE_12_H, UIStrings::GetStr(ALL_STRINGS_ENUM::STR_TIME_FORMAT_12H)},
                    {TIMEMODE_24_H, UIStrings::GetStr(ALL_STRINGS_ENUM::STR_TIME_FORMAT_24H)}
                });
    sTimeFormat->setDataModel(TimeFormat);
    sTimeFormat->setValue(SettingManager->GetIntSettingValue(SystemSettingManager::TIMEFORMAT_SETTING));

    mTimeForm->SetName(UIStrings::GetStr(ALL_STRINGS_ENUM::STR_TIME_AM)+"/"+UIStrings::GetStr(ALL_STRINGS_ENUM::STR_TIME_PM));
    ValueStrIntraData *TimeForm = new ValueStrIntraData;
    TimeForm->SetValueAndStr(
                {
                    {TIMEMODE_12_AM_H, UIStrings::GetStr(ALL_STRINGS_ENUM::STR_TIME_AM)},
                    {TIMEMODE_12_PM_H, UIStrings::GetStr(ALL_STRINGS_ENUM::STR_TIME_PM)}
                });
    mTimeForm->setDataModel(TimeForm);
    mTimeForm->setDumbValue(TIMEMODE_12_AM_H);
    //--------------------------------------------------------------------------------------------------------------------------------------------

    mCO2Unit->SetName(UIStrings::GetStr(ALL_STRINGS_ENUM::STR_CO2_UNIT));
    ValueStrIntraData *CO2Unit = new ValueStrIntraData;
    CO2Unit->SetValueAndStr({{U_PERCENT, UnitManager::GetNameStr(U_PERCENT)},
                             {U_MMHG,  UnitManager::GetNameStr(U_MMHG)}});
    mCO2Unit->setDataModel(CO2Unit);
    mCO2Unit->setValue(SettingManager->GetIntSettingValue(SystemSettingManager::AGUNIT_SETTING));

    mPressureUnit->SetName(UIStrings::GetStr(ALL_STRINGS_ENUM::STR_PAW_UNIT));
    ValueStrIntraData *PressureUnit = new ValueStrIntraData;
    auto keyValue = QMap<int, QString>{};
    for(int i = U_CMH2O;i<=U_KPA;i++)
    {
        keyValue.insert(i, UnitManager::GetNameStr((UNIT_TYPE)i));
    }
    PressureUnit->SetValueAndStr(keyValue);
    mPressureUnit->setDataModel(PressureUnit);
    mPressureUnit->setValue(SettingManager->GetIntSettingValue(SystemSettingManager::PUNIT_SETTING));


    mTrigger->SetName(UIStrings::GetStr(ALL_STRINGS_ENUM::STR_MAINMENU_TRIGSET_TXT1));
    ValueStrIntraData *Trigger = new ValueStrIntraData;
    Trigger->SetValueAndStr(
                {
                    {TRIG_FLAG_FTRIG, UIStrings::GetStr(ALL_STRINGS_ENUM::STR_SETTINGPARAM_FTRIG)},
                    {TRIG_FLAG_PTRIG, UIStrings::GetStr(ALL_STRINGS_ENUM::STR_SETTINGPARAM_PTRIG)}
                });
    mTrigger->setDataModel(Trigger);
    mTrigger->setValue(SettingManager->GetIntSettingValue(SystemSettingManager::TRIG_SETTING));
}

void NormalSetPage::SlotChangeDate(int Value)
{
    auto curDateTime = QDateTime::currentDateTime();
    curDateTime.setDate(QDate(Value / 10000, (Value / 100) % 100, Value % 100));
    SystemTimeManager::GetInstance()->ChangeDateTime(curDateTime);
}

void NormalSetPage::SlotChangeTime(int Value)
{
    auto curDateTime = QDateTime::currentDateTime();
    curDateTime.setTime(QTime(Value / 10000, (Value / 100) % 100, Value % 100));
    SystemTimeManager::GetInstance()->ChangeDateTime(curDateTime);
}

void NormalSetPage::SlotChangeDateFormat(int dateFormat)
{
    UpdateDateTimeButton();
    SystemTimeManager::GetInstance()->SetDateFormat(dateFormat);

    auto Date = new DateIntraData(nullptr, dateFormat);
    int value = mDate->value();
    mDate->setDataModel(Date);
    mDate->setDumbValue(value);
    switch(sDateFormat->value())
    {
    case DATE_FORMAT_YMD:
        mDate->SetPopupInputRegex(YEAR_ATFIRST_FORMAT);
        break;
    case DATE_FORMAT_MDY:
    case DATE_FORMAT_DMY:
        mDate->SetPopupInputRegex(YEAR_ATLAST_FORMAT);
        break;
    }
}

void NormalSetPage::SlotChangeTimeFormat(int timeFormat)
{
    UpdateDateTimeButton();
    SystemTimeManager::GetInstance()->SetTimeFormat(timeFormat);
    TimeIntraData* Time;
    Time = new TimeIntraData(nullptr, timeFormat);

    int value = mTime->value();
    mTime->setDataModel(Time);
    mTime->setDumbValue(value);

    switch(timeFormat)
    {
    case TIMEMODE_12_H:
        mTime->SetPopupInputRegex(H12_TIME_FORMAT);
        if(mTimeForm->isEnabled())
            mTimeForm->show();
        break;
    case TIMEMODE_24_H:
        mTime->SetPopupInputRegex(H24_TIME_FORMAT);
        mTimeForm->hide();
        break;
    }
}

void NormalSetPage::SlotChangeLanguage(int Value)
{
    //    int langId = m_btnLang->itemData(value);
    //    if (langId != SettingManager->GetIntSettingValue(SystemSettingManager::LANGUAGE_SETTING))
    //    {
    //        SettingManager->SetSettingValue(SystemSettingManager::LANGUAGE_SETTING, langId);
    //        MainWindow::GetInstance()->InitUiText();
    //    }
}


void NormalSetPage::SlotChangeTrigger(int flag)
{
    int oldTrigFlag = SettingManager->GetIntSettingValue(SystemSettingManager::TRIG_SETTING);
    T_MASTER_SET master_data;
    VentModeSettingManager::GetInstance()->GetSettingdata(master_data);

    if (oldTrigFlag != flag)
    {
        SettingManager->SetSettingValue(SystemSettingManager::TRIG_SETTING, flag);
        HistoryEventManager::GetInstance()->AddHistoryEvent(HistoryEventManager::HISTORY_EVENT_TYPE::OPERATE_EVENT,
                                                            LOG_VENTMODE_PARAM_ID,
                                                            SETTINGDATA_TRIGGERFLAG,
                                                            flag,
                                                            oldTrigFlag);
    }

    //==================move from gui_update.h
    unsigned short trig_mode = SettingManager->GetIntSettingValue(SystemSettingManager::TRIG_SETTING);
    VentModeSettingManager::GetInstance()->SaveSettingdata(SETTINGDATA_TRIGGERFLAG, trig_mode);
    T_MASTER_SET masterSet;
    VentModeSettingManager::GetInstance()->GetSettingdata(masterSet);
    SettingSpinboxManager::GetInstance()->SetTrigMode(trig_mode);
    //==================
}

void NormalSetPage::InitUI()
{
    //---------------------------------------------------------------------------------------------
    mLanguage = new TableButton(TableButton::COMBOBOX_BUTTON, this);
    auto *languageSettingGroup = new ListItemGroup("", this);
    languageSettingGroup->AddWidget(mLanguage);
    //---------------------------------------------------------------------------------------------

    mCO2Unit = new TableButton(TableButton::COMBOBOX_BUTTON, this);
    mPressureUnit = new TableButton(TableButton::COMBOBOX_BUTTON, this);




    auto *unitSettingGroup = new ListItemGroup("", this);
    unitSettingGroup->AddWidget(mCO2Unit);
    unitSettingGroup->AddWidget(mPressureUnit);

    mTrigger = new TableButton(TableButton::COMBOBOX_BUTTON, this);
    //---------------------------------------------------------------------------------------------
    //TableButton默认构造为KEY_NUMBER_BUTTON
    mDate = new TableButton();
    mDate->setIsCloseRefershPopup(false);
    mDateIntraData = new DateIntraData(nullptr, SettingManager->GetIntSettingValue(SystemSettingManager::DATEFORMAT_SETTING));
    mDate->setDataModel(mDateIntraData);

    mTime = new TableButton();
    mTime->setIsCloseRefershPopup(false);
    mTimeIntraData = new TimeIntraData(nullptr, SettingManager->GetIntSettingValue(SystemSettingManager::TIMEFORMAT_SETTING));
    mTime->setDataModel(mTimeIntraData);


    sDateFormat = new TableButton(TableButton::COMBOBOX_BUTTON, this);
    sTimeFormat = new TableButton(TableButton::COMBOBOX_BUTTON, this);
    mTimeForm   = new TableButton(TableButton::COMBOBOX_BUTTON, this);

    dateSettingGroup = new ListItemGroup("", this);
    dateSettingGroup->AddWidget(sDateFormat);
    dateSettingGroup->AddWidget(sTimeFormat);
    dateSettingGroup->AddFoucusSonWidget(sDateFormat);
    dateSettingGroup->AddFoucusSonWidget(sTimeFormat);

    timeFormSettingGroup = new ListItemGroup("", this);
    timeFormSettingGroup->AddWidget(mDate);
    timeFormSettingGroup->AddWidget(mTime);
    timeFormSettingGroup->AddWidget(mTimeForm);
    timeFormSettingGroup->AddFoucusSonWidget(mDate);
    timeFormSettingGroup->AddFoucusSonWidget(mTime);
    timeFormSettingGroup->AddFoucusSonWidget(mTimeForm);

    mLanguage->SetName(UIStrings::GetStr(ALL_STRINGS_ENUM::STR_LANGUAGE));
    ValueStrIntraData *languageEnumStrMap = new ValueStrIntraData;
    languageEnumStrMap->SetValueAndStr({
                                           {UIStrings::CHINESE, UIStrings::GetStr(ALL_STRINGS_ENUM::STR_LANGUAGE_CHN)},
                                           {UIStrings::ENGLISH, UIStrings::GetStr(ALL_STRINGS_ENUM::STR_LANGUAGE_ENG)},
                                           {UIStrings::UKRAINIAN, UIStrings::GetStr(ALL_STRINGS_ENUM::STR_LANGUAGE_UKR)},
                                           {UIStrings::FRENCH, UIStrings::GetStr(ALL_STRINGS_ENUM::STR_LANGUAGE_FRE)},
                                           //                                           {UIStrings::RUSSIAN, UIStrings::GetStr(ALL_STRINGS_ENUM::STR_LANGUAGE_RUS)}
                                       });
    mLanguage->setDataModel(languageEnumStrMap);
    mLanguage->setValue(SettingManager->GetIntSettingValue(SystemSettingManager::LANGUAGE_SETTING));

    //------------------------------------------------------------------------------------------------------------------------------------------

    mDate->SetName(UIStrings::GetStr(ALL_STRINGS_ENUM::STR_MAINMENU_TIMEDATE_DATE));
    mTime->SetName(UIStrings::GetStr(ALL_STRINGS_ENUM::STR_MAINMENU_TIMEDATE_TIME));

    sDateFormat->SetName(UIStrings::GetStr(ALL_STRINGS_ENUM::STR_DATE_FORMAT));
    ValueStrIntraData *DateFormat = new ValueStrIntraData;
    DateFormat->SetValueAndStr({
                                   {DATE_FORMAT_YMD, UIStrings::GetStr(ALL_STRINGS_ENUM::STR_MAINMENU_YMD)},
                                   {DATE_FORMAT_MDY, UIStrings::GetStr(ALL_STRINGS_ENUM::STR_MAINMENU_MDY)},
                                   {DATE_FORMAT_DMY, UIStrings::GetStr(ALL_STRINGS_ENUM::STR_MAINMENU_DMY)}});
    sDateFormat->setDataModel(DateFormat);
    sDateFormat->setValue(SystemTimeManager::GetInstance()->DateFormat());


    sTimeFormat->SetName(UIStrings::GetStr(ALL_STRINGS_ENUM::STR_TIME_FORMAT));
    ValueStrIntraData *TimeFormat = new ValueStrIntraData;
    TimeFormat->SetValueAndStr(
                {
                    {TIMEMODE_12_H, UIStrings::GetStr(ALL_STRINGS_ENUM::STR_TIME_FORMAT_12H)},
                    {TIMEMODE_24_H, UIStrings::GetStr(ALL_STRINGS_ENUM::STR_TIME_FORMAT_24H)}
                });
    sTimeFormat->setDataModel(TimeFormat);
    sTimeFormat->setValue(SystemTimeManager::GetInstance()->TimeFormat());

    mTimeForm->SetName(UIStrings::GetStr(ALL_STRINGS_ENUM::STR_TIME_AM)+"/"+UIStrings::GetStr(ALL_STRINGS_ENUM::STR_TIME_PM));
    ValueStrIntraData *TimeForm = new ValueStrIntraData;
    TimeForm->SetValueAndStr(
                {
                    {TIMEMODE_12_AM_H, UIStrings::GetStr(ALL_STRINGS_ENUM::STR_TIME_AM)},
                    {TIMEMODE_12_PM_H, UIStrings::GetStr(ALL_STRINGS_ENUM::STR_TIME_PM)}
                });
    mTimeForm->setDataModel(TimeForm);
    mTimeForm->setDumbValue(TIMEMODE_12_AM_H);
    //--------------------------------------------------------------------------------------------------------------------------------------------

    mCO2Unit->SetName(UIStrings::GetStr(ALL_STRINGS_ENUM::STR_CO2_UNIT));
    ValueStrIntraData *CO2Unit = new ValueStrIntraData;
    CO2Unit->SetValueAndStr({{U_PERCENT, UnitManager::GetNameStr(U_PERCENT)},
                             {U_MMHG,  UnitManager::GetNameStr(U_MMHG)}});
    mCO2Unit->setDataModel(CO2Unit);
    mCO2Unit->setValue(SettingManager->GetIntSettingValue(SystemSettingManager::AGUNIT_SETTING));
    bool isHaveCo2 = ModuleTestManager::GetInstance()->GetAgModuleType() == CO2;
    bool isHaveAg = ModuleTestManager::GetInstance()->GetAgModuleType() == AG;
    if(!isHaveCo2&&!isHaveAg)
    {
        mCO2Unit->setVisible(false);
    }

    mPressureUnit->SetName(UIStrings::GetStr(ALL_STRINGS_ENUM::STR_PAW_UNIT));
    ValueStrIntraData *PressureUnit = new ValueStrIntraData;
    auto keyValue = QMap<int, QString>{};
    for(int i =U_CMH2O;i<=U_KPA;i++)
    {
        keyValue.insert(i, UIStrings::GetStr((ALL_STRINGS_ENUM)UnitManager::GetInfoSt((UNIT_TYPE)i).mUnitStrId));
    }
    PressureUnit->SetValueAndStr(keyValue);
    mPressureUnit->setDataModel(PressureUnit);
    mPressureUnit->setValue(SettingManager->GetIntSettingValue(SystemSettingManager::PUNIT_SETTING));

    mTrigger->SetName(UIStrings::GetStr(ALL_STRINGS_ENUM::STR_MAINMENU_TRIGSET_TXT1));
    ValueStrIntraData *Trigger = new ValueStrIntraData;
    Trigger->SetValueAndStr(
                {
                    {TRIG_FLAG_FTRIG, UIStrings::GetStr(ALL_STRINGS_ENUM::STR_SETTINGPARAM_FTRIG)},
                    {TRIG_FLAG_PTRIG, UIStrings::GetStr(ALL_STRINGS_ENUM::STR_SETTINGPARAM_PTRIG)}
                });
    mTrigger->setDataModel(Trigger);
    mTrigger->setValue(SettingManager->GetIntSettingValue(SystemSettingManager::TRIG_SETTING));


    if (ConfigManager->GetConfig<bool>(SystemConfigManager:: SIMVVC_BOOL_INDEX ) ||
            ConfigManager->GetConfig<bool>(SystemConfigManager:: SIMVPC_BOOL_INDEX ) ||
            ConfigManager->GetConfig<bool>(SystemConfigManager:: PSV_BOOL_INDEX ) ||
            ConfigManager->GetConfig<bool>(SystemConfigManager:: PRVC_BOOL_INDEX ))
    {
        mTrigger->show();
    }
    else
    {
        mTrigger->hide();
    }

    QVBoxLayout *mainLayout = new QVBoxLayout;
    mainLayout->setContentsMargins(0, 0, 0, 0);
    mainLayout->addWidget(languageSettingGroup);
    mainLayout->addWidget(unitSettingGroup);
    mainLayout->addWidget(mTrigger);
    mainLayout->addWidget(dateSettingGroup);
    mainLayout->addWidget(timeFormSettingGroup);
    mainLayout->addStretch();

    setLayout(mainLayout);
}

void NormalSetPage::InitConnect()
{
    mTimer = new QTimer();
    connect(mTimer,&QTimer::timeout,this,[=](){
        UpdateDateTimeButton();
        mTimer->start(TIME_OUT_TIME);
    });
    connect(mTime, &TristateBtn::SignalBeginEdit,this,[=](){
        mTimer->stop();
    });
    connect(mTime, &TristateBtn::SignalEndEdit,this,[=](){
        UpdateDateTimeButton();
        mTimer->start(TIME_OUT_TIME);
    });
    connect(mDate, &TristateBtn::SignalBeginEdit,this,[=](){
        mTimer->stop();
    });
    connect(mDate, &TristateBtn::SignalEndEdit,this,[=](){
        UpdateDateTimeButton();
        mTimer->start(TIME_OUT_TIME);
    });
    connect(mTimeForm,&TableButton::SignalValueChanged,this,[=](int val){
        switch(val)
        {
        case TIMEMODE_12_AM_H:
            if(QTime::currentTime().hour()>BORDER_AT_AMPM)
                SlotChangeTime((QTime::currentTime().hour()-12)*10000+QTime::currentTime().minute()*100+QTime::currentTime().second());
            break;
        case TIMEMODE_12_PM_H:
            if(QTime::currentTime().hour()<=BORDER_AT_AMPM)
                SlotChangeTime((QTime::currentTime().hour()+12)*10000+QTime::currentTime().minute()*100+QTime::currentTime().second());
            break;
        }
    });
    connect(mDate, &TableButton::SignalValueChanged, this, &NormalSetPage::SlotChangeDate);
    connect(mTime, &TableButton::SignalValueChanged, this, &NormalSetPage::SlotChangeTime);
    connect(sDateFormat, &TableButton::SignalValueChanged, this, &NormalSetPage::SlotChangeDateFormat);
    connect(sTimeFormat, &TableButton::SignalValueChanged, this, &NormalSetPage::SlotChangeTimeFormat);
    connect(mTrigger, &TableButton::SignalValueChanged, this, &NormalSetPage::SlotChangeTrigger);
    connect(mPressureUnit, &TableButton::SignalValueChanged, this, [](int unitType){
        int preUnitType = UnitManager::GetInstance()->GetCurPressureUnitType();
        if (preUnitType == unitType)
        {
            return;
        }
        SettingManager->SetSettingValue(SystemSettingManager::PUNIT_SETTING, unitType);
        UnitManager::GetInstance()->SetCategoryCurUnit(CONVERTIBLE_UNIT_CATEGORY::PRESSURE_CATEGORY, (UNIT_TYPE)unitType);
    });


    connect(mCO2Unit, &TableButton::SignalValueChanged, this, [](int unitType){
        SettingManager->SetSettingValue(SystemSettingManager::AGUNIT_SETTING, unitType);
        UnitManager::GetInstance()->SetCategoryCurUnit(CONVERTIBLE_UNIT_CATEGORY::CO2_CATEGORY, (UNIT_TYPE)unitType);
    });


    connect(mLanguage, &TableButton::SignalValueChanged, UIStrings::GetInstance(), &UIStrings::Setlanguage);

    connect(SystemTimeManager::GetInstance(), &SystemTimeManager::SignalChangeEnable, this, [this](bool enable){
        mTime->setEnabled(enable);
        mDate->setEnabled(enable);
        mTimeForm->setEnabled(enable);
        sDateFormat->setEnabled(enable);
        sTimeFormat->setEnabled(enable);
        if(enable)
        {
            dateSettingGroup->show();
            timeFormSettingGroup->show();
            if(sTimeFormat->value()!=TIMEMODE_12_H)
                mTimeForm->hide();
        }

        dateSettingGroup->update();
        timeFormSettingGroup->update();
    });
}

void NormalSetPage::UpdateDateTimeButton()
{
    QDateTime newDateTime = SystemTimeManager::GetInstance()->currentDateTime();
    mDate->setDumbValue(newDateTime.date().year()*10000+newDateTime.date().month()*100+newDateTime.date().day());
    mTime->setDumbValue(newDateTime.time().hour()*10000+newDateTime.time().minute()*100+newDateTime.time().second());
    switch(sDateFormat->value())
    {
    case DATE_FORMAT_YMD:
        mDate->setText(newDateTime.toString(Y_M_D_FORMAT));
        break;
    case DATE_FORMAT_MDY:
        mDate->setText(newDateTime.toString(M_D_Y_FORMAT));
        break;
    case DATE_FORMAT_DMY:
        mDate->setText(newDateTime.toString(D_M_Y_FORMAT));
        break;
    }

    switch(sTimeFormat->value())
    {
    case TIMEMODE_12_H:
        mTime->setText(newDateTime.toString("AP hh:mm").split(" ").at(1));
        break;
    case TIMEMODE_24_H:
        mTime->setText(newDateTime.toString("hh:mm"));
        break;
    }

    if(sTimeFormat->value() == TIMEMODE_12_H)
    {
        switch(mTimeForm->value())
        {
        case TIMEMODE_12_AM_H:
            if(QTime::currentTime().hour()>BORDER_AT_AMPM)
                mTimeForm->setDumbValue(TIMEMODE_12_PM_H);
            break;
        case TIMEMODE_12_PM_H:
            if(QTime::currentTime().hour()<=BORDER_AT_AMPM)
                mTimeForm->setDumbValue(TIMEMODE_12_AM_H);
            break;
        }
    }

}

void NormalSetPage::showEvent(QShowEvent *e)
{
    switch(sDateFormat->value())
    {
    case DATE_FORMAT_YMD:
        mDate->SetPopupInputRegex(YEAR_ATFIRST_FORMAT);
        break;
    case DATE_FORMAT_MDY:
    case DATE_FORMAT_DMY:
        mDate->SetPopupInputRegex(YEAR_ATLAST_FORMAT);
        break;
    }
    switch(sTimeFormat->value())
    {
    case TIMEMODE_12_H:
        mTime->SetPopupInputRegex(H12_TIME_FORMAT);
        break;
    case TIMEMODE_24_H:
        mTime->SetPopupInputRegex(H24_TIME_FORMAT);
        break;
    }
    switch(sTimeFormat->value())
    {
    case TIMEMODE_12_H:
        if(mTimeForm->isEnabled())
            mTimeForm->show();
        break;
    case TIMEMODE_24_H:
        mTimeForm->hide();
        break;
    }
    UpdateDateTimeButton();
    mTimer->start(TIME_OUT_TIME);
    mIsAutoInitFocusControl=true;
    FocusWidget::showEvent(e);
}

void NormalSetPage::hideEvent(QHideEvent *ev)
{
    mTimer->stop();
    FocusWidget::hideEvent(ev);
}

