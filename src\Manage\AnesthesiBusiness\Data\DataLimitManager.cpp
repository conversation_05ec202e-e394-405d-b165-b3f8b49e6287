#include <QtGui>


#include "AlarmManager.h"
#include "UnitManager.h"
#include "DataLimitManager.h"
#include "RangeController.h"
#include "UiApi.h"
#include "String/UIStrings.h"
#include "SystemSettingManager.h"
#include "SystemConfigManager.h"
#include "DataManager.h"
#include "HistoryEventManager.h"
#include "AlarmLimitInterface.h"
#include "ParamLabelManage.h"
#include "VentModeSettingManager.h"
#include "ModuleTestManager.h"



//配置文件中参数的名称
const char *alarm_key_table[] =
{
    "VALUEDATA_VTE",
    "VALUEDATA_VTI",
    "VALUEDATA_MV",
    "VALUEDATA_RATE",
    "VALUEDATA_R",
    "VALUEDATA_C",
    "VALUEDATA_C20C",
    "VALUEDATA_FSPN",
    "VALUEDATA_MVSPN",
    "VALUEDATA_IE",

    "VALUEDATA_PPEAK",
    "VALUEDATA_TOP_PAW",
    "VALUEDATA_PPLAT",
    "VALUEDATA_PEEP",
    "VALUEDATA_PMEAN",

    //O2,CO2参数
    "VALUEDATA_FIO2",
    "VALUEDATA_FICO2",
    "VALUEDATA_ETCO2",
    "VALUEDATA_FIN2O",
    "VALUEDATA_ETN2O",

    //麻醉气体参数
    "VALUEDATA_FI_AA1",
    "VALUEDATA_FI_AGENT2",
    "VALUEDATA_FIHAL",
    "VALUEDATA_FIENF",
    "VALUEDATA_FISEV",
    "VALUEDATA_FIISO",
    "VALUEDATA_FIDES",

    "VALUEDATA_ET_AA1",
    "VALUEDATA_ET_AGENT2",
    "VALUEDATA_ETHAL",
    "VALUEDATA_ETENF",
    "VALUEDATA_ETSEV",
    "VALUEDATA_ETISO",
    "VALUEDATA_ETDES",

    "VALUEDATA_MAC",
    //血氧参数
    "VALUEDATA_SPO2",
    "VALUEDATA_PR",
    //流量计参数
    "VALUEDATA_FLOWMETER_O2",
    "VALUEDATA_FLOWMETER_N2O",
    "VALUEDATA_FLOWMETER_AIR",
};

//备份限值，作为报警上下限默认值
struct S_BACK_LIMIT
{
    int low_limit;
    int high_limit;
};

//备份限值，作为报警上下限默认值
const S_BACK_LIMIT back_limit[VALUEDATA_MAX]=
{
    /* VALUEDATA_VTE  */  			{100,				780},
    /* VALUEDATA_VTI  */  			{INVALID_VALUE,		INVALID_VALUE},
    /* VALUEDATA_MV  */  			{30,				90},
    /* VALUEDATA_RATE  */  			{5,		    		50},
    /* VALUEDATA_R  */  			{INVALID_VALUE,		INVALID_VALUE},
    /* VALUEDATA_C  */  			{INVALID_VALUE,		INVALID_VALUE},
    /* VALUEDATA_C20C  */  			{INVALID_VALUE,		INVALID_VALUE},
    /* VALUEDATA_FSPN  */  			{INVALID_VALUE,		INVALID_VALUE},
    /* VALUEDATA_MVSPN  */  		{INVALID_VALUE,		INVALID_VALUE},
    /* VALUEDATA_IE  */  			{INVALID_VALUE,		INVALID_VALUE},

    /* VALUEDATA_PPEAK  */  		{2,					40},
    /* VALUEDATA_TOP_PAW  */  		{2,					40},
    /* VALUEDATA_PPLAT  */  		{INVALID_VALUE,		INVALID_VALUE},
    /* VALUEDATA_PEEP  */  			{INVALID_VALUE,		INVALID_VALUE},
    /* VALUEDATA_PMEAN  */  		{INVALID_VALUE,		INVALID_VALUE},

    //O2,CO2参数
    /* VALUEDATA_FIO2  */  			{18,				100},
    /* VALUEDATA_FICO2  */  		{0,					4},
    /* VALUEDATA_ETCO2  */  		{15,				50},
    /* VALUEDATA_FIN2O  */  		{0,					53},
    /* VALUEDATA_ETN2O  */  		{0,					55},

    //麻醉气体参数
    /* VALUEDATA_FI_AA1  */  		{INVALID_VALUE,		INVALID_VALUE},
    /* VALUEDATA_FI_AGENT2  */  	{0,					180},
    /* VALUEDATA_FIHAL  */  		{0,					50},
    /* VALUEDATA_FIENF  */  		{0,					50},
    /* VALUEDATA_FISEV  */  		{0,					80},
    /* VALUEDATA_FIISO  */  		{0,					50},
    /* VALUEDATA_FIDES  */  		{0,					60},

    /* VALUEDATA_ET_AA1  */  		{INVALID_VALUE,		INVALID_VALUE},
    /* VALUEDATA_ET_AGENT2  */  	{0,					180},
    /* VALUEDATA_ETHAL  */  		{0,					50},
    /* VALUEDATA_ETENF  */  		{0,					50},
    /* VALUEDATA_ETSEV  */  		{0,					70},
    /* VALUEDATA_ETISO  */  		{0,					50},
    /* VALUEDATA_ETDES  */  		{0,					60},

    /* VALUEDATA_MAC  */  			{INVALID_VALUE,		INVALID_VALUE},
    //血氧参数
    /* VALUEDATA_SPO2  */  			{90,        		100},
    /* VALUEDATA_PR  */  			{30,		 		250},
    //流量计参数
    /* VALUEDATA_FLOWMETER_O2  */   {INVALID_VALUE,		INVALID_VALUE},
    /* VALUEDATA_FLOWMETER_N2O  */  {INVALID_VALUE,		INVALID_VALUE},
    /* VALUEDATA_FLOWMETER_AIR  */  {INVALID_VALUE,		INVALID_VALUE}
};

ALARM_INFO DataLimitManager::sLimitInfo[VALUEDATA_MAX]
{				/*          minOff——flag low_lowest                  low_limit		              low_highest   high_lowest               high_limit                         high_highest        	low_alarm_id        	high_alarm_id           digit;    maxOff_flag  step  low_high_difference  */
    /* VALUEDATA_VTE  */  			{1,		10,				back_limit[VALUEDATA_VTE].low_limit,	1500,			15,				back_limit[VALUEDATA_VTE].high_limit,		1550,				ALARM_VTE_LOW,			ALARM_VTE_HIGH,         DIGIT_0,		1,		5,     5},
    /* VALUEDATA_VTI  */  			{1,		INVALID_VALUE,	back_limit[VALUEDATA_VTI].low_limit,	INVALID_VALUE,	INVALID_VALUE,	back_limit[VALUEDATA_VTI].high_limit,		INVALID_VALUE,},
    /* VALUEDATA_MV  */  			{1,	    0,     			back_limit[VALUEDATA_MV].low_limit,		600,			5,	    		back_limit[VALUEDATA_MV].high_limit,    	601,				ALARM_MV_LOW,			ALARM_MV_HIGH,          DIGIT_1,		1,		1,      1},
    /* VALUEDATA_RATE  */  			{1,		0,				back_limit[VALUEDATA_RATE].low_limit,	99,				1,				back_limit[VALUEDATA_RATE].high_limit,		101,				ALARM_RATE_LOW,     	ALARM_RATE_HIGH,        DIGIT_0,		1,      1,      1},
    /* VALUEDATA_R  */  			{0,		INVALID_VALUE,	back_limit[VALUEDATA_R].low_limit,		INVALID_VALUE,	INVALID_VALUE,	back_limit[VALUEDATA_R].high_limit,			INVALID_VALUE,},
    /* VALUEDATA_C  */  			{0,		INVALID_VALUE,	back_limit[VALUEDATA_C].low_limit,		INVALID_VALUE,	INVALID_VALUE,	back_limit[VALUEDATA_C].high_limit,			INVALID_VALUE,},
    /* VALUEDATA_C20C  */  			{0,		INVALID_VALUE,	back_limit[VALUEDATA_C20C].low_limit,	INVALID_VALUE,	INVALID_VALUE,	back_limit[VALUEDATA_C20C].high_limit,		INVALID_VALUE,},
    /* VALUEDATA_FSPN  */  			{0,		INVALID_VALUE,	back_limit[VALUEDATA_FSPN].low_limit,	INVALID_VALUE,	INVALID_VALUE,	back_limit[VALUEDATA_FSPN].high_limit,		INVALID_VALUE,},
    /* VALUEDATA_MVSPN  */  		{0,		INVALID_VALUE,	back_limit[VALUEDATA_MVSPN].low_limit,	INVALID_VALUE,	INVALID_VALUE,	back_limit[VALUEDATA_MVSPN].high_limit,		INVALID_VALUE,},
    /* VALUEDATA_IE  */  			{0,		INVALID_VALUE,	back_limit[VALUEDATA_IE].low_limit,	    INVALID_VALUE,	INVALID_VALUE,	back_limit[VALUEDATA_IE].high_limit,		INVALID_VALUE,},

    /* VALUEDATA_PPEAK  */  		{1,		0,				back_limit[VALUEDATA_PPEAK].low_limit,	30,				1,				back_limit[VALUEDATA_PPEAK].high_limit,		100,       			ALARM_PAW_LOW,			ALARM_PAW_HIGH,         DIGIT_0,		0,      1,      1},
    /* VALUEDATA_TOP_PAW  */  		{1,		0,				back_limit[VALUEDATA_TOP_PAW].low_limit,30,				1,				back_limit[VALUEDATA_TOP_PAW].high_limit,	100,      			NULL, /*ALARM_PAW_LOW*/	NULL,/*ALARM_PAW_HIGH*/ DIGIT_0,		0,      1,      1},
    /* VALUEDATA_PPLAT  */  		{0,		INVALID_VALUE,	back_limit[VALUEDATA_PPLAT].low_limit,	INVALID_VALUE,	INVALID_VALUE,	back_limit[VALUEDATA_PPLAT].high_limit,		INVALID_VALUE,},
    /* VALUEDATA_PEEP  */  			{0,		INVALID_VALUE,	back_limit[VALUEDATA_PEEP].low_limit,	INVALID_VALUE,	INVALID_VALUE,	back_limit[VALUEDATA_PEEP].high_limit,		INVALID_VALUE,},
    /* VALUEDATA_PMEAN  */  		{0,		INVALID_VALUE,	back_limit[VALUEDATA_PMEAN].low_limit,	INVALID_VALUE,	INVALID_VALUE,	back_limit[VALUEDATA_PMEAN].high_limit,		INVALID_VALUE,},

    //O2,CO2参数
    /* VALUEDATA_FIO2  */  			{0,		18,				back_limit[VALUEDATA_FIO2].low_limit,	99,				20,				back_limit[VALUEDATA_FIO2].high_limit,		100 ,    			ALARM_FIO2_LOW,			ALARM_FIO2_HIGH,        DIGIT_0,		1,      1,      1},
    /* VALUEDATA_FICO2  */  		{1,		0,				back_limit[VALUEDATA_FICO2].low_limit,	112,			1,				back_limit[VALUEDATA_FICO2].high_limit,		114,				ALARM_FICO2_LOW,		ALARM_FICO2_HIGH,       DIGIT_0,		0,      1,      1},
    /* VALUEDATA_ETCO2  */  		{0,		0,				back_limit[VALUEDATA_ETCO2].low_limit,	112,			1,				back_limit[VALUEDATA_ETCO2].high_limit,		114,				ALARM_ETCO2_LOW,		ALARM_ETCO2_HIGH,       DIGIT_0,		0,      1,      1},
    /* VALUEDATA_FIN2O  */  		{1,		0,				back_limit[VALUEDATA_FIN2O].low_limit,	98,				2,				back_limit[VALUEDATA_FIN2O].high_limit,		100,				ALARM_FIN2O_LOW,		ALARM_FIN2O_HIGH,       DIGIT_0,		0,      1,      2},
    /* VALUEDATA_ETN2O  */  		{1,		0,				back_limit[VALUEDATA_ETN2O].low_limit,	98, 			2,				back_limit[VALUEDATA_ETN2O].high_limit,		100,				ALARM_ETN2O_LOW,		ALARM_ETN2O_HIGH,       DIGIT_0,		0,      1,      2},

    //麻醉气体参数
    /* VALUEDATA_FI_AA1  */         {1,		INVALID_VALUE,	back_limit[VALUEDATA_FI_AA1].low_limit,	INVALID_VALUE,	INVALID_VALUE,	back_limit[VALUEDATA_FI_AA1].high_limit,	INVALID_VALUE,		ALARM_FI_AGENT1_LOW,	ALARM_FI_AGENT1_HIGH,	DIGIT_1,		0,      1,      2},
    /* VALUEDATA_ET_AA2  */         {1,		0,				back_limit[VALUEDATA_ET_AA2].low_limit,	180,			1,				back_limit[VALUEDATA_ET_AA2].high_limit,	181,				ALARM_FI_AGENT2_LOW,	ALARM_FI_AGENT2_HIGH,	DIGIT_1,		0,      1,      2},
    /* VALUEDATA_FIHAL  */  		{1,		0,				back_limit[VALUEDATA_FIHAL].low_limit,	78,				2,				back_limit[VALUEDATA_FIHAL].high_limit,		80,					ALARM_FIHAL_LOW,		ALARM_FIHAL_HIGH,		DIGIT_1,		0,      1,      2},
    /* VALUEDATA_FIENF  */  		{1,		0,				back_limit[VALUEDATA_FIENF].low_limit,	78,				2,				back_limit[VALUEDATA_FIENF].high_limit,		80,					ALARM_FIENF_LOW,		ALARM_FIENF_HIGH,		DIGIT_1,		0,      1,      2},
    /* VALUEDATA_FISEV  */  		{1,		0,				back_limit[VALUEDATA_FISEV].low_limit,	98,				2,				back_limit[VALUEDATA_FISEV].high_limit,		100,				ALARM_FISEV_LOW,		ALARM_FISEV_HIGH,		DIGIT_1,		0,      1,      2},
    /* VALUEDATA_FIISO  */  		{1,		0,				back_limit[VALUEDATA_FIISO].low_limit,	78,				2,				back_limit[VALUEDATA_FIISO].high_limit,		80,					ALARM_FIISO_LOW,		ALARM_FIISO_HIGH,		DIGIT_1,		0,      1,      2},
    /* VALUEDATA_FIDES  */  		{1,		0,				back_limit[VALUEDATA_FIDES].low_limit,	218,    		2,				back_limit[VALUEDATA_FIDES].high_limit,		220,        		ALARM_FIDES_LOW,		ALARM_FIDES_HIGH,		DIGIT_1,		0,      1,      2},

    /* VALUEDATA_ET_AA1  */         {1,		INVALID_VALUE,	back_limit[VALUEDATA_ET_AA1].low_limit,	INVALID_VALUE,	INVALID_VALUE,	back_limit[VALUEDATA_ET_AA1].high_limit,	INVALID_VALUE,		ALARM_ET_AGENT1_LOW,	ALARM_ET_AGENT1_HIGH,	DIGIT_1,    	0,      1,      2},
    /* VALUEDATA_ET_AA2  */         {1,		0,				back_limit[VALUEDATA_ET_AA2].low_limit,	180,			1,				back_limit[VALUEDATA_ET_AA2].high_limit,	181,				ALARM_ET_AGENT2_LOW,	ALARM_ET_AGENT2_HIGH,	DIGIT_1,		0,      1,      2},
    /* VALUEDATA_ETHAL  */  		{1,		0,				back_limit[VALUEDATA_ETHAL].low_limit,	78,				2,				back_limit[VALUEDATA_ETHAL].high_limit,		80,					ALARM_ETHAL_LOW,		ALARM_ETHAL_HIGH,		DIGIT_1,		0,      1,      2},
    /* VALUEDATA_ETENF  */  		{1,		0,				back_limit[VALUEDATA_ETENF].low_limit,	78,				2,				back_limit[VALUEDATA_ETENF].high_limit,		80,					ALARM_ETENF_LOW,		ALARM_ETENF_HIGH,		DIGIT_1,		0,      1,      2},
    /* VALUEDATA_ETSEV  */  		{1,		0,				back_limit[VALUEDATA_ETSEV].low_limit,	98,				2,				back_limit[VALUEDATA_ETSEV].high_limit,		100,				ALARM_ETSEV_LOW,		ALARM_ETSEV_HIGH,		DIGIT_1,		0,      1,      2},
    /* VALUEDATA_ETISO  */  		{1,		0,				back_limit[VALUEDATA_ETISO].low_limit,	78,				2,				back_limit[VALUEDATA_ETISO].high_limit,		80,					ALARM_ETISO_LOW,		ALARM_ETISO_HIGH,		DIGIT_1,		0,      1,      2},
    /* VALUEDATA_ETDES  */  		{1,		0,				back_limit[VALUEDATA_ETDES].low_limit,	218,			2,				back_limit[VALUEDATA_ETDES].high_limit,		220,				ALARM_ETDES_LOW,		ALARM_ETDES_HIGH,		DIGIT_1,		0,      1,      2},

    /* VALUEDATA_MAC  */  			{0,	 	INVALID_VALUE,	INVALID_VALUE,							INVALID_VALUE},
    //血氧参数
    /* VALUEDATA_SPO2  */  			{1,		69,				back_limit[VALUEDATA_SPO2].low_limit,	100,			INVALID_VALUE,	back_limit[VALUEDATA_SPO2].high_limit,		INVALID_VALUE,		ALARM_SPO2_LOW,			ALARM_SPO2_HIGH,		DIGIT_0,		0,      1,      1},
    /* VALUEDATA_PR  */  			{1,		29,				back_limit[VALUEDATA_PR].low_limit,		249,			30,				back_limit[VALUEDATA_PR].high_limit,		250,				ALARM_PR_LOW,			ALARM_PR_HIGH,          DIGIT_0,		0,      1,      1},
    //流量计参数
    /* VALUEDATA_FLOWMETER_O2  */   {0,     INVALID_VALUE,	INVALID_VALUE,							INVALID_VALUE},
    /* VALUEDATA_FLOWMETER_N2O  */  {0,     INVALID_VALUE,	INVALID_VALUE,							INVALID_VALUE},
    /* VALUEDATA_FLOWMETER_AIR  */  {0,     INVALID_VALUE,	INVALID_VALUE,							INVALID_VALUE}

    /* VALUEDATA_MAX  */
};

//PAW的上、下限调节控件，在压力单位发生改变时更新
static RangeController *sPawRangeCtrl;
static RangeController *s_Co2FI_range_ctrl;
static RangeController *s_Co2ET_range_ctrl;
static QMutex sLocker;

double ExcuteCO2MMHGtoPCT(double srcValue)
{
    if(srcValue>0)
    {
        srcValue = (srcValue)*(10*10000)/75.0/1013+0.5;
    }
    return  srcValue;
}

double ExcutePCTtoCO2MMHG(double srcValue)
{
    if(srcValue>0)
    {
        srcValue = (srcValue) * 1013 * 75.0 / (10 * 10000) + 0.5;
    }
    return  srcValue;
}

DataLimitManager::DataLimitManager()
{
    m_modify = 0;

    connect(UnitManager::GetInstance(), &UnitManager::SignalCategoryUnitChanged, this, [this](CONVERTIBLE_UNIT_CATEGORY paramType, UNIT_TYPE , UNIT_TYPE newUnit){
        if (paramType == CONVERTIBLE_UNIT_CATEGORY::PRESSURE_CATEGORY)
        {
            OnPressureUnitChanged(newUnit);
        }
        else if (paramType == CONVERTIBLE_UNIT_CATEGORY::CO2_CATEGORY)
        {
            SetAgUnitChange(newUnit);
        }
    });

    OnPressureUnitChanged(UnitManager::GetInstance()->GetCurPressureUnitType());
    SetAgUnitChange(UnitManager::GetInstance()->GetCategoryCurUnit(CONVERTIBLE_UNIT_CATEGORY::CO2_CATEGORY));

    HistoryEventManager::GetInstance()->RegisterOperateLogConvertFunc(LOG_ALARM_LMT_HIGH_ID, [](const HistoryEventSt&paramSt){
        int paramId = paramSt.mParameter1;
        int newValue = paramSt.mParameter2;
        int oldValue = paramSt.mParameter3;
        int Co2CurUnit = paramSt.mParameter4;

        QString logStrTemplate1;
        if(UIStrings::GetInstance()->GetCurLanguage()!=UIStrings::LANG_ENUM::CHINESE)
        {
            logStrTemplate1 = UIStrings::GetStr(ALL_STRINGS_ENUM::STR_HIGH_ALARM_LIMIT_CHANGE) + " %1: " + "%2" + "%3->" + "%4" + "%5";
        }
        else
        {
            logStrTemplate1 = UIStrings::GetStr(ALL_STRINGS_ENUM::STR_HIGH_ALARM_LIMIT_CHANGE) + "%1: " + "%2" + "%3->" + "%4" + "%5";
        }

        if (SETTINGDATA_ASPHY  == paramId)         //呼吸窒息
        {
            return logStrTemplate1.arg(UIStrings::GetStr(ALL_STRINGS_ENUM::STR_VALUEPARAM_ASPHY),
                                       QString::number(oldValue),
                                       "s",
                                       QString::number(newValue),
                                       "s");
        }

        unsigned short high_highest = DataLimitManager::GetInstance()->GetHighHighest((MONITOR_PARAM_TYPE)paramId);
        unsigned short highLimitClosable = DataLimitManager::GetInstance()->IsHighLimitClosable((MONITOR_PARAM_TYPE)paramId);
        unsigned short paramName = DataManager::GetParamNameId(paramId);

        if (paramName == STR_VALUEPARAM_PPEAK) {
            paramName = STR_SETTINGLIMIT_PAW;
        }



        auto pd = ParamLabelManage::GetParamInfoSt((MONITOR_PARAM_TYPE)paramId);
        auto digit = DataLimitManager::GetInstance()->GetLimitDigit((MONITOR_PARAM_TYPE)paramId);

        if((paramId == VALUEDATA_FICO2 || paramId == VALUEDATA_ETCO2)
                && Co2CurUnit != UnitManager::GetInstance()->GetCategoryCurUnit(CO2_CATEGORY))
        {
            double tempValue = DataManager::RevertToFloat(paramId, oldValue);
            tempValue = UnitManager::GetInstance()->ConvertValue(CO2_CATEGORY,
                                                                 (UNIT_TYPE)Co2CurUnit,
                                                                 UnitManager::GetInstance()->GetCategoryCurUnit(CO2_CATEGORY),
                                                                 tempValue);
            oldValue = tempValue;

            tempValue = DataManager::RevertToFloat(paramId, newValue);
            tempValue = UnitManager::GetInstance()->ConvertValue(CO2_CATEGORY,
                                                                 (UNIT_TYPE)Co2CurUnit,
                                                                 UnitManager::GetInstance()->GetCategoryCurUnit(CO2_CATEGORY),
                                                                 tempValue);
            newValue = tempValue;
        }

        if(newValue == high_highest && highLimitClosable)
        {
            if (oldValue == INVALID_VALUE)
                return logStrTemplate1.arg(UIStrings::GetStr((ALL_STRINGS_ENUM)paramName),
                                           "XXX",
                                           UIStrings::GetStr(pd.mUnitStrId),
                                           "OFF",
                                           UIStrings::GetStr(pd.mUnitStrId));
            else
                return logStrTemplate1.arg(UIStrings::GetStr((ALL_STRINGS_ENUM)paramName),
                                           QString::number(((double)oldValue) / qPow(10, digit), 'f', digit),
                                           UIStrings::GetStr(pd.mUnitStrId),
                                           "OFF",
                                           UIStrings::GetStr(pd.mUnitStrId));
        }
        else if(oldValue == high_highest && highLimitClosable)
        {
            return logStrTemplate1.arg(UIStrings::GetStr((ALL_STRINGS_ENUM)paramName),
                                       "OFF",
                                       UIStrings::GetStr(pd.mUnitStrId),
                                       QString::number(((double)newValue) / qPow(10, digit), 'f', digit),
                                       UIStrings::GetStr(pd.mUnitStrId));
        }
        else
        {
            if (oldValue == INVALID_VALUE)
                return logStrTemplate1.arg(UIStrings::GetStr((ALL_STRINGS_ENUM)paramName),
                                           "XXX",
                                           UIStrings::GetStr(pd.mUnitStrId),
                                           QString::number(((double)newValue) / qPow(10, digit), 'f', digit),
                                           UIStrings::GetStr(pd.mUnitStrId));
            else
                return logStrTemplate1.arg(UIStrings::GetStr((ALL_STRINGS_ENUM)paramName),
                                           QString::number(((double)oldValue) / qPow(10, digit), 'f', digit),
                                           UIStrings::GetStr(pd.mUnitStrId),
                                           QString::number(((double)newValue) / qPow(10, digit), 'f', digit),
                                           UIStrings::GetStr(pd.mUnitStrId));
        }
    });


    HistoryEventManager::GetInstance()->RegisterOperateLogConvertFunc(LOG_ALARM_LMT_LOW_ID, [this](const HistoryEventSt&paramSt){
        auto paramId = paramSt.mParameter1;
        auto newValue = paramSt.mParameter2;
        auto oldValue = paramSt.mParameter3;
        auto Co2CurUnit = paramSt.mParameter4;

        unsigned short low_lowest = DataLimitManager::GetInstance()->GetLowLowest((MONITOR_PARAM_TYPE)paramId);
        unsigned short min_off_flag = DataLimitManager::GetInstance()->GetMinOffFlag((MONITOR_PARAM_TYPE)paramId);
        unsigned short context_name_id = DataManager::GetParamNameId((paramId));
        if (context_name_id == STR_VALUEPARAM_PPEAK) {
            context_name_id = STR_SETTINGLIMIT_PAW;
        }



        QString logStrTemplate1;
        if(UIStrings::GetInstance()->GetCurLanguage()!=UIStrings::LANG_ENUM::CHINESE)
        {
            logStrTemplate1 = UIStrings::GetStr(ALL_STRINGS_ENUM::STR_LOW_ALARM_LIMIT_CHANGE) + " %1: " + "%2" + "%3->" + "%4" + "%5";
        }
        else
        {
            logStrTemplate1 = UIStrings::GetStr(ALL_STRINGS_ENUM::STR_LOW_ALARM_LIMIT_CHANGE) + "%1: " + "%2" + "%3->" + "%4" + "%5";
        }


        auto pd = ParamLabelManage::GetParamInfoSt((MONITOR_PARAM_TYPE)paramId);
        auto digit = DataLimitManager::GetInstance()->GetLimitDigit((MONITOR_PARAM_TYPE)paramId);

        if((paramId == VALUEDATA_FICO2 || paramId == VALUEDATA_ETCO2)
                && Co2CurUnit != UnitManager::GetInstance()->GetCategoryCurUnit(CO2_CATEGORY))
        {
            double tempValue = DataManager::RevertToFloat(paramId, oldValue);
            tempValue = UnitManager::GetInstance()->ConvertValue(CO2_CATEGORY,
                                                                 (UNIT_TYPE)Co2CurUnit,
                                                                 UnitManager::GetInstance()->GetCategoryCurUnit(CO2_CATEGORY),
                                                                 tempValue);
            oldValue = tempValue;

            tempValue = DataManager::RevertToFloat(paramId, newValue);
            tempValue = UnitManager::GetInstance()->ConvertValue(CO2_CATEGORY,
                                                                 (UNIT_TYPE)Co2CurUnit,
                                                                 UnitManager::GetInstance()->GetCategoryCurUnit(CO2_CATEGORY),
                                                                 tempValue);
            newValue = tempValue;
        }

        if(newValue == low_lowest && min_off_flag)
        {
            if (oldValue == INVALID_VALUE)
                return logStrTemplate1.arg(UIStrings::GetStr((ALL_STRINGS_ENUM)context_name_id),
                                           "XXX",
                                           UIStrings::GetStr(pd.mUnitStrId),
                                           "OFF",
                                           UIStrings::GetStr(pd.mUnitStrId));
            else
                return logStrTemplate1.arg(UIStrings::GetStr((ALL_STRINGS_ENUM)context_name_id),
                                           QString::number(((double)oldValue) / qPow(10, digit), 'f', digit),
                                           UIStrings::GetStr(pd.mUnitStrId),
                                           "OFF",
                                           UIStrings::GetStr(pd.mUnitStrId));
        }
        else if(oldValue == low_lowest && min_off_flag)
        {
            return logStrTemplate1.arg(UIStrings::GetStr((ALL_STRINGS_ENUM)context_name_id),
                                       "OFF",
                                       UIStrings::GetStr(pd.mUnitStrId),
                                       QString::number(((double)newValue) / qPow(10, digit), 'f', digit),
                                       UIStrings::GetStr(pd.mUnitStrId));
        }
        else
        {
            if (oldValue == INVALID_VALUE)
                return logStrTemplate1.arg(UIStrings::GetStr((ALL_STRINGS_ENUM)context_name_id),
                                           "XXX",
                                           UIStrings::GetStr(pd.mUnitStrId),
                                           QString::number(((double)newValue) / qPow(10, digit), 'f', digit),
                                           UIStrings::GetStr(pd.mUnitStrId));
            else
                return logStrTemplate1.arg(UIStrings::GetStr((ALL_STRINGS_ENUM)context_name_id),
                                           QString::number(((double)oldValue) / qPow(10, digit), 'f', digit),
                                           UIStrings::GetStr(pd.mUnitStrId),
                                           QString::number(((double)newValue) / qPow(10, digit), 'f', digit),
                                           UIStrings::GetStr(pd.mUnitStrId));
        }
    });


    HistoryEventManager::GetInstance()->RegisterOperateLogConvertFunc(LOG_ALARM_LIMIT_RESTORE_DEFAULT, [](const HistoryEventSt&){
        return UIStrings::GetStr(ALL_STRINGS_ENUM::STR_ALARM_LIMIT_RESTORE_DEFAULT);
    });
    HistoryEventManager::GetInstance()->RegisterOperateLogConvertFunc(LOG_VENTILATOR_LIMIT_RESTORE_DEFAULT, [](const HistoryEventSt&){
        return UIStrings::GetStr(ALL_STRINGS_ENUM::STR_VENTILATOR_LIMIT_RESTORE_DEFAULT);
    });
    HistoryEventManager::GetInstance()->RegisterOperateLogConvertFunc(LOG_ANESTHETIC_GAS_LIMIT_RESTORE_DEFAULT, [](const HistoryEventSt&){
        return UIStrings::GetStr(ALL_STRINGS_ENUM::STR_ANESTHETIC_GAS_LIMIT_RESTORE_DEFAULT);
    });
}

void DataLimitManager::Init(void)
{
    QMap<int,LimitSt> allLimit;
    AlarmLimitInterface::GetInstance()->GetAllAlarmLimit(allLimit);
    for (int i = VALUEDATA_BEGIN; i < VALUEDATA_MAX; ++i)
    {
        if(allLimit.contains(i))
        {
            LimitSt limitSt = allLimit.value(i);
            sLimitInfo[i].high_limit = limitSt.mHighLimit;
            sLimitInfo[i].low_limit = limitSt.mLowLimit;
        }
        else
        {
            sLimitInfo[i].high_limit = back_limit[i].high_limit;
            sLimitInfo[i].low_limit = back_limit[i].low_limit;
        }
    }
}


void DataLimitManager::SetAllLimitDefault()
{
    SetMachineLimitDefault();
    SetGasLimitDefault();
}

void DataLimitManager::SetMachineLimitDefault()
{
    HistoryEventManager::GetInstance()->AddHistoryEvent(HistoryEventManager::HISTORY_EVENT_TYPE::OPERATE_EVENT,
                                                        LOG_VENTILATOR_LIMIT_RESTORE_DEFAULT);
    for(int i = VALUEDATA_VTE; i <= VALUEDATA_FIO2; i ++)
    {
        SetHighLimit((MONITOR_PARAM_TYPE)i, back_limit[i].high_limit);
        SetLowLimit((MONITOR_PARAM_TYPE)i, back_limit[i].low_limit);
        sLocker.lock();
        m_modify = 1;
        sLocker.unlock();
    }
}

void DataLimitManager::SetGasLimitDefault()
{
    HistoryEventManager::GetInstance()->AddHistoryEvent(HistoryEventManager::HISTORY_EVENT_TYPE::OPERATE_EVENT,
                                                        LOG_ANESTHETIC_GAS_LIMIT_RESTORE_DEFAULT);
    for(int i = VALUEDATA_FICO2; i < VALUEDATA_MAX; i ++)
    {
        if(i == VALUEDATA_ETCO2 || i == VALUEDATA_FICO2)
        {
            if (ModuleTestManager::GetInstance()->GetAgModuleType() == None)
                continue;
            if(mPrevAgUnitType != -1)
            {
                if(mPrevAgUnitType == U_PERCENT)
                {
                    SetHighLimit((MONITOR_PARAM_TYPE)i, ExcuteCO2MMHGtoPCT(back_limit[i].high_limit));
                    SetLowLimit((MONITOR_PARAM_TYPE)i, ExcuteCO2MMHGtoPCT(back_limit[i].low_limit));
                }
                else
                {
                    SetHighLimit((MONITOR_PARAM_TYPE)i, back_limit[i].high_limit);
                    SetLowLimit((MONITOR_PARAM_TYPE)i, back_limit[i].low_limit);
                }
            }
        }
        else if ((i == VALUEDATA_ETN2O || i == VALUEDATA_FIN2O) && ModuleTestManager::GetInstance()->GetAgModuleType() != AG)
        {
            continue;
        }
        else
        {
            SetHighLimit((MONITOR_PARAM_TYPE)i, back_limit[i].high_limit);
            SetLowLimit((MONITOR_PARAM_TYPE)i, back_limit[i].low_limit);
        }
        sLocker.lock();
        m_modify = 1;
        sLocker.unlock();
    }
}
/* ***********************************************************************
***********************************************************************  */
int DataLimitManager::SetAgUnitChange(int unitType)
{
    if(mPrevAgUnitType != unitType)
    {
        if((UNIT_TYPE)unitType >= U_PERCENT && (UNIT_TYPE)unitType <= U_MMHG)
        {
            double (*tempExcuteFuction)(double) {};
            int digit =0;
            if((UNIT_TYPE)unitType == U_PERCENT)
            {
                tempExcuteFuction = ExcuteCO2MMHGtoPCT;
                digit=1;

            }
            else if((UNIT_TYPE)unitType == U_MMHG)
            {
                if(mPrevAgUnitType!=-1)
                    tempExcuteFuction = ExcutePCTtoCO2MMHG;
            }
            if(tempExcuteFuction!=NULL)
            {
                sLimitInfo[VALUEDATA_ETCO2].high_lowest = tempExcuteFuction(sLimitInfo[VALUEDATA_ETCO2].high_lowest);
                sLimitInfo[VALUEDATA_ETCO2].high_highest = tempExcuteFuction(sLimitInfo[VALUEDATA_ETCO2].high_highest);
                sLimitInfo[VALUEDATA_ETCO2].low_highest = tempExcuteFuction(sLimitInfo[VALUEDATA_ETCO2].low_highest);
                if(tempExcuteFuction == ExcuteCO2MMHGtoPCT) //TBD EtCO2 14.7->14.8
                {
                    sLimitInfo[VALUEDATA_ETCO2].low_highest += 1;
                }
                sLimitInfo[VALUEDATA_ETCO2].low_lowest= tempExcuteFuction(sLimitInfo[VALUEDATA_ETCO2].low_lowest);
                sLimitInfo[VALUEDATA_ETCO2].digit =digit;

                sLimitInfo[VALUEDATA_FICO2].high_lowest = tempExcuteFuction(sLimitInfo[VALUEDATA_FICO2].high_lowest);
                sLimitInfo[VALUEDATA_FICO2].high_highest = tempExcuteFuction(sLimitInfo[VALUEDATA_FICO2].high_highest);
                sLimitInfo[VALUEDATA_FICO2].low_highest = tempExcuteFuction(sLimitInfo[VALUEDATA_FICO2].low_highest);
                sLimitInfo[VALUEDATA_FICO2].low_lowest= tempExcuteFuction(sLimitInfo[VALUEDATA_FICO2].low_lowest);
                sLimitInfo[VALUEDATA_FICO2].digit =digit;


                short ETlow_limit;
                short EThigh_limit;
                short FIlow_limit;
                short FIhigh_limit;
                if(mPrevAgUnitType!=-1)
                {

                    ETlow_limit = tempExcuteFuction(sLimitInfo[VALUEDATA_ETCO2].low_limit);
                    EThigh_limit = tempExcuteFuction(sLimitInfo[VALUEDATA_ETCO2].high_limit);

                    FIlow_limit = tempExcuteFuction(sLimitInfo[VALUEDATA_FICO2].low_limit);
                    FIhigh_limit = tempExcuteFuction(sLimitInfo[VALUEDATA_FICO2].high_limit);
                }
                else
                {

                    ETlow_limit = (sLimitInfo[VALUEDATA_ETCO2].low_limit);
                    EThigh_limit = (sLimitInfo[VALUEDATA_ETCO2].high_limit);

                    FIlow_limit =(sLimitInfo[VALUEDATA_FICO2].low_limit);
                    FIhigh_limit =(sLimitInfo[VALUEDATA_FICO2].high_limit);
                }
                sLimitInfo[VALUEDATA_ETCO2].low_limit = ETlow_limit;
                sLimitInfo[VALUEDATA_ETCO2].high_limit = EThigh_limit;
                sLimitInfo[VALUEDATA_FICO2].low_limit = FIlow_limit;
                sLimitInfo[VALUEDATA_FICO2].high_limit = FIhigh_limit;
                if(s_Co2ET_range_ctrl!=NULL&&s_Co2FI_range_ctrl!=NULL)
                {
                    s_Co2FI_range_ctrl->SetUnitId(UnitManager::GetInfoSt((UNIT_TYPE)unitType).mUnitStrId);
                    s_Co2ET_range_ctrl->SetUnitId(UnitManager::GetInfoSt((UNIT_TYPE)unitType).mUnitStrId);

                    s_Co2FI_range_ctrl->setDigit(sLimitInfo[VALUEDATA_FICO2].digit);
                    s_Co2ET_range_ctrl->setDigit(sLimitInfo[VALUEDATA_ETCO2].digit);
                    if(sLimitInfo[VALUEDATA_FICO2].digit == 1) {
                        s_Co2FI_range_ctrl->setZoom(10.0);
                        s_Co2ET_range_ctrl->setZoom(10.0);
                    } else {
                        s_Co2FI_range_ctrl->setZoom(1.0);
                        s_Co2ET_range_ctrl->setZoom(1.0);
                    }

                    s_Co2ET_range_ctrl->setHighMinMax(sLimitInfo[VALUEDATA_ETCO2].high_lowest,sLimitInfo[VALUEDATA_ETCO2].high_highest);
                    s_Co2ET_range_ctrl->setLowMinMax(sLimitInfo[VALUEDATA_ETCO2].low_lowest,sLimitInfo[VALUEDATA_ETCO2].low_highest);

                    s_Co2FI_range_ctrl->setHighMinMax(sLimitInfo[VALUEDATA_FICO2].high_lowest,sLimitInfo[VALUEDATA_FICO2].high_highest);
                    s_Co2FI_range_ctrl->setLowMinMax(sLimitInfo[VALUEDATA_FICO2].low_lowest,sLimitInfo[VALUEDATA_FICO2].low_highest);

                    s_Co2ET_range_ctrl->ResetHighLowValue(EThigh_limit ,ETlow_limit);
                    s_Co2FI_range_ctrl->ResetHighLowValue(FIhigh_limit ,FIlow_limit);

                    s_Co2FI_range_ctrl->refreshText();
                    s_Co2ET_range_ctrl->refreshText();

                    int unitId = UnitManager::GetStrId((UNIT_TYPE)unitType);
                    s_Co2FI_range_ctrl->SetUnitId(unitId);
                    s_Co2ET_range_ctrl->SetUnitId(unitId);
                }
            }
            mPrevAgUnitType = unitType;
        }
    }
    return 1;
}


/* ***********************************************************************
  根据输入值是否在上下限范围内，判断该值的合法性。
  说明:输出值： 0：该值合法；1：该值超出低限；2：该值超出高限
***********************************************************************  */
VALUE_TYPE DataLimitManager::ValueType(MONITOR_PARAM_TYPE id, short value)
{
    short high_limit = GetHighLimit((MONITOR_PARAM_TYPE)id);
    short low_limit = GetLowLimit((MONITOR_PARAM_TYPE)id);

    if(id == VALUEDATA_MV) //TBD 同下
    {
        if(high_limit!=INVALID_VALUE)
            high_limit *= 10.0;
        if(low_limit!=INVALID_VALUE)
            low_limit *= 10.0;
    }

    if (INVALID_VALUE == value)
    {//当前值为无效值，则不考虑上下限问题
        return NORMAL_TYPE;
    }

    if((id == VALUEDATA_FICO2 || id == VALUEDATA_ETCO2)  // TBD check函数里面做单位换算
            && UnitManager::GetInstance()->GetCategoryCurUnit(CONVERTIBLE_UNIT_CATEGORY::CO2_CATEGORY)
            == U_MMHG)
    {
        double tempValue = DataManager::RevertToFloat(id, value);
        tempValue = UnitManager::GetInstance()->ConvertValue(CO2_CATEGORY,
                                                             DataManager::GetDefaultUnit((MONITOR_PARAM_TYPE)id),
                                                             U_MMHG,
                                                             tempValue);
        value = tempValue;
    }

    if ( (value > high_limit) && (INVALID_VALUE != high_limit)&&
         AlarmManager::GetInstance()->IsEnable((E_ALARM_ID)sLimitInfo[id].high_alarm_id))
    {//大于高限，不合法
        return INVALID_HIG_TYPE;
    }
    if ((value < low_limit)  && (INVALID_VALUE != low_limit)&&
            AlarmManager::GetInstance()->IsEnable((E_ALARM_ID)sLimitInfo[id].low_alarm_id))
    {//小于低限，不合法
        return INVALID_LOW_TYPE;
    }
    return NORMAL_TYPE;
}

void DataLimitManager::register_paw_limit_ctrl(RangeController *ctrl)
{
    sPawRangeCtrl = ctrl;
}

void DataLimitManager::register_CO2FI_limit_ctrl(RangeController *ctrl)
{
    s_Co2FI_range_ctrl =ctrl;
}

void DataLimitManager::register_CO2ET_limit_ctrl(RangeController *ctrl)
{
    s_Co2ET_range_ctrl =ctrl;
}

//high_limit、low_limit是作为数值判断的限值，而top_high_limit、base_low_limit又是设置限值时所不能超越的范围(开闭区间)
int DataLimitManager::SetHighLimit(MONITOR_PARAM_TYPE id, short value)
{
    if (id >= VALUEDATA_MAX)
    {
        return -1;
    }

    if (id < 0)
    {
        return -1;
    }

    if(sLimitInfo[id].high_limit == value)
    {
        return 0;
    }


    short oldValue = sLimitInfo[id].high_limit;
    sLimitInfo[id].high_limit = value;
    AlarmLimitInterface::GetInstance()->SetAlarmLimit(id, {sLimitInfo[id].low_limit, value});
    if(mPrevAgUnitType == UnitManager::GetInstance()->GetCategoryCurUnit(CO2_CATEGORY))
    {
        HistoryEventManager::GetInstance()->AddHistoryEvent(HistoryEventManager::HISTORY_EVENT_TYPE::OPERATE_EVENT,
                                                            LOG_ALARM_LMT_HIGH_ID,
                                                            id,
                                                            value,
                                                            oldValue,
                                                            UnitManager::GetInstance()->GetCategoryCurUnit(CO2_CATEGORY));
    }
    return 1;
}

int DataLimitManager::SetLowLimit(MONITOR_PARAM_TYPE id, short value)
{
    if (id >= VALUEDATA_MAX)
    {
        return -1;
    }

    if(sLimitInfo[id].low_limit == value)
    {
        return 0;
    }

    short oldValue = sLimitInfo[id].low_limit;
    sLimitInfo[id].low_limit = value;

    AlarmLimitInterface::GetInstance()->SetAlarmLimit(id, {value, sLimitInfo[id].high_limit});
    if(mPrevAgUnitType == UnitManager::GetInstance()->GetCategoryCurUnit(CO2_CATEGORY))
    {
        HistoryEventManager::GetInstance()->AddHistoryEvent(HistoryEventManager::HISTORY_EVENT_TYPE::OPERATE_EVENT,
                                                            LOG_ALARM_LMT_LOW_ID,
                                                            id,
                                                            value,
                                                            oldValue,
                                                            UnitManager::GetInstance()->GetCategoryCurUnit(CO2_CATEGORY));
    }
    return 1;
}

int DataLimitManager::GetHighLimit(MONITOR_PARAM_TYPE id)
{
    if (id >= VALUEDATA_MAX)
    {
        return -1;
    }
    //类似于get_high_limit的取高限,高限小于高限极值有效 
    if (sLimitInfo[id].high_lowest <= sLimitInfo[id].high_limit &&
            sLimitInfo[id].high_limit <= sLimitInfo[id].high_highest)
    {
        if(sLimitInfo[id].max_off_flag&&sLimitInfo[id].high_limit >= sLimitInfo[id].high_highest)
            return INVALID_VALUE;
        return sLimitInfo[id].high_limit;
    }

    return INVALID_VALUE;
}

int DataLimitManager::GetLowLimit(MONITOR_PARAM_TYPE id)
{
    if (id >= VALUEDATA_MAX)
    {
        return -1;
    }
    //取低限,低限大于低限极值有效
    if (sLimitInfo[id].low_lowest <= sLimitInfo[id].low_limit &&
            sLimitInfo[id].low_limit <= sLimitInfo[id].low_highest)
    {
        if(sLimitInfo[id].min_off_flag&&sLimitInfo[id].low_limit <= sLimitInfo[id].low_lowest)
            return INVALID_VALUE;
        return  sLimitInfo[id].low_limit;
    }
    return INVALID_VALUE;
}

//显示的数值小数位数
unsigned short DataLimitManager::GetLimitDigit(MONITOR_PARAM_TYPE id)
{
    if (id >= VALUEDATA_MAX)
    {
        DebugAssert(false);
        return 0;
    }
    return sLimitInfo[id].digit;
}

/*
short DataLimitManager::GetLowLowest(MONITOR_PARAM_TYPE id)
{
    if (id >= VALUEDATA_MAX)
    {
        DebugAssert(false);
        return 0;
    }
    return sLimitInfo[id].base_low_limit;
}
  */
unsigned short DataLimitManager::GetChangeStep(MONITOR_PARAM_TYPE id)
{
    if (id >= VALUEDATA_MAX)
    {
        DebugAssert(false);
        return 0;
    }
    return sLimitInfo[id].step;
}

unsigned short DataLimitManager::GetDifferenceOfLowHigh(MONITOR_PARAM_TYPE id)
{
    if (id >= VALUEDATA_MAX)
    {
        DebugAssert(false);
        return 0;
    }
    return sLimitInfo[id].low_high_difference;
}

short DataLimitManager::GetLowLowest(MONITOR_PARAM_TYPE id)
{
    if (id >= VALUEDATA_MAX)
    {
        DebugAssert(false);
        return 0;
    }
    return sLimitInfo[id].low_lowest;
}

short DataLimitManager::GetLowHighest(MONITOR_PARAM_TYPE id)
{
    if (id >= VALUEDATA_MAX)
    {
        DebugAssert(false);
        return 0;
    }
    return sLimitInfo[id].low_highest;
}

short DataLimitManager::GetHighLowest(MONITOR_PARAM_TYPE id)
{
    if (id >= VALUEDATA_MAX)
    {
        DebugAssert(false);
        return 0;
    }
    return sLimitInfo[id].high_lowest;
}

short DataLimitManager::GetHighHighest(MONITOR_PARAM_TYPE id)
{
    if (id >= VALUEDATA_MAX)
    {
        DebugAssert(false);
        return 0;
    }
    return sLimitInfo[id].high_highest;
}

short DataLimitManager::GetHighAlarmId(MONITOR_PARAM_TYPE id)
{
    if (id >= VALUEDATA_MAX)
    {
        DebugAssert(false);
        return 0;
    }
    return sLimitInfo[id].high_alarm_id;
}

short DataLimitManager::GetLowAlarmId(MONITOR_PARAM_TYPE id)
{
    if (id >= VALUEDATA_MAX)
    {
        DebugAssert(false);
        return 0;
    }
    return sLimitInfo[id].low_alarm_id;
}

/*
short DataLimitManager::get_top_high_limit(MONITOR_PARAM_TYPE id)//用于获取限值的最大值；
{
    if (id >= VALUEDATA_MAX)
    {
        DebugAssert(false);
        return 0;
    }
    return sLimitInfo[id].top_high_limit;
}
  */

/* *********************************************
功能说明:   获取上下限是否能关闭信息
********************************************  */
unsigned short DataLimitManager::GetMinOffFlag(MONITOR_PARAM_TYPE id)
{
    return  sLimitInfo[id].min_off_flag;
}

unsigned short DataLimitManager::IsHighLimitClosable(MONITOR_PARAM_TYPE id)
{
    return  sLimitInfo[id].max_off_flag;
}

E_ALARMPRIO DataLimitManager::GetAlarmLevel(MONITOR_PARAM_TYPE id, unsigned int out_range_type)
{
    if (INVALID_HIG_TYPE == out_range_type)
    {
        return AlarmManager::GetInstance()->GetAlarmPrio((E_ALARM_ID)(sLimitInfo[id].high_alarm_id));
    }
    else
    {
        return AlarmManager::GetInstance()->GetAlarmPrio((E_ALARM_ID)(sLimitInfo[id].low_alarm_id));
    }
}

void DataLimitManager::CheckAllValue(void* , void* )
{
    int id;
    short value;

    short high_limit;
    short low_limit;

    AlarmManager* alarmManager = AlarmManager::GetInstance();


    for(id = VALUEDATA_VTE; id < VALUEDATA_MAX; id++)
    {
        VALUE_STATUS_ENUM isParamDateOut = DataManager::GetInstance()->GetMonitorData(id, value);
        if(isParamDateOut == VALUE_STATUS_ENUM::VALUE_EXPIRED)
        {
            alarmManager->TriggerAlarm((E_ALARM_ID)sLimitInfo[id].low_alarm_id,0);
            alarmManager->TriggerAlarm((E_ALARM_ID)sLimitInfo[id].high_alarm_id,0);
            continue;
        }


        if (INVALID_VALUE == value)
        {
            continue;
        }
        if((id == VALUEDATA_FICO2 || id == VALUEDATA_ETCO2) //TBD check函数里面做单位换算
                && UnitManager::GetInstance()->GetCategoryCurUnit(CONVERTIBLE_UNIT_CATEGORY::CO2_CATEGORY)
                == U_MMHG)
        {
            double tempValue = DataManager::RevertToFloat(id, value);
            tempValue = UnitManager::GetInstance()->ConvertValue(CO2_CATEGORY,
                                                                 DataManager::GetDefaultUnit((MONITOR_PARAM_TYPE)id),
                                                                 U_MMHG,
                                                                 tempValue);
            value = tempValue;
        }
        //类似于get_high_limit的取高限,高限小于高限极值有效
        if ((sLimitInfo[id].high_lowest <= sLimitInfo[id].high_limit &&sLimitInfo[id].high_limit < sLimitInfo[id].high_highest)||
                (sLimitInfo[id].high_limit == sLimitInfo[id].high_highest&&!sLimitInfo[id].max_off_flag))
        {
            high_limit = sLimitInfo[id].high_limit;
        }
        else
        {
            high_limit = INVALID_VALUE;
        }
        //类似于get_high_limit的取低限,低限大于低限极值有效
        if ((sLimitInfo[id].low_lowest < sLimitInfo[id].low_limit &&sLimitInfo[id].low_limit <= sLimitInfo[id].low_highest)||
                (sLimitInfo[id].low_lowest == sLimitInfo[id].low_limit&&!sLimitInfo[id].min_off_flag))
        {
            low_limit = sLimitInfo[id].low_limit;
        }
        else
        {
            low_limit = INVALID_VALUE;
        }

        if (id == VALUEDATA_MV)
        {
            float scale = 10;
            if(high_limit!=INVALID_VALUE)
                high_limit *= scale;
            if(low_limit!=INVALID_VALUE)
                low_limit *= scale;
        }

        if ((value > high_limit) && (INVALID_VALUE != high_limit))
        {//达到高限，不合法
            alarmManager->TriggerAlarm((E_ALARM_ID)sLimitInfo[id].high_alarm_id, 1);
        }
        if ((value < low_limit)  && (INVALID_VALUE != low_limit))
        {//达到低限，不合法
            alarmManager->TriggerAlarm((E_ALARM_ID)sLimitInfo[id].low_alarm_id, 1);
        }
        if ((value <= high_limit) || INVALID_VALUE == high_limit)
        {//数值未超过高限
            alarmManager->TriggerAlarm((E_ALARM_ID)sLimitInfo[id].high_alarm_id, 0);
        }
        if ((value >= low_limit) || INVALID_VALUE == low_limit)
        {//数值未超过低限
            alarmManager->TriggerAlarm((E_ALARM_ID)sLimitInfo[id].low_alarm_id, 0);
        }
    }
    alarmManager->RefurbishWarning();
}


void DataLimitManager::OnPressureUnitChanged(unsigned short unitType)
{
    if (UnitManager::GetInstance()->InCategory(CONVERTIBLE_UNIT_CATEGORY::PRESSURE_CATEGORY, (UNIT_TYPE)unitType))
    {
        UnitInfoSt unitInfo = UnitManager::GetInfoSt((UNIT_TYPE)unitType);
        sLimitInfo[VALUEDATA_PPEAK].digit = (char)unitInfo.digit;

        if (sPawRangeCtrl)
        {
            sPawRangeCtrl->SetUnitId(UnitManager::GetStrId((UNIT_TYPE)unitType));
            sPawRangeCtrl->setDigit(sLimitInfo[VALUEDATA_PPEAK].digit);
            sPawRangeCtrl->refreshText();
        }
    }
}
