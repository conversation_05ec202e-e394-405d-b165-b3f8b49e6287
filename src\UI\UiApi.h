﻿#ifndef UIAPI_H
#define UIAPI_H

#include <QHBoxLayout>
#include <QVBoxLayout>
#include "String/UIStrings.h"
#include "UiAttribute.h"
#include "UiConfig.h"
#include "KeyConfig.h"

#ifdef WIN32

#ifndef WINVER
#define   WINVER   0x0501
#endif
#define RGB_TO_QCOLOR(rgb) QColor(rgb)


#define BG_RGB(r,g,b) ((unsigned int)(((unsigned char)(b)|((short)((unsigned char)(g))<<8))|(((unsigned int)(unsigned char)(r))<<16)))
#define BG_RGB_2(r,g,b) BG_RGB(r,g,b)
#define BG_RGB_HEX(hexRgb) BG_RGB((hexRgb&0XFF),(hexRgb>>8&0XFF),(hexRgb>>16&0XFF))
#else

#define ASSERT(condition)	\
    do                          \
{                           \
    if ( !( condition ) )   \
{                       \
    DebugAssert(0);			\
    }              			\
    } while ( 0 )      // 注意while后边不能加分号
#define BG_RGB(r, g, b)  (((b >> 3) & 0x0000001F) | (((g >> 2) << 5) & 0x00007e0) | (((r >> 3) << 11) & 0xf800))


#define BG_RGB_R(rgb) ((rgb >> 8 )&0xF8)
#define BG_RGB_G(rgb) ((rgb >> 3 )&0xFC)
#define BG_RGB_B(rgb) ((rgb << 3)&0xF8)
#define BG_RGB_2(r,g,b) ((unsigned int)(((unsigned char)(b)|((short)((unsigned char)(g))<<8))|(((unsigned int)(unsigned char)(r))<<16)))
//在linux平台，不用风格表设置的颜色，要把颜色转成16位
#define RGB_TO_QCOLOR(rgb) QColor(BG_RGB_R(rgb), BG_RGB_G(rgb), BG_RGB_B(rgb))
#endif

#define BG_RGB_T  unsigned int



//颜色转换成styleSheet中设置颜色的字符串，在风格表中颜色使用24位
#ifdef WIN32
#define COLOR_TO_STYLE_STR(color) QString("rgb(%1, %2, %3);").arg(\
                                    color.red()).arg(color.green()).arg(color.blue())
#else
#define COLOR_TO_STYLE_STR(color) QString("rgb(%1, %2, %3);").arg(\
                                    (color.rgb() >> 8)& 0xf8).arg((color.rgb() >> 3)&0xfc).arg((color.rgb() << 3)&0xf8)
#endif

template <typename EnumType>
static QString EnumToStr(EnumType e)
{
    QMetaEnum metaEnum = QMetaEnum::fromType<EnumType>();
    return metaEnum.valueToKey(e);
}

template <typename EnumType>
static EnumType StrToEnum(QString str)
{
    QMetaEnum metaEnum = QMetaEnum::fromType<EnumType>();
    return static_cast<EnumType>(metaEnum.keyToValue(str.toStdString().data()));
}
QString& GetQssFileStr(QString fileName);

inline int FlotToIntBpyDigit(const double num, const int digit) { return static_cast<int>((num + 1 * qPow(0.1, digit + 1)) * qPow(10, digit)); }

class CustomStyle;
class StyleSet : public QObject
{
    Q_OBJECT
public:
    enum BACKICON_RULE
    {
        FULL_BACK_GROUND,
        KEEP_CENTER
    };
public:
    static void  DrawRichText(QPainter *painter, QRect rect, int alignment, const QPalette &pal,
                                                  const QString &text);
    static void  SetBackgroundColor(QWidget* widget,const QColor &acolor);

    static void  SetTextColor(QWidget* widget,const QColor &acolor);

    static void  SetTextFont(QWidget* widget,const QFont& font);

    static void  SetTextFont(QWidget* widget,int fontSize,const QString fontName=FONT_FAMILY_NINA,bool isBold=false);

    static void  SetBorder(QWidget* widget,const QColor &acolor,int width=1,int xradius=0,int yradisu=-1);

    static void  SetBackGroundIcon(QWidget* widget,const QString &iconPath,int iconRule=FULL_BACK_GROUND);

private:
    static  CustomStyle* MakeCustomStyleOnWidget(QWidget* widget);
};

#endif // UIAPI_H
