﻿#include "SIMV_VCModeSheet.h"
#include "SettingSpinbox/ExtSettingSpinbox.h"
#include "SettingSpinboxManager.h"
#include "RuleManager.h"
#include "String/UIStrings.h"
#include "SystemConfigManager.h"
#include <QtGui>

//realdata文件中定义
extern int convert_IE_value_2_percent(short & percent, short value);

SIMV_VCModeSheet::SIMV_VCModeSheet(QWidget *parent) :
    ModeSheetBase(parent)
{
    mModeId = VENTMODE_FLAG_VCVSIMV;
    InitUi();
    InitConnect();
}

void SIMV_VCModeSheet::InitUi()
{
    m_VT = new VTSpinbox;
    SettingSpinboxManager::GetInstance()->RegisterModesetSpn(SETTINGDATA_VT, m_VT);

    mRate = new SettingSpinbox;
    SettingSpinboxManager::GetInstance()->RegisterModesetSpn(SETTINGDATA_RATE, mRate);
    mRate->setBaseMinMax(4,40);

    mTinsp = new SettingSpinbox;
    SettingSpinboxManager::GetInstance()->RegisterModesetSpn(SETTINGDATA_TINSP, mTinsp);

    m_Plimit = new SettingSpinbox;
    SettingSpinboxManager::GetInstance()->RegisterModesetSpn(SETTINGDATA_PLIMIT, m_Plimit);

    m_TipTi = new SettingSpinbox;
    SettingSpinboxManager::GetInstance()->RegisterModesetSpn(SETTINGDATA_TITP, m_TipTi);

    mPsupp = new PsuppSpinbox;
    SettingSpinboxManager::GetInstance()->RegisterModesetSpn(SETTINGDATA_PSUPP, mPsupp);

    mPeep = new SettingSpinbox;
    SettingSpinboxManager::GetInstance()->RegisterModesetSpn(SETTINGDATA_PEEP, mPeep);

    mTrig = new SettingSpinbox;
    SettingSpinboxManager::GetInstance()->RegisterModesetSpn(SETTINGDATA_FLOW_TRIGGERVALUE, mTrig);

    //SIMV_VC 布局
    mMainLayout->setSpacing(MODULE_ITEM_SPACING);
    mMainLayout->addWidget(m_VT);
    mMainLayout->addWidget(mRate);
    mMainLayout->addWidget(mTinsp);
    mMainLayout->addWidget(m_Plimit);
    mMainLayout->addWidget(m_TipTi);
    mMainLayout->addWidget(mPsupp);
    mMainLayout->addWidget(mPeep);
    mMainLayout->addWidget(mTrig);
    mMainLayout->addStretch();
}
