﻿#include <QStyleOption>
#include <QVBoxLayout>
#include <QStackedLayout>

#include "AgView.h"
#include "MainWindow/MainWindow.h"
#include "HistoryDataDialog.h"
#include "ParamLabelManage.h"
#include "SystemConfigManager.h"
#include "ParamLabel/ParamLabelBase.h"
#include "String/UIStrings.h"
#include "RunModeManage.h"
#include "AGModule.h"
#include "LoopChartWithSwitcher.h"
#include "UiApi.h"
#include "SystemSettingManager.h"
#include "ModuleTestManager.h"

#define VALUE_HEIGHT (55)
#define MAC_VALUE_WIDTH (VALUE_WIDTH*2)
#define AG_NAME_LABEL_WIDTH 60
#define TAG_WIDTH 45
#define LOOP_INDEX 2
class AgParamLabelName : public ParamLabelBase
{
public:
    AgParamLabelName(QWidget *parent = NULL);

    void SetNoUnit();

private:
    virtual void SetLabelScale(int) override;

    virtual void SetName(QString name) override;

    void InitUi();
};

class AgParamLabelValue : public ParamLabelBase
{

public:
    AgParamLabelValue(QWidget *parent =NULL) : ParamLabelBase(parent)
    {
        InitUi();
        mValueLabel->SetTextFontSize(FONT_XL);
        mValueLabel->SetLabelBgColor(COLOR_BLACK);
        setSizePolicy(QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);
        mValueLabel->setAlignment(Qt::AlignCenter);
    }

private:
    virtual void SetLabelScale(int) override {}

    void InitUi()
    {
        mNameLabel->hide();
        mUnitLabel->hide();
        mHighLimitLabel->hide();
        mLowLimitLabel->hide();

        QVBoxLayout *mainLayout = new QVBoxLayout;
        mainLayout->setContentsMargins(0, 0, 0, 0);
        mainLayout->setSpacing(0);
        mainLayout->addWidget(mValueLabel);
        setLayout(mainLayout);
    }
};


AgWidget::AgWidget(QWidget *parent) :
    FocusWidget(parent)
{
    mInspLabel = new QLabel(UIStrings::GetStr(STR_INSP));
    mInspLabel->setAlignment(Qt::AlignCenter);
    mInspLabel->setFixedWidth(TAG_WIDTH);
    StyleSet::SetBackgroundColor(mInspLabel,COLOR_BLACK);
    StyleSet::SetTextColor(mInspLabel,COLOR_DARK_GRAY);
    StyleSet::SetTextFont(mInspLabel,FONT_M,FONT_FAMILY_NINA);

    mExpLabel = new QLabel(UIStrings::GetStr(STR_EXP));
    mExpLabel->setFixedWidth(TAG_WIDTH);
    mExpLabel->setAlignment(Qt::AlignCenter);
    StyleSet::SetBackgroundColor(mExpLabel,COLOR_BLACK);
    StyleSet::SetTextColor(mExpLabel,COLOR_DARK_GRAY);
    StyleSet::SetTextFont(mExpLabel,FONT_M,FONT_FAMILY_NINA);


    m_CO2Name = new AgParamLabelName(this);
    m_CO2FiValue = new AgParamLabelValue(this);
    m_CO2EtValue = new AgParamLabelValue(this);


    m_N2OName = new AgParamLabelName(this);
    m_N2OFiValue = new AgParamLabelValue(this);
    m_N2OEtValue = new AgParamLabelValue(this);


    m_AAName = new AgParamLabelName(this);
    m_AAFiValue = new AgParamLabelValue(this);
    m_AAEtValue = new AgParamLabelValue(this);



    m_MACName = new AgParamLabelName;
    m_MACName->SetNoUnit();
    m_MACValue = new AgParamLabelValue(this);

    if (ModuleTestManager::GetInstance()->GetAgModuleType() == None)
    {
        mInspLabel->hide();
        mExpLabel->hide();
    }
    if (ModuleTestManager::GetInstance()->GetAgModuleType() == AG)
    {
        ParamLabelManage::GetInstance()->RegisterParamView(VALUEDATA_FICO2, m_CO2Name);
        ParamLabelManage::GetInstance()->RegisterParamView(VALUEDATA_FICO2, m_CO2FiValue);
        ParamLabelManage::GetInstance()->RegisterParamView(VALUEDATA_ETCO2, m_CO2EtValue);

        ParamLabelManage::GetInstance()->RegisterParamView(VALUEDATA_FIN2O, m_N2OName);
        ParamLabelManage::GetInstance()->RegisterParamView(VALUEDATA_FIN2O, m_N2OFiValue);
        ParamLabelManage::GetInstance()->RegisterParamView(VALUEDATA_ETN2O, m_N2OEtValue);

        ParamLabelManage::GetInstance()->RegisterParamView(VALUEDATA_FI_AA1, m_AAName);
        ParamLabelManage::GetInstance()->RegisterParamView(VALUEDATA_FI_AA1, m_AAFiValue);
        ParamLabelManage::GetInstance()->RegisterParamView(VALUEDATA_ET_AA1, m_AAEtValue);

        ParamLabelManage::GetInstance()->RegisterParamView(VALUEDATA_MAC, m_MACName);
        ParamLabelManage::GetInstance()->RegisterParamView(VALUEDATA_MAC, m_MACValue);
    }
    else if(ModuleTestManager::GetInstance()->GetAgModuleType() == CO2)
    {
        ParamLabelManage::GetInstance()->RegisterParamView(VALUEDATA_FICO2, m_CO2Name);
        ParamLabelManage::GetInstance()->RegisterParamView(VALUEDATA_FICO2, m_CO2FiValue);
        ParamLabelManage::GetInstance()->RegisterParamView(VALUEDATA_ETCO2, m_CO2EtValue);
    }

    auto gridLayout = new QGridLayout;
    gridLayout->addWidget(mInspLabel,0,1);
    gridLayout->setAlignment(mInspLabel,Qt::AlignCenter);
    gridLayout->addWidget(mExpLabel,0,2);
    gridLayout->setAlignment(mExpLabel,Qt::AlignCenter);


    gridLayout->addWidget(m_CO2Name,1,0);
    gridLayout->addWidget(m_CO2FiValue,1,1);
    gridLayout->addWidget(m_CO2EtValue,1,2);


    gridLayout->addWidget(m_N2OName,2,0);
    gridLayout->addWidget(m_N2OFiValue,2,1);
    gridLayout->addWidget(m_N2OEtValue,2,2);

    gridLayout->addWidget(m_AAName,3,0);
    gridLayout->addWidget(m_AAFiValue,3,1);
    gridLayout->addWidget(m_AAEtValue,3,2);

    gridLayout->addWidget(m_MACName,4,0);
    gridLayout->addWidget(m_MACValue,4,1,1,2);
    gridLayout->setContentsMargins(4,2,2,2);
    gridLayout->setSpacing(0);
    setLayout(gridLayout);

    connect(AGModule::GetInstance(), &AGModule::SignalAgentTypeChanged, this, [this](int type){
        if (ModuleTestManager::GetInstance()->GetAgModuleType() == AG)
        {
            auto idPair = AGModule::GetInstance()->AgTypeToParamType(type);
            ParamLabelManage::GetInstance()->RegisterParamView(idPair.first, m_AAName);
            ParamLabelManage::GetInstance()->RegisterParamView(idPair.first, m_AAFiValue);
            ParamLabelManage::GetInstance()->RegisterParamView(idPair.second, m_AAEtValue);
        }
    });
}

void AgWidget::InitUiText()
{
    mInspLabel->setText(UIStrings::GetStr(ALL_STRINGS_ENUM::STR_INSP));
    mExpLabel->setText(UIStrings::GetStr(ALL_STRINGS_ENUM::STR_EXP));
}



AgView::AgView(QWidget *parent) : FocusWidget(parent)
{
    mAgWidget = new AgWidget;
    StyleSet::SetBackgroundColor(mAgWidget,COLOR_BLACK);
    mAgWidget->installEventFilter(this);
    LOOP_TYPE type = (LOOP_TYPE)(SettingManager->GetIntSettingValue(SystemSettingManager::LOOP_AG));
    mLoop = new LoopChartWithSwitcher(type);
    ChartManager::GetInstance()->RegisterChart(ChartManager::CHART_TYPE_ENUM::LOOP_CHART, type, mLoop);

    mLabelWidget = new QWidget;
    mLabelWidget->setContentsMargins(0, 0, 0, 0);
    StyleSet::SetBackgroundColor(mLabelWidget,COLOR_BLACK);

    mAgLabel = new QLabel("AG", mLabelWidget);
    mAgLabel->setAlignment(Qt::AlignCenter);
    StyleSet::SetBackgroundColor(mAgLabel,COLOR_BLACK);
    StyleSet::SetTextColor(mAgLabel,COLOR_WHITE);
    StyleSet::SetTextFont(mAgLabel,FONT_XXL,FONT_FAMILY_NINA,true);


    auto mLayout = new QVBoxLayout;
    mLayout->addWidget(mAgLabel);
    mLabelWidget->setLayout(mLayout);

    mMainLayout = new QStackedLayout;
    mMainLayout->addWidget(mLabelWidget);
    mMainLayout->addWidget(mAgWidget);
    mMainLayout->addWidget(mLoop);
    setLayout(mMainLayout);
    mLoop->installEventFilter(this);

    connect(mLoop, &LoopChartWithSwitcher::SignalLoopClick, this, [=]{
        MainWindow::GetInstance()->showDialog(MainWindow::DLG_HISTORY_DATA);
        MainWindow::GetInstance()->SetHistoryDialogPage(HistoryDataDialog::LOOP_DETAIL_PAGE);
    });

    connect(SettingManager,&SystemSettingManager::SignalSettingChanged,[=](int settingId){
        if(settingId == SystemSettingManager::LOOP_AG)
        {
            LOOP_TYPE newType = (LOOP_TYPE)(SettingManager->GetIntSettingValue(SystemSettingManager::LOOP_AG));
            if(mLoop->GetType() != newType)
                mLoop->SetLoopType(newType);
        }
    });
    connect(mLoop,&LoopChartWithSwitcher::SignalLoopTypeChanged,[=](LOOP_TYPE newType){
        SettingManager->SetSettingValue(SystemSettingManager::LOOP_AG,newType);
    });


    connect(RunModeManage::GetInstance(), &RunModeManage::SignalModeChanged,this,&AgView::SlotRumModeChange);
}

void AgView::InitUiText()
{
    mAgWidget->InitUiText();
}

bool AgView::IsCurrentLoop()
{
    return mMainLayout->currentIndex() == LOOP_INDEX;
}

bool AgView::IsAgLoopEnable()
{
    return ModuleTestManager::GetInstance()->GetAgModuleType() != AG && ConfigManager->GetConfig<bool>(SystemConfigManager::LOOP_BOOL_INDEX);
}

void AgView::SlotRumModeChange()
{
    setEnabled(false);
    if (ModuleTestManager::GetInstance()->GetAgModuleType() != AG)
    {
        if (!RunModeManage::GetInstance()->IsModeActive(E_RUNMODE::MODE_RUN_VENT))
        {
            mAgLabel->hide();
            mMainLayout->setCurrentWidget(mLabelWidget);
        }
        else
        {
            if (ConfigManager->GetConfig<bool>(SystemConfigManager::LOOP_BOOL_INDEX))
            {
                mMainLayout->setCurrentWidget(mLoop);
                setEnabled(true);
            }
            else
            {
                mAgLabel->hide();
                mMainLayout->setCurrentWidget(mLabelWidget);
            }
        }
    }
    else
    {
        if (!RunModeManage::GetInstance()->IsModeActive(E_RUNMODE::MODE_RUN_VENT))
        {
            mAgLabel->show();
            mMainLayout->setCurrentWidget(mLabelWidget);
        }
        else
        {
            mMainLayout->setCurrentWidget(mAgWidget);
            setEnabled(true);
        }
    }
}

AgParamLabelName::AgParamLabelName(QWidget *parent) : ParamLabelBase(parent)
{
    InitUi();
}

void AgParamLabelName::SetNoUnit()
{
    mUnitLabel->hide();
}

void AgParamLabelName::SetLabelScale(int) {}

void AgParamLabelName::SetName(QString name)
{
    if (name == "")
    {
        return ;
    }
    name.replace("Fi","");
    mNameLabel->setText(name);
}

void AgParamLabelName::InitUi()
{
    setFixedWidth(AG_NAME_LABEL_WIDTH);
    mNameLabel->SetTextFontBold(true);
    mNameLabel->SetTextFontSize(FONT_M);
    mUnitLabel->SetTextFontSize(FONT_M);
    setSizePolicy(QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

    mValueLabel->hide();
    mHighLimitLabel->hide();
    mLowLimitLabel->hide();

    QVBoxLayout *mainLayout = new QVBoxLayout;
    mainLayout->setContentsMargins(0, 0, 0, 0);
    mainLayout->setSpacing(0);
    mainLayout->addWidget(mNameLabel);
    mainLayout->addWidget(mUnitLabel);
    setLayout(mainLayout);
}
