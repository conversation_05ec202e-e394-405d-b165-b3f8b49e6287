﻿#include <QHBoxLayout>
#include <QVBoxLayout>
#include <QGridLayout>

#include "UiApi.h"
#include "FlowZeroCalPage.h"
#include "String/UIStrings.h"
#include "Flowmeter.h"
#include "calibration/FlowZeroCalManage.h"
#include "PushButton.h"
#include "KeyConfig.h"
#include "DebugLogManager.h"
#include "calibration/CalModuleManage.h"
#include "HistoryEventManager.h"
#include "SystemConfigManager.h"

#define IMAGE_HEIGHT    340
#define IMAGE_WEIGHT    280
#define PAGE_HEIGHT     462
#define PAGE_WEIGHT     558
#define TIP_IMAGE_WIDTH 290
#define TIP_IMAGE_HEIGHT 285
#define RESULT_WIDGET_HEIGHT 150
#define RESULT_LABLE_HEIGHT 24

const QMap<MACHINE_SERIAL,QString> sFlowmeterImageMap =
{
    {CENAR_30,  ":/Calibration/FlowZeroCal40.png"},
    {CENAR_40,  ":/Calibration/FlowZeroCal40.png"},
    {CENAR_50,  ":/Calibration/FlowZeroCal50.png"},
    {ELUNA_60,  ":/Calibration/FlowZeroCal60.png"},
    {ELUNA_70,  ":/Calibration/FlowZeroCal70.png"},
    {ELUNA_80,  ":/Calibration/FlowZeroCal80.png"},
    {CENAR_30M, ":/Calibration/FlowZeroCal40.png"},
};

FlowZeroCalPage::FlowZeroCalPage(QWidget* parent) : FocusWidget(parent)
{
    mCalStepTextId = STR_MAINMENU_FLOWCAL_TXT2;
    mCalBtnTextId = STR_START;
    mCalResultTextId = STR_NULL;

    InitUI();
    InitUiText();

    connect(mCalButton, &PushButton::clicked, this, &FlowZeroCalPage::SlotOnCalBtnClicked);
    connect(FlowZeroCalManage::GetInstance(), &FlowZeroCalManage::SignalCalStateChanged,
            this, &FlowZeroCalPage::SlotRefurbishCalResult);

    auto calResult = CalModuleManage::GetInstance()->GetLatestCalResult(CAL_TYPE::CAL_TYPE_FLOW_ZERO);
    if (calResult.first == -1)
    {
        mResultBlock->hide();
        return;
    }
    SetCalResult(QDateTime::fromSecsSinceEpoch(calResult.first), (CAL_RESULT)calResult.second.mCalResult);
}

void FlowZeroCalPage::InitUI()
{
    mCalStepTipTitle = new QLabel(UIStrings::GetStr(STR_ZERO_CAL_TIP) + ":");
    mCalStepTipTitle->setFixedHeight(35);
    mCalStepTipTitle->setContentsMargins(5, 0, 0, 0);
    mCalStepTipTitle->setAlignment(Qt::AlignVCenter);
    mCalStepTipTitle->setObjectName("mCalStepTipTitle");
    StyleSet::SetTextFont(mCalStepTipTitle, mCalStepTipTitle->font().pointSize(), FONT_FAMILY_TAHOMA);

    mCalTipImage = new QLabel;
    mCalTipImage->setFixedWidth(TIP_IMAGE_WIDTH);
    mCalTipImage->setFixedHeight(TIP_IMAGE_HEIGHT);
    StyleSet::SetBackgroundColor(mCalTipImage,COLOR_WHITE_GRAY);
    mCalTipImage->setScaledContents(true);
    mCalStepTipText = new QTextEdit(this);
    mCalStepTipText->setFrameStyle(QFrame::Box);
    mCalStepTipText->setLineWidth(0);
    mCalStepTipText->setFocusPolicy(Qt::NoFocus);
    StyleSet::SetTextFont(mCalStepTipText,FONT_M);
    mCalStepTipText->setReadOnly(true);
    mCalStepTipText->setTextInteractionFlags(Qt::NoTextInteraction);
    mCalProgressBar = new CountdownProgressBar;
    mCalProgressBar->hide();

    mCalModuleStateTip = new QLabel();
    mCalModuleStateTip->setText(UIStrings::GetStr(STR_FLOWMETER_STOP));
    mCalModuleStateTip->hide();

    mCalButton = new PushButton(UIStrings::GetStr(STR_START));
    mCalButton->setCheckable(true);

    mCalResultTitle = new QLabel;
    mCalResultTitle->setFixedHeight(RESULT_LABLE_HEIGHT);
    StyleSet::SetTextColor(mCalResultTitle,COLOR_BLACK);
    StyleSet::SetTextFont(mCalResultTitle, FONT_M);
    mCalResult = new QLabel;
    mCalResult->setFixedHeight(RESULT_LABLE_HEIGHT);

    mPreCalTime = new DateTimeWidget(this, Qt::Horizontal);
    mPreCalTime->SetDateColor(COLOR_BLACK);
    mPreCalTime->SetTimeColor(COLOR_BLACK);
    mPreCalTime->setFixedHeight(RESULT_LABLE_HEIGHT);


    auto resultLayout = new QVBoxLayout;
    resultLayout->setContentsMargins(13, 0, 0, 0);
    resultLayout->addWidget(mCalResultTitle);
    resultLayout->addWidget(mCalResult);
    resultLayout->addWidget(mPreCalTime);
    resultLayout->addStretch();



    mResultBlock = new QFrame;
    mResultBlock->setLayout(resultLayout);


    StyleSet::SetBackgroundColor(mCalResultTitle,COLOR_WHITE_GRAY);
    StyleSet::SetBackgroundColor(mCalResult,COLOR_WHITE_GRAY);
    StyleSet::SetBackgroundColor(mPreCalTime,COLOR_WHITE_GRAY);
    StyleSet::SetBackgroundColor(mResultBlock,COLOR_WHITE_GRAY);
    StyleSet::SetTextFont(mCalResult, FONT_M);
    auto centerLayout = new QHBoxLayout;
    centerLayout->addWidget(mCalStepTipText);
    centerLayout->addWidget(mCalTipImage);
    //centerLayout->setSpacing(0);

    QHBoxLayout *buttonLayout = new QHBoxLayout;
    buttonLayout->addWidget(mCalModuleStateTip);
    buttonLayout->addStretch();
    buttonLayout->addWidget(mCalButton);
    buttonLayout->setAlignment(mCalButton, Qt::AlignRight);
    QVBoxLayout *bottomLayout = new QVBoxLayout;
    bottomLayout->addWidget(mResultBlock);
    bottomLayout->addStretch();
    bottomLayout->addWidget(mCalProgressBar);
    bottomLayout->addLayout(buttonLayout);
    bottomLayout->setAlignment(buttonLayout, Qt::AlignRight | Qt::AlignBottom);

    auto mainLayout = new QVBoxLayout;
    mainLayout->setContentsMargins(0, 0, 0, 0);
    mainLayout->setSpacing(0);
    mainLayout->addWidget(mCalStepTipTitle);
    mainLayout->addLayout(centerLayout);
    mainLayout->addSpacing(2);
    mainLayout->addLayout(bottomLayout);
    setLayout(mainLayout);       
    mCalTipImage->setPixmap(QPixmap(
                                sFlowmeterImageMap.value((MACHINE_SERIAL)ConfigManager->GetConfig<int>(SystemConfigManager::MACHINE_MODEL_INDEX))));
    mCalTipImage->setContentsMargins(0, 0, 5, 0);
    mCalButton->setText(UIStrings::GetStr(STR_START));
    mCalProgressBar->SetCountTime(FlowZeroCalManage::GetInstance()->GetCalLimitTime());
    StyleSet::SetBackgroundColor(this,COLOR_WHITE_GRAY);
}

void FlowZeroCalPage::SetCalResult(QDateTime calTime, CAL_RESULT result)
{
    switch(result)
    {
    case SUCCESS:
        mCalResultTextId = STR_SUCCESS;
        StyleSet::SetTextColor(mCalResult,COLOR_LIGHT_GREEN);
        break;
    case FAIL:
        mCalResultTextId = STR_FAIL;
        StyleSet::SetTextColor(mCalResult,COLOR_RED);
        break;
    case CANCEL:
        mCalResultTextId = STR_CANCEL;
        StyleSet::SetTextColor(mCalResult,COLOR_YELLOW);
        break;
    }

    mPreCalTime->SetDateTime(calTime);
    mCalResult->setText(UIStrings::GetStr(mCalResultTextId));
}

void FlowZeroCalPage::SetStepText(QString text)
{
    mCalStepTipText->clear();
    QTextDocument *doc = mCalStepTipText->document();
    QTextCursor cursor(doc);

    QTextBlockFormat blockFormat;
    blockFormat.setAlignment(Qt::AlignTop);
    blockFormat.setTopMargin(0);
    blockFormat.setBottomMargin(3);
    blockFormat.setLeftMargin(10);
    blockFormat.setRightMargin(3);
    blockFormat.setLineHeight(3, QTextBlockFormat::LineDistanceHeight);

    QFont tempFont;
    tempFont.setFamily(FONT_FAMILY_TAHOMA);
    tempFont.setPixelSize(FONT_M);
    mCalStepTipText->setFont(tempFont);


    cursor.insertBlock(blockFormat);
    cursor.insertText(text);
    cursor.insertBlock();


    mCalStepTipText->setDocument(doc);
    mCalStepTipText->setTextCursor(cursor);

}


void FlowZeroCalPage::InitUiText()
{
    mCalStepTipTitle->setText(UIStrings::GetStr(STR_ZERO_CAL_TIP) + ":");
    SetStepText(UIStrings::GetStr(mCalStepTextId));
    mCalButton->setText(UIStrings::GetStr(mCalBtnTextId));
    mCalResultTitle->setText(UIStrings::GetStr(ALL_STRINGS_ENUM::STR_LATEST_ZERO_CAL_TIME) + ":");
    mCalResult->setText(UIStrings::GetStr(mCalResultTextId));
    mCalModuleStateTip->setText(UIStrings::GetStr(STR_FLOWMETER_STOP));
}


void FlowZeroCalPage::dispCancel()
{
    mCalStepTextId = STR_MAINMENU_FLOWCAL_TXT2;
    SetStepText(UIStrings::GetStr(STR_MAINMENU_FLOWCAL_TXT2));

    mCalBtnTextId = STR_START;
    mCalButton->setText(UIStrings::GetStr(mCalBtnTextId));

    mCalProgressBar->hide();
}

void FlowZeroCalPage::SlotOnCalBtnClicked()
{
    auto state = FlowZeroCalManage::GetInstance()->GetCalState();
    if (state != FlowZeroCalManage::FLOW_ZERO_CAL_DISABLE &&
            state != FlowZeroCalManage::FLOW_ZERO_CAL_ACTIVE)
    {
        FlowZeroCalManage::GetInstance()->StartFlowZeroCal();

        mCalStepTextId = STR_MAINMENU_FLOWCAL_TXT4;
        SetStepText(UIStrings::GetStr(STR_MAINMENU_FLOWCAL_TXT4));

        mCalBtnTextId = STR_CANCEL;
        mCalButton->setText(UIStrings::GetStr(STR_CANCEL));

        mResultBlock->hide();

        mCalProgressBar->ResetTimer();
        mCalProgressBar->show();
        mCalProgressBar->StartCount();
    }
    else
    {
        FlowZeroCalManage::GetInstance()->CancelFlowZeroCal();
        dispCancel();
    }
}

void FlowZeroCalPage::SlotRefurbishCalResult(unsigned short state)
{
    if (state == FlowZeroCalManage::FLOW_ZERO_CAL_INACTIVE)
    {
        mCalProgressBar->SetBarFinish();
        mCalProgressBar->hide();
        mCalProgressBar->ResetTimer();

        auto calResult = CalModuleManage::GetInstance()->GetLatestCalResult(CAL_TYPE::CAL_TYPE_FLOW_ZERO);
        if (calResult.first != -1)
        {
            SetCalResult(QDateTime::fromSecsSinceEpoch(calResult.first), (CAL_RESULT)calResult.second.mCalResult);
            mResultBlock->show();
        }

        mCalStepTextId = STR_MAINMENU_FLOWCAL_TXT2;
        SetStepText(UIStrings::GetStr(STR_MAINMENU_FLOWCAL_TXT2));

        mCalBtnTextId = STR_START;
        mCalButton->setText(UIStrings::GetStr(STR_START));

        mCalModuleStateTip->hide();
        mCalButton->setEnabled(true);
        
    }
    else if (state == FlowZeroCalManage::FLOW_ZERO_CAL_DISABLE)
    {
        mCalModuleStateTip->show();
        mCalButton->setEnabled(false);    
    }
}

void FlowZeroCalPage::showEvent(QShowEvent *event)
{
    emit SignalChangeCalState(true);
    FocusWidget::showEvent(event);
}

void FlowZeroCalPage::hideEvent(QHideEvent *event)
{
    auto state = FlowZeroCalManage::GetInstance()->GetCalState();
    if(state == FlowZeroCalManage::FLOW_ZERO_CAL_ACTIVE)
    {
        FlowZeroCalManage::GetInstance()->CancelFlowZeroCal();
        dispCancel();
    }
    emit SignalChangeCalState(false);
    FocusWidget::hideEvent(event);
}


