﻿#ifndef PUSH_BUTTON_H
#define PUSH_BUTTON_H

//基本按钮，处理飞梭压入消息，相当于点击按钮，并增加对富文本的支持

#include <QPushButton>
#include <QLabel>

//飞梭压入触发点击事件的按钮
class PushButton : public QPushButton
{
    Q_OBJECT
public:
    PushButton(QWidget *parent = 0);
    PushButton(const QString &text, QWidget *parent = 0);
public:
signals:
    void SignalFocusIn();
    void SignalFocusOut();
    void SignalPress();
    void SignalRelease();
protected:
    void focusInEvent(QFocusEvent *ev);
    void focusOutEvent(QFocusEvent *ev);
    void changeEvent(QEvent *event);
    void keyPressEvent(QKeyEvent * ev);
    void paintEvent(QPaintEvent *event);
protected:
    virtual void DisableStyle();
    virtual void NormalStyle();
    virtual void FocusStyle();
    virtual void PressStyle();
};

//需要支持富文本的按钮控件，暂时只有流量计定标中的几个按钮
class GasButton : public PushButton
{
    Q_OBJECT
public:
    GasButton(QWidget *parent = 0);

    GasButton(const QString &text, QWidget *parent = 0);
};

#endif // PUSH_BUTTON_H

