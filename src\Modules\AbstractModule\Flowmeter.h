#ifndef FLOWMETER_H
#define FLOWMETER_H

#include <QSerialPort>
#include "LowerProxyBase.h"
#include <QTimer>
#include <QFile>
#include "protocol/protocol.h"
#include <QString>

class Flowmeter : public LowerProxyBase
{
public:
    enum
    {
        CONTROL_NULL = 0,
        CONTROL_AIR,
        CONTROL_N2O
    };

    enum QueryState
    {
        QUERY_STATE_IDLE,
        QUERY_STATE_VERSION,
        QUERY_STATE_GAS_TYPE,
        QUERY_STATE_SENSOR_TYPE,
        QUERY_STATE_FINISHED,
        QUERY_STATE_FAILED
    };

    static Flowmeter* GetInstance();

    void FlowmeterCal(unsigned char CalType, unsigned char CalValue, unsigned short value);
    void FlowmeterValueSet(short o2Control, int assistType, short assistValue );
    void FlowmeterProportionalCal(int cmd);
    void AddFlowmeterFlowValue(int  asssistType);
    void DecFlowmeterFlowValue(int  asssistType);
    void StartFlowmeterCalZero();
    void CancelFlowmeterCalZero();
    void InitSerial();
    void CloseSerial();
    void CloseAllTechAlarm();
    bool SendQueryVersion();
    bool QueryGasType();
    bool QuerySensorType();
    void StartUpgrade();

    void StartInitQuery();

    QString GetQueriedVersion() const;
    QString GetQueriedGasType() const;
    QString GetQueriedSensorType() const;

protected slots:
    virtual void SlotReadData() override;
    void SlotProcessQuery();

protected :
    virtual  void UnpackData(int msgType,QByteArray data) override;
private:
    explicit Flowmeter(QObject *parent =NULL);
    QSerialPort mSerialPort;
    void ProcessAlamStatus(int almStatus);
    void UnpackVersion(QByteArray data);
    void UnpackGasSourceType(QString typeText);
    void UnpackSensorType(QString typeText);

    bool SendIsOpenReportMsg(bool isOpen);
    bool SendUpdateHeadPack();
    void SendUpdatePartFileData();
    void ResendUpdatePartFileData();
    bool SendUpdateFileData(UpdateData data);
    bool SendUpdateCmd(unsigned char cmd);
    void ProcessUpdateRecv();
    void ProcessRecvCmd(unsigned char cmd);
    void UpgradeFinish();

    static Flowmeter* mObject;

    QTimer* mUpgradeTimeoutTimer;
    bool mCurUpgrading = false;
    QFile mUpdateFile;
    UpdateData mLastSendData;
    int mSendedFileSize;

    QTimer* mQueryTimer;
    QueryState mQueryState = QUERY_STATE_IDLE;
    int mQueryRetryCount;
    QString mQueriedVersion;
    QString mQueriedGasType;
    QString mQueriedSensorType;
};

#endif // FLOWMETER_H
