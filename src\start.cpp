﻿#include <thread>
#include <signal.h>
#include "DBManager/DBManager.h"
#include "HistoryEventDbInterface.h"
#include "Monitoring.h"
#include "AGModule.h"
#include "Flowmeter.h"
#include "Power.h"
#include "ManageFactory.h"
#include "DeviceManageBase.h"
#include "SystemConfigManager.h"
#include "UiConfig.h"
#include "AlarmManager.h"
#include "AlarmModeManage.h"
#include "SystemSettingManager.h"
#include "DataLimitManager.h"
#include "DemoModules.h"
#include "calibration/CalModuleManage.h"
#include "LeakTestManage.h"
#include "ModuleTestManager.h"
#include "calibration/Co2CalManage.h"
#include "calibration/FlowZeroCalManage.h"
#include "calibration/o2CalManage.h"
#include "MainWindow/MainWindow.h"
#include "DeviceThread.h"
#include "DebugLogManager.h"
#include "HistoryEventManager.h"
#include "HistoryDataDialog.h"
#include "CRC.h"
#include "VTModel.h"
#include "TrendDataManager.h"
#include "Battery.h"
#include "base.h"
#include "Com/TCP/ToolCommProxy.h"
#include "UpgradeManager/UpgradeManager.h"

void InitSerial()
{
    Make_Table_8();
    Monitoring::GetInstance()->InitSerial();
    Monitoring::GetInstance()->SendQueryVersion();

    Flowmeter::GetInstance()->InitSerial();
    Flowmeter::GetInstance()->StartInitQuery();
    Power::GetInstance()->SendQueryVersion();
    AGModule::GetInstance();
}

void ProgramExit(int type)
{
    Monitoring::GetInstance()->CloseSerial();
    Flowmeter::GetInstance()->CloseSerial();
    Power::GetInstance()->CloseSerial();
    AGModule::GetInstance()->CloseSerial();
    PowerDelayOff1(type);
}

//启动时红灯亮1秒再亮黄灯，直到显示自检界面时熄灭黄灯
void StartupLedAndSound()
{
    ManageFactory::GetInstance()->GetDeviceManage()->SetLedStatu(DeviceManageBase::LED_RED, DeviceManageBase::LED_OP_CLOSE);
    ManageFactory::GetInstance()->GetDeviceManage()->SetLedStatu(DeviceManageBase::LED_YELLOW, DeviceManageBase::LED_OP_CLOSE);

    ManageFactory::GetInstance()->GetDeviceManage()->SetLedStatu(DeviceManageBase::LED_RED,DeviceManageBase::LED_OP_OPEN);
    ::std::this_thread::sleep_for(::std::chrono::seconds(1));
    ManageFactory::GetInstance()->GetDeviceManage()->SetLedStatu(DeviceManageBase::LED_RED,DeviceManageBase::LED_OP_CLOSE);
    ::std::this_thread::sleep_for(::std::chrono::milliseconds(250));
    ManageFactory::GetInstance()->GetDeviceManage()->SetLedStatu(DeviceManageBase::LED_YELLOW,DeviceManageBase::LED_OP_OPEN);
    ::std::this_thread::sleep_for(::std::chrono::seconds(1));
    ManageFactory::GetInstance()->GetDeviceManage()->MaxVolumePlay();
    ManageFactory::GetInstance()->GetDeviceManage()->SetLedStatu(DeviceManageBase::LED_YELLOW,DeviceManageBase::LED_OP_CLOSE);
}

bool CheckVersionChange()
{
    if(DBManager::GetInstance()->GetVersionIsChange())
    {
        QString versionString = QLatin1String(DEV_SOFT_VERSION);
        QStringList parts = versionString.mid(1).split('.');
        QList<int> versionNumbers;

        foreach(const QString &part, parts) {
            bool ok;
            int number = part.toInt(&ok);
            if (ok)
            {
                versionNumbers.append(number);
            } else {
               DebugLog << "版本号转换整数失败!";
               return false;
            }
        }

        HistoryEventManager::GetInstance()->AddHistoryEvent(
                    HistoryEventManager::OPERATE_EVENT,
                    LOG_UPGRADE,
                    versionNumbers[0],
                    versionNumbers[1],
                    versionNumbers[2]
                    );
    }

    return true;
}

void CheckSystemTime()
{
#ifdef ARM
    QProcess process;
    QString cmd = "cat /var/log/messages";
    QObject::connect(&process, &QProcess::errorOccurred, [&process](QProcess::ProcessError error) {
        DebugLog << "获取系统时间信息进程错误退出状态: " << error << process.errorString();
    });
    process.start(cmd);
    process.waitForFinished();
    QString res = process.readAllStandardOutput();

    QStringList lines = res.split("\n");
    res.clear();
    for (const QString& line : lines)
    {
        if ( line.contains("rtc") )
        {
            if( line.contains("low voltage") || line.contains("invalid date") )
            {
                res = line;
                DebugLog << "cmd Res " << res;
                break;
            }
        }
    }

    if(res.isEmpty())
    {
        ModuleTestManager::GetInstance()->UpdateModuleState(TEST_SYSTEM_TIME, ABLE);
    }
    else
    {
        ModuleTestManager::GetInstance()->UpdateModuleState(TEST_SYSTEM_TIME, DISABLE);
    }
#endif
}

void start()
{
    DebugLog << "当前软件版本:" << DEV_SOFT_VERSION;
    DEBUG_RUNTIME(DBManager::GetInstance()->InitDB()); //TBD 15 MS
    DEBUG_RUNTIME(SettingManager->Init());
    DEBUG_RUNTIME(ConfigManager);
    DEBUG_RUNTIME(CheckVersionChange());
    DEBUG_RUNTIME(VentModeSettingManager::GetInstance()->Init());


    DEBUG_RUNTIME(InitUiCfg());

    TrendDataManager::GetInstance()->Init();
    DEBUG_RUNTIME(HistoryEventManager::GetInstance()->AddHistoryEvent(HistoryEventManager::HISTORY_EVENT_TYPE::OPERATE_EVENT, LOG_SYS_START_ID, 0, 0));
    DEBUG_RUNTIME(CalModuleManage::GetInstance()->Init());
    DEBUG_RUNTIME(AlarmModeManage::GetInstance());
    RunModeManage::GetInstance()->ActiveRunMode(E_RUNMODE::MODE_RUN_SELFTEST);
    DEBUG_RUNTIME(AlarmManager::GetInstance());
    DEBUG_RUNTIME(ManageFactory::GetInstance()->GetDeviceManage()->Init());
    DEBUG_RUNTIME(DataLimitManager::GetInstance()->Init()); //TBD 30 MS
    DEBUG_RUNTIME(ManageFactory::GetInstance()->GetDeviceManage()->start());
    DEBUG_RUNTIME(ChartManager::GetInstance());
    DEBUG_RUNTIME(ModuleTestManager::GetInstance());
    DEBUG_RUNTIME(UIStrings::GetInstance());
    DEBUG_RUNTIME(CheckSystemTime());

    DEBUG_RUNTIME(VTModel::SetVtType(VTModel::VT_MINI));
#ifdef ARM
    ::std::thread(StartupLedAndSound).detach();
    signal(SIGINT, ProgramExit);
    signal(SIGTERM, ProgramExit);
    signal(SIGABRT, ProgramExit);
    signal(SIGSEGV, ProgramExit);
#endif

    if (DEMO_MODE == SettingManager->GetIntSettingValue(SystemSettingManager::SOFT_MODE_SETTING))
    {//演示
#ifdef ARM
        Make_Table_8();
        Power::GetInstance()->SendQueryVersion();
        Monitoring::GetInstance()->InitSerial();
        Monitoring::GetInstance()->SendQueryVersion();
#endif
        DEBUG_RUNTIME(DemoThread::GetInstance()->start());
    }
    else
    {
        DEBUG_RUNTIME(InitSerial());

        DEBUG_RUNTIME(DemoThread::GetInstance()->CreateDemoDataFile());
    }
    DEBUG_RUNTIME(ToolCommProxy::GetInstance());
#ifdef ARM
    DEBUG_RUNTIME(DeviceThread::GetInstance()->start());
#endif

    //在自检后，决定co2校零是否可用
    DEBUG_RUNTIME(O2CalManage::GetInstance()->Initialize());

    DEBUG_RUNTIME(ModuleTestManager::GetInstance()->StartModuleTest());


    DEBUG_RUNTIME(UpgradeManager::GetInstance());

    DEBUG_RUNTIME(MainWindow::GetInstance()->show());
    DEBUG_RUNTIME(DBManager::GetInstance()->MoveToThread());
}
