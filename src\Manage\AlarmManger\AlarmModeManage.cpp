﻿
#include "VentModeSettingManager.h"
#include "AlarmModeManage.h"
#include "SystemSettingManager.h"
#include "DemoModules.h"
#include "Monitoring.h"
#include "RunModeManage.h"
#define VENT_STARET_MAX_COUNT 3
//ACGO
static const QVector<E_ALARM_ID> sBanAlarmInAcgo =
{
    ALARM_PAW_LOW,
    ALARM_PAW_HIGH,
    ALARM_VTE_HIGH,
    ALARM_VTE_LOW,
    ALARM_MV_HIGH,
    ALARM_MV_LOW,
    ALARM_RATE_HIGH,
    ALARM_RATE_LOW,
    ALARM_FIO2_HIGH,
    ALARM_FIO2_LOW,
    ALARM_NEGATIVE_PRESS,
    ALARM_PRESS_HIGH_CONTINUA,
    ALARM_PRESS_LOW_CONTINUA,
    ALARM_APNEA,
    ALARM_APNEA_120,
    PROMPT_PRESSURE_LIMITD
};

//MANUAL
static const QVector<E_ALARM_ID> sBanAlarmInManual =
{
    ALARM_VTE_HIGH,
    ALARM_VTE_LOW,
    ALARM_MV_HIGH,
    ALARM_MV_LOW,
    ALARM_RATE_HIGH,
    ALARM_RATE_LOW,
    PROMPT_PRESSURE_LIMITD
};

//STANDBY
static const QVector<E_ALARM_ID> sBanAlarmInStandby =
{
    /*PHY_ALARM_BEGIN*/
    ALARM_FIO2_HIGH,    ALARM_FIO2_LOW,
    ALARM_PR_HIGH,      ALARM_PR_LOW,
    ALARM_MV_HIGH,		ALARM_MV_LOW,
    ALARM_RATE_HIGH,	ALARM_RATE_LOW,
    ALARM_VTE_HIGH,		ALARM_VTE_LOW,

    ALARM_FICO2_HIGH,	ALARM_FICO2_LOW,
    ALARM_ETCO2_HIGH,	ALARM_ETCO2_LOW,
    ALARM_FIN2O_HIGH,	ALARM_FIN2O_LOW,
    ALARM_ETN2O_HIGH,	ALARM_ETN2O_LOW,
    ALARM_SPO2_HIGH,	ALARM_SPO2_LOW,

    ALARM_FI_AGENT1_HIGH,ALARM_FI_AGENT1_LOW,
    ALARM_FI_AGENT2_HIGH,ALARM_FI_AGENT2_LOW,
    ALARM_FIHAL_HIGH,	ALARM_FIHAL_LOW,
    ALARM_FIENF_HIGH,	ALARM_FIENF_LOW,
    ALARM_FISEV_HIGH,	ALARM_FISEV_LOW,
    ALARM_FIISO_HIGH,	ALARM_FIISO_LOW,
    ALARM_FIDES_HIGH,	ALARM_FIDES_LOW,
    ALARM_ET_AGENT1_HIGH,ALARM_ET_AGENT1_LOW,
    ALARM_ET_AGENT2_HIGH,ALARM_ET_AGENT2_LOW,
    ALARM_ETHAL_HIGH,	ALARM_ETHAL_LOW,
    ALARM_ETENF_HIGH,	ALARM_ETENF_LOW,
    ALARM_ETSEV_HIGH,	ALARM_ETSEV_LOW,
    ALARM_ETISO_HIGH,	ALARM_ETISO_LOW,
    ALARM_ETDES_HIGH,	ALARM_ETDES_LOW,

    ALARM_PAW_LOW,		ALARM_PAW_HIGH,
    ALARM_APNEA,
    ALARM_APNEA_120,  //呼吸窒息超过120
    ALARM_FLOW_SENSOR_ERR,//流量传感器错误
    ALARM_PRESS_HIGH_CONTINUA,
    ALARM_PRESS_LOW_CONTINUA,
    ALARM_NEGATIVE_PRESS,
    //流量计流速监控
    ALARM_FLOW_HIGH,			// 补充总流量报警 2022-02-9
    /*PHY_ALARM_END*/
    TECHALARM_FLOW_TOO_LOW,
    PROMPT_PRESSURE_LIMITD,
};

//HLM
static const  QVector<E_ALARM_ID> sBanAlarmsInHlm =
{
    /*PHY_ALARM_BEGIN*/
    ALARM_FIO2_HIGH,    ALARM_FIO2_LOW,
    ALARM_PR_HIGH,      ALARM_PR_LOW,
    ALARM_MV_HIGH,		ALARM_MV_LOW,
    ALARM_RATE_HIGH,	ALARM_RATE_LOW,
    ALARM_VTE_HIGH,		ALARM_VTE_LOW,

    ALARM_FICO2_HIGH,	ALARM_FICO2_LOW,
    ALARM_ETCO2_HIGH,	ALARM_ETCO2_LOW,
    ALARM_FIN2O_HIGH,	ALARM_FIN2O_LOW,
    ALARM_ETN2O_HIGH,	ALARM_ETN2O_LOW,
    ALARM_SPO2_HIGH,	ALARM_SPO2_LOW,

    ALARM_FI_AGENT1_HIGH,ALARM_FI_AGENT1_LOW,
    ALARM_FI_AGENT2_HIGH,ALARM_FI_AGENT2_LOW,
    ALARM_FIHAL_HIGH,	ALARM_FIHAL_LOW,
    ALARM_FIENF_HIGH,	ALARM_FIENF_LOW,
    ALARM_FISEV_HIGH,	ALARM_FISEV_LOW,
    ALARM_FIISO_HIGH,	ALARM_FIISO_LOW,
    ALARM_FIDES_HIGH,	ALARM_FIDES_LOW,
    ALARM_ET_AGENT1_HIGH,ALARM_ET_AGENT1_LOW,
    ALARM_ET_AGENT2_HIGH,ALARM_ET_AGENT2_LOW,
    ALARM_ETHAL_HIGH,	ALARM_ETHAL_LOW,
    ALARM_ETENF_HIGH,	ALARM_ETENF_LOW,
    ALARM_ETSEV_HIGH,	ALARM_ETSEV_LOW,
    ALARM_ETISO_HIGH,	ALARM_ETISO_LOW,
    ALARM_ETDES_HIGH,	ALARM_ETDES_LOW,

    ALARM_PAW_LOW,		ALARM_PAW_HIGH,
    ALARM_APNEA,
    ALARM_APNEA_120,  //呼吸窒息超过120
    ALARM_FLOW_SENSOR_ERR,//流量传感器错误
    ALARM_PRESS_HIGH_CONTINUA,
    ALARM_PRESS_LOW_CONTINUA,
    ALARM_NEGATIVE_PRESS,
    //流量计流速监控
    ALARM_FLOW_HIGH,			// 补充总流量报警 2022-02-9

    PROMPT_PRESSURE_LIMITD
    /*PHY_ALARM_END*/
};
static const QVector<E_ALARM_ID> sBanAlarmsInVentJustStart={ //TBD 暂时不实现先该功能
                                                             //    ALARM_VTE_HIGH,
                                                             //    ALARM_VTE_LOW,
                                                             //    ALARM_MV_HIGH,
                                                             //    ALARM_MV_LOW,
                                                             //    ALARM_RATE_HIGH,
                                                             //    ALARM_RATE_LOW,
                                                           };
static const QVector<E_ALARM_ID> sBanAlarmsInGener={};
static const QVector<E_ALARM_ID> sBanAlarmsInVend=sBanAlarmsInGener;

static QVector<E_ALARM_ID> sBanAlarmsInSelfTest={};
static QVector<E_ALARM_ID> sBanAlarmsInShutDown={};
static QVector<E_ALARM_ID> DisableAlarmByModelVe[ALARM_MODE_MAX];




AlarmModeManage::AlarmModeManage()
{
    for(int i=ALARM_BEGIN;i<ALARM_END;i++)
    {
        sBanAlarmsInSelfTest<<(E_ALARM_ID)i;
        sBanAlarmsInShutDown<<(E_ALARM_ID)i;
    }
    DisableAlarmByModelVe[ALARM_MODE_IN_ACGO]=sBanAlarmInAcgo;
    DisableAlarmByModelVe[ALARM_MODE_IN_MANUAL]=sBanAlarmInManual;
    DisableAlarmByModelVe[ALARM_MODE_IN_STANDBY]=sBanAlarmInStandby;
    DisableAlarmByModelVe[ALARM_MODE_IN_VENT_JUST_START]=sBanAlarmsInVentJustStart;
    DisableAlarmByModelVe[ALARM_MODE_IN_VENT]=sBanAlarmsInVend;
    DisableAlarmByModelVe[ALARM_MODE_IN_HLM_AND_OPEN_PHY_ALARM]=sBanAlarmsInHlm;
    DisableAlarmByModelVe[ALARM_MODE_IN_SELF_TEST]=sBanAlarmsInSelfTest;
    DisableAlarmByModelVe[ALARM_MODE_IN_SHUT_DOWN]=sBanAlarmsInShutDown;
    DisableAlarmByModelVe[ALARM_MODE_GENER]=sBanAlarmsInGener;
    connect(VentModeSettingManager::GetInstance(), &VentModeSettingManager::SignalModeChanged, this, [this]{
        mRespiratoryCycleCount=0;
        UpCurAlarmModel();
    });
    connect(VentModeSettingManager::GetInstance(), static_cast<void(VentModeSettingManager::*)(E_SETTING_DATA_ID, short)>(&VentModeSettingManager::SignalSettingDataChanged), this, [this](E_SETTING_DATA_ID id, short){
        if(id==SETTINGDATA_VT||id==SETTINGDATA_RATE)
        {
            mRespiratoryCycleCount=0;
        }
        if(id==SETTINGDATA_PHY_ALARM_STATE&&
                VentModeSettingManager::GetInstance()->GetSettingdata(SETTINGDATA_VENTMODE)==VENTMODE_FLAG_HLM)
            UpCurAlarmModel();
    });

    connect(RunModeManage::GetInstance(), &RunModeManage::SignalModeChanged, this, [&](E_RUNMODE preMode, E_RUNMODE curMode){
        mRespiratoryCycleCount=0;
        UpCurAlarmModel();
    });

    connect(Monitoring::GetInstance(), &Monitoring::SignalCurWavePeriodEnd, this, [=](){
        mRespiratoryCycleCount++;
        if(mRespiratoryCycleCount>=VENT_STARET_MAX_COUNT)
        {
            if(mCurAlarmModel==ALARM_MODE_IN_VENT_JUST_START)
            {
                mCurAlarmModel=ALARM_MODE_IN_VENT;
                SetAlarmEnableForModel(ALARM_MODE_IN_VENT_JUST_START);
            }
            mRespiratoryCycleCount=0;

        }
    }, Qt::DirectConnection);

    connect(DemoThread::GetInstance(), &DemoThread::SignalCurWavePeriodEnd, this, [=](){
        mRespiratoryCycleCount++;
        if(mRespiratoryCycleCount>=VENT_STARET_MAX_COUNT)
        {
            if(mCurAlarmModel==ALARM_MODE_IN_VENT_JUST_START)
            {
                mCurAlarmModel=ALARM_MODE_IN_VENT;
                SetAlarmEnableForModel(ALARM_MODE_IN_VENT_JUST_START);
            }
            mRespiratoryCycleCount=0;
        }
    }, Qt::DirectConnection);
}

void AlarmModeManage::UpCurAlarmModel()
{
    int oldCurAlarmModel=mCurAlarmModel;
    int curRunMode = RunModeManage::GetInstance()->GetCurRunMode();
    if(curRunMode==MODE_RUN_VENT)
    {
        int curVentMode = VentModeSettingManager::GetInstance()->GetSettingdata(SETTINGDATA_VENTMODE);
        if(curVentMode==VENTMODE_FLAG_HLM)
        {
            mCurAlarmModel=ALARM_MODE_GENER;
            if(VentModeSettingManager::GetInstance()->GetPhyAlarmState()==PHY_ALARM_OFF)
                mCurAlarmModel=ALARM_MODE_IN_HLM_AND_OPEN_PHY_ALARM;
        }
        else
            mCurAlarmModel = ALARM_MODE_IN_VENT_JUST_START;
    }
    else if(curRunMode == MODE_RUN_MANUAL)
    {
        mCurAlarmModel=ALARM_MODE_IN_MANUAL;
    }
    else if(curRunMode == MODE_RUN_STANDBY)
    {
        mCurAlarmModel=ALARM_MODE_IN_STANDBY;
    }
    else if(curRunMode == MODE_RUN_ACGO)
    {
        mCurAlarmModel=ALARM_MODE_IN_ACGO;
    }
    else if(curRunMode == MODE_RUN_SELFTEST)
    {
        mCurAlarmModel=ALARM_MODE_IN_SELF_TEST;
    }
    else if(curRunMode == MODE_RUN_SHUTDOWN_DELAY)
    {
        mCurAlarmModel=ALARM_MODE_IN_SHUT_DOWN;
    }
    else if(curRunMode!=MODE_RUN_END)
        mCurAlarmModel=ALARM_MODE_GENER;
    if(oldCurAlarmModel!=mCurAlarmModel)
    {
        SetAlarmEnableForModel(oldCurAlarmModel);
    }
    if(curRunMode==MODE_RUN_VENT)
    {
        int curVentMode = VentModeSettingManager::GetInstance()->GetSettingdata(SETTINGDATA_VENTMODE);
        if(curVentMode==VENTMODE_FLAG_HLM)
            AlarmManager::GetInstance()->DisableAnAlarm(PROMPT_PRESSURE_LIMITD);
        else
            AlarmManager::GetInstance()->EnableAnAlarm(PROMPT_PRESSURE_LIMITD);
    }
    int MachineType = ConfigManager->GetConfig<int>(SystemConfigManager::MACHINE_MODEL_INDEX);
    foreach(E_ALARM_ID alarmId,disable_By_MachineType.value(((MACHINE_SERIAL)MachineType))) //TBD 补丁
    {
        AlarmManager::GetInstance()->DisableAnAlarm(alarmId);
    }
}

void AlarmModeManage::SetAlarmEnableForModel(int oldAlarmModel)
{
    bool isEnablePhy =false;
    if(oldAlarmModel != ALARM_MODE_INVALID)
    {
        for(int i=0;i<DisableAlarmByModelVe[oldAlarmModel].length();i++)
        {
            if(DisableAlarmByModelVe[oldAlarmModel][i] != TECHALARM_FLOW_TOO_LOW)
            {
                AlarmManager::GetInstance()->EnableAnAlarm(DisableAlarmByModelVe[oldAlarmModel][i]);
            }
        }
    }

    for(int i=0;i<DisableAlarmByModelVe[mCurAlarmModel].length();i++)
    {
        AlarmManager::GetInstance()->DisableAnAlarm(DisableAlarmByModelVe[mCurAlarmModel][i]);
        if(i>=PHY_ALARM_BEGIN&&i<PHY_ALARM_END&&!isEnablePhy)
            isEnablePhy=true;
    }
    if(isEnablePhy)
        AlarmManager::GetInstance()->TriggerAlarm(PROMPT_PHY_ALARM_CLOSED, true);
    else
        AlarmManager::GetInstance()->TriggerAlarm(PROMPT_PHY_ALARM_CLOSED, false);
}




