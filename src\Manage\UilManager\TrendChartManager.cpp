﻿#include "UiApi.h"
#include "TrendChartManager.h"
#include "HistoryEventManager.h"
#include "TrendDataManager.h"
#include "SystemTimeManager.h"
#include "RunModeManage.h"
#define mTrendUnitColor BG_RGB(131,129,131)

static QMap<TREND_PARAM_TYPE, TrendChartDisplaySt> sTrendDescriptors
{
    {TREND_FLOWMETER_O2,     {   BG_RGB(48,152,237), 	 BG_RGB(131,129,131),     BG_RGB(48,152,237), 	BG_RGB(48,152,237),   BG_RGB(131,129,131), BG_RGB(49,153,222)},    },
    {TREND_FLOWMETER_AIR,    {   BG_RGB(48,152,237), 	 BG_RGB(131,129,131),     BG_RGB(48,152,237), 	BG_RGB(48,152,237),   BG_RGB(131,129,131), BG_RGB(49,153,222)},    },
    {TREND_FLOWMETER_N2O,    {   BG_RGB(48,152,237),     BG_RGB(131,129,131),     BG_RGB(48,152,237), 	BG_RGB(48,152,237),   BG_RGB(131,129,131), BG_RGB(49,153,222)},    },
    {TREND_PPEAK       ,     {   BG_RGB(255,124,42),	 BG_RGB(131,129,131),	  BG_RGB(255,124,42),	BG_RGB(255,124,42),	  BG_RGB(131,129,131), BG_RGB(255,124,42)},    },
    {TREND_PPLAT       ,     {   BG_RGB(255,124,42),	 BG_RGB(131,129,131),	  BG_RGB(255,124,42),	BG_RGB(255,124,42),   BG_RGB(131,129,131), BG_RGB(255,124,42)},    },
    {TREND_PEEP       ,      {   BG_RGB(255,124,42),	 BG_RGB(131,129,131),	  BG_RGB(255,124,42),	BG_RGB(255,124,42),   BG_RGB(131,129,131), BG_RGB(255,124,42)},    },
    {TREND_PMEAN       ,     {   BG_RGB(255,124,42),	 BG_RGB(131,129,131),	  BG_RGB(255,124,42),	BG_RGB(255,124,42),   BG_RGB(131,129,131), BG_RGB(255,124,42)},    },
    {TREND_VTE         ,     {   BG_RGB(48,152,237),	 BG_RGB(48,152,237),	  BG_RGB(48,152,237),	BG_RGB(48,152,237),	  BG_RGB(131,129,131), BG_RGB(48,152,237)},    },
    {TREND_VTI         ,     {   BG_RGB(48,152,237),	 BG_RGB(48,152,237),	  BG_RGB(48,152,237),	BG_RGB(48,152,237),	  BG_RGB(131,129,131), BG_RGB(48,152,237)},    },
    {TREND_MV          ,     {   BG_RGB(48,152,237), 	 BG_RGB(48,152,237),	  BG_RGB(48,152,237),	BG_RGB(48,152,237),	  BG_RGB(131,129,131), BG_RGB(48,152,237)},    },
    {TREND_MVSPN       ,     {   BG_RGB(48,152,237), 	 BG_RGB(48,152,237),	  BG_RGB(48,152,237),	BG_RGB(48,152,237),	  BG_RGB(131,129,131), BG_RGB(48,152,237)},    },
    {TREND_RATE        ,     {   BG_RGB(83,239,106),	 BG_RGB(83,239,106),	  BG_RGB(83,239,106),	BG_RGB(83,239,106),	  BG_RGB(131,129,131), BG_RGB(83,239,106)},    },
    {TREND_FSPN        ,     {   BG_RGB(83,239,106),	 BG_RGB(83,239,106),	  BG_RGB(83,239,106),	BG_RGB(83,239,106),	  BG_RGB(131,129,131), BG_RGB(83,239,106)},    },
    {TREND_R           ,     {   BG_RGB(0, 128, 128),	 BG_RGB(0, 128, 128),	  BG_RGB(0, 128, 128),	BG_RGB(0, 128, 128),  BG_RGB(131,129,131), BG_RGB(0, 128, 128)},   },
    {TREND_C           ,     {   BG_RGB(0, 128, 128),	 BG_RGB(0, 128, 128),	  BG_RGB(0, 128, 128),	BG_RGB(0, 128, 128),  BG_RGB(131,129,131), BG_RGB(0, 128, 128)},   },
    {TREND_FIO2        ,     {   BG_RGB(81, 169, 199),	 BG_RGB(81, 169, 199),	  BG_RGB(81, 169, 199),	BG_RGB(81, 169, 199), BG_RGB(131,129,131), BG_RGB(81, 169, 199)},  },
    {TREND_FICO2      ,      {   BG_RGB(255, 255, 255),  BG_RGB(255, 255, 255),   BG_RGB(255, 255, 255),BG_RGB(255, 255, 255),BG_RGB(131,129,131), BG_RGB(255, 255, 255)}, },
    {TREND_ETCO2      ,      {   BG_RGB(255, 255, 255),  BG_RGB(255, 255, 255),   BG_RGB(255, 255, 255),BG_RGB(255, 255, 255),BG_RGB(131,129,131), BG_RGB(255, 255, 255)}, },
    {TREND_FIN2O      ,      {   BG_RGB(197,198,205),    BG_RGB(131,129,131),     BG_RGB(197,198,205),	BG_RGB(197,198,205),  BG_RGB(131,129,131), BG_RGB(172,174,172)},   },
    {TREND_ETN2O      ,      {   BG_RGB(197,198,205),    BG_RGB(131,129,131),     BG_RGB(197,198,205),	BG_RGB(197,198,205),  BG_RGB(131,129,131), BG_RGB(172,174,172)},   },
    {TREND_FIENF      ,      {   BG_RGB(255,121,0),	     BG_RGB(131,129,131),	  BG_RGB(255,121,0),	BG_RGB(255,121,0),    BG_RGB(131,129,131), BG_RGB(255,121,0)},     },
    {TREND_ETENF      ,      {   BG_RGB(255,121,0),	     BG_RGB(131,129,131),	  BG_RGB(255,121,0),	BG_RGB(255,121,0),    BG_RGB(131,129,131), BG_RGB(255,121,0)},     },
    {TREND_FIISO      ,      {   BG_RGB(226,130,210),    BG_RGB(131,129,131),     BG_RGB(226,130,210),	BG_RGB(226,130,210),  BG_RGB(131,129,131), BG_RGB(226,130,210)},   },
    {TREND_ETISO      ,      {   BG_RGB(226,130,210),    BG_RGB(131,129,131),     BG_RGB(226,130,210),	BG_RGB(226,130,210),  BG_RGB(131,129,131), BG_RGB(226,130,210)},   },
    {TREND_FISEV      ,      {   BG_RGB(217,239,0),	     BG_RGB(131,129,131),	  BG_RGB(217,239,0),	BG_RGB(217,239,0),    BG_RGB(131,129,131), BG_RGB(217,239,0)},     },
    {TREND_ETSEV      ,      {   BG_RGB(217,239,0),	     BG_RGB(131,129,131),	  BG_RGB(217,239,0),	BG_RGB(217,239,0),    BG_RGB(131,129,131), BG_RGB(217,239,0)},     },
    {TREND_FIHAL      ,      {   BG_RGB(255,0,0),	     BG_RGB(131,129,131),     BG_RGB(255,0,0),		BG_RGB(255,0,0),      BG_RGB(131,129,131), BG_RGB(255,0,0)},       },
    {TREND_ETHAL      ,      {   BG_RGB(255,0,0),	     BG_RGB(131,129,131),     BG_RGB(255,0,0),		BG_RGB(255,0,0),      BG_RGB(131,129,131), BG_RGB(255,0,0)},       },
    {TREND_FIDES      ,      {   BG_RGB(0,255,255),	     BG_RGB(131,129,131),	  BG_RGB(0,255,255),	BG_RGB(0,255,255),    BG_RGB(131,129,131), BG_RGB(0,255,255)},     },
    {TREND_ETDES      ,      {   BG_RGB(0,255,255),	     BG_RGB(131,129,131),	  BG_RGB(0,255,255),	BG_RGB(0,255,255),    BG_RGB(131,129,131), BG_RGB(0,255,255)},     },
};

static const QMap<UNIT_TYPE,QMap<int,int>> CO2_Covert=
{
    {U_PERCENT,{{30,4},
                {60,8},
                {120,15},}
    },
    {U_MMHG,{{4,30},
             {8,60},
             {15,120},}
    }
};

TrendChartManager::TrendChartManager()
{
    mCurScalesSt = sTrendYDefaultScales;
    for (int i = PRESSURE_CATEGORY; i < NONE_CATEGORY; ++i)
        SlotOnUnitChanged((CONVERTIBLE_UNIT_CATEGORY)i, U_MAX, UnitManager::GetInstance()->GetCategoryCurUnit((CONVERTIBLE_UNIT_CATEGORY)i));

    InitConnect();
}

bool TrendChartManager::RegisterTrend(TREND_PARAM_TYPE type, TrendChart *chartPtr)
{
    if (mTrendChartPtrMap[type].contains(chartPtr))
        return false;

    mTrendChartPtrMap[type] << chartPtr;
    InitTrend(type, chartPtr);

    for (auto &dataSt : TrendDataManager::GetInstance()->GetAllTrendDatas())
    {
        auto curDataMap = TrendDataManager::GetInstance()->ConvertTrendDataSt(dataSt);
        AddTrendData(type, dataSt.mRelativeTimestamp, curDataMap[type]);
    }

    return true;
}

void TrendChartManager::InitTrend(TREND_PARAM_TYPE type, BaseChart *chart)
{
    auto trendChart = dynamic_cast<TrendChart *>(chart);
    trendChart->ClearData();
    trendChart->SetYName(TrendDataManager::GetTrendName(type));
    trendChart->SetYUnit(TrendDataManager::GetTrendUnit(type));
    trendChart->SetDataDigit(TrendDataManager::GetDisplayPrecision(type));
    trendChart->SetNameColor(RGB_TO_QCOLOR(sTrendDescriptors[type].mTrendNameColor));
    trendChart->SetUnitColor(RGB_TO_QCOLOR(mTrendUnitColor));
    trendChart->SetDataLabelColor(RGB_TO_QCOLOR(sTrendDescriptors[type].mTrendDataColor));
    trendChart->SetScaleColor(RGB_TO_QCOLOR(BG_RGB(134, 134, 134)));
    trendChart->SetAxisColor(RGB_TO_QCOLOR(BG_RGB(53, 53, 53)));
    trendChart->SetCurveColor(RGB_TO_QCOLOR(sTrendDescriptors[type].mCurveColor));
    trendChart->SetYScale(mCurScalesSt[type].first());
}

void TrendChartManager::SlotOnUnitChanged(CONVERTIBLE_UNIT_CATEGORY category, UNIT_TYPE , UNIT_TYPE newType)
{
    UnitInfoSt unitInfo = UnitManager::GetInstance()->GetInfoSt(newType);
    auto unitStrId = (ALL_STRINGS_ENUM)unitInfo.mUnitStrId;
    auto digit = unitInfo.digit;

    auto datas = TrendDataManager::GetInstance()->GetAllTrendDatas();
    auto timeVec = datas.keys();

    for(int trendType = TREND_PARAM_TYPE::TREND_PARAM_TYPE_START;
        trendType <  TREND_PARAM_TYPE::TREND_PARAM_TYPE_END;
        ++trendType)
    {
        auto eTrendType = (TREND_PARAM_TYPE)trendType;
        auto relevantMonitorParam = TrendDataManager::GetRelevantMonitorParam(eTrendType);

        if (DataManager::GetParamUnitCategory(relevantMonitorParam) == category)
        {
            auto scales = sTrendYDefaultScales[eTrendType];
            auto convertFunc = UnitManager::ConvertFunctor(category, DataManager::GetDefaultUnit(relevantMonitorParam), newType);
            if( (eTrendType == TREND_FICO2 || eTrendType == TREND_ETCO2) && newType == U_MMHG)
            {
                for (auto &scale : scales)
                {
                    //手动设置CO2单位从%切换为mmHg时的范围，不使用转换函数
                    scale.mMax = CO2_Covert.value(U_MMHG).value(scale.mMax);
                    for (auto &i : scale.mDetailScale)
                    {
                        i = CO2_Covert.value(U_MMHG).value(i);
                    }
                    scale.mDisplayPrecison = digit;
                }
            }
            else
            {
                for (auto &scale : scales)
                {
                    scale.mMin = convertFunc(scale.mMin);
                    scale.mMax = convertFunc(scale.mMax);
                    scale.mDisplayPrecison = digit;
                    for (auto &i : scale.mDetailScale)
                    {
                        i = convertFunc(i);
                    }
                }
            }


            mCurScalesSt[trendType] = scales;

            //QVector<QPointF> datasVec{};
            QMap<qint64,qreal> datasVec{};
            foreach (auto timestamp, timeVec)
            {
                auto curDataMap = TrendDataManager::GetInstance()->ConvertTrendDataSt(datas[timestamp]);

                if(curDataMap[eTrendType]==INVALID_VALUE)
                {
                    datasVec.insert(timestamp, INVALID_VALUE);
                    continue;
                }

                qreal tmpData = curDataMap[eTrendType];

                auto relevantParam = TrendDataManager::GetRelevantMonitorParam(eTrendType);
                tmpData = DataManager::RevertToFloat(relevantParam, tmpData);
                if (tmpData == INVALID_VALUE)
                    continue;
                if (newType != DataManager::GetDefaultUnit(relevantMonitorParam))
                {
                    tmpData = convertFunc(tmpData);
                }
                datasVec.insert(timestamp, tmpData);
            }

            foreach (auto chart, mTrendChartPtrMap[eTrendType])
            {
                auto trendChartPtr = dynamic_cast<TrendChart *>(chart);
                trendChartPtr->SetYUnit(UIStrings::GetStr(unitStrId));
                trendChartPtr->SetDataDigit(digit);
                trendChartPtr->Replace(std::move(datasVec));
                if(eTrendType==TREND_ETCO2||eTrendType==TREND_FICO2||eTrendType==TREND_PPEAK||eTrendType==TREND_PPLAT||eTrendType==TREND_PEEP||eTrendType==TREND_PMEAN)
                    trendChartPtr->UpdateCurves();

                if (chart->GetAllData().size() != 0)
                {
                    auto scalePair = GetAppropriateScale(trendType, chart->GetAllData());
                    chart->SetYScale(scalePair.second);
                }
                else
                {
                    chart->SetYScale(mCurScalesSt[eTrendType].first());
                }
            }
        }
    }
}

QPair<ScaleSt, ScaleSt> TrendChartManager::GetAppropriateScale(int paramType, const QMap<qint64,qreal> &datas)
{
    std::unique_ptr<ScaleAdjustStrategy> p(new ScaleAdjustStrategy);
    p->SetScaleArray({}, mCurScalesSt[paramType]);

    bool hasValidData = false;
    
    for (auto ite = datas.begin(); ite != datas.end(); ite++)
    {
        if (ite.value() != INVALID_VALUE)
        {
            p->DataFilter(ite.key(), ite.value());
            hasValidData = true;
        }
    }
    
    if (!hasValidData)
    {
        return {{}, mCurScalesSt[paramType].first()};
    }
    
    p->SetTimesToSwitch(1);
    p->AdjustScale();

    return {{}, mCurScalesSt[paramType][p->GetCurScale()]};
}


QColor TrendChartManager::GetTrendColor(TREND_PARAM_TYPE type)
{
    return sTrendDescriptors[type].mCurveColor;
}

void TrendChartManager::AddTrendData(TrendDataSt dataSt)
{
    auto curDataMap = TrendDataManager::GetInstance()->ConvertTrendDataSt(dataSt);
    for(int trendType = TREND_PARAM_TYPE::TREND_PARAM_TYPE_START;
        trendType <  TREND_PARAM_TYPE::TREND_PARAM_TYPE_END;
        ++trendType)
    {
        AddTrendData((TREND_PARAM_TYPE)trendType, dataSt.mRelativeTimestamp, curDataMap[(TREND_PARAM_TYPE)trendType]);
    }
}

void TrendChartManager::AddTrendData(TREND_PARAM_TYPE type, qint64 timestamp, int data)
{
    auto eTrendType = type;
    double tmpAverData = data;
    if (tmpAverData == INVALID_VALUE)
    {
        foreach (auto chart, mTrendChartPtrMap[eTrendType])
            chart->AddData(timestamp, tmpAverData);
        return;

    }
    auto relevantParam = TrendDataManager::GetRelevantMonitorParam(eTrendType);
    tmpAverData = DataManager::RevertToFloat(relevantParam, tmpAverData);

    auto category = DataManager::GetParamUnitCategory(relevantParam);
    if (category != CONVERTIBLE_UNIT_CATEGORY::NONE_CATEGORY &&
            UnitManager::GetInstance()->GetCategoryCurUnit(category) != DataManager::GetDefaultUnit(relevantParam))
    {
        auto convertFunc = UnitManager::ConvertFunctor(category,
                                                       DataManager::GetDefaultUnit(relevantParam),
                                                       UnitManager::GetInstance()->GetCategoryCurUnit(category));
        tmpAverData = convertFunc(tmpAverData);
    }
    foreach (auto chart, mTrendChartPtrMap[eTrendType])
        chart->AddData(QDateTime::fromMSecsSinceEpoch(timestamp), tmpAverData);
}

void TrendChartManager::InitConnect()
{
    connect(UnitManager::GetInstance(), &UnitManager::SignalCategoryUnitChanged, this, &TrendChartManager::SlotOnUnitChanged);
    connect(TrendDataManager::GetInstance(), &TrendDataManager::SignalDataAdded, this,  [this](){
        DEBUG_RUNTIME(AddTrendData(TrendDataManager::GetInstance()->GetCurTrendDataSt()));
    });
    connect(UIStrings::GetInstance(), &UIStrings::SignalLanguageChanged, this, [this]{
        for(int trendType = TREND_PARAM_TYPE::TREND_PARAM_TYPE_START;
            trendType <  TREND_PARAM_TYPE::TREND_PARAM_TYPE_END;
            ++trendType)
        {
            auto eTrendType = (TREND_PARAM_TYPE)trendType;
            foreach (auto chart, mTrendChartPtrMap[eTrendType])
            {
                chart->SetYName(TrendDataManager::GetTrendName(eTrendType));
                chart->SetYUnit(TrendDataManager::GetTrendUnit(eTrendType));
            }
        }
    });
}

void TrendChartManager::ClearData()
{
    for(int trendType = TREND_PARAM_TYPE::TREND_PARAM_TYPE_START;
        trendType <  TREND_PARAM_TYPE::TREND_PARAM_TYPE_END;
        ++trendType)
    {
        auto eTrendType = (TREND_PARAM_TYPE)trendType;
        foreach (auto chart, mTrendChartPtrMap[eTrendType])
        {
            chart->ClearData();
        }
    }
}
