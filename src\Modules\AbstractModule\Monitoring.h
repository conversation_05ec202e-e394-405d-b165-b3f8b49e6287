#ifndef MONITORING_H
#define MONITORING_H

#include <QSerialPort>
#include "LowerProxyBase.h"
#include "VentModeSettingManager.h"
#include "../Com/Qt4SerialPort/qextserialbase.h"

class Monitoring : public LowerProxyBase
{
    Q_OBJECT
public:
    static Monitoring* GetInstance();

    bool SendMonitoringSetting(T_MASTER_SET);
    bool SendO2CalCmd(int cmd);
    bool SendSelfChekCmd(int cmd);
    bool SendQueryVersion();
    bool SendIsOpenHeat(bool isOpen);
    bool SendOnePack(int packType,int msgID,unsigned char MsgLen, const unsigned char* Msg);
    bool SendStartCalLowFlow();
    bool SendStartCalHightFlow();
    bool SendStartCalPressure();
    bool SendCancelCalFowPressure();
    bool SendFlowValue(int calPoint,int value);
    void InitSerial();
    void CloseSerial();

    void StartUpgrade();
signals:
    void SignalCurWavePeriodEnd();
    void SignalUpgradeFinish();
protected:
    virtual void UnpackData(int msgType,QByteArray data);

private:
    explicit Monitoring(QObject *parent =NULL);
    void UnpackO2Cal(QByteArray data);
    void UnpackWaveData(QByteArray data);
    void UnpackValueData(QByteArray data);
    void UnpackAlarm(QByteArray data);
    void UnpackLeakTest(QByteArray data);
    void UnpackVersion(QByteArray data);
    void UnpackFlowCal(QByteArray data);
    void UnpackPressureCal(QByteArray data);

    bool WaitSerialRespon(bool isNeedCheckValue = false);
    bool WriteDataToSerial(PROT_DATA * pdata);
    void CrcMonitorPack(MONITOR_UPDATE_PACK *pack);
private:
    int mCurCalId{};
    QSerialPort mSerialPort;
    QextSerialBase* mGsSerial = NULL;

    QString mGasFilePath = "/usr/local/Update/GasControl.bin";
};

#endif // MONITORING_H
