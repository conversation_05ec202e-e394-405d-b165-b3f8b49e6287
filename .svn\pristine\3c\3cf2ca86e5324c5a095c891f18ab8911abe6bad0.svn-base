﻿#include <QKeyEvent>

#include "UiApi.h"
#include "MainWindow/MainWindow.h"
#include "../MyApplication.cpp"
#include "SettingsMenu.h"
#include "TopTip/MessageBoard.h"
#include "ModeSetTabWidget.h"
#include "AlarmMenu.h"
#include "AlarmManager.h"
#include "ControlButtonManager.h"
#include "RunModeManage.h"
#include "EngneerMenu.h"
#include "StandByPage/StandbyPage.h"
#include "MainMenu/CalibrationMenu/CalibrationMenu.h"
#include "BytteryUiFlush.h"
#include "TopTip/SysInfoBoard.h"
#include "FlowControlDialog.h"
#include "DebugView/FlowerDebugDialog.h"
#include "HotKeyArea/HotKeyWidget.h"
#include "AgView.h"
#include "PoweroffConfirmDlg.h"
#include "ChartManager.h"
#include "UiConfig.h"
#include "MonitorParamView.h"
#include "HistoryDataDialog.h"
#include "MainDisplayWidget.h"
#include "CfgButton.h"
#include "PowerOnSelfTest/SelftestDlg.h"
#include "SystemSoundManager.h"
#include "HlmModeManager.h"
#include "VentModeSettingManager.h"
#include "FlowmeterView.h"
#include "TipDialog.h"
#include "ModuleTestManager.h"
#include "AGModule.h"
#include "UpgradeManager/UpgradeManager.h"
#include "SpO2ParamLabel.h"
#include "WaveChart.h"

//#define SHOW_FPS_LABEL
//#define SHOW_AUTO_CLOS_DIALOG_LABEL

#define BORDER_WIDTH (2)
#define DLG_FLOWMETER_CONTROL_WIDTH 545
#define DLG_FLOWMETER_CONTROL_HEIGHT 517
#define STANDBY_CONFIRM_Y_OFFSET -100

#define SPO2_PARAM_HEIGHT 80
extern QString StaticFpsLog;

constexpr int CONTROL_MARGIN = 2;



QMap<MainWindow::MAINWINDOW_COMPONENT_ENUM, QSize> sControlSizeMinimum
{
    {MainWindow:: MESSAGE_BOARD,                      {1016, 55}},
    {MainWindow:: PARAMLIST,                          {212, 574}},
    {MainWindow:: HOT_KEY_LIST,                       {92 , 574}},
    {MainWindow:: GRAPH,                              {502, 574}},
    {MainWindow:: MODESET,                            {812, 130}},
    {MainWindow:: AGVIEW,                             {187, 190}},
    {MainWindow:: POPUP,                              {735, 517}},
    {MainWindow:: TIPS_DIALOG,                        {350, 200}},
};

MainWindow::MainWindow(QWidget *parent) :
    FocusWidget(parent)
{
    DEBUG_RUNTIME(InitUi());
    DEBUG_RUNTIME(InitConnect());
    DEBUG_RUNTIME(InitUiText());
#ifdef WIN32
    setWindowIcon(QIcon(":/Item/logo.png"));
#endif
}

void MainWindow::CleanUp()
{
    SystemTimeManager::GetInstance()->StopSystemTimer();

    DemoThread::GetInstance()->requestInterruption();
    DemoThread::GetInstance()->quit();
    DemoThread::GetInstance()->wait();
}

void MainWindow::InitUi()
{
    setCursor(Qt::ArrowCursor);
    mAllDialog.resize(MainWindow::DLG_MAX_NUM);

    QPalette pal = palette();
    pal.setColor(QPalette::Window, COLOR_BLACK_GRAY);
    setAutoFillBackground(true);
    setPalette(pal);
    setFixedSize(DESKTOP_WIDTH, DESKTOP_HEIGHT);

    DEBUG_RUNTIME(mMessageBoard = new MessageBoard(this));

    DEBUG_RUNTIME(mMainDisplayWidget = new MainDisplayWidget(this));
    DEBUG_RUNTIME(mMonitorParam = new MonitorParamView(this));
    DEBUG_RUNTIME(mModeWidget = new ModeSetTabWidget(this));
    DEBUG_RUNTIME(mAgView = new AgView);
    DEBUG_RUNTIME(mFlowmeterWidget = new FlowmeterView);
    DEBUG_RUNTIME(mHotKeyWidget = new HotKeyWidget(this));
    DEBUG_RUNTIME(mSpo2ParamLabel = new Spo2ParamLabel(this));
    DEBUG_RUNTIME(mSpo2Wave = new WaveChart(this));

    mSpo2ParamLabel->SetLabelScale(ParamLabelLayoutHorizontal::LABEL_SCALE_ENUM::MID);
    mSpo2ParamLabel->setFixedWidth(sControlSizeMinimum[MAINWINDOW_COMPONENT_ENUM::PARAMLIST].width());
    mSpo2ParamLabel->setFixedHeight(SPO2_PARAM_HEIGHT);
    mSpo2ParamLabel->setVisible(false);

    mSpo2Wave->setMaximumHeight(SPO2_PARAM_HEIGHT);
    ChartManager::GetInstance()->RegisterChart(ChartManager::CHART_TYPE_ENUM::WAVE_CHART, DataManager::WAVE_PLETH, mSpo2Wave);
    mSpo2Wave->setVisible(false);
    mSpo2Wave->SetYUnit("");

    ParamLabelManage::GetInstance()->RegisterParamView(VALUEDATA_SPO2, mSpo2ParamLabel);

    if(!ConfigManager->IsFullTouch())
        mHotKeyWidget->setVisible(false);
    mHotKeyWidget->setMinimumSize(sControlSizeMinimum[MAINWINDOW_COMPONENT_ENUM::HOT_KEY_LIST]);
    mMessageBoard->setMinimumSize(sControlSizeMinimum[MAINWINDOW_COMPONENT_ENUM::MESSAGE_BOARD]);
    mMonitorParam->setFixedSize(sControlSizeMinimum[MAINWINDOW_COMPONENT_ENUM::PARAMLIST]);
    mMainDisplayWidget->setFixedHeight(sControlSizeMinimum[MAINWINDOW_COMPONENT_ENUM::GRAPH].height());

    mModeWidget->setMinimumSize(sControlSizeMinimum[MAINWINDOW_COMPONENT_ENUM::MODESET]);
    mAgView->setFixedHeight(sControlSizeMinimum[MAINWINDOW_COMPONENT_ENUM::AGVIEW].height());
    mAgView->setFixedWidth(sControlSizeMinimum[MAINWINDOW_COMPONENT_ENUM::AGVIEW].width());
    mFlowmeterWidget->setFixedWidth(sControlSizeMinimum[MAINWINDOW_COMPONENT_ENUM::AGVIEW].width());

    int machineModelIndex = ConfigManager->GetConfig<int>(SystemConfigManager::MACHINE_MODEL_INDEX);
    if((machineModelIndex == CENAR_30M || (machineModelIndex != ELUNA_80 && !ConfigManager->GetConfig<bool>(SystemConfigManager::ENABLE_FLOWMETER_MODULE))) &&
            ModuleTestManager::GetInstance()->GetAgModuleType() != ModuleTestManager::AG &&
            !ConfigManager->GetConfig<bool>(SystemConfigManager::LOOP_BOOL_INDEX) )
    {
        mAgView->setVisible(false);
        mFlowmeterWidget->setVisible(false);
    }

    DEBUG_RUNTIME(mPowerOffDlg = new PoweroffConfirmDlg);
    mPowerOffDlg->setFixedSize(size());
    mPowerOffDlg->hide();
    mPowerOffDlg->move(0, 0);
    mPowerOffDlg->setWindowFlags(Qt::Dialog|Qt::FramelessWindowHint);
    mPowerOffDlg->setWindowModality(Qt::ApplicationModal);
    if(ConfigManager->IsFullTouch() && ConfigManager->IsFullElecFlomter())
    {
        auto mainDisplayLayout = new QHBoxLayout;
        mainDisplayLayout->setMargin(0);
        mainDisplayLayout->setContentsMargins(0, 0, 0, 0);
        mainDisplayLayout->setSpacing(CONTROL_MARGIN);
        mainDisplayLayout->addWidget(mMainDisplayWidget);
        mainDisplayLayout->addWidget(mMonitorParam);

        auto minorDisplayLayout = new QHBoxLayout;
        minorDisplayLayout->setMargin(0);
        minorDisplayLayout->setContentsMargins(0, 0, 0, 0);
        minorDisplayLayout->setSpacing(CONTROL_MARGIN);
        minorDisplayLayout->addWidget(mSpo2Wave);
        minorDisplayLayout->addWidget(mSpo2ParamLabel);
        minorDisplayLayout->setAlignment(mSpo2ParamLabel, Qt::AlignRight);

        auto midDisplayLayout = new QVBoxLayout;
        midDisplayLayout->setMargin(0);
        midDisplayLayout->setContentsMargins(0, 0, 0, 0);
        midDisplayLayout->setSpacing(CONTROL_MARGIN);
        midDisplayLayout->addLayout(mainDisplayLayout);
        midDisplayLayout->addLayout(minorDisplayLayout);

        auto midLayout = new QHBoxLayout;
        midLayout->setMargin(0);
        midLayout->setContentsMargins(0, 0, 0, 0);
        midLayout->setSpacing(CONTROL_MARGIN);
        midLayout->addLayout(midDisplayLayout);
        midLayout->addWidget(mHotKeyWidget);

        auto rightLayout = new QVBoxLayout;
        rightLayout->setMargin(0);
        rightLayout->setContentsMargins(0, 0, 0, 0);
        rightLayout->setSpacing(CONTROL_MARGIN);
        rightLayout->addLayout(midLayout);
        rightLayout->addWidget(mModeWidget);

        auto leftLayout = new QVBoxLayout;
        leftLayout->setMargin(0);
        leftLayout->setContentsMargins(0, 0, 0,0);
        leftLayout->setSpacing(2);
        leftLayout->addWidget(mAgView);
        leftLayout->addWidget(mFlowmeterWidget);

        auto bottomLayout = new QHBoxLayout;
        bottomLayout->setMargin(0);
        bottomLayout->setContentsMargins(CONTROL_MARGIN, 0, CONTROL_MARGIN, CONTROL_MARGIN);
        bottomLayout->setSpacing(CONTROL_MARGIN);
        bottomLayout->addLayout(leftLayout);
        bottomLayout->addLayout(rightLayout);

        auto mainLayout = new QVBoxLayout;
        mainLayout->setMargin(0);
        mainLayout->setContentsMargins(0, 0, 0, 0);
        mainLayout->setSpacing(CONTROL_MARGIN);
        mainLayout->addWidget(mMessageBoard);
        mainLayout->addLayout(bottomLayout);

        setLayout(mainLayout);
    }
    else
    {
        auto leftLayout = new QVBoxLayout;
        leftLayout->setMargin(0);
        leftLayout->setContentsMargins(0, 0, 0,0);
        leftLayout->setSpacing(2);
        leftLayout->addWidget(mAgView);
        leftLayout->addWidget(mFlowmeterWidget);


        auto mainDisplayLayout = new QHBoxLayout;
        mainDisplayLayout->setMargin(0);
        mainDisplayLayout->setContentsMargins(0, 0, 0, 0);
        mainDisplayLayout->setSpacing(CONTROL_MARGIN);
        mainDisplayLayout->addWidget(mMainDisplayWidget);
        mainDisplayLayout->addWidget(mMonitorParam);

        auto minorDisplayLayout = new QHBoxLayout;
        minorDisplayLayout->setMargin(0);
        minorDisplayLayout->setContentsMargins(0, 0, 0, 0);
        minorDisplayLayout->setSpacing(CONTROL_MARGIN);
        minorDisplayLayout->addWidget(mSpo2Wave);
        minorDisplayLayout->addWidget(mSpo2ParamLabel);
        minorDisplayLayout->setAlignment(mSpo2ParamLabel, Qt::AlignRight);

        auto midDisplayLayout = new QVBoxLayout;
        midDisplayLayout->setMargin(0);
        midDisplayLayout->setContentsMargins(0, 0, 0, 0);
        midDisplayLayout->setSpacing(CONTROL_MARGIN);
        midDisplayLayout->addLayout(mainDisplayLayout);
        midDisplayLayout->addLayout(minorDisplayLayout);

        auto midLayout = new QHBoxLayout;
        midLayout->setMargin(0);
        midLayout->setContentsMargins(0, 0, 0, 0);
        midLayout->setSpacing(CONTROL_MARGIN);
        midLayout->addLayout(leftLayout);
        midLayout->addLayout(midDisplayLayout);
        midLayout->addWidget(mHotKeyWidget);


        auto mainLayout = new QVBoxLayout;
        mainLayout->setMargin(0);
        mainLayout->setContentsMargins(0, 0, 0, 0);
        mainLayout->setSpacing(CONTROL_MARGIN);
        mainLayout->addWidget(mMessageBoard);
        mainLayout->addLayout(midLayout);
        mainLayout->addWidget(mModeWidget);

        setLayout(mainLayout);
    }
    OpenFocusCycle(true);
#ifdef SHOW_FPS_LABEL
    mFpsLabel= new QLabel(this);
    mFpsLabel->setObjectName("FPSLABEL");
    mFpsLabel->setStyleSheet("background-color : balck; color: white;");
    mFpsLabel->move(0,0);
    mFpsLabel->raise();
    mFpsLabel->show();
    QTimer* rufushFpsTimer = new QTimer(this);
    rufushFpsTimer->connect(rufushFpsTimer,&QTimer::timeout,rufushFpsTimer,[=](){
        mFpsLabel->setText(StaticFpsLog);
    });
    rufushFpsTimer->start(500);
#endif
#ifdef SHOW_AUTO_CLOS_DIALOG_LABEL
    mAutoCloseDialogCountLabel= new QLabel(this);
    mAutoCloseDialogCountLabel->setStyleSheet("background-color : balck; color: white;");
    mAutoCloseDialogCountLabel->move(100,0);
    mAutoCloseDialogCountLabel->raise();
    mAutoCloseDialogCountLabel->show();
#endif

    for(int i =0;i<DLG_MAX_NUM;i++)
    {
        MakeDialog(i);
        mAllDialog[i]->hide();
    }

}

MainWindow *MainWindow::GetInstance()
{
    static MainWindow sInstance;
    return &sInstance;
}

void MainWindow::show()
{
    ((MyApplication*)QApplication::instance())->setIsCloseKeyEvent(true);
    ((MyApplication*)QApplication::instance())->SetIsCloseMouseEvent(true);
    FocusWidget::show();
    if(ConfigManager->IsFullTouch())
        mHotKeyWidget->show();
    activateWindow();
    DEBUG_RUNTIME(showDialog(DLG_STANDBY));
    DEBUG_RUNTIME(showDialog(MainWindow::DLG_SYSTEM_SELFTEST));
    ((MyApplication*)QApplication::instance())->setIsCloseKeyEvent(false);
    ((MyApplication*)QApplication::instance())->SetIsCloseMouseEvent(false);
}

void MainWindow::handleKey(int key)
{
    if (RunModeManage::GetInstance()->GetCurRunMode() == MODE_RUN_SELFTEST ||
            RunModeManage::GetInstance()->GetCurRunMode() == MODE_RUN_SHUTDOWN_DELAY || mIsBlockDialogShow)
        return;
    switch(key)
    {
    case KEY_ENGNEER_DLG:
    {
        showDialog(DLG_ENGNEER_CAL);
    }
        break;
    case KEY_SOFT_CONFIG_DLG:
    {
    }
        break;
    case KEY_PRODUCT_CONFIG_DLG:
    {
        //showDialog(DLG_PRODUCT_CFG);
    }
        break;
    case KEY_MAIN_MENU:
        //显示主菜单
        showDialog(DLG_SYS_SETTING_MENU);
        //        mStackedLayout->setCurrentIndex(1);
        break;
    case KEY_ALARM_MENU:
        //显示报警菜单
        showDialog(DLG_ALARM_MENU);
        break;
    case KEY_SYS_TIMER:
    {
        for (int i = 0; i < DLG_MAX_NUM; ++i)
        {
            if (DLG_STANDBY_CONFIRM == i ||
                    DLG_SYS_SETTING_MENU == i ||
                    DLG_ALARM_MENU == i ||
                    DLG_ENGNEER_CAL == i ||
                    DLG_DEBUG_VIEW == i ||
                    DLG_HISTORY_DATA == i ||
                    DLG_SYSTEM_SELFTEST == i ||
                    DLG_FLOWMETER_CONTROL ==i )
            {
                if ( mAllDialog[i]!=nullptr && mAllDialog[i]->isVisible())
                    hideDialog(i);
            }
        }
        mMessageBoard->ShowTimePopup();
    }
        break;
    case KEY_MAIN_WINDOW:
        showDialog(DLG_HISTORY_DATA);
        break;
    case KEY_SOUND:
    {
        bool isOpenSound = AlarmManager::GetInstance()->isOpenAlarmSound();
        if(isOpenSound)
            AlarmManager::GetInstance()->MuteAlarmSound();
        else
            AlarmManager::GetInstance()->OpenAlarmSound();
    }

        break;
    case KEY_FREEZE:
        if (!RunModeManage::GetInstance()->IsModeActive(MODE_RUN_STANDBY))
        {
            ChartManager::GetInstance()->SetFreeze(!ChartManager::GetInstance()->GetIsFreeze());
        }
        break;
    case KEY_STANDBY_CONFIRM:
        if (!RunModeManage::GetInstance()->IsModeActive(MODE_RUN_STANDBY))
        {
            showDialog(DLG_STANDBY_CONFIRM);
        }
        break;
    case KEY_DEBUG_WINDOW:
        showDialog(DLG_DEBUG_VIEW);
        break;
    default:
        break;
    }
}

//各个popup对话框的按键事件在这里处理
bool MainWindow::eventFilter(QObject *obj, QEvent *ev)
{
    if( (mAllDialog.contains((QWidget*)obj) || mMessageBoard->IsTimePopup(obj))
            && ev->type()==QEvent::Show)
    {
        mHotKeyWidget->SetKeyEnabled(ControlButtonManager::STANDBY, false);
    }

    if( (mAllDialog.contains((QWidget*)obj) || mMessageBoard->IsTimePopup(obj))
            && ev->type()==QEvent::Hide)
    {
        if(RunModeManage::GetInstance()->GetCurRunMode() != MODE_RUN_STANDBY)
        {
            mHotKeyWidget->SetKeyEnabled(ControlButtonManager::STANDBY, true);
        }

        if(obj==mAllDialog[DLG_DEBUG_VIEW])
            ((MyApplication*)QApplication::instance())->setIsCloseKeyEvent(false);
    }

    if(obj==this&&ev->type()==QEvent::Move)
    {
        if(mHotKeyWidget!=NULL)
            mHotKeyWidget->UpPoint();
        mMessageBoard->UpPointALarmBox();
    }

    if (QEvent::KeyPress == ev->type())
    {
        if (KEY_ANTI_CLOSEWISE != ((QKeyEvent *)ev)->key() &&
                KEY_CLOSEWISE != ((QKeyEvent *)ev)->key() &&
                KEY_PRESS != ((QKeyEvent *)ev)->key())
        {//增加对各个菜单功能键处理
            handleKey(((QKeyEvent *)ev)->key());
            return true;
        }
    }

    return FocusWidget::eventFilter(obj, ev);
}

void MainWindow::showEvent(QShowEvent *ev)
{
    if(mAgView->IsAgLoopEnable())
        mAgView->setFocusPolicy(Qt::StrongFocus);
    FocusWidget::showEvent(ev);
}

void MainWindow::showDialog(unsigned short ctrl)
{
    if((mAllDialog.value(ctrl,NULL)!=nullptr&&mAllDialog[ctrl]->isVisible()) || mIsBlockDialogShow)
        return;

    MakeDialog(ctrl);
    int ctrlDisplayOrigX =  this->geometry().bottomLeft().x();
    int ctrlDisplayOrigY = this->geometry().bottomLeft().y();

    for (int i = 0; i < DLG_MAX_NUM; ++i)
    {
        if (DLG_STANDBY_CONFIRM == i ||
                DLG_SYS_SETTING_MENU == i ||
                DLG_ALARM_MENU == i ||
                DLG_ENGNEER_CAL == i ||
                DLG_DEBUG_VIEW == i ||
                DLG_HISTORY_DATA == i ||
                DLG_SYSTEM_SELFTEST == i ||
                DLG_FLOWMETER_CONTROL == i )
        {
            if ( (ctrl != i && mAllDialog[i]!=nullptr&&mAllDialog[i]->isVisible()) || mMessageBoard->IsTimePopupVisable() )
            {
                if(ctrl==DLG_STANDBY_CONFIRM)
                    return;
                if(mMessageBoard->IsTimePopupVisable())
                    mMessageBoard->CloseTimePopup();
                else
                    hideDialog(i);
            }
        }
    }
    switch (ctrl)
    {
    case DLG_SYS_SETTING_MENU:
    case DLG_ALARM_MENU:
    case DLG_ENGNEER_CAL:
    case DLG_DEBUG_VIEW:
    case DLG_HISTORY_DATA:
    {
        mAllDialog[ctrl]->setWindowFlags(Qt::Dialog | Qt::FramelessWindowHint);
        if(DLG_DEBUG_VIEW ==ctrl)
            ((MyApplication*)QApplication::instance())->setIsCloseKeyEvent(true);
        mAllDialog[ctrl]->move(ctrlDisplayOrigX, ctrlDisplayOrigY -  mAllDialog[ctrl]->height());
        mAllDialog[ctrl]->setWindowModality(Qt::WindowModal);
        mAllDialog[ctrl]->show();
        mAllDialog[ctrl]->setFocus();
    }
        break;
    case DLG_FLOWMETER_CONTROL:
    {
        mAllDialog[ctrl]->setWindowFlags(Qt::Dialog | Qt::FramelessWindowHint);
        mAllDialog[ctrl]->move(ctrlDisplayOrigX + mFlowmeterWidget->width(), ctrlDisplayOrigY -  mAllDialog[ctrl]->height());
        mAllDialog[ctrl]->setWindowModality(Qt::WindowModal);
        mAllDialog[ctrl]->show();
        mAllDialog[ctrl]->setFocus();
    }
        break;
    case DLG_SYSTEM_SELFTEST:
    {
        mAllDialog[ctrl]->move(mapToGlobal(QPoint(0, mMessageBoard->height())));
        mAllDialog[ctrl]->show();
    }
        break;
    case DLG_STANDBY_CONFIRM:
    {
        mAllDialog[ctrl]->show();
        QPoint windowCenterGlobal = this->geometry().center();
        int dialogX = windowCenterGlobal.x() - mAllDialog[ctrl]->width() / 2;
        int dialogY = windowCenterGlobal.y() - mAllDialog[ctrl]->height() / 2 + STANDBY_CONFIRM_Y_OFFSET;
        mAllDialog[ctrl]->move(dialogX, dialogY);
    }
        break;
    case DLG_STANDBY:
    {
        mMessageBoard->UpPointALarmBox();
        mAllDialog[ctrl]->setFixedWidth(mMainDisplayWidget->width() + mMonitorParam->width() + BORDER_WIDTH);
        mAllDialog[ctrl]->move(mMainDisplayWidget->geometry().topLeft());
        mAllDialog[ctrl]->show();
        mAllDialog[ctrl]->setFocus();
        mMainDisplayWidget->setEnabled(false);
        break;
    }

    case DLG_NONE:
    {//不影响standby对话框的状态
        for (int i = 0; i < DLG_MAX_NUM; ++i)
        {
            if (DLG_STANDBY == i)
            {
                continue;
            }
            if (mAllDialog[i]!=nullptr&&mAllDialog[i]->isVisible())
            {
                mAllDialog[i]->hide();
                mMainDisplayWidget->activateWindow();
                mMainDisplayWidget->setFocus();
            }
        }
    }
    default:
        break;
    }
    mAutoExitEditTime.start(AUTO_EXIT_TIME);
#ifdef SHOW_AUTO_CLOS_DIALOG_LABEL
    mAutoCloseDialogCountLabel->setText(QString::number(AUTO_EXIT_TIME/1000));
    mAutoCloseCountTime.start(1000);
#endif
}

void MainWindow::hideDialog(unsigned short ctrl)
{
    DebugAssert(0 <= ctrl && ctrl < DLG_MAX_NUM);

    mAllDialog[ctrl]->hide();
    /*    if(ctrl==DLG_ALARM_MENU)
        ControlButtonManager::GetInstance()->setControlButtonState(ControlButtonManager::ALARM_MENU,ControlButtonManager::NORMAL);
    else if(ctrl == DLG_SYS_SETTING_MENU)
        ControlButtonManager::GetInstance()->setControlButtonState(ControlButtonManager::SETTING,ControlButtonManager::NORMAL);
    else if(ctrl == DLG_HISTORY_DATA )
        ControlButtonManager::GetInstance()->setControlButtonState(ControlButtonManager::HISTORY_DATA,ControlButtonManager::NORMAL);
    else*/ if(ctrl == DLG_STANDBY)
    {
        mMainDisplayWidget->setEnabled(true);
        mMessageBoard->resetTimer();
    }
    setFocus();
    mAutoExitEditTime.stop();
#ifdef SHOW_AUTO_CLOS_DIALOG_LABEL
    mAutoCloseDialogCountLabel->clear();
    mAutoCloseCountTime.stop();
#endif
}

void MainWindow::SetHistoryDialogPage(int index)
{
    if(index>=0&&mAllDialog[DLG_HISTORY_DATA])
        qobject_cast<HistoryDataDialog *>(mAllDialog[DLG_HISTORY_DATA])->setCurPage(index);
}

void MainWindow::InitUiText(int initDialogType)
{
    if(initDialogType==DLG_NONE)
    {
        mMessageBoard->InitUiText();
        mModeWidget->InitUiText();
        mMonitorParam->InitUiText();
        mAgView->InitUiText();
        if (mHotKeyWidget)
            mHotKeyWidget->InitUiText();
        mFlowmeterWidget->InitUiText();
        mMainDisplayWidget->InitUiText();
        mPowerOffDlg->InitUiText();
    }
    else if(initDialogType == DLG_ALL)
    {
        mMessageBoard->InitUiText();
        mModeWidget->InitUiText();
        mMonitorParam->InitUiText();
        mAgView->InitUiText();
        if (mHotKeyWidget)
            mHotKeyWidget->InitUiText();
        mFlowmeterWidget->InitUiText();
        mMainDisplayWidget->InitUiText();
        mPowerOffDlg->InitUiText();
        if(mAllDialog[DLG_STANDBY]!=nullptr)
            qobject_cast<StandbyPage *>(mAllDialog[DLG_STANDBY])->InitUiText();
        if(mAllDialog[DLG_SYS_SETTING_MENU]!=nullptr)
            qobject_cast<SettingsMenu *>(mAllDialog[DLG_SYS_SETTING_MENU])->InitUiText();
        if(mAllDialog[DLG_ALARM_MENU]!=nullptr)
            qobject_cast<AlarmMenu *>(mAllDialog[DLG_ALARM_MENU])->InitUiText();
        if(mAllDialog[DLG_ENGNEER_CAL]!=nullptr)
            qobject_cast<EngneerMenu *>(mAllDialog[DLG_ENGNEER_CAL])->InitUiText();
        if(mAllDialog[DLG_SYSTEM_SELFTEST]!=nullptr)
            qobject_cast<SelftestDlg *>(mAllDialog[DLG_SYSTEM_SELFTEST])->InitUiText();
        if(mAllDialog[DLG_HISTORY_DATA]!=nullptr)
            qobject_cast<HistoryDataDialog *>(mAllDialog[DLG_HISTORY_DATA])->InitUiText();
        if(mAllDialog[DLG_FLOWMETER_CONTROL]!=nullptr)
            qobject_cast<FlowControlDialog *>(mAllDialog[DLG_FLOWMETER_CONTROL])->InitUiText();
        if(mAllDialog[DLG_STANDBY_CONFIRM]!=nullptr)
        {
            qobject_cast<TipDialog *>(mAllDialog[DLG_STANDBY_CONFIRM])->SetDialogText(UIStrings::GetStr(STR_STANDBY_STRING1));
            qobject_cast<TipDialog *>(mAllDialog[DLG_STANDBY_CONFIRM])->InitUiText();
        }
    }
    else
    {
        switch (initDialogType) {
        case DLG_STANDBY_CONFIRM:
            if(mAllDialog[DLG_STANDBY_CONFIRM]!=nullptr)
            {
                qobject_cast<TipDialog *>(mAllDialog[DLG_STANDBY_CONFIRM])->SetDialogText(UIStrings::GetStr(STR_STANDBY_STRING1));
                qobject_cast<TipDialog *>(mAllDialog[DLG_STANDBY_CONFIRM])->InitUiText();
            }
            break;
        case DLG_STANDBY:
            if(mAllDialog[DLG_STANDBY]!=nullptr)
                qobject_cast<StandbyPage *>(mAllDialog[DLG_STANDBY])->InitUiText();
            break;
        case DLG_SYS_SETTING_MENU:
            if(mAllDialog[DLG_SYS_SETTING_MENU]!=nullptr)
                qobject_cast<SettingsMenu *>(mAllDialog[DLG_SYS_SETTING_MENU])->InitUiText();
            break;
        case DLG_ALARM_MENU:
            if(mAllDialog[DLG_ALARM_MENU]!=nullptr)
                qobject_cast<AlarmMenu *>(mAllDialog[DLG_ALARM_MENU])->InitUiText();
            break;
        case DLG_ENGNEER_CAL:
            if(mAllDialog[DLG_ENGNEER_CAL]!=nullptr)
                qobject_cast<EngneerMenu *>(mAllDialog[DLG_ENGNEER_CAL])->InitUiText();
            break;
        case DLG_DEBUG_VIEW:
            break;
        case DLG_HISTORY_DATA:
            if(mAllDialog[DLG_HISTORY_DATA]!=nullptr)
                qobject_cast<HistoryDataDialog *>(mAllDialog[DLG_HISTORY_DATA])->InitUiText();
            break;
        case DLG_SYSTEM_SELFTEST:
            if(mAllDialog[DLG_SYSTEM_SELFTEST]!=nullptr)
                qobject_cast<SelftestDlg *>(mAllDialog[DLG_SYSTEM_SELFTEST])->InitUiText();
            break;
        case DLG_FLOWMETER_CONTROL:
            if(mAllDialog[DLG_FLOWMETER_CONTROL]!=nullptr)
                qobject_cast<FlowControlDialog *>(mAllDialog[DLG_FLOWMETER_CONTROL])->InitUiText();
            break;
        }
    }

}


void MainWindow::CloseAllFocus(QWidget *parent)
{
    QObjectList allList = parent->children();
    for(auto ite: allList)
    {
        if(qobject_cast<QWidget*>(ite)!=NULL)
        {
            CloseAllFocus((QWidget*)ite);
            ((QWidget*)ite)->setFocusPolicy(Qt::NoFocus);
        }
    }
}

void MainWindow::MakeDialog(int DialogType)
{
    if(DialogType<DLG_MAX_NUM&&DialogType>=0)
    {
        if(mAllDialog[DialogType]==nullptr)
        {
            switch (DialogType) {
            case DLG_STANDBY_CONFIRM:
                DEBUG_RUNTIME(mAllDialog[DLG_STANDBY_CONFIRM] = new TipDialog(this));
                ((TipDialog *)mAllDialog[DLG_STANDBY_CONFIRM])->SetDialogText(UIStrings::GetStr(STR_STANDBY_STRING1));
                connect((TipDialog *)mAllDialog[DLG_STANDBY_CONFIRM], &TipDialog::accepted, this, [=]{
                    RunModeManage::GetInstance()->StartStandbyMode();
                    mMessageBoard->resetTimer();
                });

                connect((TipDialog *)mAllDialog[DLG_STANDBY_CONFIRM], &TipDialog::rejected, this, []{
                    MainWindow::GetInstance()->showDialog(DLG_NONE);
                });
                break;
            case DLG_STANDBY:
                DEBUG_RUNTIME(mAllDialog[DLG_STANDBY] = new StandbyPage(this));
                break;
            case DLG_SYS_SETTING_MENU:
                DEBUG_RUNTIME(mAllDialog[DLG_SYS_SETTING_MENU] = new SettingsMenu(this));
                break;
            case DLG_ALARM_MENU:
                DEBUG_RUNTIME(mAllDialog[DLG_ALARM_MENU] = new AlarmMenu(this));
                break;
            case DLG_ENGNEER_CAL:
                DEBUG_RUNTIME(mAllDialog[DLG_ENGNEER_CAL] = new EngneerMenu(this));
                break;
            case DLG_DEBUG_VIEW:
                DEBUG_RUNTIME(mAllDialog[DLG_DEBUG_VIEW] = new FlowerDebugDialog(this));
                break;
            case DLG_HISTORY_DATA:
                DEBUG_RUNTIME(mAllDialog[DLG_HISTORY_DATA] = new HistoryDataDialog(this));
                break;
            case DLG_SYSTEM_SELFTEST:
                DEBUG_RUNTIME(mAllDialog[DLG_SYSTEM_SELFTEST] = new SelftestDlg());
                break;
            case DLG_FLOWMETER_CONTROL:
                DEBUG_RUNTIME(mAllDialog[DLG_FLOWMETER_CONTROL] = new FlowControlDialog(this));
                break;
            default:break;
            }
            auto popupSize= sControlSizeMinimum[MAINWINDOW_COMPONENT_ENUM::POPUP];
            if(DialogType!=DLG_SYSTEM_SELFTEST)
                mAllDialog[DialogType]->setFixedSize(popupSize);
            if(DialogType==DLG_STANDBY)
                mAllDialog[DialogType]->setFixedSize(mMonitorParam->x() + mMonitorParam->width() - mMainDisplayWidget->x(), mMainDisplayWidget->height());
            if(DialogType==DLG_FLOWMETER_CONTROL)
                mAllDialog[DialogType]->setFixedSize(DLG_FLOWMETER_CONTROL_WIDTH,DLG_FLOWMETER_CONTROL_HEIGHT);
            mAllDialog[DialogType]->installEventFilter(this);
            InitUiText(DialogType);
        }
    }
}

void MainWindow::UpdateSpO2Display()
{
    if(RunModeManage::GetInstance()->IsModeActive(MODE_RUN_VENT)
            && ConfigManager->GetConfig<int>(SystemConfigManager::SPO2_BOOL_INDEX)
            && SettingManager->GetIntSettingValue(SystemSettingManager::SPO2_MODULE_SWITCH))
    {
        mMonitorParam->setFixedSize(QSize(sControlSizeMinimum[MAINWINDOW_COMPONENT_ENUM::PARAMLIST].width(), sControlSizeMinimum[MAINWINDOW_COMPONENT_ENUM::PARAMLIST].height() - SPO2_PARAM_HEIGHT));
        mMainDisplayWidget->setFixedHeight(sControlSizeMinimum[MAINWINDOW_COMPONENT_ENUM::GRAPH].height() - SPO2_PARAM_HEIGHT);
        mSpo2ParamLabel->setVisible(true);
        mSpo2Wave->setVisible(true);
    }
    else
    {
        mMonitorParam->setFixedSize(sControlSizeMinimum[MAINWINDOW_COMPONENT_ENUM::PARAMLIST]);
        mMainDisplayWidget->setFixedHeight(sControlSizeMinimum[MAINWINDOW_COMPONENT_ENUM::GRAPH].height());
        mSpo2ParamLabel->setVisible(false);
        mSpo2Wave->setVisible(false);
    }
}


void MainWindow::SlotRefurbishAutoHideTimer()
{
    foreach (auto p, mAllDialog)
    {
        if (p && p->isVisible())
        {
            if(mAutoExitEditTime.isActive())
            {
                mAutoExitEditTime.start(AUTO_EXIT_TIME);
#ifdef SHOW_AUTO_CLOS_DIALOG_LABEL
                mAutoCloseDialogCountLabel->setText(QString::number(AUTO_EXIT_TIME/1000));
                mAutoCloseCountTime.start(1000);
#endif
            }
            break;
        }
    }
}

void MainWindow::SlotRefreshAutoExitTimer(bool CurIsCal)
{
    if(CurIsCal)
    {
        mAutoExitEditTime.stop();
    }
    else
    {
        mAutoExitEditTime.start(AUTO_EXIT_TIME);
    }
}
void MainWindow::ShowTip(QWidget *parent, QString text)
{
    mTipDialog->setParent(parent);
    mTipDialog->SetDialogText(text);
    mTipDialog->show();
}

void MainWindow::ShowCalibrationMenu()
{
    showDialog(DLG_SYS_SETTING_MENU);
    qobject_cast<SettingsMenu *>(mAllDialog[DLG_SYS_SETTING_MENU])->ShowCalibrationMenu();
}

void MainWindow::SetAlarmDialogPage(int index, int index2)
{
    if(mAllDialog[DLG_ALARM_MENU]!=nullptr)
        qobject_cast<AlarmMenu *>(mAllDialog[DLG_ALARM_MENU])->ShowTab(index,index2);
}

void MainWindow::InitConnect()
{
    connect(MyApp, &MyApplication::SignalMouseEvent, this,&MainWindow::SlotRefurbishAutoHideTimer,Qt::QueuedConnection);
    connect(MyApp, &MyApplication::SignalKeyEvent, this, &MainWindow::SlotRefurbishAutoHideTimer,Qt::QueuedConnection);
    connect(MyApp,  &MyApplication::SignalTouchEvent, this, &MainWindow::SlotRefurbishAutoHideTimer,Qt::QueuedConnection);
    connect(&mAutoExitEditTime, &QTimer::timeout, this, [&]{
        if(mIsBlockDialogShow)
            return;
#ifndef WIN32
        for (int idx = DLG_STANDBY_CONFIRM; idx < DLG_MAX_NUM; ++idx)
        {
            if (idx != -1 && idx != MAINWINDOW_DIALOG_ENUM::DLG_STANDBY && idx != MAINWINDOW_DIALOG_ENUM::DLG_SYSTEM_SELFTEST
                    && idx != MAINWINDOW_DIALOG_ENUM::DLG_ENGNEER_CAL)
                if (mAllDialog[idx]!=nullptr&&mAllDialog[idx]->isVisible())
                {
                    hideDialog(idx);
                }
        }
#endif
    });
    connect(qApp, &QApplication::aboutToQuit, this, &MainWindow::CleanUp);
    connect(UIStrings::GetInstance(), &UIStrings::SignalLanguageChanged, this, [this]{
        this->InitUiText(DLG_ALL);
    });
    connect(VentModeSettingManager::GetInstance(), &VentModeSettingManager::SignalModeChanged, this, [this](int mode)
    {
        mFocusControl->CleanAll();
        if (VENTMODE_FLAG_HLM == mode)
        {
            mMainDisplayWidget->ShowCPBInfo();
            mMainDisplayWidget->setFocusPolicy(Qt::NoFocus);
        }
        else
        {
            mMainDisplayWidget->ShowPlotInfo();
            mMainDisplayWidget->setFocusPolicy(Qt::StrongFocus);
        }
        mIsAutoInitFocusControl = true;
        AutoInitFocusSonWidget();
    });

    SettingsMenu* SettingMenu = dynamic_cast<SettingsMenu *>(mAllDialog[DLG_SYS_SETTING_MENU]);
    if(SettingMenu)
    {
        connect(SettingMenu, &SettingsMenu::SignalChangeCalState, this, &MainWindow::SlotRefreshAutoExitTimer, Qt::QueuedConnection);
    }

    connect(UpgradeManager::GetInstance(), &UpgradeManager::SignalUpdateStateChange, this, [this]()
    {
        UpgradeManager::E_UPGRADE_STATE state = UpgradeManager::GetInstance()->GetUpgradeState();
        if(state == UpgradeManager::E_UPGRADE_WORKING)
        {
            mIsBlockDialogShow = true;
            this->setDisabled(true);
        }
    });

    connect(UpgradeManager::GetInstance(), &UpgradeManager::SignalUpdateFinish, this, [this]()
    {
        mIsBlockDialogShow = false;
        this->setDisabled(false);
    });


    connect(ManageFactory::GetInstance()->GetDeviceManage(), &DeviceManageBase::SignalShundownStateChanged,
            this, [this](DeviceManageBase::SHUTDOWN_STATE state)
    {
        if(state == DeviceManageBase::SHUTDOWN_STATE::ON_COUNTDOWN)
            mPowerOffDlg->show();
        else if (state == DeviceManageBase::SHUTDOWN_STATE::SHUTDOWN_NORMAL)
            mPowerOffDlg->hide();
    }, Qt::QueuedConnection);
#ifdef SHOW_AUTO_CLOS_DIALOG_LABEL
    connect(&mAutoCloseCountTime, &QTimer::timeout, this, [&]
    {
        if(mAutoCloseDialogCountLabel->text().toInt()-1 == 0)
        {
            mAutoCloseCountTime.stop();
            mAutoCloseDialogCountLabel->clear();
        }
        else
            mAutoCloseDialogCountLabel->setText(QString::number(mAutoCloseDialogCountLabel->text().toInt()-1));
    });
#endif

    connect(RunModeManage::GetInstance(), &RunModeManage::SignalModeChanged, this, [this](E_RUNMODE preMode, E_RUNMODE curMode)
    {
        UpdateSpO2Display();
    });

    connect(SettingManager,&SystemSettingManager::SignalSettingChanged,[=](int settingId)
    {
        if(settingId == SystemSettingManager::SPO2_MODULE_SWITCH)
        {
            UpdateSpO2Display();
        }
    });

    connect(mSpo2Wave, &WaveChart::SignalMousePress, this, [this](const QPointF& pos)
    {
        mSpo2Wave->SetYCursor(pos.x());
        mMainDisplayWidget->UpdateAllWaveCursors(pos.x());
    });


    connect(mMainDisplayWidget, &MainDisplayWidget::SignalWaveCursorChanged, this, [this](qreal xPos)
    {
        mSpo2Wave->SetYCursor(xPos);
    });

    installEventFilter(this);
    mMessageBoard->installEventFilter(this);
    mMainDisplayWidget->installEventFilter(this);
    mMonitorParam->installEventFilter(this);
    mModeWidget->installEventFilter(this);
}

