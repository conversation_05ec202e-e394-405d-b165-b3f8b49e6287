﻿#include <QHBoxLayout>
#include <QVBoxLayout>
#include <QGridLayout>
#include "SIMV_PCModeSheet.h"
#include "SettingSpinbox/ExtSettingSpinbox.h"
#include "SettingSpinboxManager.h"
#include "RuleManager.h"
#include "String/UIStrings.h"
#include "SystemConfigManager.h"


//realdata文件中定义
extern int convert_IE_value_2_percent(short & percent, short value);

SIMV_PCModeSheet::SIMV_PCModeSheet(QWidget *parent) :
    ModeSheetBase(parent)
{
    mModeId = VENTMODE_FLAG_PCVSIMV;
    InitUi();
    InitConnect();
}

void SIMV_PCModeSheet::InitUi()
{
    mPinsp = new SettingSpinbox;
    SettingSpinboxManager::GetInstance()->RegisterModesetSpn(SETTINGDATA_PINSP, mPinsp);

    mRate = new SettingSpinbox;
    SettingSpinboxManager::GetInstance()->RegisterModesetSpn(SETTINGDATA_RATE, mRate);
    mRate->setBaseMinMax(4,40);

    mTinsp = new SettingSpinbox;
    SettingSpinboxManager::GetInstance()->RegisterModesetSpn(SETTINGDATA_TINSP, mTinsp);

    mSlope = new SlopeSpinbox;
    SettingSpinboxManager::GetInstance()->RegisterModesetSpn(SETTINGDATA_SLOPE, mSlope);

    mPsupp = new PsuppSpinbox;
    SettingSpinboxManager::GetInstance()->RegisterModesetSpn(SETTINGDATA_PSUPP, mPsupp);

    mPeep = new SettingSpinbox;
    SettingSpinboxManager::GetInstance()->RegisterModesetSpn(SETTINGDATA_PEEP, mPeep);

    mTrig = new SettingSpinbox;
    SettingSpinboxManager::GetInstance()->RegisterModesetSpn(SETTINGDATA_FLOW_TRIGGERVALUE, mTrig);

    //SIMV_PC 布局
    mMainLayout->setSpacing(MODULE_ITEM_SPACING);
    mMainLayout->addWidget(mPinsp);
    mMainLayout->addWidget(mRate);
    mMainLayout->addWidget(mTinsp);
    mMainLayout->addSpacing(UiSettingBoxWidth  + MODULE_ITEM_SPACING);
    mMainLayout->addWidget(mSlope);
    mMainLayout->addWidget(mPsupp);
    mMainLayout->addWidget(mPeep);
    mMainLayout->addWidget(mTrig);
    mMainLayout->addStretch();
}
