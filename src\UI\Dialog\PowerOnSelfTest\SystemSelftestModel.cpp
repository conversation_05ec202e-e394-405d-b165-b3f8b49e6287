﻿#include "SystemSelftestModel.h"
#include "SystemSettingManager.h"
#include "ModuleTestManager.h"

#define TEST_INTERVAL 1000 //msec

SystemSelftestModel::SystemSelftestModel(QObject *parent) : QObject(parent)
{
    HistoryEventManager::GetInstance()->RegisterOperateLogConvertFunc(LOG_SYS_SELF_TEST_ID, LogStToString);


    auto logs = HistoryEventManager::GetInstance()->GetLogByType(OPERATE_EVENT_TYPE::LOG_SYS_SELF_TEST_ID);
    if (logs.size() == 0)
        mSystemState = SYSTEM_SELFTEST_RESULT::ENUM_END;
    else
    {
        mSystemState = (SYSTEM_SELFTEST_RESULT)logs.first().mParameter1;
        mLastestTime = QDateTime::fromSecsSinceEpoch(logs.first().mTimestamp);
    }

    ResetBuf();

    mAutoTestTimer = new QTimer;

    connect(mAutoTestTimer,&QTimer::timeout,this,[=](){
        auto keys = mTestBuf.keys();
        if (keys.size() == 0)
            return ;

        auto idx = keys.indexOf(mCurTestItemId);
        if (idx == -1)
        {
            mCurTestItemId = -1;
            DebugAssert(0);
        }
        else if (idx < keys.size())
        {
            auto moduleState = ModuleTestManager::GetInstance()->GetModuleState(mCurTestItemId);
            if (moduleState == E_MODULE_STATE::ABLE)
                SetItemState(mCurTestItemId, ITEM_STATE::CHECKED_SUCCESS);
            else
                SetItemState(mCurTestItemId, ITEM_STATE::CHECKED_FAIL);

            if (idx == keys.size() - 1)
            {
                mCheckFinishFlag = true;
                RefurbishSystemTestState();
                return;
            }

            mCurTestItemId = keys.at(++idx);
        }
    });
}

void SystemSelftestModel::SetSystemTestState(SYSTEM_SELFTEST_RESULT state)
{
    mSystemState = state;
    mLastestTime = QDateTime::currentDateTime();
    HistoryEventManager::GetInstance()->AddHistoryEvent(HistoryEventManager::HISTORY_EVENT_TYPE::OPERATE_EVENT,
                                                       OPERATE_EVENT_TYPE::LOG_SYS_SELF_TEST_ID,
                                                       mSystemState);

    emit SignalSystemStateChanged(mSystemState);
}


void SystemSelftestModel::SkipTest()
{
    mAutoTestTimer->stop();
    auto keys = mTestBuf.keys();
    if (keys.size() == 0)
        return ;

    auto idx = keys.indexOf(mCurTestItemId);
    if (idx == -1)
    {
        mCurTestItemId = -1;
        DebugAssert(0);
    }
    else
    {
        foreach(auto index,keys)
        {
            if(index >= mCurTestItemId)
            {
                if(index == keys.last() && mTestBuf[index].mState != ITEM_STATE::UNCHECKED)
                    break;
                mTestBuf[index].mState = ITEM_STATE::SKIP;
            }
        }
    }
    SetSystemTestState(SYSTEM_SELFTEST_RESULT::SKIP);
}

void SystemSelftestModel::RefurbishSystemTestState()
{
    if (DEMO_MODE == SettingManager->GetIntSettingValue(SystemSettingManager::SOFT_MODE_SETTING)) //在demo模式下所有检查项都是有效地；
    {
#ifndef WIN32
        if  (mTestBuf[TEST_AC].mState != ITEM_STATE::CHECKED_SUCCESS && mTestBuf[TEST_BATTERY].mState != ITEM_STATE::CHECKED_SUCCESS)
            mSystemState = SYSTEM_SELFTEST_RESULT::BREAKDOWN;
        else if  (mTestBuf[TEST_AC].mState != ITEM_STATE::CHECKED_SUCCESS || mTestBuf[TEST_BATTERY].mState != ITEM_STATE::CHECKED_SUCCESS)
            mSystemState = SYSTEM_SELFTEST_RESULT::NORMAL;
        else
            mSystemState = SYSTEM_SELFTEST_RESULT::SUCCESS;
#else
        mSystemState = SYSTEM_SELFTEST_RESULT::SUCCESS;
#endif
    }
    else
    {
        bool flag{true};
        foreach (auto itemInfoSt, mTestBuf)
        {
            if (itemInfoSt.mState != ITEM_STATE::CHECKED_SUCCESS)
            {
                flag = false;
            }
        }

        if (flag)
        {
            mSystemState = SYSTEM_SELFTEST_RESULT::SUCCESS;
        }
        else if (mTestBuf[TEST_MONITOR].mState != ITEM_STATE::CHECKED_SUCCESS ||
                 mTestBuf[TEST_EXHALATION_VALUE].mState != ITEM_STATE::CHECKED_SUCCESS  ||
                 mTestBuf[TEST_INHALATION_VALUE].mState != ITEM_STATE::CHECKED_SUCCESS ||
                 //mTestBuf[TEST_DATABASE].mState != ITEM_STATE::CHECKED_SUCCESS ||
                 (mTestBuf[TEST_AC].mState != ITEM_STATE::CHECKED_SUCCESS && mTestBuf[TEST_BATTERY].mState != ITEM_STATE::CHECKED_SUCCESS))
        {

            mSystemState = SYSTEM_SELFTEST_RESULT::BREAKDOWN;
        }
        else
        {
            mSystemState = SYSTEM_SELFTEST_RESULT::NORMAL;
        }
    }

    SetSystemTestState(mSystemState);
    mAutoTestTimer->stop();
}

void SystemSelftestModel::SetItemState(int id, ITEM_STATE state)
{
    mTestBuf[id].mState = state;
    emit SignalItemStateChanged(id, state);
}

// total - cost[i] >= 0  total + gas[i + 1] - cost[i]
void SystemSelftestModel::ResetBuf()
{
    mTestBuf.clear();
    auto ids = ModuleTestManager::GetInstance()->GetTestableItemsId();

    foreach (auto id, ids)
    {
        if (ModuleTestManager::GetInstance()->GetModuleState((TEST_MODULE_ID)id) == E_MODULE_STATE::NO_EXIST)
        {
            if (mTestBuf.contains(id))
                mTestBuf.remove(id);
            continue;
        }

        mTestBuf[id].mId = id;
        mTestBuf[id].mStrId = ModuleTestManager::GetInstance()->GetModuleName(id);
        mTestBuf[id].mState = ITEM_STATE::UNCHECKED;
    }
}

int SystemSelftestModel::GetTestableCnt()
{
    return ModuleTestManager::GetInstance()->GetTestCount();
}

bool SystemSelftestModel::IsSkippable()
{
    if (mCheckFinishFlag)
        return false;
    if (mTestBuf[TEST_MONITOR].mState != ITEM_STATE::CHECKED_SUCCESS ||
            mTestBuf[TEST_EXHALATION_VALUE].mState != ITEM_STATE::CHECKED_SUCCESS  ||
            mTestBuf[TEST_INHALATION_VALUE].mState != ITEM_STATE::CHECKED_SUCCESS ||
            //mTestBuf[TEST_DATABASE].mState != ITEM_STATE::CHECKED_SUCCESS ||
            (mTestBuf[TEST_AC].mState != ITEM_STATE::CHECKED_SUCCESS && mTestBuf[TEST_BATTERY].mState != ITEM_STATE::CHECKED_SUCCESS))
    {

        return false;
    }
    else
    {
        return true;
    }
}

SystemSelftestModel::SYSTEM_SELFTEST_RESULT SystemSelftestModel::GetSystemTestState()
{
    return mSystemState;
}

QDateTime SystemSelftestModel::GetLastestTestTime()
{
    return mLastestTime;
}

QVector<SystemSelftestModel::ItemInfoSt> SystemSelftestModel::GetItemsInfo()
{
    return mTestBuf.values().toVector();
}


QString SystemSelftestModel::GetSystemStateStr(SYSTEM_SELFTEST_RESULT state)
{
    switch (state)
    {
    case SYSTEM_SELFTEST_RESULT::SUCCESS:
        return UIStrings::GetStr(ALL_STRINGS_ENUM::STR_PASS);
        break;
    case SYSTEM_SELFTEST_RESULT::NORMAL:
        return UIStrings::GetStr(ALL_STRINGS_ENUM::STR_NORMAL);
        break;
    case SYSTEM_SELFTEST_RESULT::BREAKDOWN:
        return UIStrings::GetStr(ALL_STRINGS_ENUM::STR_BREAKDOWN);
        break;
    case SYSTEM_SELFTEST_RESULT::SKIP:
        return UIStrings::GetStr(ALL_STRINGS_ENUM::STR_SKIP);
        break;
    default:
        return UIStrings::GetStr(ALL_STRINGS_ENUM::STR_NULL);
        break;
    }
}

QString SystemSelftestModel::GetItemStateStr(ITEM_STATE state)
{
    switch (state)
    {
    case ITEM_STATE::CHECKED_SUCCESS:
        return UIStrings::GetStr(ALL_STRINGS_ENUM::STR_PASS);
        break;
    case ITEM_STATE::CHECKED_FAIL:
        return UIStrings::GetStr(ALL_STRINGS_ENUM::STR_FAIL);
        break;
    case ITEM_STATE::CHECKING:
        return UIStrings::GetStr(ALL_STRINGS_ENUM::STR_NULL);
        break;
    case ITEM_STATE::SKIP:
        return UIStrings::GetStr(ALL_STRINGS_ENUM::STR_SKIP);
        break;
    default:
        return UIStrings::GetStr(ALL_STRINGS_ENUM::STR_NULL);
        break;
    }
}

QString SystemSelftestModel::LogStToString(HistoryEventSt logSt)
{
    auto systemState = logSt.mParameter1;
    return UIStrings::GetStr(ALL_STRINGS_ENUM::STR_LATEST_SELFTEST) + ": " +
            SystemSelftestModel::GetInstance()->GetSystemStateStr((SystemSelftestModel::SYSTEM_SELFTEST_RESULT)systemState);
}

void SystemSelftestModel::PauseTest()
{
    mAutoTestTimer->stop();
}

void SystemSelftestModel::RecoverTest()
{
    mAutoTestTimer->start(TEST_INTERVAL);
}

void SystemSelftestModel::StartTest()
{
    ResetBuf();
    mCheckFinishFlag = false;
    auto keys = mTestBuf.keys();
    if (keys.size() == 0)
        return ;
    else
        mCurTestItemId = keys.first();
    mAutoTestTimer->setInterval(TEST_INTERVAL);
    mAutoTestTimer->start(TEST_INTERVAL);
}
