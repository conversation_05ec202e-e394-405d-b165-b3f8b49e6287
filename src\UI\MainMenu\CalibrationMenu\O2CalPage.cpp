﻿#include <QHBoxLayout>
#include <QVBoxLayout>
#include <QGridLayout>

#include "UiApi.h"
#include "O2CalPage.h"
#include "String/UIStrings.h"
#include "calibration/o2CalManage.h"
#include "PushButton.h"
#include "ModuleTestManager.h"
#include "KeyConfig.h"
#include "RunModeManage.h"

#define O2_CAL_BEGIN_IMAGE_URL (":/Calibration/O2CalBegin.png")
#define O2_CAL_END_IMAGE_URL    (":/Calibration/O2CalEnd.png")

#define IMAGE_HEIGHT    340
#define IMAGE_WEIGHT    280
#define PAGE_HEIGHT     462
#define PAGE_WEIGHT     558

#define TIP_IMAGE_WIDTH 250
#define TIP_IMAGE_HEIGHT 320
#define RESULT_WIDGET_HEIGHT 150
#define RESULT_LABLE_HEIGHT 24
O2CalPage::O2CalPage(QWidget* parent):FocusWidget(parent)
{
    mCalStepTextId = STR_MAINMENU_O2CAL_TXT3;
    mCalBtnTextId = STR_START;
    mCalStateTipTextId = STR_O2_CONCENTRATION_ERROR;

    InitUi();
    InitUiText();
    connect(O2CalManage::GetInstance(), &O2CalManage::SignalCalStateChanged, this, &O2CalPage::DisplayStageTip, Qt::DirectConnection);

    connect(mCalButton, &PushButton::clicked, this, &O2CalPage::SlotOnCalBtnClicked);

    int o2State = (ModuleTestManager::GetInstance()->GetModuleState(TEST_O2));
    if (o2State != ABLE)
    {
        DisplayStageTip(O2_DISABLE);
    }
    else
    {
        DisplayStageTip(O2_NORMAL);
    }
    connect(RunModeManage::GetInstance(), &RunModeManage::SignalModeChanged, this, [this](E_RUNMODE preMode, E_RUNMODE curMode){
        if(RunModeManage::GetInstance()->IsModeActive(MODE_RUN_MANUAL) || RunModeManage::GetInstance()->IsModeActive(MODE_RUN_ACGO))
        {
            EndCurCal();
            if(ModuleTestManager::GetInstance()->GetModuleState(TEST_O2) == ABLE)
            {
                mCalStateTipTextId = STR_MENUAL_ACGO_STATE_TIP;
                mCalModuleStateTip->setText(UIStrings::GetStr(mCalStateTipTextId));
            }
            mCalModuleStateTip->show();
            if(mFinishCalButton->isVisible())
            {
                if(mCalButton->hasFocus())
                    mFinishCalButton->setFocus();
            }
            mCalButton->setEnabled(false);
        }
        else
        {
            if(ModuleTestManager::GetInstance()->GetModuleState(TEST_O2) == ABLE)
            {
                mCalModuleStateTip->hide();
                mCalButton->setEnabled(true);
            }
            else
            {
                mCalStateTipTextId = STR_O2_CONCENTRATION_ERROR;
                mCalModuleStateTip->setText(UIStrings::GetStr(mCalStateTipTextId));
            }
        }
    });
}

void O2CalPage::InitUi()
{
    mBiginPng= QPixmap(O2_CAL_BEGIN_IMAGE_URL);
    mEndPng= QPixmap(O2_CAL_END_IMAGE_URL);

    mCalTipImage = new QLabel;
    mCalTipImage->setFixedWidth(TIP_IMAGE_WIDTH);
    mCalTipImage->setFixedHeight(TIP_IMAGE_HEIGHT);
    StyleSet::SetBackgroundColor(mCalTipImage,COLOR_WHITE_GRAY);
    mCalTipImage->setScaledContents(true);

    mCalModuleStateTip = new QLabel;
    mCalModuleStateTip->setText(UIStrings::GetStr(mCalStateTipTextId));
    mCalModuleStateTip->hide();

    mCalStepTipTitle = new QLabel(UIStrings::GetStr(STR_MAINMENU_CAL_TIP) + ":");
    mCalStepTipTitle->setFixedHeight(35);
    mCalStepTipTitle->setContentsMargins(5, 0, 0, 0);
    mCalStepTipTitle->setAlignment(Qt::AlignVCenter);
    mCalStepTipTitle->setObjectName("mCalStepTipTitle");
    StyleSet::SetTextFont(mCalStepTipTitle, mCalStepTipTitle->font().pointSize(), FONT_FAMILY_TAHOMA);

    mCalStepTipText = new QTextEdit(this);
    mCalStepTipText->setFocusPolicy(Qt::NoFocus);
    mCalStepTipText->setFrameStyle(QFrame::Box);
    mCalStepTipText->setLineWidth(0);
    StyleSet::SetTextFont(mCalStepTipText,FONT_M);
    StyleSet::SetBackgroundColor(mCalStepTipText,COLOR_WHITE_GRAY);
    mCalStepTipText->setReadOnly(true);
    mCalStepTipText->setTextInteractionFlags(Qt::NoTextInteraction);

    auto centerLayout = new QHBoxLayout;
    centerLayout->addWidget(mCalStepTipText);
    centerLayout->addWidget(mCalTipImage);

    mCalProgressBar = new CountdownProgressBar;
    mCalProgressBar->SetCountTime(O2CalManage::GetCalLimitTime());
    mCalProgressBar->hide();

    mPreCalTime = new DateTimeWidget(this, Qt::Horizontal);
    mPreCalTime->SetDateColor(COLOR_BLACK);
    mPreCalTime->SetTimeColor(COLOR_BLACK);
    mPreCalTime->setFixedHeight(RESULT_LABLE_HEIGHT);

    m21ResultTitle = new QLabel;
    StyleSet::SetTextColor(m21ResultTitle,COLOR_BLACK);
    StyleSet::SetTextFont(m21ResultTitle, FONT_M);
    m21ResultTitle->setFixedHeight(RESULT_LABLE_HEIGHT);

    m21CalResult = new QLabel;
    m21CalResult->setFixedHeight(RESULT_LABLE_HEIGHT);

    auto pct21ResultLayout = new QHBoxLayout;
    pct21ResultLayout->setContentsMargins(0, 0, 0, 0);
    pct21ResultLayout->addWidget(m21ResultTitle);
    pct21ResultLayout->addWidget(m21CalResult);
    pct21ResultLayout->addStretch();

    m21ResultBlock = new QFrame;
    m21ResultBlock->setLayout(pct21ResultLayout);

    StyleSet::SetBackgroundColor(m21ResultTitle,COLOR_WHITE_GRAY);
    StyleSet::SetBackgroundColor(m21CalResult,COLOR_WHITE_GRAY);
    StyleSet::SetBackgroundColor(mPreCalTime,COLOR_WHITE_GRAY);
    StyleSet::SetBackgroundColor(m21ResultBlock,COLOR_WHITE_GRAY);
    StyleSet::SetTextFont(m21CalResult, FONT_M);


    m100ResultTitle = new QLabel;
    StyleSet::SetTextColor(m100ResultTitle,COLOR_BLACK);
    StyleSet::SetTextFont(m100ResultTitle, FONT_M);
    m100ResultTitle->setFixedHeight(RESULT_LABLE_HEIGHT);

    m100CalResult = new QLabel;
    m100CalResult->setFixedHeight(RESULT_LABLE_HEIGHT);

    auto pct100ResultLayout = new QHBoxLayout;
    pct100ResultLayout->setContentsMargins(0, 0, 0, 0);
    pct100ResultLayout->addWidget(m100ResultTitle);
    pct100ResultLayout->addWidget(m100CalResult);
    pct100ResultLayout->addStretch();

    m100ResultBlock = new QFrame;
    m100ResultBlock->setLayout(pct100ResultLayout);

    mResultBlock = new QFrame;
    auto resultLayout = new QVBoxLayout(mResultBlock);

    StyleSet::SetBackgroundColor(m100ResultTitle,COLOR_WHITE_GRAY);
    StyleSet::SetBackgroundColor(m100CalResult,COLOR_WHITE_GRAY);
    StyleSet::SetBackgroundColor(mPreCalTime,COLOR_WHITE_GRAY);
    StyleSet::SetBackgroundColor(m100ResultBlock,COLOR_WHITE_GRAY);
    StyleSet::SetTextFont(m100CalResult, FONT_M);

    resultLayout->setContentsMargins(13, 0, 0, 0);
    resultLayout->setSpacing(0);
    resultLayout->addWidget(m21ResultBlock);
    resultLayout->addWidget(m100ResultBlock);
    resultLayout->addWidget(mPreCalTime);

    mInfoWidget = new QStackedWidget;
    mInfoWidget->addWidget(mResultBlock);
    mInfoWidget->addWidget(mCalProgressBar);

    mCalButton = new PushButton(UIStrings::GetStr(STR_START));

    mFinishCalButton = new PushButton(UIStrings::GetStr(STR_MAINMENU_CAL_FIN));
    QSizePolicy sp1 = mFinishCalButton->sizePolicy();
    sp1.setVerticalPolicy(QSizePolicy::Expanding);
    sp1.setRetainSizeWhenHidden(true);
    mFinishCalButton->setSizePolicy(sp1);
    mFinishCalButton->hide();
    connect(mFinishCalButton, SIGNAL(clicked()), this, SLOT(onFinishButton()));

    QHBoxLayout *buttonLayout = new QHBoxLayout;
    buttonLayout->addWidget(mCalModuleStateTip);
    buttonLayout->addStretch();
    buttonLayout->addWidget(mFinishCalButton);
    buttonLayout->addSpacing(4);
    buttonLayout->addWidget(mCalButton);
    buttonLayout->setAlignment(mCalButton, Qt::AlignRight);

    auto bottomLayout = new QVBoxLayout;
    bottomLayout->addWidget(mInfoWidget);
    bottomLayout->addStretch();
    bottomLayout->addLayout(buttonLayout);
    bottomLayout->setAlignment(buttonLayout, Qt::AlignBottom);

    auto mainLayout = new QVBoxLayout;
    mainLayout->setContentsMargins(0, 0, 0, 0);
    mainLayout->setSpacing(0);
    mainLayout->addWidget(mCalStepTipTitle);
    mainLayout->addLayout(centerLayout);
    mainLayout->addSpacing(2);
    mainLayout->addLayout(bottomLayout);

    mCalTipImage->setPixmap(mBiginPng);

    RefurbishCalResult();
    setLayout(mainLayout);
    StyleSet::SetBackgroundColor(this,COLOR_WHITE_GRAY);
    AddFoucusSonWidget(mFinishCalButton);
}

void O2CalPage::EndCurCal()
{
    auto O2CalStatus = O2CalManage::GetInstance()->GetO2CalStatus();
    if(O2CalStatus == O2_21_CAL)
    {
        O2CalManage::GetInstance()->EndO2Cal(O2_21_CAL);
        O2CalManage::GetInstance()->EndO2Cal(O2_FINISH_CAL);
        DisplayStageTip(O2_NORMAL);
    }
    else if(O2CalStatus == O2_100_CAL)
    {
        O2CalManage::GetInstance()->EndO2Cal(O2_100_CAL);
        O2CalManage::GetInstance()->EndO2Cal(O2_21_FINISH);
        O2CalManage::GetInstance()->EndO2Cal(O2_FINISH_CAL);
        DisplayStageTip(O2_NORMAL);
    }
}


//设置校准各阶段超时时间，作为校准进度
void O2CalPage::SetStageTime(unsigned short time)
{
    //mCalProgressBar->SetCountTime(0, time);
}

//进度为总时间减去剩余时间
void O2CalPage::SetRemainTime(unsigned short )
{
    //mCalProgressBar->setValue(mCalProgressBar->maximum() - time);
}

void O2CalPage::SetStepText(QString text)
{
    mCalStepTipText->clear();
    QTextDocument *doc = mCalStepTipText->document();
    QTextCursor cursor(doc);

    QTextBlockFormat blockFormat;
    blockFormat.setAlignment(Qt::AlignTop);
    blockFormat.setTopMargin(0);
    blockFormat.setBottomMargin(3);
    blockFormat.setLeftMargin(10);
    blockFormat.setRightMargin(3);
    blockFormat.setLineHeight(3, QTextBlockFormat::LineDistanceHeight);

    QFont tempFont;
    tempFont.setFamily(FONT_FAMILY_TAHOMA);
    tempFont.setPixelSize(FONT_M);
    mCalStepTipText->setFont(tempFont);


    cursor.insertBlock(blockFormat);
    cursor.insertText(text);
    cursor.insertBlock();


    mCalStepTipText->setDocument(doc);
    mCalStepTipText->setTextCursor(cursor);
}

void O2CalPage::InitUiText()
{
    mCalStepTipTitle->setText(UIStrings::GetStr(STR_MAINMENU_CAL_TIP) + ":");
    SetStepText(UIStrings::GetStr(mCalStepTextId));

    m21ResultTitle->setText(UIStrings::GetStr(ALL_STRINGS_ENUM::STR_MAINMENU_21PO2CAL) + " " +
                            UIStrings::GetStr(ALL_STRINGS_ENUM::STR_RECENT_CAL) + ":");
    m21CalResult->setText(UIStrings::GetStr(mCal21ResultTextId));


    m100ResultTitle->setText(UIStrings::GetStr(ALL_STRINGS_ENUM::STR_MAINMENU_100PO2CAL) + " " +
                            UIStrings::GetStr(ALL_STRINGS_ENUM::STR_RECENT_CAL) + ":");
    m100CalResult->setText(UIStrings::GetStr(mCal100ResultTextId));

    mCalButton->setText(UIStrings::GetStr(mCalBtnTextId));
    mFinishCalButton->setText(UIStrings::GetStr(STR_MAINMENU_CAL_FIN));
    mCalModuleStateTip->setText(UIStrings::GetStr(mCalStateTipTextId));
}

bool O2CalPage::eventFilter(QObject *obj, QEvent *ev)
{
    return FocusWidget::eventFilter(obj, ev);
}

void O2CalPage::showEvent(QShowEvent *event)
{
    emit SignalChangeCalState(true);
    FocusWidget::showEvent(event);
}

void O2CalPage::hideEvent(QHideEvent *event)
{
    EndCurCal();
    emit SignalChangeCalState(false);
    FocusWidget::hideEvent(event);
}

//界面是在发回命令回复后显示校准阶段
void O2CalPage::SlotOnCalBtnClicked()
{
    if(ModuleTestManager::GetInstance()->GetModuleState(TEST_O2) != ABLE)
    {
        mCalModuleStateTip->show();
    }
    else
    {
        switch(O2CalManage::GetInstance()->GetO2CalStatus())
        {
        case O2_NORMAL:
        {
            mResultBlock->hide();
            O2CalManage::GetInstance()->StartO2Pct21Cal();
            if(mCalModuleStateTip->isVisible())
                mCalModuleStateTip->hide();
            mCalProgressBar->ResetTimer();
            mInfoWidget->setCurrentWidget(mCalProgressBar);
            mCalProgressBar->SetCountTime(O2CalManage::GetCalLimitTime());
            mCalProgressBar->StartCount();
        }
            break;
        case O2_21_CAL:
            O2CalManage::GetInstance()->EndO2Cal(O2_21_CAL);
            break;
        case O2_100_CAL:
            O2CalManage::GetInstance()->EndO2Cal(O2_100_CAL);
            break;
        case O2_21_DONE:
        case O2_WAIT_FOR_100:
        case O2_100_CANCEL:
            O2CalManage::GetInstance()->StartO2Pct100Cal();
            mCalProgressBar->ResetTimer();
            mInfoWidget->setCurrentWidget(mCalProgressBar);
            mCalProgressBar->SetCountTime(O2CalManage::GetCalLimitTime());
            mCalProgressBar->StartCount();
            break;

        case O2_21_CANCEL:
        case O2_21_FINISH:
            //已确定不校100
        case O2_FINISH_CAL:
            //新一轮的校准提示
            O2CalManage::GetInstance()->EndO2Cal(O2_FINISH_CAL);
            //这个命令不会发给下位机，要在这里就刷新界面
            DisplayStageTip(O2_NORMAL);
            break;
        default:
            break;
        }
    }

}

//校完21后结束
void O2CalPage::onFinishButton()
{
    O2CalManage::GetInstance()->EndO2Cal(O2_21_FINISH);
    m21ResultBlock->show();
}

void O2CalPage::Set21O2CalResult(CAL_RESULT result)
{
    switch(result)
    {
    case CAL_RESULT::CAL_SUCCESS:
        mCal21ResultTextId = STR_SUCCESS;
        StyleSet::SetTextColor(m21CalResult,COLOR_LIGHT_GREEN);
        break;
    case CAL_RESULT::CAL_FAIL:
        mCal21ResultTextId = STR_FAIL;
        StyleSet::SetTextColor(m21CalResult,COLOR_RED);
        break;
    case CAL_RESULT::CAL_CANCEL:
        mCal21ResultTextId = STR_CANCEL;
        StyleSet::SetTextColor(m21CalResult,COLOR_YELLOW);
        break;
    }
    m21CalResult->setText(UIStrings::GetStr(mCal21ResultTextId));
}

void O2CalPage::Set100O2CalResult(CAL_RESULT result)
{
    switch(result)
    {
    case CAL_RESULT::CAL_SUCCESS:
        mCal100ResultTextId = STR_SUCCESS;
        StyleSet::SetTextColor(m100CalResult,COLOR_LIGHT_GREEN);
        break;
    case CAL_RESULT::CAL_FAIL:
        mCal100ResultTextId = STR_FAIL;
           StyleSet::SetTextColor(m100CalResult,COLOR_RED);
        break;
    case CAL_RESULT::CAL_CANCEL:
        mCal100ResultTextId = STR_CANCEL;
           StyleSet::SetTextColor(m100CalResult,COLOR_YELLOW);
        break;
    }
    m100CalResult->setText(UIStrings::GetStr(mCal100ResultTextId));
}



void O2CalPage::RefurbishCalResult()
{
    auto cal21Result = CalModuleManage::GetInstance()->GetLatestCalResult(CAL_TYPE::CAL_TYPE_O2_21);
    auto cal100Result = CalModuleManage::GetInstance()->GetLatestCalResult(CAL_TYPE::CAL_TYPE_O2_100);
    if (cal100Result.first == -1)
    {
        mResultBlock->hide();
        return;
    }
    Set21O2CalResult((CAL_RESULT)cal21Result.second.mCalResult);
    Set100O2CalResult((CAL_RESULT)cal100Result.second.mCalResult);
    mPreCalTime->SetDateTime(QDateTime::fromSecsSinceEpoch(cal100Result.first));
}

//氧浓度校准各个阶段提示
void O2CalPage::DisplayStageTip(unsigned short stage)
{
    switch (stage)
    {
    case O2_NORMAL:
        mCalStepTextId = STR_MAINMENU_O2CAL_TXT3;
        mCalBtnTextId = STR_START;
        SetStepText(UIStrings::GetStr(mCalStepTextId));
        mCalButton->setText(UIStrings::GetStr(STR_START));
        mCalTipImage->setPixmap(mBiginPng);
        mCalTipImage->show();
        mInfoWidget->setCurrentWidget(mResultBlock);

        mInfoWidget->show();
        mFinishCalButton->hide();

        if(!RunModeManage::GetInstance()->IsModeActive(MODE_RUN_MANUAL) && !RunModeManage::GetInstance()->IsModeActive(MODE_RUN_ACGO))
        {
            mCalModuleStateTip->hide();
            mCalButton->setEnabled(true);
        }
        else
        {
            mCalStateTipTextId = STR_MENUAL_ACGO_STATE_TIP;
            mCalModuleStateTip->setText(UIStrings::GetStr(mCalStateTipTextId));
        }
        break;
    case O2_21_CAL:
        mCalStepTextId = STR_MAINMENU_CAL_IN_PROCESS;
        mCalBtnTextId = STR_CANCEL;
        SetStepText(UIStrings::GetStr(mCalStepTextId));
        mCalButton->setText(UIStrings::GetStr(STR_CANCEL));
        mCalProgressBar->ResetTimer();
        mInfoWidget->setCurrentWidget(mCalProgressBar);
        break;
    case O2_21_CANCEL:
        mCalStepTextId = STR_O2_CAL_CANCEL;
        mCalBtnTextId = STR_FINISH;
        SetStepText(UIStrings::GetStr(mCalStepTextId));
        mCalButton->setText(UIStrings::GetStr(STR_FINISH));
        mInfoWidget->setCurrentWidget(mResultBlock);
        mInfoWidget->show();
        mCalTipImage->setPixmap(mEndPng);
        mCalTipImage->show();
        break;
    case O2_21_DONE:
        mCalProgressBar->SetBarFinish();
        mCalProgressBar->ResetTimer();
        mCalStepTextId = STR_MAINMENU_CAL_21_DONE;
        mCalBtnTextId = STR_MAINMENU_100PO2CAL;
        SetStepText(UIStrings::GetStr(mCalStepTextId));
        mCalStepTipText->show();
        mCalButton->setText(UIStrings::GetStr(STR_MAINMENU_100PO2CAL));
        mInfoWidget->hide();
        mFinishCalButton->show();
        mCalTipImage->setPixmap(mEndPng);
        break;
    case O2_WAIT_FOR_100:

        break;
    case O2_100_CAL:
        mCalStepTextId = STR_MAINMENU_CAL_IN_PROCESS;
        mCalBtnTextId = STR_CANCEL;
        SetStepText(UIStrings::GetStr(mCalStepTextId));
        mCalButton->setText(UIStrings::GetStr(STR_CANCEL));
        mCalProgressBar->ResetTimer();
        mInfoWidget->setCurrentWidget(mCalProgressBar);
        mInfoWidget->show();
        mFinishCalButton->hide();
        break;
    case O2_100_CANCEL:
        mCalStepTextId = STR_O2_CAL_CANCEL;
        mCalBtnTextId = STR_MAINMENU_100PO2CAL;
        SetStepText(UIStrings::GetStr(mCalStepTextId));
        mCalButton->setText(UIStrings::GetStr(STR_MAINMENU_100PO2CAL));
        mInfoWidget->setCurrentWidget(mResultBlock);
        mInfoWidget->show();
        mCalTipImage->setPixmap(mEndPng);
        mCalTipImage->show();
        mFinishCalButton->show();
        break;
    case O2_100_DONE:
        mCalProgressBar->SetBarFinish();
        mCalProgressBar->ResetTimer();
        mCalStepTextId = STR_MAINMENU_CAL_100_DONE;
        mCalBtnTextId = STR_FINISH;
        SetStepText(UIStrings::GetStr(mCalStepTextId));
        mCalButton->setText(UIStrings::GetStr(STR_FINISH));
        mInfoWidget->setCurrentWidget(mResultBlock);
        mInfoWidget->show();
        mCalTipImage->setPixmap(mEndPng);
        mCalTipImage->show();
        mFinishCalButton->hide();
        break;
    case O2_21_FINISH:
        mCalStepTextId = STR_MAINMENU_CAL_FINISH;
        mCalBtnTextId = STR_FINISH;
        SetStepText(UIStrings::GetStr(mCalStepTextId));
        mCalButton->setText(UIStrings::GetStr(STR_FINISH));
        mCalTipImage->setPixmap(mEndPng);
        mCalTipImage->show();
        mFinishCalButton->hide();
        mInfoWidget->setCurrentWidget(mResultBlock);
        mInfoWidget->show();
        break;
    case O2_FINISH_CAL:
        mCalStepTextId = STR_MAINMENU_CAL_FINISH;
        mCalBtnTextId = STR_FINISH;
        SetStepText(UIStrings::GetStr(mCalStepTextId));
        mCalButton->setText(UIStrings::GetStr(STR_FINISH));
        mFinishCalButton->hide();
        mInfoWidget->setCurrentWidget(mResultBlock);
        mInfoWidget->show();
        mCalTipImage->setPixmap(mEndPng);
        mCalTipImage->show();
        break;
    case O2_DISABLE:
        mCalStateTipTextId = STR_O2_CONCENTRATION_ERROR;
        mCalModuleStateTip->setText(UIStrings::GetStr(mCalStateTipTextId));
        mCalModuleStateTip->show();
        if(mFinishCalButton->isVisible())
        {
            if(mCalButton->hasFocus())
                mFinishCalButton->setFocus();
        }
        mCalButton->setEnabled(false);
        break;
    default:
        break;
    }
    RefurbishCalResult();
}
