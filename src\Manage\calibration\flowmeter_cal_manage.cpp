#include "calibration/flowmeter_cal_manage.h"
#include "FlowmeterCal.h"
#include "CalDemoModule.h"
#include "FlowmeterView.h"
#include "Flowmeter.h"
#include "SystemConfigManager.h"
#include "SystemSettingManager.h"
#include "SystemTimeManager.h"
#include "calibration/CalModuleManage.h"

const int DEF_CANCEL_TIME = 900;

flowmeter_cal_manage::flowmeter_cal_manage()
{
    m_recv_enable = 0;
    mCalState = FM_CAL_STATUS_NORMAL;
    m_gas_type = FM_CAL_O2;
    m_cal_value = 0;
    mPvCalStatus = PV_CAL_STATUS_INACTIVE;
    ResetTimer();
    SystemTimeManager::GetInstance()->register_syn_second_timer(flowmeter_cal_manage::RefurbishTimer);
}

//流量计定标
void flowmeter_cal_manage::cal_flowmeter(unsigned short gas_type, short cal_value)
{
    unsigned short cal_cmd;

    m_gas_type = gas_type;
    m_cal_value = cal_value;

    m_recv_enable = 1;

    if (mCalState == FM_CAL_STATUS_NORMAL)
    {
        mCalState = FM_CAL_STATUS_IN_CAL;
        if (m_cal_value != 0)
            m_cal_value = 0;//第一个定标点必须为0
        cal_cmd = FM_CAL_BEGIN;
    }
    else
        cal_cmd = FM_CAL_NEXT;

    if (DEMO_MODE == SettingManager->GetIntSettingValue(SystemSettingManager::SOFT_MODE_SETTING))
        CalDemoModule::GetInstance()->ProcessCalCommand(CAL_TYPE_FLOWMETER, cal_cmd);//演示DEMO
    Flowmeter::GetInstance()->FlowmeterCal(m_gas_type, cal_cmd, m_cal_value);
}

//结束定标,定标状态恢复原始状态
void flowmeter_cal_manage::end_cal()
{
    switch (mCalState)
    {
    case FM_CAL_STATUS_IN_CAL:
        //未完成，发送取消命令
        Flowmeter::GetInstance()->FlowmeterCal(m_gas_type, FM_CAL_CANCEL, 0);
        break;
    case FM_CAL_STATUS_FINISH:
        //发送定标完成确认命令
        Flowmeter::GetInstance()->FlowmeterCal(m_gas_type, FM_CAL_OK, 0);
        break;
    default:
        break;
    }
    mCalState = FM_CAL_STATUS_NORMAL;
}

//处理定标结果
void flowmeter_cal_manage::ReceiveCalPack(unsigned short cal_result)
{
    DebugLog<<"Flowmeter Cal:Recieve Result-"<<cal_result<<"Accepted-"<<m_recv_enable;
    if (m_recv_enable == 0 || mCalState == FM_CAL_STATUS_NORMAL) //TBD
        return ;
    else
        m_recv_enable = 0;//一次定标接收一次结果
    switch (cal_result)
    {
    case FM_CAL_SINGLE_DATA_FAIL:
        refurbish_cal_result(FM_CAL_STATUS_FAIL);
        break;
    case FM_CAL_SINGLE_DATA_DONE:
        refurbish_cal_result(FM_CAL_STATUS_DONE);
        break;
    case FM_CAL_ALL_DATA_END:
        mCalState = FM_CAL_STATUS_FINISH;
        refurbish_cal_result(FM_CAL_STATUS_FINISH);
        break;
    default:
        break;
    }
}

void flowmeter_cal_manage::refurbish_cal_result(unsigned short cal_result)
{
    if (mCalPage)
    {
        mCalPage->showCalResult(m_cal_value, cal_result);
    }
}

void flowmeter_cal_manage::PvCalControl(PV_CAL_CMD calCmd)
{
    Flowmeter::GetInstance()->FlowmeterProportionalCal(calCmd);
    if (calCmd == PV_CAL_START)
    {
        mPvCalStatus = PV_CAL_STATUS_ACTIVE;
    }
    if (calCmd == PV_CAL_END)
    {
        mPvCalStatus = PV_CAL_STATUS_INACTIVE;
        emit SignalShowPVValueCalResult(PV_CAL_STATUS_FAIL, 0);
        ResetTimer();
    }
}

void flowmeter_cal_manage::ProcessPvCalResult(PV_CAL_RESULT calResult)
{
    switch (calResult) {
    case PV_CAL_SUCCEES:
        emit SignalShowPVValueCalResult(PV_CAL_STATUS_DONE, 100);
        break;
    case PV_CAL_FAIL:
        emit SignalShowPVValueCalResult(PV_CAL_STATUS_FAIL, 0);
        break;
    default:
        emit SignalShowPVValueCalResult(PV_CAL_STATUS_FAIL, 0);
        break;
    }
    mPvCalStatus = PV_CAL_STATUS_INACTIVE;
    ResetTimer();
}


void flowmeter_cal_manage::RefurbishTimer(void *wParam, void *lParam)
{
    Q_UNUSED(wParam);
    Q_UNUSED(lParam);
    flowmeter_cal_manage::GetInstance()->CountDown();
}

void flowmeter_cal_manage::ResetTimer()
{
    m_cancel_count = DEF_CANCEL_TIME;
}

void flowmeter_cal_manage::CountDown()
{
    if (PV_CAL_STATUS_INACTIVE == mPvCalStatus)
    {
        return ;
    }
    --m_cancel_count;
    if (mCalPage)
    {
        emit SignalShowPVValueCalResult(PV_CAL_STATUS_ACTIVE, (float)(DEF_CANCEL_TIME - m_cancel_count)/DEF_CANCEL_TIME * 100);
    }
    if (0 == m_cancel_count)
    {
        //超时，校零失败
        if (mCalPage)
        {
            emit SignalShowPVValueCalResult(PV_CAL_STATUS_FAIL, 100);
        }
        PvCalControl(PV_CAL_END);
    }
}
