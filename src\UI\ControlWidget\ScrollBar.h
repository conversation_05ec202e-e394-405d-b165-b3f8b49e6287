﻿#ifndef SCROLLBAR_H
#define SCROLLBAR_H

#include <QScrollBar>

class ScrollBar : public QScrollBar
{
    Q_OBJECT
public:
    explicit ScrollBar(Qt::Orientation ori, QWidget *parent =NULL) ;
    void setEnable(bool enable);
protected:
    void keyPressEvent(QKeyEvent *event);
    void focusInEvent(QFocusEvent* event);
    void focusOutEvent(QFocusEvent* event);
protected:
    bool mIsEdit =false;
};


#endif // SCROLLBAR_H
