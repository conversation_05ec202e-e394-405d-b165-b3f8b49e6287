﻿/* ****************************************************************************************
  mTimeoutCheck: 0:超时检查关闭，但模块状态随实际情况更新； 1：提供超时检查
****************************************************************************************  */
#include <mutex>

#include "queue.h"
#include "AlarmManager.h"
#include "ModuleTestManager.h"
#include "SystemTimeManager.h"
#include "SystemConfigManager.h"
#include "SystemSettingManager.h"
#include "AGModule.h"

#include "HistoryEventManager.h"
#include "ManageFactory.h"
#include "Flowmeter.h"

#define DEVICE_QUERY_STR QStringLiteral("ls -l /dev/input/by-id/")
#define DEVICE_PATTERN_STR QStringLiteral("SINO_WEALTH_USB")

QMap<int, ModuleTestManager::ModuleTestInfoSt> ModuleTestManager::sModuleTestInfoStVec
{
    {TEST_MONITOR,             {ALL_STRINGS_ENUM::STR_SELFTEST_MONITORING,         true,       DISABLE,	  2,	1,	5}},
    {TEST_BATTERY,             {ALL_STRINGS_ENUM::STR_SELFTEST_BATTERY,            false,      DISABLE,	  2,	1,	2}},
    {TEST_AC,                  {ALL_STRINGS_ENUM::STR_SELFTEST_AC,                 false,      DISABLE,	  2,	1,	2}},
    {TEST_GAS_SOURCE,          {ALL_STRINGS_ENUM::STR_SELFTEST_GAS_SOURCE,         true,       DISABLE,	  2,	1,	2}},
    {TEST_SYSTEM_TIME,         {ALL_STRINGS_ENUM::STR_SELFTEST_SYS_TIME,           false,      DISABLE,	  2,	1,	2}},
    {TEST_FLOWMETER,           {ALL_STRINGS_ENUM::STR_SELFTEST_FLOWMETER,          true,       DISABLE,	  2,	1,	2}},
    {TEST_AG_CO2,              {ALL_STRINGS_ENUM::STR_SELFTEST_AG,                 true,       DISABLE,	  2,	1,	2}},
    {TEST_O2,                  {ALL_STRINGS_ENUM::STR_SELFTEST_O2,                 true,       DISABLE,	  2,	1,	2}},
    {TEST_EXHALATION_VALUE,    {ALL_STRINGS_ENUM::STR_SELFTEST_EXHALATION_VALUE,   false,      ABLE,	  2,	1,	2}}, //TBD
    {TEST_INHALATION_VALUE,    {ALL_STRINGS_ENUM::STR_SELFTEST_INHALATION_VALUE,   false,      ABLE,	  2,	1,	2}}, //TBD
    {TEST_DATABASE,            {ALL_STRINGS_ENUM::STR_SELFTEST_DATABASE,           false,      DISABLE,	  2,	1,	2}},
    {TEST_TOUCH_PAD,           {ALL_STRINGS_ENUM::STR_TOUCH_PAD,                   false,      DISABLE,   2,    1,  2}},
};

ModuleTestManager::ModuleTestManager()
{
    ConfigRefurbish();
}

void ModuleTestManager::ConfigRefurbish()
{
    RefurbishAgConfig();

    if (!ConfigManager->GetConfig<bool>(SystemConfigManager::O2_BOOL_INDEX))
    {
        sModuleTestInfoStVec[TEST_O2].mModuleState = NO_EXIST;
    }

    if (!ConfigManager->GetConfig<bool>(SystemConfigManager::ENABLE_FLOWMETER_MODULE))
    {
        sModuleTestInfoStVec[TEST_FLOWMETER].mModuleState = NO_EXIST;
    }

    if(ConfigManager->GetConfig<int>(SystemConfigManager::MACHINE_MODEL_INDEX) != ELUNA_70 &&
            ConfigManager->GetConfig<int>(SystemConfigManager::MACHINE_MODEL_INDEX) != ELUNA_80)
    {
        sModuleTestInfoStVec[TEST_TOUCH_PAD].mModuleState = NO_EXIST;
    }
}

unsigned short ModuleTestManager::StartModuleTest()
{
   static std::once_flag flag;
    std::call_once(flag, [](){
        SystemTimeManager::GetInstance()->RegisterTimer(1000, register_internal_timer);
    });


    ConfigRefurbish();
    ModuleStateWatching();

    return 1;
}

void ModuleTestManager::register_internal_timer(void* , void* )
{
    ModuleTestManager::GetInstance()->ModuleStateWatching();
}

//更新模块的状态，并重置等待时间
unsigned short ModuleTestManager::UpdateModuleState(unsigned short moduleId, unsigned short state)
{
    if (NO_EXIST == sModuleTestInfoStVec[moduleId].mModuleState)
    {
        return 0;
    }

    reset_wait_time(moduleId);

    if (sModuleTestInfoStVec[moduleId].mModuleState != state)
    {
        sModuleTestInfoStVec[moduleId].mModuleState = state;
        emit SignalModuleStateChanged(moduleId, state);
    }

    return 1;
}


void ModuleTestManager::reset_wait_time(unsigned short moduleId)
{
    sModuleTestInfoStVec[moduleId].wait_time = sModuleTestInfoStVec[moduleId].mInvalidTime;
}

bool ModuleTestManager::IsModuleDisable(TEST_MODULE_ID id)
{
    if (sModuleTestInfoStVec[id].mModuleState == E_MODULE_STATE::DISABLE)
        return true;
    else
        return false;
}

bool ModuleTestManager::IsModuleAble(TEST_MODULE_ID id)
{
    if (sModuleTestInfoStVec[id].mModuleState == E_MODULE_STATE::ABLE)
        return true;
    else
        return false;
}

ALL_STRINGS_ENUM ModuleTestManager::GetAgDisableStateTextId()
{
    QMap<int, AG_CO2_DisableTextIdSt> sTextIdMap =
    {
        {AG, {STR_AG_ERROR, STR_TECHALARM_AG_ADAPTOR_ERR, STR_AG_WARMING, STR_CO2_CANNOT_ZERO_TIP}},
        {CO2, {STR_CO2_ERROR, STR_TECHALARM_CO2_ADAPTOR_ERR, STR_CO2_WARMING, STR_CO2_CANNOT_ZERO_TIP}}
    };
    if(mAgModuleType != None)
    {
        if(sModuleTestInfoStVec[TEST_AG_CO2].mModuleState == E_MODULE_STATE::DISABLE)
        {
            return sTextIdMap[mAgModuleType].Ununited;
        }
        else if(!mAgIsHaveAdapter)
        {
            return sTextIdMap[mAgModuleType].Adapter;
        }
        else if(mAgIsWarming)
        {
            return sTextIdMap[mAgModuleType].Warming;
        }
        else if(!mAgIsCanZero)
        {
            return sTextIdMap[mAgModuleType].CannotZero;
        }
        else
        {
            return STR_NULL;
        }
    }
    else
    {
        return STR_NULL;
    }
}

void ModuleTestManager::ResetAgParaValue()
{
    for(int i = VALUEDATA_FICO2; i <= VALUEDATA_MAC; i++)
    {
        DataManager::GetInstance()->SaveMonitorData(i, INVALID_VALUE);
    }
}

void ModuleTestManager::CloseAgAllAlarm()
{
    AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_AG_ADAPTOR_ERR, 0);
    AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_AG_SAMPLING_ERR, 0);
    AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_CO2_ADAPTOR_ERR, 0);
    AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_CO2_SAMPLING_ERR, 0);
    AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_AG_MIX_AX, 0);
    AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_AG_SW_ERR, 0);
    AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_AG_HW_ERR, 0);
    AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_AG_MOTO_ERR, 0);
    AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_AG_LOST_CAIL, 0);
    AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_AG_REPLACE, 0);
    AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_CO2_REPLACE, 0);
    AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_AG_SAMPLING_CLOGGED, 0);
    AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_CO2_SAMPLING_CLOGGED, 0);
    AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_CO2_OUT_OF_RANGE, 0);
    AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_N2O_OUT_OF_RANGE, 0);
    AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_AX_OUT_OF_RANGE, 0);
    AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_TEM_OUT_OF_RANGE, 0);
    AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_PRESS_OUT_OF_RANGE, 0);
    AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_AG_NEED_RECAIL, 0);
    AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_AG_DATA_UNTRUSTABLE, 0);
    AlarmManager::GetInstance()->TriggerAlarm(PROMPT_AG_WARMING, 0);
    AlarmManager::GetInstance()->TriggerAlarm(PROMPT_CO2_WARMING, 0);


    AlarmManager::GetInstance()->TriggerAlarm(ALARM_FICO2_HIGH, 0);
    AlarmManager::GetInstance()->TriggerAlarm(ALARM_FICO2_LOW, 0);
    AlarmManager::GetInstance()->TriggerAlarm(ALARM_ETCO2_HIGH, 0);
    AlarmManager::GetInstance()->TriggerAlarm(ALARM_ETCO2_LOW, 0);

    AlarmManager::GetInstance()->TriggerAlarm(ALARM_FIN2O_HIGH, 0);
    AlarmManager::GetInstance()->TriggerAlarm(ALARM_FIN2O_LOW, 0);
    AlarmManager::GetInstance()->TriggerAlarm(ALARM_ETN2O_HIGH, 0);
    AlarmManager::GetInstance()->TriggerAlarm(ALARM_ETN2O_LOW, 0);
    AlarmManager::GetInstance()->TriggerAlarm(ALARM_FIHAL_HIGH, 0);
    AlarmManager::GetInstance()->TriggerAlarm(ALARM_FIHAL_LOW, 0);
    AlarmManager::GetInstance()->TriggerAlarm(ALARM_ETHAL_HIGH, 0);
    AlarmManager::GetInstance()->TriggerAlarm(ALARM_ETHAL_LOW, 0);
    AlarmManager::GetInstance()->TriggerAlarm(ALARM_FIENF_HIGH, 0);
    AlarmManager::GetInstance()->TriggerAlarm(ALARM_FIENF_LOW, 0);
    AlarmManager::GetInstance()->TriggerAlarm(ALARM_ETENF_HIGH, 0);
    AlarmManager::GetInstance()->TriggerAlarm(ALARM_ETENF_LOW, 0);
    AlarmManager::GetInstance()->TriggerAlarm(ALARM_FISEV_HIGH, 0);
    AlarmManager::GetInstance()->TriggerAlarm(ALARM_FISEV_LOW, 0);
    AlarmManager::GetInstance()->TriggerAlarm(ALARM_ETSEV_HIGH, 0);
    AlarmManager::GetInstance()->TriggerAlarm(ALARM_ETSEV_LOW, 0);
    AlarmManager::GetInstance()->TriggerAlarm(ALARM_FIISO_HIGH, 0);
    AlarmManager::GetInstance()->TriggerAlarm(ALARM_FIISO_LOW, 0);
    AlarmManager::GetInstance()->TriggerAlarm(ALARM_ETISO_HIGH, 0);
    AlarmManager::GetInstance()->TriggerAlarm(ALARM_ETISO_LOW, 0);
    AlarmManager::GetInstance()->TriggerAlarm(ALARM_FIDES_HIGH, 0);
    AlarmManager::GetInstance()->TriggerAlarm(ALARM_FIDES_LOW, 0);
    AlarmManager::GetInstance()->TriggerAlarm(ALARM_ETDES_HIGH, 0);
    AlarmManager::GetInstance()->TriggerAlarm(ALARM_ETDES_LOW, 0);
}

void ModuleTestManager::RefurbishAgConfig()
{
    if(ConfigManager->GetConfig<bool>(SystemConfigManager::AG_BOOL_INDEX))
    {
        mAgModuleType = AG;
        sModuleTestInfoStVec[TEST_AG_CO2].mItemNameId = STR_SELFTEST_AG;
    }
    else if(ConfigManager->GetConfig<bool>(SystemConfigManager::CO2_BOOL_INDEX))
    {
        mAgModuleType = CO2;
        sModuleTestInfoStVec[TEST_AG_CO2].mItemNameId = STR_SELFTEST_CO2;
    }
    else
    {
        mAgModuleType = None;
        sModuleTestInfoStVec[TEST_AG_CO2].mModuleState = NO_EXIST;
    }
}

void ModuleTestManager::ChangeAgConfigByType(AG_MODULE_TYPE agType)
{
    if(mAgModuleType != agType)
    {
        mAgModuleType = agType;
        if(mAgModuleType == AG)
        {
            sModuleTestInfoStVec[TEST_AG_CO2].mItemNameId = STR_SELFTEST_AG;
        }
        else if(mAgModuleType == CO2)
        {
            sModuleTestInfoStVec[TEST_AG_CO2].mItemNameId = STR_SELFTEST_CO2;
        }
        else
        {
            sModuleTestInfoStVec[TEST_AG_CO2].mModuleState = NO_EXIST;
        }
    }

}

void ModuleTestManager::ModuleStateWatching()
{
    //实时模式时检查模块状态，demo模式下不进行状态检查；
    if (RUN_MODE != SettingManager->GetIntSettingValue(SystemSettingManager::SOFT_MODE_SETTING))
    {
        return;
    }
    for(int i = TEST_MONITOR; i < TEST_MODULE_MAX; i ++)
    {
        if (!sModuleTestInfoStVec[i].mTimeoutCheck || NO_EXIST == sModuleTestInfoStVec[i].mModuleState)
        {//如果检查关闭,则不需要做多余的超时检查
            continue;
        }
        else
        {	//检查开启情况下，正常检查
            if (sModuleTestInfoStVec[i].wait_time > 0)
            {
                sModuleTestInfoStVec[i].wait_time --;
            }
            else
            {
                sModuleTestInfoStVec[i].wait_time = sModuleTestInfoStVec[i].mInvalidTime;

                sModuleTestInfoStVec[i].mModuleState = DISABLE;
                emit SignalModuleStateChanged(i, sModuleTestInfoStVec[i].mModuleState);
                if(i == TEST_MONITOR)
                {
                    //DebugLog<<"xxx Monitor state to DIsable time:"<<QTime::currentTime().toString("mm::ss:zzz");
                }
            }
        }
    }

    notify_module_state();
}

//设置每次等待状态无效时间
unsigned short ModuleTestManager::set_wait_time(unsigned short moduleId, unsigned short sec)
{
    return sModuleTestInfoStVec[moduleId].wait_time = sModuleTestInfoStVec[moduleId].mInvalidTime = sec;
}

QVector<TEST_MODULE_ID> ModuleTestManager::GetTestableItemsId()
{
    QVector<TEST_MODULE_ID> ids{};
    for (int item{}; item < TEST_MODULE_MAX; ++item)
    {
        if (NO_EXIST != sModuleTestInfoStVec[item].mModuleState)
        {
            ids << (TEST_MODULE_ID)item;
        }
    }
    return ids;
}

TEST_MODULE_ID ModuleTestManager::GetNextTestableItemid(TEST_MODULE_ID id)
{
    for (int item = id + 1; item < TEST_MODULE_MAX; item ++)
    {
        if (NO_EXIST != sModuleTestInfoStVec[item].mModuleState)
        {
            return (TEST_MODULE_ID)item;
        }
    }
    return TEST_MODULE_MAX;
}

int ModuleTestManager::GetModuleState(unsigned short moduleId)
{
    DebugAssert(moduleId < TEST_MODULE_MAX);

    if (DEMO_MODE == SettingManager->GetIntSettingValue(SystemSettingManager::SOFT_MODE_SETTING)) //在demo模式下所有检查项都是有效地；
    {
#ifndef WIN32
        if (moduleId != TEST_BATTERY &&
                moduleId != TEST_AC &&
                moduleId != TEST_DATABASE)
            return ABLE;
#else
        if (moduleId != TEST_DATABASE)
            return ABLE;
#endif
    }

    // 检测触摸板设备
    QProcess process;
    QString command = DEVICE_QUERY_STR;
    QObject::connect(&process, &QProcess::errorOccurred, [&process](QProcess::ProcessError error) {
        DebugLog << "检测到触摸板设备进程错误退出状态: " << error << process.errorString();
    });
    process.start(command);
    process.waitForFinished();
    QString result = process.readAllStandardOutput();
    if (sModuleTestInfoStVec[TEST_TOUCH_PAD].mModuleState != NO_EXIST)
    {
        if (result.indexOf(DEVICE_PATTERN_STR) == -1)
            UpdateModuleState(TEST_TOUCH_PAD, DISABLE);
        else
            UpdateModuleState(TEST_TOUCH_PAD, ABLE);
    }

    return sModuleTestInfoStVec[moduleId].mModuleState;
}

ALL_STRINGS_ENUM ModuleTestManager::GetModuleName(unsigned short moduleId)
{
    return sModuleTestInfoStVec[moduleId].mItemNameId;
}

/* *********************************************
编写者:   	hudongnan
生成日期:   2011/10/28
功能说明:   需要进行时间限检查的项会在此判断是否触发报警或者提示
其它说明:   其余，不需要时间限检查的项在信息源进行了判断和触发提示
********************************************  */
void ModuleTestManager::notify_module_state()
{
    //通信启动后有点延时，保证检查在通信启动后进行
    static int s_count = 0;

    if (s_count < 2)
    {
        s_count ++;

        return;
    }

    //检查监控模块通信
    if(DISABLE == sModuleTestInfoStVec[TEST_MONITOR].mModuleState)
    {
        AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_MINOTORING_MODULE_ERR, 1);
    }
    else
    {
        AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_MINOTORING_MODULE_ERR, 0);
    }

    //检查AG模块通信
    if(DISABLE == sModuleTestInfoStVec[TEST_AG_CO2].mModuleState)
    {
        if(GetAgModuleType() == AG)
        {
            AlarmManager::GetInstance()->TriggerAlarm(PROMPT_AG_ERROR, 1);
        }
        else if(GetAgModuleType() == CO2)
        {
            AlarmManager::GetInstance()->TriggerAlarm(PROMPT_CO2_ERROR, 1);
        }
        ResetAgParaValue();
        CloseAgAllAlarm();
        AGModule::GetInstance()->ResetWarmingStatus();
    }
    else
    {
        AlarmManager::GetInstance()->TriggerAlarm(PROMPT_AG_ERROR, 0);
        AlarmManager::GetInstance()->TriggerAlarm(PROMPT_CO2_ERROR, 0);
    }

    if(DISABLE == sModuleTestInfoStVec[TEST_FLOWMETER].mModuleState)
    {
        AlarmManager::GetInstance()->TriggerAlarm(PROMPT_FLOWMETER_BREAK_OFF, 1);
        DataManager::GetInstance()->SaveMonitorData(VALUEDATA_FLOWMETER_O2, INVALID_VALUE);
        DataManager::GetInstance()->SaveMonitorData(VALUEDATA_FLOWMETER_AIR, INVALID_VALUE);
        DataManager::GetInstance()->SaveMonitorData(VALUEDATA_FLOWMETER_N2O, INVALID_VALUE);
        Flowmeter::GetInstance()->CloseAllTechAlarm();
    }
    else
    {
        AlarmManager::GetInstance()->TriggerAlarm(PROMPT_FLOWMETER_BREAK_OFF, 0);
    }

    //system time test
    if(DISABLE == sModuleTestInfoStVec[TEST_SYSTEM_TIME].mModuleState)
    {
        AlarmManager::GetInstance()->TriggerAlarm(PROMPT_SYSTEM_TIME_ERR, 1);
    }
    else
    {
        AlarmManager::GetInstance()->TriggerAlarm(PROMPT_SYSTEM_TIME_ERR, 0);
    }
}

unsigned int ModuleTestManager::GetTestCount()
{
    return std::count_if(sModuleTestInfoStVec.cbegin(), sModuleTestInfoStVec.cend(), [](const ModuleTestInfoSt &infoSt){
        if (infoSt.mModuleState != NO_EXIST)
            return true;
        else
            return false;
    });
}
