﻿#include "Monitoring.h"
#include "DataManager.h"
#include "RunModeManage.h"
#include "HlmModeManager.h"
#include "DebugLogManager.h"
#include "AlarmManager.h"
#include "ModuleTestManager.h"
#include "LeakTestManage.h"
#include "SystemSettingManager.h"

#include "SystemConfigManager.h"
#include "IntraData.h"
#include "DataManager.h"
#include "ChartManager.h"
#include "VTModel.h"
#include "CalDemoModule.h"
#include "FlowValveCalManage.h"
#include "peep_valve_cal_manage.h"
#include "o2CalManage.h"
#include "../Modules/DeviceModel/BytteryUiFlush.h"
#include "UpgradeManager/UpgradeManager.h"
#include "ManageFactory.h"
#include "gpio.h"
#include "../Com/Qt4SerialPort/posix_qextserialport.h"

#ifdef WIN32
#define SERIALNAME "com4"
#else
#define SERIALNAME "/dev/ttymxc4"
#endif

#define BAUDRATE (19200)
#define UPDATE_BAUDRATE (115200)
#define STOPBIT QSerialPort::OneStop
#define PARITY QSerialPort::NoParity

#define ACK_SUCCESS 0X06
unsigned int cur_monitor_addr =0x00;

Monitoring::Monitoring(QObject *parent) : LowerProxyBase(parent)
{
    connect(PowerUiFlush::GetInstance(), &PowerUiFlush::signalChangeHeatingModuleState, this, [this](bool isOpen){
        this->SendIsOpenHeat(isOpen);
    }, Qt::QueuedConnection);
}

Monitoring *Monitoring::GetInstance()
{
    static Monitoring *mObject = new Monitoring;
    static Qt::HANDLE curthired = QThread::currentThreadId();
    if(curthired!=QThread::currentThreadId())
    {
        if(mObject!=NULL)
            return mObject;
    }
    return  mObject;
}

bool Monitoring::SendMonitoringSetting(T_MASTER_SET masterSet)
{
    if (RunModeManage::GetInstance()->IsModeActive(MODE_RUN_STANDBY))
    {
        masterSet.VentMode = VENTMODE_FLAG_STANDBY;
    }

    //下发peep如果小于4即认为是0；
    if (4 > masterSet.Peep)
    {
        masterSet.Peep = 0;
    }

    //触发值为0时认为触发关闭
    if (0 == masterSet.TriggerValue)
    {
        masterSet.TriggerFlag = 0;//TRIG-OFF
    }
    else if ( 0 > masterSet.TriggerValue && TRIG_FLAG_PTRIG  == masterSet.TriggerFlag ) //tcg
    {//ptrig情况下，发送正数
        masterSet.TriggerValue = -masterSet.TriggerValue;
    }

    //HLM模式下，只有CPAP有效，转化为pinsp
    if (VENTMODE_FLAG_HLM == masterSet.VentMode )
    {
        masterSet.Pinsp = HlmModeManager::GetInstance()->GetHlmSettingdata(SETTINGDATA_CPAP);
    }

    if (masterSet.Psupp < 5)
    {
        masterSet.Psupp = 0;
    }

    QByteArray sendData((char*)&masterSet,sizeof(masterSet));
    DebugLog << sendData << __FUNCTION__;
    return SendData(MM_COMM_SETTING_MSGID,sendData);
}

bool Monitoring::SendO2CalCmd(int cmd)
{
    if (DEMO_MODE == SettingManager->GetIntSettingValue(SystemSettingManager::SOFT_MODE_SETTING))
    {
        CalDemoModule::GetInstance()->ProcessCalCommand(CAL_TYPE_O2_21, cmd);
        return true;
    }
    unsigned char correct_cmd_value =cmd;
    QByteArray sendData((char*)&correct_cmd_value, sizeof(correct_cmd_value));
    return SendData(MM_COMM_O2_CORRECT_MSGID, sendData);
}

bool Monitoring::SendSelfChekCmd(int cmd)
{
    LeakTestResult command;
    command.mCmd=cmd;
    command.c_data = INVALID_VALUE;
    command.r_data = INVALID_VALUE;
    QByteArray sendData((char*)&command,sizeof(command));
    return SendData(MM_COMM_SELFCHECK_MSGID,sendData);
}

bool Monitoring::SendQueryVersion()
{
    QByteArray sendData;
    sendData.push_back((char)0);
    return SendData(MM_COMM_QUERY_VERSION_ID,sendData);
}

bool Monitoring::SendIsOpenHeat(bool isOpen)
{
    QByteArray sendData;
    if(isOpen)
    {
        sendData.push_back((char)1);
    }
    else
    {
        sendData.push_back((char)0);
    }

    return SendData(MM_COMM_HEAT_ID,sendData);
}

bool Monitoring::SendOnePack(int packType, int msgID, unsigned char MsgLen, const unsigned char* Msg)
{
    return SendData(msgID,(const char *)Msg,MsgLen);
}

bool Monitoring::SendStartCalLowFlow()
{
    mCurCalId=0;
    FlowPressCmdAndData data;
    data.cur_cal_cmd=CAL_CMD_LOW_FLOW_START;
    data.cur_cal_index=0;
    QByteArray sendData((char*)&data,sizeof(data));
    return SendData(MM_COMM_CAL_MSGID,sendData);
}

bool Monitoring::SendStartCalHightFlow()
{
    mCurCalId=0;
    FlowPressCmdAndData data;
    data.cur_cal_cmd=CAL_CMD_HIGH_FLOW_START;
    QByteArray sendData((char*)&data,sizeof(data));
    return SendData(MM_COMM_CAL_MSGID,sendData);
}

bool Monitoring::SendStartCalPressure()
{
    FlowPressCmdAndData data;
    data.cur_cal_cmd=CAL_CMD_PRESS_START;
    QByteArray sendData((char*)&data,sizeof(data));
    return SendData(MM_COMM_CAL_MSGID,sendData);
}

bool Monitoring::SendCancelCalFowPressure()
{
    mCurCalId=0;
    FlowPressCmdAndData data;
    data.cur_cal_cmd=CAL_CMD_CANCLE;
    QByteArray sendData((char*)&data,sizeof(data));
    return SendData(MM_COMM_CAL_MSGID,sendData);
}

void Monitoring::InitSerial()
{
    mSerialPort.setPortName(SERIALNAME);
    mSerialPort.setBaudRate(BAUDRATE);
    mSerialPort.setStopBits(STOPBIT);
    mSerialPort.setParity(PARITY);

    if(!SetCommIODevice(&mSerialPort))
        DebugLog<<"Monitoring serial connect error";
}

void Monitoring::CloseSerial()
{
    if(mSerialPort.isOpen())
        mSerialPort.close();
}

void Monitoring::StartUpgrade()
{
    DebugLog << "StartUpgrade";
    if(!QFile::exists(mGasFilePath))
        return;

    AlarmManager::GetInstance()->DisableAnAlarm(TECHALARM_MINOTORING_MODULE_ERR);
    CloseSerial();

    ManageFactory::GetInstance()->GetDeviceManage()->ResetartMonitoring();
    GpioWriteBit(BANK_DOWNLOAD,GPIO_6_NR_23,0);
    ::std::this_thread::sleep_for(::std::chrono::milliseconds(20));
    GpioWriteBit(BANK_DOWNLOAD,GPIO_6_NR_22,0);
    ::std::this_thread::sleep_for(::std::chrono::milliseconds(20));
    GpioWriteBit(BANK_DOWNLOAD,GPIO_6_NR_22,1);
    ::std::this_thread::sleep_for(::std::chrono::milliseconds(500));

#ifndef WIN32
    mGsSerial = new Posix_QextSerialPort(SERIALNAME,QextSerialBase::Polling);

    if (mGsSerial->open(QIODevice::ReadWrite))
    {
        mGsSerial->setBaudRate(BAUD115200);
        mGsSerial->setDataBits(DATA_8);
        mGsSerial->setStopBits(STOP_1);
        mGsSerial->setParity(PAR_NONE);
        mGsSerial->setFlowControl(FLOW_OFF);
        mGsSerial->setTimeout(10);
        mGsSerial->flush();
    }

    char enterDMcode=0x08;
    mGsSerial->writeData(&enterDMcode,1);
    if(WaitSerialRespon())
    {
        DebugLog << "接收到回应";

        PROT_DATA datapack = {0XA2,512,};
        PROT_CMD prot_cmd = {0XA1,};
        prot_cmd.target_type = 0XC1;

        QFile binfile;
        binfile.setFileName(mGasFilePath);
        binfile.open(QIODevice::ReadOnly);
        binfile.seek(0);
        int file_size = binfile.size();
        int cur_read_index =0;
        while(1)
        {
            memset(datapack.data,0,PACK_DAT_LEN);
            if(cur_read_index >= (file_size - 1))
            {
                prot_cmd.cmd = 0X5C;
                WriteDataToSerial(NULL);
                break;
            }
            else if((cur_read_index + PACK_DAT_LEN) > file_size)
            {
                datapack.datalen = file_size - cur_read_index;
                binfile.seek(cur_read_index);
                binfile.read((char *)(datapack.data),datapack.datalen);
                cur_read_index += file_size - cur_read_index;
                if(!WriteDataToSerial(&datapack))
                    break;
            }
            else
            {
                binfile.seek(cur_read_index);
                binfile.read((char *)(datapack.data),PACK_DAT_LEN);
                cur_read_index += PACK_DAT_LEN;
                if(!WriteDataToSerial(&datapack))
                    break;
            }
        }

        binfile.close();
    }
    DebugLog << "重启设备";
    mGsSerial->close();
    ManageFactory::GetInstance()->GetDeviceManage()->ResetartMonitoring();
    InitSerial();

    if(QFile::exists(mGasFilePath))
        QFile::remove(mGasFilePath);

    emit SignalUpgradeFinish();
#endif
}

void Monitoring::UnpackData(int msgType,QByteArray data)
{
    if(msgType==7)
        DebugLog<<"RECV  : "<< msgType << data.toHex().rightJustified(2, '0');
    switch (msgType) {
    case MM_COMM_WAVE_MSGID:	//波形包
        if (SettingManager->GetIntSettingValue(SystemSettingManager::SOFT_MODE_SETTING) == DEMO_MODE)
        {
            if (RunModeManage::GetInstance()->GetCurRunMode() == E_RUNMODE::MODE_RUN_MANUAL
                    || RunModeManage::GetInstance()->GetCurRunMode() == E_RUNMODE::MODE_RUN_ACGO)
            {
                UnpackWaveData(data);
            }
        }
        else
            UnpackWaveData(data);
        break;
    case MM_COMM_MONITOR_PARA_MSGID:	//监控参数包
        UnpackValueData(data);
        break;
    case MM_COMM_ALARM_MSGID:	//报警包
        UnpackAlarm(data);
        break;
    case MM_COMM_O2_CORRECT_MSGID:		//O2浓度校准包
        UnpackO2Cal(data);
        break;
    case MM_COMM_FLOW_CORRETC_MSGID:	//吸入呼出端流速校准包
        // Unpack_CAL_rate(&ReceiveMsgBuff[5]);TBD并没有用？
        break;
    case MM_COMM_CAL_MSGID:
    {
        if(FlowValveCalManage::GetInstance()->GetCalState()!=FLOW_CAL_NORMAL)
            UnpackFlowCal(data);
        else if(peep_valve_cal_manage::GetInstance()->GetCalState()!=PRESS_CAL_NORMAL)
            UnpackPressureCal(data);
    }
        break;//流速、压力、校准工具定标包
    case MM_COMM_IMPORT_CAL_DATA_MSGID:
    case MM_COMM_EXPORT_CAL_DATA_MSGID:{		//导出流速与压力定标数据
        // 以上包通过网络发往PC
        UnpackWaveData(data);
    }
        break;
    case MM_COMM_SELFCHECK_MSGID:
        UnpackLeakTest(data);
        break;
    case MM_COMM_QUERY_VERSION_ID:
        // TODO:
        UnpackVersion(data);
        break;
    }
}

void Monitoring::UnpackO2Cal(QByteArray data)
{
    unsigned char cal_o2_cmd = data[0];
    O2CalManage::GetInstance()->ReceiveCalPack(cal_o2_cmd);
}

void Monitoring::UnpackWaveData(QByteArray data)
{
    MonitorWaveDataStr wave;
    wave.Press = CHAR2_TO_INT16(data[0],data[1]);
    wave.Flow = CHAR2_TO_INT16(data[2],data[3]);
    wave.Vol = CHAR2_TO_INT16(data[4],data[5]);
    wave.spontFlag = CHAR2_TO_INT16(data[6], data[7]);

    DataManager::GetInstance()->UpdateWaveData(DataManager::WAVE_PAW, wave.Press, wave.spontFlag);
    DataManager::GetInstance()->UpdateWaveData(DataManager::WAVE_FLOW, wave.Flow,  wave.spontFlag);
    DataManager::GetInstance()->UpdateWaveData(DataManager::WAVE_VOL, wave.Vol,  wave.spontFlag);

    //自主呼吸是一种和模式相关的状态
    if(wave.spontFlag)
    {
        RunModeManage::GetInstance()->ActiveRunMode(MODE_RUN_SPONT);
    }
    else
    {
        RunModeManage::GetInstance()->InactiveRunMode(MODE_RUN_SPONT);
    }
}

void Monitoring::UnpackValueData(QByteArray data)
{
    MonitorWatchParam paramData;

    paramData.Vte = CHAR2_TO_INT16(data[0], data[1]);
    paramData.Vti = CHAR2_TO_INT16(data[2], data[3]);
    paramData.MV = CHAR2_TO_INT16(data[4], data[5]);
    paramData.MVSpont	= CHAR2_TO_INT16(data[6], data[7]);
    paramData.Rate	= CHAR2_TO_INT16(data[8], data[9]);
    paramData.RateSpont	= CHAR2_TO_INT16(data[10], data[11]);
    paramData.IE	= CHAR2_TO_INT16(data[12], data[13]);
    paramData.Cdyn = CHAR2_TO_INT16(data[14], data[15]);
    paramData.C20C	= CHAR2_TO_INT16(data[16], data[17]);
    paramData.R	= CHAR2_TO_INT16(data[18], data[19]);
    paramData.Ppeak =  CHAR2_TO_INT16(data[20], data[21]);
    paramData.Pplat = CHAR2_TO_INT16(data[22], data[23]);
    paramData.Peep  = CHAR2_TO_INT16(data[24], data[25]);
    paramData.Pmean	= CHAR2_TO_INT16(data[26], data[27]);


    DataManager::GetInstance()->SaveMonitorData(VALUEDATA_VTE, paramData.Vte);
    DataManager::GetInstance()->SaveMonitorData(VALUEDATA_VTI, paramData.Vti);
    DataManager::GetInstance()->SaveMonitorData(VALUEDATA_MV, paramData.MV);
    DataManager::GetInstance()->SaveMonitorData(VALUEDATA_MVSPN,paramData.MVSpont);
    DataManager::GetInstance()->SaveMonitorData(VALUEDATA_RATE, paramData.Rate);
    DataManager::GetInstance()->SaveMonitorData(VALUEDATA_FSPN, paramData.RateSpont);
    DataManager::GetInstance()->SaveMonitorData(VALUEDATA_IE, paramData.IE);
    DataManager::GetInstance()->SaveMonitorData(VALUEDATA_C, paramData.Cdyn);
    DataManager::GetInstance()->SaveMonitorData(VALUEDATA_C20C, paramData.C20C);
    DataManager::GetInstance()->SaveMonitorData(VALUEDATA_R, paramData.R);

    short pmean = 0, ppeep = 0, pplat, ppeek;

    pplat = paramData.Pplat;
    DataManager::GetInstance()->SaveMonitorData(VALUEDATA_PPLAT, (pplat +5)/10);
    ppeek = paramData.Ppeak;
    DataManager::GetInstance()->SaveMonitorData(VALUEDATA_PPEAK, (ppeek+5)/10);
    ////该值与ppeak值一样，但用途不同,用于最高压力检查
    DataManager::GetInstance()->SaveMonitorData(VALUEDATA_TOP_PAW, (ppeek+5)/10);
    pmean = paramData.Pmean;
    DataManager::GetInstance()->SaveMonitorData(VALUEDATA_PMEAN, (pmean + 5) / 10);
    ppeep = paramData.Peep;

    //如果peep设置值为off,则peep值无效
    T_MASTER_SET pack;
    VentModeSettingManager::GetInstance()->GetSettingdata(pack);
    if (pack.Peep < 4 && RunModeManage::GetInstance()->GetCurRunMode() == E_RUNMODE::MODE_RUN_VENT)
    {
        ppeep = INVALID_VALUE;
    }

    DataManager::GetInstance()->SaveMonitorData(VALUEDATA_PEEP, (ppeep + 5) / 10);

    emit SignalCurWavePeriodEnd(); //TBD 一个呼吸周期？
}

void Monitoring::UnpackAlarm(QByteArray data)
{
    MonitorAlarmStr alarmData;
    memcpy(&alarmData, data.data(), sizeof(MonitorAlarmStr));

    ModuleTestManager::GetInstance()->UpdateModuleState(TEST_MONITOR, ABLE);
    DataManager::GetInstance()->SaveMonitorData(VALUEDATA_FIO2,alarmData.FiO2);

    if(alarmData.MonitorState & STATE_MANUAL_BIT)
    {//手动方式
        RunModeManage::GetInstance()->ActiveRunMode(MODE_RUN_MANUAL);
    }
    else
    {//机控方式
        RunModeManage::GetInstance()->InactiveRunMode(MODE_RUN_MANUAL);
    }

    if(alarmData.MonitorState & STATE_ACGO_BIT )
    {
        AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_VENT_STATE_ACGO,1);
        RunModeManage::GetInstance()->ActiveRunMode(MODE_RUN_ACGO);
    }
    else
    {
        AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_VENT_STATE_ACGO,0);
        RunModeManage::GetInstance()->InactiveRunMode(MODE_RUN_ACGO);
    }

    //呼吸机模块报警
    if(alarmData.MonitorState & STATE_PRESS_SENSOR_ERR_BIT/* ALARM_PRESS_OVER_LIMIT_BIT  */  )
    {	//压力传感器错误
        AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_PRESSURE_SENSOR_ERR, 1);
    }
    else
    {
        AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_PRESSURE_SENSOR_ERR, 0);
    }

    //监控传感器校零失败
    if(alarmData.MonitorState & STATE_SENSOR_CAL_ZERO_FAILD_BIT)
    {
        AlarmManager::GetInstance()->TriggerAlarm(PROMPT_MONIT_SENSOR_CAL_FAIL, 1);
    }
    else
    {
        AlarmManager::GetInstance()->TriggerAlarm(PROMPT_MONIT_SENSOR_CAL_FAIL, 0);
    }
    //定标数据异常
    if(alarmData.MonitorState & STATE_SENSOR_CAL_DATA_EXCEPTION_BIT)
    {
        AlarmManager::GetInstance()->TriggerAlarm(PROMPT_MONITOR_ACCURACY_LOW, 1);
    }
    else
    {
        AlarmManager::GetInstance()->TriggerAlarm(PROMPT_MONITOR_ACCURACY_LOW, 0);
    }

    //呼气阀失效
    if(alarmData.MonitorState & STATE_EXP_VALVE_INVALID_BIT	)
    {
        AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_EXP_VALVE_ERR, 1);
    }
    else
    {
        AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_EXP_VALVE_ERR, 0);
    }

    //呼气阀堵死
    if(alarmData.MonitorState & STATE_EXP_VALVE_SEAL_OFF_BIT)
    {
        AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_EXP_VALVE_CLOSE_OFF, 1);
    }
    else
    {
        AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_EXP_VALVE_CLOSE_OFF, 0);
    }

    //吸气阀故障
    if(alarmData.MonitorState & STATE_INSP_VALVE_ERR_BIT)
    {
        AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_INSP_VALVE_ERR, 1);
    }
    else
    {
        AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_INSP_VALVE_ERR, 0);
    }

    //加热模块故障
    if(alarmData.MonitorState & STATE_HEATING_MODULE_INVALID_BIT)
    {
        AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_HEATING_MODULE_ERR, 1);
    }
    else
    {
        AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_HEATING_MODULE_ERR, 0);
    }

    //管道脱落
    if(alarmData.MonitorState & STATE_PIPE_FALL_OFF_BIT)
    {
        AlarmManager::GetInstance()->TriggerAlarm(ALARM_PRESS_LOW_CONTINUA,1);
    }
    else
    {
        AlarmManager::GetInstance()->TriggerAlarm(ALARM_PRESS_LOW_CONTINUA,0);
    }

    //快速充氧超时
    if(alarmData.MonitorState & STATE_ADDO2_OVERTIME_BIT)
    {
        AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_ADDO2_OVERTIME,1);
    }
    else
    {
        AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_ADDO2_OVERTIME,0);
    }
    //快速充氧故障
    if(alarmData.MonitorState & STATE_ADDO2_ERR_BIT)
    {
        AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_ADDO2_ERR,1);
    }
    else
    {
        AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_ADDO2_ERR,0);
    }

    //回路故障
    if(alarmData.MonitorState &  STATE_GAS_LOOP_ERR_BIT)
    {
        AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_GAS_LOOP_ERR,1);
    }
    else
    {
        AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_GAS_LOOP_ERR,0);
    }

    //气源压力低 仅真机
    if(RUN_MODE == SettingManager->GetIntSettingValue(SystemSettingManager::SOFT_MODE_SETTING))
    {
        if(alarmData.MonitorState & STATE_GAS_SOURCE_PRESS_LOW_BIT)
        {
            AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_GASSOURCE_LOW,1);
            //同样,气源模块检查也以此作为依据
            ModuleTestManager::GetInstance()->UpdateModuleState(TEST_GAS_SOURCE, DISABLE);
        }
        else
        {
            AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_GASSOURCE_LOW,0);
            //同样,气源模块检查也以此作为依据
            ModuleTestManager::GetInstance()->UpdateModuleState(TEST_GAS_SOURCE, ABLE);
        }
    }


    //氧气气源压力低 仅真机
    if(RUN_MODE == SettingManager->GetIntSettingValue(SystemSettingManager::SOFT_MODE_SETTING))
    {
        if(alarmData.MonitorState & STATE_O2_SOURCE_PRESS_LOW_BIT)
        {
            AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_GASSOURCE_O2_LOW,1);
            //同样,气源模块检查也以此作为依据
        }
        else
        {
            AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_GASSOURCE_O2_LOW,0);
            //同样,气源模块检查也以此作为依据

        }
    }


    //钠石灰罐未安装
    if(alarmData.MonitorState & STATE_BYPASS_NOT_INSTALL_BIT)
    {
        AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_BYPASS_ERR,1);
    }
    else
    {
        AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_BYPASS_ERR,0);
    }

    //压力是否达到限制
    if(alarmData.MonitorAlarm & ALARM_PRESS_OVER_LIMIT_BIT)
    {
        AlarmManager::GetInstance()->TriggerAlarm(PROMPT_PRESSURE_LIMITD, 1);
    }
    else
    {
        AlarmManager::GetInstance()->TriggerAlarm(PROMPT_PRESSURE_LIMITD, 0);
    }
    //持续气道压力高
    if(alarmData.MonitorAlarm & ALARM_PRESS_LAST_HIGH_BIT)
    {
        AlarmManager::GetInstance()->TriggerAlarm(ALARM_PRESS_HIGH_CONTINUA,1);
    }
    else
    {
        AlarmManager::GetInstance()->TriggerAlarm(ALARM_PRESS_HIGH_CONTINUA,0);
    }

    //气道负压
    if(alarmData.MonitorAlarm & ALARM_NEGATIVE_PRESS_BIT)
    {
        AlarmManager::GetInstance()->TriggerAlarm(ALARM_NEGATIVE_PRESS,1);
    }
    else
    {
        AlarmManager::GetInstance()->TriggerAlarm(ALARM_NEGATIVE_PRESS,0);
    }

    //窒息
    if(alarmData.MonitorAlarm & ALARM_APNEA_BIT)
    {
        AlarmManager::GetInstance()->TriggerAlarm(ALARM_APNEA,1);
    }
    else
    {
        AlarmManager::GetInstance()->TriggerAlarm(ALARM_APNEA,0);
    }

    //120s窒息
    if(alarmData.MonitorAlarm & ALARM_APNEA_120S_BIT)
    {
        AlarmManager::GetInstance()->TriggerAlarm(ALARM_APNEA_120,1);
    }
    else
    {
        AlarmManager::GetInstance()->TriggerAlarm(ALARM_APNEA_120,0);
    }

    //氧浓度传感器未连接TBD
    if(alarmData.MonitorAlarm & ALARM_O2_SENSOR_ERR_BIT && ConfigManager->GetConfig<int>(SystemConfigManager::O2_BOOL_INDEX))
    {
        //	AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_O2_SENSOR_ERR, 1);
        AlarmManager::GetInstance()->TriggerAlarm(PROMPT_O2_CONCENTRATION_ERROR, 1);
        ModuleTestManager::GetInstance()->UpdateModuleState(TEST_O2, DISABLE);
        DataManager::GetInstance()->SaveMonitorData(VALUEDATA_FIO2, INVALID_VALUE);
        AlarmManager::GetInstance()->TriggerAlarm(ALARM_FIO2_HIGH, 0);
        AlarmManager::GetInstance()->TriggerAlarm(ALARM_FIO2_LOW, 0);
    }
    else
    {
        //AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_O2_SENSOR_ERR, 0);
        AlarmManager::GetInstance()->TriggerAlarm(PROMPT_O2_CONCENTRATION_ERROR, 0);
        ModuleTestManager::GetInstance()->UpdateModuleState(TEST_O2, ABLE);
        DataManager::GetInstance()->SaveMonitorData(VALUEDATA_FIO2, alarmData.FiO2);
    }
    if(alarmData.MonitorAlarm & ALARM_FLOW_SENSOR_ERR_BIT)
    {
        AlarmManager::GetInstance()->TriggerAlarm(ALARM_FLOW_SENSOR_ERR, 1);
    }else
    {
        AlarmManager::GetInstance()->TriggerAlarm(ALARM_FLOW_SENSOR_ERR, 0);
    }
}


void Monitoring::UnpackLeakTest(QByteArray data)
{
    LeakTestResult st_result;
    memcpy(&st_result, data, sizeof(LeakTestResult));
    int value;
    switch(st_result.mCmd)
    {
    case SELFTEST_DONE:
        value = st_result.c_data;
        break;
    case SELFTEST_FAIL:
        value = INVALID_VALUE;
        break;
    default:
        value = INVALID_VALUE;
        break;
    }
    LeakTestManage::GetInstance()->ReceiveTestPack(st_result.mCmd, st_result.r_data, value);
}

void Monitoring::UnpackVersion(QByteArray data)
{
    if ( !data.isEmpty() )
    {
        UpgradeManager::GetInstance()->SetMachineVersion(MONITOR_BOARD, data);
    }
}

void Monitoring::UnpackFlowCal(QByteArray data)
{
    FlowPressCmdAndData tResult;
    memcpy(&tResult, data, sizeof(FlowPressCmdAndData));
    if(tResult.cur_cal_cmd==CAL_CMD_LOW_FLOW_PRO||tResult.cur_cal_cmd==CAL_CMD_HIGH_FLOW_PRO)
    {
        if(tResult.cur_cal_cmd==CAL_CMD_LOW_FLOW_PRO)
            DebugLog<<"Low Flow Cal. ing"<<tResult.cur_cal_cmd;
        else
            DebugLog<<"High Flow Cal. ing"<<tResult.cur_cal_cmd;
        double flow = VTManager->GetFlow();
        tResult.cur_da_flow_press=flow*100;
        DebugLog<<"Tell Monitor Now Flow Value is"<<tResult.cur_da_flow_press;
        QByteArray sendData((char*)&tResult,sizeof(tResult));
        SendData(MM_COMM_CAL_MSGID,sendData);
    }
    else if(tResult.cur_cal_cmd == CAL_CMD_LOW_FLOW_SUCCESS)
    {
        DebugLog<<"Low Flow Cal. Success!";
        DebugLog<<"Tell Monitor LowFlowConfirm";
        tResult.cur_cal_cmd=CAL_CMD_LOW_FLOW_CONFIRM;
        QByteArray sendData((char*)&tResult,sizeof(tResult));
        SendData(MM_COMM_CAL_MSGID,sendData);
    }
    else if(tResult.cur_cal_cmd == CAL_CMD_HIGH_FLOW_SUCCESS)
    {
        DebugLog<<"High Flow Cal. Success!";
        DebugLog<<"Tell Monitor HighFlowConfirm";
        tResult.cur_cal_cmd=CAL_CMD_HIGH_FLOW_CONFIRM;
        QByteArray sendData((char*)&tResult,sizeof(tResult));
        SendData(MM_COMM_CAL_MSGID,sendData);
    }
    else
    {
        DebugLog<<"Flow Cal Cmd"<<tResult.cur_cal_cmd;
    }
    FlowValveCalManage::GetInstance()->ReceiveCalPack(tResult.cur_cal_cmd);
}

void Monitoring::UnpackPressureCal(QByteArray data)
{
    FlowPressCmdAndData tResult;
    memcpy(&tResult, data, sizeof(FlowPressCmdAndData));
    if(tResult.cur_cal_cmd==CAL_CMD_PRESS_PRO)
    {
        DebugLog<<"Pressure Cal. ing";
        double press = VTManager->GetPress();
        tResult.cur_da_flow_press=press*10;
        DebugLog<<"Tell Monitor Now Pressure Value is"<<tResult.cur_da_flow_press;
        QByteArray sendData((char*)&tResult,sizeof(tResult));
        SendData(MM_COMM_CAL_MSGID,sendData);
    }
    else if(tResult.cur_cal_cmd == CAL_CMD_PRESS_SUCCESS)
    {
        DebugLog<<"Pressure Cal. Success!";
        DebugLog<<"Tell Monitor PressureConfirm";
        tResult.cur_cal_cmd=CAL_CMD_PRESS_CONFIRM;
        QByteArray sendData((char*)&tResult,sizeof(tResult));
        SendData(MM_COMM_CAL_MSGID,sendData);
    }
    else
    {
        DebugLog<<"Pressure Cal Cmd"<<tResult.cur_cal_cmd;
    }
    peep_valve_cal_manage::GetInstance()->ReceiveCalPack(tResult.cur_cal_cmd);
}

bool Monitoring::WaitSerialRespon(bool isNeedCheckValue)
{
    int failTime = 2000;
    while(1)
    {
        QByteArray recvData= mGsSerial->readAll();
        if(recvData.length()>0)
        {
            if(!isNeedCheckValue)
            {
                static int recvFirstMsgCount =0;
                recvFirstMsgCount+=recvData.length();
                if(recvFirstMsgCount==24)
                        return true;
            }
            else
            {
                char *recv =recvData.data();
                if(recv[0] == ACK_SUCCESS)
                    return true;
                else
                    return false;
            }
        }
        ::std::this_thread::sleep_for(::std::chrono::milliseconds(2));
        failTime-=2;
        if(failTime<=0)
            return false;
    }
}

bool Monitoring::WriteDataToSerial(PROT_DATA *pdata)
{
    static int pack128Count =0;
    static int packWCount =0;
    MONITOR_UPDATE_PACK pack = {0X07,0X0E,};
    if(pdata == NULL)   // 包已发送完毕
    {
        pack.datasize = 5;
        pack.cmd = 'R';
        pack.addr = 0x01;
        CrcMonitorPack(&pack);
        mGsSerial->writeData((char*)&pack,4 + pack.datasize);
        return WaitSerialRespon(true);
    }
    pack.datasize = 6;
    pack.cmd = 'E';
    pack.addr = ((cur_monitor_addr & 0xff000000) >> 24) +
            ((cur_monitor_addr & 0x00ff0000) >> 8) +
            ((cur_monitor_addr & 0x0000ff00) << 8) +
            ((cur_monitor_addr & 0x000000ff) << 24);
    pack.data[0] = 1;
    CrcMonitorPack(&pack);
    mGsSerial->writeData((char*)&pack,4 + pack.datasize);
    if(!WaitSerialRespon(true))
        return false;
    packWCount++;
    for(int i = 0;i < 4;i++)
    {
        int k;
        pack.datasize = 0x5 + 128;
        pack.cmd = 'W';
        pack.addr = ((cur_monitor_addr & 0xff000000) >> 24) +
                ((cur_monitor_addr & 0x00ff0000) >> 8) +
                ((cur_monitor_addr & 0x0000ff00) << 8) +
                ((cur_monitor_addr & 0x000000ff) << 24);
        for(k = 0;k < 128;k++)
            pack.data[k] = pdata->data[k + (i << 7)];
        CrcMonitorPack(&pack);
        mGsSerial->writeData((char*)&pack,6 + pack.datasize);
        if(!WaitSerialRespon(true))
            return false;
        cur_monitor_addr += 128;
        pack128Count++;
    }
    return true;
}

void Monitoring::CrcMonitorPack(MONITOR_UPDATE_PACK *pack)
{
    char crc = 0;
    unsigned char sum = 0;
    int i;
    unsigned char *p = &(pack->datasize);
    for(i = 0;i < (pack->datasize + 1);i++)
    {
        sum += *p++;
    }
    crc -= sum;
    *p++ = crc;
}
