﻿#ifndef LOOPCHARTWITHSWITCHER_H
#define LOOPCHARTWITHSWITCHER_H

#include "FocusWidget.h"
#include "LoopChart.h"
#include "ComboBox.h"

class LoopChartWithSwitcher : public LoopChart
{
    Q_OBJECT
public:
    LoopChartWithSwitcher(LOOP_TYPE , QWidget *parent = nullptr);
    bool IsInCombox(QPoint);
    void SetLoopType(LOOP_TYPE);
    LOOP_TYPE GetType();
protected:
    void resizeEvent(QResizeEvent *event);
    void mousePressEvent(QMouseEvent *);

signals:
    void SignalLoopClick();
    void SignalLoopTypeChanged(LOOP_TYPE);

private:
    IconComboBox *mSwitchTypeComboBox{};
};

#endif // LOOPCHARTWITHSWITCHER_H
