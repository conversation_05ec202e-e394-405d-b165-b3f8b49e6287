//2025-06-04 14:34:25
//Generated by BAIGE
#pragma once
#include <QStringList>
#include "StringEnum.h"

namespace English
{
    QStringList UiStr
    {

		u8R"()", //STR_NULL
		u8R"(VCV)", //STR_LABEL_VCV
		u8R"(PCV)", //STR_LABEL_PCV
		u8R"(PSV)", //STR_LABEL_PSV
		u8R"(SIMV-VC)", //STR_LABEL_SIMVVCV
		u8R"(SIMV-PC)", //STR_LABEL_SIMVPCV
		u8R"(PRVC)", //STR_LABEL_PRVC
		u8R"(P-mode)", //STR_LABEL_PMODE
		u8R"(HLM)", //STR_LABEL_HLM
		u8R"(CPB)", //STR_LABEL_CPB
		u8R"(MECHANICAL)", //STR_MODE_MECHANICAL
		u8R"(ACGO)", //STR_MODE_ACGO
		u8R"(Standby)", //STR_MODE_STANDBY
		u8R"(Manual)", //STR_MODE_MANUAL
		u8R"(Rate)", //STR_VALUEPARAM_RATE
		u8R"(fspn)", //STR_VALUEPARAM_FSPN
		u8R"(I:E)", //STR_VALUEPARAM_IE
		u8R"(VTe)", //STR_VALUEPARAM_VTE
		u8R"(VTi)", //STR_VALUEPARAM_VTI
		u8R"(MV)", //STR_VALUEPARAM_MV
		u8R"(MVspn)", //STR_VALUEPARAM_MVSPN
		u8R"(Ppeak)", //STR_VALUEPARAM_PPEAK
		u8R"(Pplat)", //STR_VALUEPARAM_PPLAT
		u8R"(PEEP)", //STR_VALUEPARAM_PEEP
		u8R"(Pmean)", //STR_VALUEPARAM_PMEAN
		u8R"(Pmin)", //STR_VALUEPARAM_PMIN
		u8R"(R)", //STR_VALUEPARAM_R
		u8R"(C)", //STR_VALUEPARAM_C
		u8R"(FiO2)", //STR_VALUEPARAM_FIO2
		u8R"(EtCO2)", //STR_VALUEPARAM_ETCO2
		u8R"(FiCO2)", //STR_VALUEPARAM_FICO2
		u8R"(PR)", //STR_VALUEPARAM_PR
		u8R"(AA)", //STR_VALUEPARAM_AG_NULL
		u8R"(FiAA)", //STR_VALUEPARAM_AG_FINULL
		u8R"(FiN2O)", //STR_VALUEPARAM_FIN2O
		u8R"(FiHAL)", //STR_VALUEPARAM_FIHAL
		u8R"(FiENF)", //STR_VALUEPARAM_FIENF
		u8R"(FiSEV)", //STR_VALUEPARAM_FISEV
		u8R"(FiISO)", //STR_VALUEPARAM_FIISO
		u8R"(FiDES)", //STR_VALUEPARAM_FIDES
		u8R"(EtAA)", //STR_VALUEPARAM_AG_ETNULL
		u8R"(EtN2O)", //STR_VALUEPARAM_ETN2O
		u8R"(EtHAL)", //STR_VALUEPARAM_ETHAL
		u8R"(EtENF)", //STR_VALUEPARAM_ETENF
		u8R"(EtSEV)", //STR_VALUEPARAM_ETSEV
		u8R"(EtISO)", //STR_VALUEPARAM_ETISO
		u8R"(EtDES)", //STR_VALUEPARAM_ETDES
		u8R"(Apnea)", //STR_VALUEPARAM_ASPHY
		u8R"(VT)", //STR_SETTINGPARAM_VT
		u8R"(Rate)", //STR_SETTINGPARAM_RATE
		u8R"(I:E)", //STR_SETTINGPARAM_IE
		u8R"(Tip:Ti)", //STR_SETTINGPARAM_TITP
		u8R"(Pinsp)", //STR_SETTINGPARAM_PINSP
		u8R"(CPAP)", //STR_SETTINGPARAM_CPAP
		u8R"(Plimit)", //STR_SETTINGPARAM_PLIMIT
		u8R"(PEEP)", //STR_SETTINGPARAM_PEEP
		u8R"(Ftrig)", //STR_SETTINGPARAM_FTRIG
		u8R"(Ptrig)", //STR_SETTINGPARAM_PTRIG
		u8R"(Tinsp)", //STR_SETTINGPARAM_TINSP
		u8R"(Slope)", //STR_SETTINGPARAM_SLOPE
		u8R"(Psupp)", //STR_SETTINGPARAM_PSUPP
		u8R"(In End Level)", //STR_SETTINGPARAM_INENDLEVEL
		u8R"(VTe)", //STR_SETTINGLIMIT_VTE
		u8R"(Paw)", //STR_SETTINGLIMIT_PAW
		u8R"(FiO2)", //STR_SETTINGLIMIT_FIO2
		u8R"(Rate)", //STR_SETTINGLIMIT_RATE
		u8R"(MV)", //STR_SETTINGLIMIT_MV
		u8R"(1/(Min.L))", //STR_UNIT_1PERMINL
		u8R"(s)", //STR_UNIT_S
		u8R"(cmH2O)", //STR_UNIT_CMH2O
		u8R"(cmH2O/(L/s))", //STR_UNIT_CMH2OLS
		u8R"(mL/cmH2O)", //STR_UNIT_MLCMH2O
		u8R"(mmHg)", //STR_UNIT_MMHG
		u8R"(%)", //STR_UNIT_PERCENTAGE
		u8R"(L)", //STR_UNIT_L
		u8R"(mL)", //STR_UNIT_ML
		u8R"(L/min)", //STR_UNIT_LPERMIN
		u8R"(min)", //STR_UNIT_MIN
		u8R"(bpm)", //STR_UNIT_BPM
		u8R"(kg)", //STR_UNIT_KG
		u8R"(lb)", //STR_UNIT_LB
		u8R"(m)", //STR_UNIT_M
		u8R"(ft)", //STR_UNIT_FT
		u8R"(hPa)", //STR_UNIT_HPA
		u8R"(kPa)", //STR_UNIT_KPA
		u8R"(mBar)", //STR_UNIT_MBAR
		u8R"(---)", //STR_DASH
		u8R"(Insp.)", //STR_INSP
		u8R"(Exp.)", //STR_EXP
		u8R"(MAC)", //STR_MAC
		u8R"(Paw)", //STR_GRAPHNAME_PAW
		u8R"(Flow)", //STR_GRAPHNAME_FLOW
		u8R"(Volume)", //STR_GRAPHNAME_VOL
		u8R"(CO2)", //STR_GRAPHNAME_CO2
		u8R"(SPO2)", //STR_GRAPHNAME_SPO2
		u8R"(C%10 / C)", //STR_GRAPHNAME_C20C
		u8R"(Vol-Flow)", //STR_GRAPHNAME_VF
		u8R"(Paw-Vol)", //STR_GRAPHNAME_PV
		u8R"(Flow-Paw)", //STR_GRAPHNAME_FP
		u8R"(Vol-CO2)", //STR_GRAPHNAME_VCO2
		u8R"(O2)", //STR_GASNAME_O2
		u8R"(N2O)", //STR_GASNAME_N2O
		u8R"(CO2)", //STR_GASNAME_CO2
		u8R"(Air)", //STR_GASNAME_AIR
		u8R"(Pinsp)", //STR_CPAP
		u8R"(Phy-alarm)", //STR_BIO_ALARM
		u8R"(Set-time)", //STR_SET_TIME
		u8R"(VENTURA)", //STR_OEM
		u8R"(ELUNA-60)", //STR_ELUNA_60
		u8R"(ELUNA-70)", //STR_ELUNA_70
		u8R"(ELUNA-80)", //STR_ELUNA_80
		u8R"(CENAR-30)", //STR_CENAR_30
		u8R"(CENAR-40)", //STR_CENAR_40
		u8R"(CENAR-50)", //STR_CENAR_50
		u8R"(PSV Level)", //STR_PSV_LEVEL
		u8R"(backup)", //STR_BACKUP
		u8R"(SN-)", //STR_SN_SHORT_CUT
		u8R"()", //STR_BLANK
		u8R"(BACK)", //STR_BACK
		u8R"(ENTER)", //STR_ENTER
		u8R"(Leak Test is not available.caused by:
1.In Manual mode, or 
2.Open ACGO, or
3.The flowmeters not shut off.)", //STR_DISABLE_TEST
		u8R"(Please do test as following:)", //STR_LABEL_BST_HEAD
		u8R"(1.Connecting the Y-piece to the leak test plug as shown.
2.Keep the Manual/Mechanical ventilation switch on mechanical position.
3.Press O2 flush button to completely fill the bellows
4.Adjust all flowmeters to zero. 
5.Press the 'Start' button to test.)", //STR_LABEL_BST_INIT
		u8R"(Test)", //STR_TEST
		u8R"(Leak testing in progress.
Please wait about 1 min.)", //STR_LABEL_BST_TESTING
		u8R"(Leak test failed.
Please check the circuit and try again.)", //STR_LABEL_BST_FAIL
		u8R"(Compliance)", //STR_COMPLIANCE
		u8R"(Leak Test)", //STR_LEAK
		u8R"(Fail)", //STR_FAIL
		u8R"(Pass)", //STR_QULIFIED
		u8R"(Fail)", //STR_UNQULIFIED
		u8R"(Last test)", //STR_RecentSTT
		u8R"(Last calibration)", //STR_RECENT_CAL
		u8R"(Total Flow)", //STR_TOTAL
		u8R"(Exit)", //STR_EXIT
		u8R"(OK)", //STR_CONFIRM
		u8R"(Cancel)", //STR_CANCEL
		u8R"(Start)", //STR_START
		u8R"(Reset)", //STR_RESET
		u8R"(Waveforms)", //STR_LABEL_WAVEFORMS
		u8R"(Loops)", //STR_LABEL_SPIROMETRY
		u8R"(Graphic Trends)", //STR_LABEL_TREND
		u8R"(All Values)", //STR_LABEL_VALUES
		u8R"(Save Loop)", //STR_LOOP_BUTTON
		u8R"(Resume)", //STR_LOOP_BUTTONRELEASE
		u8R"(Flowmeter)", //STR_LABEL_FLOWMETER
		u8R"(Alarm!)", //STR_ALARM
		u8R"(message)", //STR_MESSAGE
		u8R"(Main Menu)", //STR_MAINMENU
		u8R"(Zero)", //STR_MAINMENU_ZERO
		u8R"(Load Defaults)", //STR_MAINMENU_DEFAULT
		u8R"(Calibration)", //STR_CALIBRATION
		u8R"(Calibration is not available in Manual mode or with the ACGO switch on)", //STR_CAL_DIABLE
		u8R"(Calibration)", //STR_MAINMENU_CAL
		u8R"(Calibration Success)", //STR_MAINMENU_CALDONE
		u8R"(Calibration Fail!)", //STR_MAINMENU_CALFAIL
		u8R"(Please do calibration as following)", //STR_MAINMENU_CAL_TIP
		u8R"(Flowmeter Calibration)", //STR_MAINMENU_FLOWCAL
		u8R"(1.Ensure you are in Standby mode.
2.Adjust all flowmeters to zero.
3.Click the 'Start' button.)", //STR_MAINMENU_FLOWCAL_TXT2
		u8R"(Select 'Calibration’)", //STR_MAINMENU_FLOWCAL_TXT3
		u8R"(Zero calibration is in process, it may take 1-2 minutes.)", //STR_MAINMENU_FLOWCAL_TXT4
		u8R"(O2 Sensor Calibration)", //STR_MAINMENU_O2CAL
		u8R"(21%Cal.)", //STR_MAINMENU_21PO2CAL
		u8R"(100%Cal.)", //STR_MAINMENU_100PO2CAL
		u8R"(Done)", //STR_MAINMENU_CAL_FIN
		u8R"(1.Ensure you are in Standby mode.
2.Turn off all fresh gas flows.
3.Open the plug and inspiratory check valve as shown.
4.Click the 'Start' button to perform 21% oxygen concentration calibration.)", //STR_MAINMENU_O2CAL_TXT3
		u8R"(O2 sensor calibration is in process, please wait several minutes…)", //STR_MAINMENU_CAL_IN_PROCESS
		u8R"(21% calibration is completed, please install the plug and inspiratory check valve as shown, then select '100% Cal.' or 'Done'.)", //STR_MAINMENU_CAL_21_DONE
		u8R"(100% O2 concentration calibration is completed.)", //STR_MAINMENU_CAL_100_DONE
		u8R"(21% O2 calibration failed; please revert to the breathing circuit system as shown.)", //STR_MAINMENU_CAL_21_FAIL
		u8R"(100% O2 calibration failed.)", //STR_MAINMENU_CAL_100_FAIL
		u8R"(O2 calibration is completed, please check the breathing circuit system.)", //STR_MAINMENU_CAL_FINISH
		u8R"(AG/CO2)", //STR_MAINMENU_AGCO2CAL
		u8R"(1.Make sure you are in Standby mode.
2.Keep the concentration of CO2 the same as in the atmosphere.)", //STR_MAINMENU_AGCO2CAL_TXT1_TITLE
		u8R"(AG/CO2 Module Is Warming Up)", //STR_MAINMENU_CAL_STATUS_DISABLE
		u8R"(In Zeroing...)", //STR_MAINMENU_CAL_STATUS_IN_PROG
		u8R"(Zero Calibration Success)", //STR_MAINMENU_CAL_STATUS_ZERODONE
		u8R"(Zero Calibration Failed)", //STR_MAINMENU_CAL_STATUS_FAILED
		u8R"(Last Zero Calibration)", //STR_LATEST_ZERO_CAL_TIME
		u8R"(Flow Sensor)", //STR_MAINMENU_SENSORCAL
		u8R"(Pressure Sensor)", //STR_MAINMENU_SENSORCAL_TXT3
		u8R"(System)", //STR_MAINMENU_SYSSETTING
		u8R"(General Setting)", //STR_MAINMENU_EQUIPSET
		u8R"(System Volume)", //STR_MAINMENU_EQUIPSET_TXT2
		u8R"(ON)", //STR_ON
		u8R"(OFF)", //STR_OFF
		u8R"(Pressure Unit)", //STR_PAW_UNIT
		u8R"(CO2 Unit)", //STR_CO2_UNIT
		u8R"(Time)", //STR_MAINMENU_TIMEDATE
		u8R"(Date)", //STR_MAINMENU_TIMEDATE_DATE
		u8R"(Time)", //STR_MAINMENU_TIMEDATE_TIME
		u8R"(Time(AM))", //STR_MAINMENU_TIMEDATE_12HOUR_AM
		u8R"(Time(PM))", //STR_MAINMENU_TIMEDATE_12HOUR_PM
		u8R"(Date Format)", //STR_DATE_FORMAT
		u8R"(Time Format)", //STR_TIME_FORMAT
		u8R"(Y - M - D)", //STR_MAINMENU_YMD
		u8R"(M - D - Y)", //STR_MAINMENU_MDY
		u8R"(D - M - Y)", //STR_MAINMENU_DMY
		u8R"(12h)", //STR_TIME_FORMAT_12H
		u8R"(24h)", //STR_TIME_FORMAT_24H
		u8R"(Change the trigger type)", //STR_MAINMENU_TRIGSET
		u8R"(IP)", //STR_MAINMENU_IP
		u8R"(Trigger Type)", //STR_MAINMENU_TRIGSET_TXT1
		u8R"(Display)", //STR_MAINMENU_DISPSETTING
		u8R"(Fast)", //STR_MAINMENU_WAVESET_HIGH_SPEED
		u8R"(Normal)", //STR_MAINMENU_WAVESET_MID_SPEED
		u8R"(Slow)", //STR_MAINMENU_WAVESET_LOW_SPEED
		u8R"(Loop)", //STR_MAINMENU_LOOPSET
		u8R"(Value)", //STR_MAINMENU_VALUE
		u8R"(Graphic trends)", //STR_MAINMENU_TRENDSET
		u8R"(Trend Time)", //STR_MAINMENU_TRENDSET_TXT4
		u8R"(Sys Info)", //STR_MAINMENU_SYSINFO
		u8R"(Version)", //STR_MAINMENU_VER
		u8R"(Name)", //STR_MAINMENU_VER_TITLE1
		u8R"(Host Software)", //STR_MAINMENU_VER_TITLE1_TXT1
		u8R"(Version)", //STR_MAINMENU_VER_TITLE2
		u8R"(Date)", //STR_MAINMENU_VER_TITLE3
		u8R"(Config)", //STR_MAINMENU_CONFIG
		u8R"(Serial Number)", //STR_MAINMENU_CONFIG_TXT1
		u8R"(System Check)", //STR_MAINMENU_SELFTEST
		u8R"(O2 Flow Sensor Test)", //STR_MAINMENU_SELFTEST_TXT1
		u8R"(Air Flow Sensor Test)", //STR_MAINMENU_SELFTEST_TXT2
		u8R"(Exhalation Flow Sensor Test)", //STR_MAINMENU_SELFTEST_TXT3
		u8R"(Pressure Sensor Test)", //STR_MAINMENU_SELFTEST_TXT4
		u8R"(PEEP Valve Test)", //STR_MAINMENU_SELFTEST_TXT5
		u8R"(Safty Valve Test)", //STR_MAINMENU_SELFTEST_TXT6
		u8R"(O2 Sensor Test)", //STR_MAINMENU_SELFTEST_TXT7
		u8R"(Leakage(mL/min))", //STR_MAINMENU_SELFTEST_TXT8
		u8R"(Compliance(mL/cmH2O))", //STR_MAINMENU_SELFTEST_TXT9
		u8R"(Circuit Resistance(cmH2O/L/s))", //STR_MAINMENU_SELFTEST_TXT10
		u8R"(Done)", //STR_FINISH
		u8R"(Engineer)", //STR_MAINMENU_EGRSETTING
		u8R"(Engineer Setup)", //STR_EGNEER_SETTING
		u8R"(Operation Log)", //STR_OPER_LOG
		u8R"(Engineer Calibration)", //STR_EG_CAL
		u8R"(Config Info)", //STR_PRODUCT_CFG
		u8R"(Flow Calibration)", //STR_FLOW_CAL
		u8R"(Pressure Calibration)", //STR_PRESS_CAL
		u8R"(Standby)", //STR_STANDBY
		u8R"(Switch to Standby Mode?)", //STR_STANDBY_STRING1
		u8R"(Time)", //STR_ALARMSET_TIME
		u8R"(Alarm Items)", //STR_ALARMSET_ALARMITEM
		u8R"(Priority)", //STR_ALARMSET_PRIORITY
		u8R"(Low)", //STR_ALARMSET_LOW
		u8R"(High)", //STR_ALARMSET_HIGH
		u8R"(Alarm Setup)", //STR_ALARMSET
		u8R"(Alarm Limit)", //STR_ALARMSET_ALARMLIMIT
		u8R"(Alarm Log)", //STR_ALARMSET_ALARMLOG
		u8R"(High FiO2!!)", //STR_ALARMINFO_HIGH_FIO2
		u8R"(FiO2 Too Low!!!)", //STR_ALARMINFO_LOW_FIO2
		u8R"(High PR!!)", //STR_ALARMINFO_HIGH_PR
		u8R"(Low PR!!)", //STR_ALARMINFO_LOW_PR
		u8R"(MV Too High!!)", //STR_ALARMINFO_HIGH_MV
		u8R"(MV Too Low!!)", //STR_ALARMINFO_LOW_MV
		u8R"(Rate Too High!)", //STR_ALARMINFO_HIGH_RATE
		u8R"(Rate Too Low!)", //STR_ALARMINFO_LOW_RATE
		u8R"(VTe Too High!!)", //STR_ALARMINFO_HIGH_VTE
		u8R"(VTe Too Low!!)", //STR_ALARMINFO_LOW_VTE
		u8R"(Paw Too Low!!)", //STR_ALARMINFO_LOW_PAW
		u8R"(Paw Too High!!!)", //STR_ALARMINFO_HIGH_PAW
		u8R"(Pressure Limit)", //STR_PRESSURE_REACH_LIMIT
		u8R"(FiN2O Too High!!!)", //STR_ALARMINFO_HIGH_FIN2O
		u8R"(FiN2O Too Low!)", //STR_ALARMINFO_LOW_FIN2O
		u8R"(EtN2O Too High!!!)", //STR_ALARMINFO_HIGH_ETN2O
		u8R"(EtN2O Too Low!)", //STR_ALARMINFO_LOW_ETN2O
		u8R"(FiCO2 Too High!!!)", //STR_ALARMINFO_HIGH_FICO2
		u8R"(FiCO2 Too Low!)", //STR_ALARMINFO_LOW_FICO2
		u8R"(EtCO2 Too High!!!)", //STR_ALARMINFO_HIGH_ETCO2
		u8R"(EtCO2 Too Low!!)", //STR_ALARMINFO_LOW_ETCO2
		u8R"(High SPO2!!)", //STR_ALARMINFO_HIGH_SPO2
		u8R"(Low SPO2!!!)", //STR_ALARMINFO_LOW_SPO2
		u8R"(FiHAL Too High!!)", //STR_ALARMINFO_HIGH_FIHAL
		u8R"(FiHAL Too Low!)", //STR_ALARMINFO_LOW_FIHAL
		u8R"(FiENF Too High!!)", //STR_ALARMINFO_HIGH_FIENF
		u8R"(FiENF Too Low!)", //STR_ALARMINFO_LOW_FIENF
		u8R"(FiSEV Too High!!)", //STR_ALARMINFO_HIGH_FISEV
		u8R"(FiSEV Too Low!)", //STR_ALARMINFO_LOW_FISEV
		u8R"(FiISO Too High!!)", //STR_ALARMINFO_HIGH_FIISO
		u8R"(FiISO Too Low!)", //STR_ALARMINFO_LOW_FIISO
		u8R"(FiDES Too High!!)", //STR_ALARMINFO_HIGH_FIDES
		u8R"(FiDES Too Low!)", //STR_ALARMINFO_LOW_FIDES
		u8R"(EtHAL Too High!!)", //STR_ALARMINFO_HIGH_ETHAL
		u8R"(EtHAL Too Low!)", //STR_ALARMINFO_LOW_ETHAL
		u8R"(EtENF Too High!!)", //STR_ALARMINFO_HIGH_ETENF
		u8R"(EtENF Too Low!)", //STR_ALARMINFO_LOW_ETENF
		u8R"(EtSEV Too High!!)", //STR_ALARMINFO_HIGH_ETSEV
		u8R"(EtSEV Too Low!)", //STR_ALARMINFO_LOW_ETSEV
		u8R"(EtISO Too High!!)", //STR_ALARMINFO_HIGH_ETISO
		u8R"(EtISO Too Low!)", //STR_ALARMINFO_LOW_ETISO
		u8R"(EtDES Too High!!)", //STR_ALARMINFO_HIGH_ETDES
		u8R"(EtDES Too Low!)", //STR_ALARMINFO_LOW_ETDES
		u8R"(Over High O2 Flow!)", //STR_ALARMINFO_O2_FLOW_HIGH
		u8R"(Over High N2O Flow!)", //STR_ALARMINFO_N2O_FLOW_HIGH
		u8R"(Over High Air Flow!)", //STR_ALARMINFO_AIR_FLOW_HIGH
		u8R"(Over High Flow!)", //STR_ALARMINFO_FLOW_HIGH
		u8R"(Abnormal Ratio of O2 and N2O!!!)", //STR_ALARMINFO_O2_N2O_UNUSUAL_RATIO
		u8R"(Low Battery!!)", //STR_TECHALARM_BATTERYLOW
		u8R"(Battery Drained, Shutting Down!!!)", //STR_TECHALARM_BATTERYEMPTY
		u8R"(No AC Power!)", //STR_TECHALARM_PLUGOUT
		u8R"(Power State Error!)", //STR_TECHALARM_POWERERR
		u8R"(Open ACGO!!)", //STR_TECHALARM_ACGO
		u8R"(Continuous Airway Pressure Too High!!!)", //STR_ALARMINFO_GASHIGH
		u8R"(Continuous Airway Pressure Too Low!!)", //STR_ALARMINFO_GASLOW
		u8R"(Circuit System Imperfect Installation!!!)", //STR_TECHALARM_GASLOOPERR
		u8R"(Drive Gas Pressure Low!!!)", //STR_TECHALARM_GASSOURCELOW
		u8R"(Low Oxygen Source Pressure!!!)", //STR_TECHALARM_O2_GASSOURCELOW
		u8R"(Negative Pressure in Airway!!!)", //STR_ALARM_NEGPRESS
		u8R"(Oxygen Flush Over time!!)", //STR_TECHALARM_ADDO2TIMEOUT
		u8R"(Oxygen Flush Error!!!)", //STR_TECHALARM_ADDO2ERR
		u8R"(CO2 Absorber Canister Not Installed!!!)", //STR_TECHALARM_BYPASSERR
		u8R"(Apnea!!)", //STR_TECHALARM_APNEAALARM
		u8R"(Apnea time > 120s!!!)", //STR_TECHALARM_APNEA_120_ALARM
		u8R"(Flow Sensor Failure!!!)", //STR_FLOW_SENSOR_ERR_ALARM
		u8R"(High POWER BOARD temperature!!!)", //STR_TECHALARM_FAN_STOP
		u8R"(O2 Sensor Disconnected!!)", //STR_TECHALARM_O2_SENSOR_ERR
		u8R"(Monitoring Module Communication stopped!!!)", //STR_TECHALARM_MINOTORING_MODULE_ERR
		u8R"(Pressure Sensor Error!!!)", //STR_TECHALARM_PRESSURE_SENSOR_ERR
		u8R"(Peep Valve Disconnected!!!)", //STR_TECHALARM_EXP_VALVE_ERR
		u8R"(Peep Valve Blocked!!!)", //STR_TECHALARM_EXP_VALVE_CLOSE_OFF
		u8R"(Inspiratory Valve Error!!!)", //STR_TECHALARM_INSP_VALVE_ERR
		u8R"(Heating Module Error!)", //STR_TECHALARM_HEATING_MODULE_ERR
		u8R"(AG module Adapter not fitted!)", //STR_TECHALARM_AG_ADAPTOR_ERR
		u8R"(Time Up!)", //STR_TECHALARM_TIME_UP
		u8R"(SPO2 sensor distconnect!!)", //STR_TECHALARM_SPO2_BREAKOFF
		u8R"(AX Concentration Out Of Range!!)", //STR_TECHALARM_AX_OUT_OF_RANGE
		u8R"(CO2  Concentration Over Range!!)", //STR_TECHALARM_CO2_OUT_OF_RANGE
		u8R"(N2O  Concentration Over Range!!)", //STR_TECHALARM_N2O_OUT_OF_RANGE
		u8R"(AG Sensor Temperature Out Of Range!!)", //STR_TECHALARM_TMP_OUT_OF_RANGE
		u8R"(AG Sensor Pressure Out Of Range!!)", //STR_TECHALARM_PRESS_OUT_OF_RANGE
		u8R"(AG Sensor Need Recailbration!!)", //STR_TECHALARM_AG_NEED_RECAIL
		u8R"(Mixed Anesthetic Agent!)", //STR_TECHALARM_AG_AGENT_MIX
		u8R"(AG Sensor Data Is Inaccurate!!)", //STR_TECHALARM_AG_UNSPECIFIED_ACCURACY
		u8R"(AG Sensor Software Error!!!)", //STR_TECHALARM_AG_SW_ERR
		u8R"(AG Sensor Hardware Error!!!)", //STR_TECHALARM_AG_HW_ERR
		u8R"(AG Sensor Moto Error!!!)", //STR_TECHALARM_MOTO_ERR
		u8R"(AG Sensor Lost Calibration Data!!)", //STR_TECHALARM_AG_LOST_CAIL
		u8R"(Need To Replace AG Sensor Adapter!!)", //STR_TECHALARM_AG_REPLACE
		u8R"(Manual Switch)", //STR_TIP_MANUAL
		u8R"(High)", //STR_HIGH
		u8R"(Mid)", //STR_MID
		u8R"(Low)", //STR_LOW
		u8R"(Timer)", //STR_TIMER1
		u8R"(Elapsed Time)", //STR_TIMER2
		u8R"(Reset)", //STR_TIMER3
		u8R"(Open)", //STR_OPEN
		u8R"(Close)", //STR_CLOSE
		u8R"(Some physical alarm closed)", //STR_BIO_ALARM_CLOSE
		u8R"(Parameter reach limit)", //STR_PARAM_LIMITED
		u8R"(System Check)", //STR_SYSTEM_SELFTEST
		u8R"(Retry)", //STR_RETRY
		u8R"(Ignore)", //STR_IGNORE
		u8R"(Monitoring Module Test)", //STR_SELFTEST_MONITORING
		u8R"(Alternating Current Test)", //STR_SELFTEST_AC
		u8R"(Battery Test)", //STR_SELFTEST_BATTERY
		u8R"(Clock Time Test)", //STR_SELFTEST_SYS_TIME
		u8R"(Flowmeter Test)", //STR_SELFTEST_FLOWMETER
		u8R"(AG/CO2 Module Test)", //STR_SELFTEST_AG_CO2
		u8R"(O2 Sensor Test)", //STR_SELFTEST_O2
		u8R"(SPO2 Module Test)", //STR_SELFTEST_SPO2
		u8R"(Touch Screen test)", //STR_SELFTEST_TOUCHSCREEN
		u8R"(Gas Supply Pressure Test)", //STR_SELFTEST_GAS_SOURCE
		u8R"(Database Loading)", //STR_SELFTEST_DATABASE
		u8R"(PEEP Valve Test)", //STR_SELFTEST_EXHALATION_VALUE
		u8R"(Inhalation Valve Test)", //STR_SELFTEST_INHALATION_VALUE
		u8R"(Flowmeter Communication Stop)", //STR_FLOWMETER_STOP
		u8R"(Monitoring Sensor Calibration Failure)", //STR_MONIT_SENSOR_CAL_FAIL
		u8R"(The Monitoring Accuracy Not High)", //STR_MONITOR_ACCURACY_LOW
		u8R"(O2 Flowmeter Sensor Error)", //STR_O2_FM_SENSOR_ERROR
		u8R"(N2O Flowmeter Sensor Error)", //STR_N2O_FM_SENSOR_ERROR
		u8R"(Air Flowmeter Sensor Error)", //STR_AIR_FM_SENSOR_ERROR
		u8R"(Flowmeter Hardware Error)", //STR_FM_HARDWARE_ERROR
		u8R"(The SPO2 Sensor Disconnected)", //STR_SPO2_SENSOR_UNUNITED
		u8R"(The AG Sensor Disconnected)", //STR_AG_ERROR
		u8R"(The O2 Sensor Disconnected)", //STR_O2_CONCENTRATION_ERROR
		u8R"(TouchScreen Pressed Down)", //STR_TOUCH_SCREEN_HOLDBACK
		u8R"(System Time Error)", //STR_SYS_TIME_ERR
		u8R"(Excessive Circuit Leakage)", //STR_BREATH_LEAK_HIGH
		u8R"(Battery Error)", //STR_BATTERYERR
		u8R"(Result)", //STR_RESULT
		u8R"(Pass)", //STR_PASS
		u8R"(Operation Event)", //STR_OPER_EVENT
		u8R"(Detailed information)", //STR_DETAIL_INFO
		u8R"(Power On)", //STR_SYS_START
		u8R"(Select mode)", //STR_RUNMODE_CHAGNE
		u8R"(Ventilation mode modified)", //STR_VENTMODE_CHAGNE
		u8R"(Ventilation parameter modified)", //STR_VENT_PARAM_CHANGE
		u8R"(Change Alarm high limit)", //STR_HIGH_ALARM_LIMIT_CHANGE
		u8R"(Change Alarm low limit)", //STR_LOW_ALARM_LIMIT_CHANGE
		u8R"(HLM mode param modified)", //STR_HLM_PARAM_CHANGE
		u8R"(Product Model)", //STR_PRODUCT_MODE
		u8R"(Serial Number)", //STR_SN
		u8R"(Production Date)", //STR_P_DATE
		u8R"(Software Version)", //STR_S_VERION
		u8R"(Skip)", //STR_SKIP
		u8R"(Click 'Start' button to start calibration.)", //STR_CAL_START
		u8R"(Flow Calibration Is In Prograss…)", //STR_CAL_IN_FLOW_PROC
		u8R"(Flow Calibration Failed.)", //STR_CAL_FLOW_FAIL
		u8R"(Flow Calibration Successful.)", //STR_CAL_FLOW_DONE
		u8R"(In Pressure Calibration...)", //STR_CAL_IN_PEEP_PROC
		u8R"(Pressure Calibration succeed.)", //STR_CAL_PEEP_DONE
		u8R"(Pressure Calibration Failed.)", //STR_CAL_PEEP_FAIL
		u8R"(Exporting Records)", //STR_EXPRT_DATA
		u8R"(Export data)", //STR_READ_CAL
		u8R"(Click 'Start' button to start exporting calibration data.)", //STR_EXPRT_START
		u8R"(Flowmeter Calibration)", //STR_FLOWMETER_CAL
		u8R"(Input 0 to start Calibration)", //STR_FLOWMETER_CAL_START
		u8R"(Cal data)", //STR_FM_CAL_DATA
		u8R"(Data calibration failed. Please try this point again.)", //STR_FM_CAL_FAIL
		u8R"(Current point done, please input the next point.)", //STR_FM_CAL_DONE_NEXT
		u8R"(Flowmeter calibration is completed, please select 'OK' or 'CANCEL'.)", //STR_FM_CAL_FINISH
		u8R"(Flowmeter calibration is in progress...)", //STR_FM_CAL_WAIT
		u8R"(Flowmeter Calibration Successful.)", //STR_FM_CAL_SUCCEED
		u8R"(IP Address)", //STR_IPADDR
		u8R"(Mask)", //STR_NETMASK
		u8R"(GateWay)", //STR_GATEWAY
		u8R"(MAC)", //STR_MACADDR
		u8R"(Confirm)", //STR_CONFIRM_NET
		u8R"(.)", //STR_DOT
		u8R"(KEYCODE)", //STR_KEYCODE
		u8R"(UPDATE)", //STR_FIRMUPDATE
		u8R"(Demo)", //STR_SOFT_MODE
		u8R"(Tip:Restart the system please!)", //STR_SOFT_MODE_SWITCHED_TIP
		u8R"(Demo mode)", //STR_SOFT_DEMO_MODE
		u8R"(Run mode)", //STR_SOFT_RUN_MODE
		u8R"(Calibrate)", //STR_CALI
		u8R"(Calibration is in progress...  Click soft key to cancel)", //STR_CALI_PROC
		u8R"(Software Config)", //STR_SOFT_CFG_TITLE
		u8R"(Configuration code error)", //STR_SOFT_CFG_INPUT_TIP
		u8R"(Config failed)", //STR_SOFT_CFG_FAIL
		u8R"(Config successful)", //STR_SOFT_CFG_SUCCEED
		u8R"(Screen Brightness)", //STR_MAINMENU_EQUIPSET_LCD
		u8R"(O2 Flow Abnormal!)", //STR_O2_FLOW_INEXACT
		u8R"(Balance Gas Flow Abnormal!)", //STR_BANLANCE_FLOW_INEXACT
		u8R"(No Fresh Gas!!)", //STR_FLOW_TOO_LOW
		u8R"(O2 Supply Failure!!)", //STR_O2_PRESS_LOW
		u8R"(N2O Supply Failure!!)", //STR_N2O_PRESS_LOW
		u8R"(Air Supply Failure!!)", //STR_AIR_PRESS_LOW
		u8R"(O2 sensor not reading calibration data!!)", //STR_FLOWMETER_O2_SENSOR_ERROR
		u8R"(N2O sensor not reading calibration data!!)", //STR_FLOWMETER_N2O_SENSOR_ERROR
		u8R"(Air sensor not reading calibration data!!)", //STR_FLOWMETER_AIR_SENSOR_ERROR
		u8R"(Backup Flow Control System is enabled!!)", //STR_BACKUP_FLOW_CONTROL_OPEN
		u8R"(History)", //STR_HISTORY_DATA
		u8R"(Setup)", //STR_SETTINGS
		u8R"(Freeze)", //STR_FREEZING
		u8R"(Alarm Mute)", //STR_ALARM_MUTE
		u8R"(LoopInfo)", //STR_LOOPINFO
		u8R"(Last System Check)", //STR_LATEST_SELFTEST
		u8R"(Previous Step)", //STR_PRE_STEP
		u8R"(Next Step)", //STR_NEXT_STEP
		u8R"(Done)", //STR_ACCOMPLISH
		u8R"(Stop)", //STR_STOP
		u8R"(Continue)", //STR_CONTINUE
		u8R"(Are you sure to skip the System Check?)", //STR_SELFTEST_SKIP_TIP
		u8R"(Leak Test)", //STR_MANUAL_LEAK_CHECK
		u8R"(1. Ensure that the system is in Standby mode and that the Manual/Mechanical ventilation switch is on Manual position.
2. Connect the manual bag to the manual bag port.
3. Plug the Y-piece into the leak test plug to block the air outlet.
4. Adjust the APL valve knob to 70 cmH2O.
5. Adjust the O2 flow rate to 0.15 L/min.
6. Press the O2 flush button to raise the airway pressure to about 30 cmH2O.
7. Release the O2 flush button. Observe the airway pressure gauge.)", //STR_MANUAL_LEAK_CHECK_TIP
		u8R"(Gas Circuit Test)", //STR_GAS_CIRCUIT_CHECK
		u8R"(1. Ensure that the breathing system is correctly connected, and the breathing pipelines are undamaged. Make sure the breathing system is locked in.
2. Check whether the CO2 absorber canister is properly installed.
3. Check if the Sodalime color is obvious, please replace the Sodalime immediately if it is.)", //STR_GAS_CIRCUIT_CHECK_TIP
		u8R"(Flowmeter Test)", //STR_FLOWMETER_CHECK
		u8R"(1. Ensure that the gas supply system is properly connected and the pressure is normal. The flowmeter reading is zero when the flowmeter knob is set to zero.
2. Ensure that the inner wall of the single-tube flowmeter is not dirty.)", //STR_FLOWMETER_CHECK_TIP
		u8R"(Vaporizer Check)", //STR_ANESTHETIC_GAS_EVAPORATOR_CHECK
		u8R"(1. Ensure the vaporizer is in the correct locked position.
2. Ensure the vaporizer is adjusted to 0.
3. Ensure that the vaporizer is filled at a normal level.
4. Ensure that evaporator is safely filled and locked.)", //STR_ANESTHETIC_GAS_EVAPORATOR_CHECK_TIP
		u8R"(Stopwatch)", //STR_TIMER
		u8R"(Countdown Timer)", //STR_COUNTDOWN
		u8R"(Ventilator Alarm Limit)", //STR_VENTILATOR_ALARM_LIMIT
		u8R"(AG Alarm Limit)", //STR_ANESTHETIC_GAS_ALARM_LIMIT
		u8R"(Current Alarm)", //STR_CUR_ALARM
		u8R"(4 Waveforms)", //STR_FOUR_WAVE
		u8R"(3 Waveforms)", //STR_THREE_WAVE
		u8R"(Loops)", //STR_LOOP
		u8R"(Fresh Gas Control)", //STR_FLOW_CONTROL_MENU
		u8R"(Quick set)", //STR_QUICK_SET
		u8R"(Control mode)", //STR_CONTROL_MODE
		u8R"(Total flow)", //STR_TOTAL_FLOW_CONTROL
		u8R"(Direct flow)", //STR_SINGLE_PIPE_FLOW_CONTROL
		u8R"(Balance gas)", //STR_BALANCE_FLOW
		u8R"(Graphic Trends)", //STR_TREND_GRAPH
		u8R"(Minutes)", //STR_MINUTE
		u8R"(Pre Event)", //STR_PRE_EVENT
		u8R"(Next Event)", //STR_NEXT_EVENT
		u8R"(List Trends)", //STR_TREND_TABLE
		u8R"(ALL)", //STR_ALL_PARAMS
		u8R"(Pressure Params)", //STR_PRESSURE_PARAMS
		u8R"(Vol Params)", //STR_VOLUME_PARAMS
		u8R"(Time Params)", //STR_TIME_PARAMS
		u8R"(Flowmeter Para.)", //STR_FLOWMETER_PARAMS
		u8R"(Ag Params)", //STR_AG_PARAMS
		u8R"(Loop Detail)", //STR_LOOP_DETAIL
		u8R"(Save Loop)", //STR_SAVE_LOOP
		u8R"(Delete)", //STR_DELETE_LOOP
		u8R"(Show Reference)", //STR_SWITCH_REFER_LOOP
		u8R"(Compare Loop)", //STR_COMPARE_LOOP
		u8R"(V-F Loop)", //STR_LOOP_VF
		u8R"(P-V Loop)", //STR_LOOP_PV
		u8R"(F-P Loop)", //STR_LOOP_FP
		u8R"(Confirm it as baseline loop?)", //STR_CONFIRM_REF_LOOP_TIP
		u8R"(Confirm to delete the loop?)", //STR_CONFIRM_DELETE_LOOP_TIP
		u8R"(Confirm it as contrast loop?)", //STR_CONFIRM_COMPARE_LOOP_TIP
		u8R"(History Events)", //STR_HISTORY_EVENT
		u8R"(Date)", //STR_DATE
		u8R"(All Events)", //STR_ALL_EVENT
		u8R"(High Level Alarm)", //STR_HIGH_LEVEL_ALARM
		u8R"(Med Level Alarm)", //STR_MID_LEVEL_ALARM
		u8R"(Low Level Alarm)", //STR_LOW_LEVEL_ALARM
		u8R"(Prompt)", //STR_PROMPT
		u8R"(Operate)", //STR_OPERATE
		u8R"(Success)", //STR_SUCCESS
		u8R"(Vol/Brightness)", //STR_VOL_BRIGHT
		u8R"(Screen Setup)", //STR_UI_SETTING
		u8R"(Maintenance)", //STR_FACTORY_MAINTENANCE
		u8R"(Language)", //STR_LANGUAGE
		u8R"(Russian)", //STR_LANGUAGE_RUS
		u8R"(Chinese)", //STR_LANGUAGE_CHN
		u8R"(English)", //STR_LANGUAGE_ENG
		u8R"(Ukrainian)", //STR_LANGUAGE_UKR
		u8R"(French)", //STR_LANGUAGE_FRE
		u8R"(Protocol)", //STR_PROTOCAL
		u8R"(Protocol Setup)", //STR_PROTOCAL_SETTINGS
		u8R"(Serial Protocol)", //STR_SERIAL_PROTOCAL
		u8R"(None)", //STR_NONE
		u8R"(Baud Rate)", //STR_BAUD_RATE
		u8R"(Data Bits)", //STR_DATA_BITS
		u8R"(Parity)", //STR_VERIFY
		u8R"(Odd)", //STR_ODD
		u8R"(Even)", //STR_EVEN
		u8R"(Stop Bits)", //STR_STOP_BIT
		u8R"(Network Protocol)", //STR_NETWORK_PROTOCAL
		u8R"(Subnet Mask)", //STR_SUBNET_MASK
		u8R"(Status)", //STR_LINK_STATE
		u8R"(Connected)", //STR_CONNECTED
		u8R"(Disconnected)", //STR_DISCONNECTED
		u8R"(Interval)", //STR_SEND_INTERVAL
		u8R"(Target IP)", //STR_TARGET_IP
		u8R"(Target Port)", //STR_TARGET_PORT
		u8R"(Comfirm to connect?)", //STR_CONFIRM_CONNECT_TIP
		u8R"(Confirm to Disconnect?)", //STR_CONFIRM_DISCONNECT_TIP
		u8R"(System Info)", //STR_SYSTEM_INFO
		u8R"(Config Info)", //STR_CONFIG_INFO
		u8R"(Activated)", //STR_CONFIGURED
		u8R"(Plimit Line)", //STR_PLIMIT_LINE
		u8R"(ON)", //STR_SHOW
		u8R"(OFF)", //STR_HIDE
		u8R"(Draw Wave)", //STR_DRAW_CURVE
		u8R"(Curve)", //STR_DRAW_LINE
		u8R"(Fill)", //STR_FILL_AREA
		u8R"(Sweep Speed)", //STR_SWEEP_SPEED
		u8R"(Zero Flowmeter)", //STR_FLOWMETER_ZERO_CAL
		u8R"(Start Ventilation)", //STR_START_VENTILATION
		u8R"(Return)", //STR_RETURN
		u8R"(Select Baseline Loop)", //STR_SWITCH_BASE_LOOP
		u8R"(Only one battery detected)", //STR_ONLY_ONE_BATTER
		u8R"(Database Damaged!!)", //STR_DB_FAIL
		u8R"(AG Sensor is warming up)", //STR_AG_WARMING
		u8R"(CO2 Sensor is warming up)", //STR_CO2_WARMING
		u8R"(Timer has reached its maximum limit)", //STR_TIMER_MAX
		u8R"(Countdown ended)", //STR_COUNTDOWN_END
		u8R"(Calibration is only available in Standby mode)", //STR_CALIB_USE_ONLY_STANBY
		u8R"(Enter Maintenance)", //STR_ENTER_MAINTENANCE
		u8R"(Maintenance is only available in Standby mode)", //STR_MAINTENANCE_USE_ONLY_STANBY
		u8R"(Factory Settings)", //STR_P_SETTINGS
		u8R"(Restore Factory Setting)", //STR_RESTORE_FACTORY_SETTING
		u8R"(Breakdown)", //STR_BREAKDOWN
		u8R"(Normal)", //STR_NORMAL
		u8R"(End Delete)", //STR_END_DELETE
		u8R"(End Switch)", //STR_END_SWITCH
		u8R"(Please perform zero calibration as following)", //STR_ZERO_CAL_TIP
		u8R"(Change vent mode)", //STR_CHANGE_VENT_MODE
		u8R"(Exceed upper limit)", //STR_OUT_OF_UPPER_LIMIT
		u8R"(Exceed lower limit)", //STR_OUT_OF_LOWER_LIMIT
		u8R"(Input value incorrect)", //STR_FORMAT_ERROR
		u8R"(Invalid value)", //STR_INVALID_VALUE
		u8R"(The O2 calibration is canceled; please revert to the breathing circuit system as shown.)", //STR_O2_CAL_CANCEL
		u8R"(Standby)", //STR_HOTKEY_STANDBY
		u8R"(Shutdown delay)", //STR_SHUTDOWN_DELAY
		u8R"(Gas)", //STR_EXTERNAL_MODULE
		u8R"(RM)", //STR_BREATHING_MECHANICS_PARAM
		u8R"(Vent. Function)", //STR_VENTILATORY_FUNCTION_PARAM
		u8R"(Change Password)", //STR_CHANGE_PASSWORD
		u8R"(Enter new password)", //STR_ENTER_NEW_PASSWORD
		u8R"(Confirm new password)", //STR_NEW_PASSWORD_CONFIRM
		u8R"(Confirm change)", //STR_CONFIRM_CHANGE
		u8R"(*The password can be zero to six characters,all characters must be numbers.)", //STR_PASSWORD_TIP
		u8R"(Password input format error)", //STR_PASSWORD_INPUT_FAULT_TIP
		u8R"(Confirm to change password?)", //STR_PASSWORD_CHANGE_TIP
		u8R"(Main Control Board)", //STR_MAIN_CONTROL_BOARD
		u8R"(Monitor Board)", //STR_MONITOR_BOARD
		u8R"(U-Boot)", //STR_U_BOOT
		u8R"(Linux File System)", //STR_LINUX_FILE_SYSTEM
		u8R"(Linux Kernel)", //STR_LINUX_KERNEL
		u8R"(Flowmeter Board)", //STR_FLOWMETER_BOARD
		u8R"(Power Board)", //STR_POWER_BOARD
		u8R"(Vt650)", //STR_VT_650
		u8R"(VtPlus)", //STR_VT_PLUS
		u8R"(VtMini)", //STR_VT_MINI
		u8R"(Calibration Device Model)", //STR_VT_MODEL_CHOICE
		u8R"(Audition)", //STR_AUDITION
		u8R"(Enter your password)", //STR_ENTER_PASSWORD
		u8R"(Touch Pad Test)", //STR_TOUCH_PAD
		u8R"(Touch Screen)", //STR_TOUCH_SCREEN
		u8R"(No Fresh Gas)", //STR_FLOW_TOO_LOW_PROMPT
		u8R"(Change)", //STR_CHANGE
		u8R"(Start ventilation. Current ventilation mode)", //STR_LOG_ENTER_VENT_MODE
		u8R"(Shutdown delay)", //STR_LOG_SHUTDOWN_DELAY
		u8R"(Cancel Shutdown delay)", //STR_LOG_CANCEL_SHUTDOWN_DELAY
		u8R"(Mechanical Ventilation)", //STR_VENT
		u8R"(Alarm limit restore default)", //STR_ALARM_LIMIT_RESTORE_DEFAULT
		u8R"(Change control mode)", //STR_CHANGE_CONTROL_MODE
		u8R"(Change balance mode)", //STR_CHANGE_BALANCE_MODE
		u8R"(Change flow value)", //STR_CHANGE_FLOW_VALUE
		u8R"(Update to)", //STR_UPGRADE
		u8R"(CENAR-30M)", //STR_CENAR_30M
		u8R"(AG Sensor)", //STR_SELFTEST_AG
		u8R"(CO2 Sensor)", //STR_SELFTEST_CO2
		u8R"(Change vent mode param)", //STR_CHANGE_VENT_MODE_PARAM
		u8R"(Change Date)", //STR_CHANGE_SYSTEM_DATE
		u8R"(Change Time)", //STR_CHANGE_SYSTEM_TIME
		u8R"(Password Error)", //STR_PASSWORD_ERROR_TIP
		u8R"(Two passwords don't match)", //STR_PASSWORD_DONOT_MATCH
		u8R"(Switch Paw Unit)", //STR_SWITCH_PAW_UNIT
		u8R"(Switch CO2 Concentration Unit)", //STR_SWITCH_CO2_UNIT
		u8R"(AM)", //STR_TIME_AM
		u8R"(PM)", //STR_TIME_PM
		u8R"(Ventilator alarm limit restore default)", //STR_VENTILATOR_LIMIT_RESTORE_DEFAULT
		u8R"(Anesthetic gas limit restore default)", //STR_ANESTHETIC_GAS_LIMIT_RESTORE_DEFAULT
		u8R"(Timer setting cannot exceed)", //STR_TIMER_LIMIT
		u8R"(Exceeds alarm limit)", //STR_INPUT_ALARM_LIMIT
		u8R"(Configuration code)", //STR_CFG_CODE
		u8R"(Flowmeter PV Calibration)", //STR_PV_CAL
		u8R"(Shutdown time remaining)", //STR_SHUTDOWN_TIME
		u8R"(To cancel the shutdown,please turn on the system switch again)", //STR_SHUTDOWN_TIP
		u8R"(Inactivated)", //STR_NOT_CONFIG
		u8R"(Enter)", //STR_IN
		u8R"(Exit)", //STR_OUT
		u8R"(The CO2 Sensor Disconnected)", //STR_CO2_ERROR
		u8R"(Increase Flow)", //STR_ADD_FLOW
		u8R"(Decrease Flow)", //STR_REDUCE_FLOW
		u8R"(SEND)", //STR_SEND
		u8R"(Factory Settings have been restored,please restart the machine)", //STR_RESTORE_TIP
		u8R"(Change the IP address to ************ for flow rate calibration. Click OK to change the IP address)", //STR_FLOW_TIP
		u8R"(AG Module In Zeroing,Please Wait)", //STR_AG_ZEROING_TIP
		u8R"(Calibration is not valid in manual or ACGO mode)", //STR_MENUAL_ACGO_STATE_TIP
		u8R"(O2 flow sensor communication stopped)", //STR_O2_FM_SENSOR_COMMUNICATION_STOP
		u8R"(Balance Gas Flow Sensor Error)", //STR_BALANCE_GAS_FM_SENSOR_ERROR
		u8R"(Backup flow control system sensor communication stopped)", //STR_BACKUP_FLOW_CONTROL_COMMUNICATION_STOP
		u8R"(CO2 Sensor Adapter not fitted!)", //STR_TECHALARM_CO2_ADAPTOR_ERR
		u8R"(1. Ensure the air supply system is connected correctly and the pressure is normal. Make sure that the buoy is at its lowest point when the flowmeter knob is at zero.
2. Ensure there is no dirt on the inner wall of the flowmeter glass tube.)", //STR_FLOWMETER_CHECK_TIP_FOR_80
		u8R"(Manual/ACGO mode disables leak testing)", //STR_MENUAL_ACGO_LEAK_STATE_TIP
		u8R"(1. Make sure the gas supply system is connected correctly, the pressure is normal, and the flow is at the lowest level when the flowmeter knob is closed (minimum value)
2. Make sure the inside wall of the single-tube flowmeter is clean.)", //STR_FLOWMETER_CHECK_TIP_FOR_C30M
		u8R"(AG Sensor Zero Calibration)", //STR_AG_ZERO_CAL
		u8R"(CO2 Sensor Zero Calibration)", //STR_CO2_ZERO_CAL
		u8R"(CO2 Sensor Zero Calibration Is In Progress,Please Wait)", //STR_CO2_ZEROING_TIP
		u8R"(Exceed)", //STR_OVER
		u8R"(Alarm Upper limit)", //STR_ALARM_HIGH_LIMIT
		u8R"(Alarm Lower limit)", //STR_ALARM_LOW_LIMIT
		u8R"(Direct)", //STR_DIRECT_FLOW
		u8R"(Please enter the calibration value!)", //STR_EMPTY_INPUT_TIP
		u8R"(Please enter the valid calibration value!)", //STR_INVALID_INPUT_TIP
		u8R"(C)", //STR_SERIAL_C
		u8R"(E)", //STR_SERIAL_E
		u8R"(21% Calibration)", //STR_21PO2CAL
		u8R"(100% Calibration)", //STR_100PO2CAL
		u8R"(Waveform Setting)", //STR_WAVEFORMS_SETTINGS
		u8R"(shutdown)", //STR_SHUT_DOWN
		u8R"(Preoperative check)", //STR_PREOPERATIVE_CHECK
		u8R"(The CO2 concentration in the loop is too high)", //STR_CO2_CANNOT_ZERO_TIP
		u8R"(Gas source configuration)", //STR_FLOWMETER_GAS_CONFIG
		u8R"(Flowmeter board type)", //STR_FLOWMETER_SENSOR_TYPE
		u8R"(Software update)", //STR_SOFTWARE_UPDATE
		u8R"(Update switch)", //STR_UPDATE_SWITCH
		u8R"(Update type)", //STR_UPDATE_TYPE
		u8R"(Previous version)", //STR_SRC_VERSION
		u8R"(Target version)", //STR_TARGET_VERSION
		u8R"(Update progress)", //STR_PROGRESS
		u8R"(Software needs to restart to complete the update, it will automatically restart later!)", //STR_UPDATE_FINISH_TIP
		u8R"(Restart immediately)", //STR_RESTART_NOW
		u8R"(Communication error during software transfer.)", //STR_UPDATE_COM_ERROR_TIP
		u8R"(Software verification failed during transfer; requesting retransmission.)", //STR_UPDATE_CRC_ERROR_TIP
		u8R"(Online configuration in progress...)", //STR_MACHINE_CONFIGURING
		u8R"(Device tree)", //STR_DEVICE_TREE
		u8R"(Custom update)", //STR_CUSTOM_UPDATE
		u8R"(Logo png)", //STR_LOGO_PNG
		u8R"(Logo bin)", //STR_LOGO_BIN
		u8R"(Daemon version)", //STR_DAEMON_VERSION
		u8R"(Online configuration completed)", //STR_UPDATE_CONFIG
		u8R"(Custom product model configuration completed)", //STR_UPDATE_PRODUCT_MODEL
		u8R"(Custom program)", //STR_UPDATE_CUSTOM_STEP
		u8R"(Software boot logo upgrade completed)", //STR_UPDATE_LOGO
		u8R"(Uboot boot logo upgrade completed)", //STR_UPDATE_LOGO_BIN
		u8R"(AG module type)", //STR_AG_MODULE_TYPE
		u8R"(Mainstream)", //STR_MAIN_STREAM
		u8R"(Sidestream)", //STR_SIDE_STREAM
		u8R"(Check AG sampling line!)", //STR_TECHALARM_AG_SAMPLING_ERR
		u8R"(Check CO2 sampling line!)", //STR_TECHALARM_CO2_SAMPLING_ERR
		u8R"(Need To Replace CO2 Sensor Adapter!!)", //STR_TECHALARM_CO2_REPLACE
		u8R"(AG sampling line clogged!!)", //STR_TECHALARM_AG_SAMPLING_CLOGGED
		u8R"(CO2 sampling line clogged!!)", //STR_TECHALARM_CO2_SAMPLING_CLOGGED
		u8R"(O2 flow rate cannot be lower than 0.2 L/min)", //STR_FLOWMETER_O2_FLOW_TIPS
		u8R"(O2 concentration cannot be lower than 25%)", //STR_FLOWMETER_O2_CONCENTRATION_TIPS

    };
}

