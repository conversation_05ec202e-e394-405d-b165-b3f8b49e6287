#ifndef UPGRADEMANAGER_H
#define UPGRADEMANAGER_H

#include <QObject>
#include <QFile>
#include <QTimer>
#include "protocol.h"
#include <QVector>
#include "VersionInfoPage.h"

class UpgradeManager : public QObject
{
    Q_OBJECT
public:
    enum E_UPGRADE_STATE
    {
        E_UPGRADE_INACTIVE,
        E_UPGRADE_IDLE,
        E_UPGRADE_WORKING,
    };

    enum E_ERROR_CODE
    {
        E_COM_ERROR,
        E_CRC_ERROR,
        E_NO_ERROR,
    };

    struct UpdateFileInfoSt
    {
        E_UPDATE_FILE_TYPE mUpdateType;
        QString mUpdateFileVersion = "";
        int mUpdateProgress = 0;
        int mErrorCode = E_NO_ERROR;
    };

    static UpgradeManager* GetInstance();
    void SetUpgradeState(E_UPGRADE_STATE state);
    E_UPGRADE_STATE GetUpgradeState();
    bool GetIsProcessCustomUpdate();

    void SetUpdateFileVersion(E_UPDATE_FILE_TYPE updateFileType, QString updateFileVersion);
    void SetUpdateFileProgress(E_UPDATE_FILE_TYPE updateFileType, int updateProgress, E_ERROR_CODE erroCode = E_NO_ERROR);

    QVector<UpdateFileInfoSt> GetUpdateFileInfo();

    ConfigMessage GetMachineConfig();
    VersionInfo GetMachineAllVersionInfo();
    QString GetMachineVersion(VERSION_TABLE_ITEM_ENUM type) const;
    void SetMachineVersion(VERSION_TABLE_ITEM_ENUM type, QString version);

    void OpenMachineConfig(bool isOpen);
    bool IsOpenMachineConfig();

    void LoadFile(E_UPDATE_FILE_TYPE updateFileType, QByteArray& data);
    void LoadProductModel(QString productModel);
    void LoadMachineConfig(ConfigMessage config);

    void LoadAllFileFinish();

private slots:
    void SlotCheckUpdateIsFinish();
    void SlotUpdateMonitorFinish();
signals:
    void SignalUpdateStateChange();
    void SignalOpenMachineConfig(bool);
    void SignalUpdateProgress();
    void SignalUpdateFinish();

    void SignalSoftwareVersionChanged(int, QString text);
private:
    explicit UpgradeManager(QObject *parent = nullptr);
    void Init();
    void InitConnect();

    void GetSystemVersionInfo();
    void CopyVersionInfo(unsigned char* dest, size_t destSize, const QByteArray& byteArray);
private:
    E_UPGRADE_STATE mCurUpgradeState = E_UPGRADE_INACTIVE;
    QTimer* mCheckUpdateFinishTimer;

    QVector<UpdateFileInfoSt> mUpdateFileInfoVec;

    bool mIsOpenMachineConfig = false;
    bool mLoadAllFileFinish = false;
    bool mIsUpdateFlowmeter = false;
    bool mIsUpdateMonitor = false;
    bool mIsCustomUpdate = false;


    QString mMonitorSoftVersion = "---";
    QString mFlowmeterSoftVersion = "---";
    QString mPowerSoftVersion = "---";
    QString mFlowmeterGasType = "---";
    QString mFlowmeterSensorType = "---";
    QString mKernelVersion = "---";
    QString mUbootVersion = "---";
    QString mFileSystemVersion = "---";
    QString mDbVersion = "---";
};

#endif // UPGRADEMANAGER_H
