﻿#ifndef PARAMLABELLAYOUTHORIZONTAL_H
#define PARAMLABELLAYOUTHORIZONTAL_H

#include "ParamLabelBase.h"

class ParamLabelLayoutHorizontal : public ParamLabelBase
{
    Q_OBJECT

public:
    enum LABEL_SCALE_ENUM
    {
        MIN,
        SMALL,
        MID,
        LARGE,
        EXTRA
    };


public:
    explicit ParamLabelLayoutHorizontal(QWidget *parent =NULL);

    void SetLabelScale(int) override;

private:
    void InitUi();

private:
    struct LabelScaleInfoSt
    {
        unsigned int fixedHeight;
        unsigned int minimumWidth;
        unsigned int normalFontSize;
        unsigned int valueFontSize;
    };

    static const QMap<int, LabelScaleInfoSt> sScaleInfoMap;
};

#endif // PARAMLABELLAYOUTHORIZONTAL_H
