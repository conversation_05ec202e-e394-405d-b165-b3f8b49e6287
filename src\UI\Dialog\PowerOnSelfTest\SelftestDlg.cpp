﻿#include <QVBoxLayout>
#include <QStackedLayout>
#include <mutex>

#include "UiApi.h"
#include "ManageFactory.h"
#include "SelftestDlg.h"
#include "MainWindow/MainWindow.h"
#include "ControlWidget/ResultLabel.h"
#include "SysSelftestPage.h"
#include "PreoperativeTestPage.h"
#include "RunModeManage.h"
#include "SystemTimeManager.h"
#include "DataLimitManager.h"
#include "SystemSelftestModel.h"

#define PAGE_WIDTH  1024
#define PAGE_HEIGHT 715
#define TITLE_HEIGHT    100
#define BORDER_WIDTH    5

SelftestDlg::SelftestDlg(QWidget *parent) : FocusWidget(parent)
{
    InitUi();
    InitConnect();
}

void SelftestDlg::InitUiText()
{
    setWindowFlags(Qt::FramelessWindowHint);
    mResultLabel->SetTitle(UIStrings::GetStr(ALL_STRINGS_ENUM::STR_LATEST_SELFTEST));
    mResultLabel->SetResult(SystemSelftestModel::GetInstance()->GetSystemTestState());
    mResultLabel->SetCalTime(SystemSelftestModel::GetInstance()->GetLastestTestTime());
    mSysSelftestPage->InitUiText();
    mPreoperativeTestPage->InitUiText();
}

void SelftestDlg::InitUi()
{
    StyleSet::SetBackgroundColor(this,COLOR_BLACK_GRAY);

    setFixedSize(PAGE_WIDTH, PAGE_HEIGHT);
    mResultLabel = new ResultInfoLabel;
    mResultLabel->setFixedSize(PAGE_WIDTH,TITLE_HEIGHT);
    mResultLabel->installEventFilter(this);
    StyleSet::SetBackgroundColor(mResultLabel,COLOR_BLACK_GRAY);

    mSysSelftestPage =  new SysSelftestPage;
    mSysSelftestPage->setFixedSize(PAGE_WIDTH, PAGE_HEIGHT - TITLE_HEIGHT);
    mPreoperativeTestPage = new PreoperativeTestPage;
    mPreoperativeTestPage->setFixedSize(PAGE_WIDTH, PAGE_HEIGHT - TITLE_HEIGHT);


    auto mainLayout = new QVBoxLayout;
    mainLayout->setSpacing(0);
    mainLayout->setContentsMargins(0, 0, 0, 0);
    mainLayout->addWidget(mResultLabel);
    mStackLayout = new QStackedLayout;
    mStackLayout->addWidget(mSysSelftestPage);
    mStackLayout->addWidget(mPreoperativeTestPage);
    mainLayout->addLayout(mStackLayout);
    mStackLayout->setCurrentIndex(PAGE_ENUM::SYSTEM_SELFTEST);
    setLayout(mainLayout);
}

void SelftestDlg::InitConnect()
{
    connect(SystemSelftestModel::GetInstance(), &SystemSelftestModel::SignalSystemStateChanged, this, [this]{
        mResultLabel->SetCalTime(SystemSelftestModel::GetInstance()->GetLastestTestTime());
        mResultLabel->SetResult(SystemSelftestModel::GetInstance()->GetSystemTestState());
    });

    connect(mSysSelftestPage, &SysSelftestPage::SignalSelftestFinish, this, &SelftestDlg::SlotOnTestFinish);

    connect(mPreoperativeTestPage, &PreoperativeTestPage::SignalSelftestFinish, this, [this]{
        QuitSelftest();
    });
}

void SelftestDlg::QuitSelftest()
{
    RunModeManage::GetInstance()->InactiveRunMode(E_RUNMODE::MODE_RUN_SELFTEST);
    RunModeManage::GetInstance()->StartStandbyMode();
    if(ModuleTestManager::GetInstance()->GetModuleState(TEST_DATABASE) == E_MODULE_STATE::DISABLE)
            AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_DB_FAIL, 1);
    static std::once_flag flag;
    std::call_once(flag, [](){
        SystemTimeManager::GetInstance()->register_syn_second_timer(DataLimitManager::CheckAllValue);
    });
    MainWindow::GetInstance()->hideDialog(MainWindow::DLG_SYSTEM_SELFTEST);
}


void SelftestDlg::showEvent(QShowEvent *event)
{
    mStackLayout->setCurrentIndex(PAGE_ENUM::SYSTEM_SELFTEST);
    RunModeManage::GetInstance()->ActiveRunMode(E_RUNMODE::MODE_RUN_SELFTEST);
    RunModeManage::GetInstance()->InactiveRunMode(MODE_RUN_STANDBY);
    FocusWidget::showEvent(event);
}

bool SelftestDlg::eventFilter(QObject *watched, QEvent *event)
{
    if (watched == mResultLabel && event->type() == QEvent::MouseButtonDblClick)
    {
        SlotOnTestFinish(SysSelftestPage::TEST_END_STATE::ABNORMAL);
    }
    return FocusWidget::eventFilter(watched, event);
}

void SelftestDlg::SlotOnTestFinish(SysSelftestPage::TEST_END_STATE state)
{
    if (state == SysSelftestPage::TEST_END_STATE::NORMAL)
    {
        mStackLayout->setCurrentIndex(PAGE_ENUM::PREOPERATIVE_TEST);
    }
    else if (state == SysSelftestPage::TEST_END_STATE::ABNORMAL)
    {
        SystemSelftestModel::GetInstance()->SkipTest();
        QuitSelftest();
    }
}
