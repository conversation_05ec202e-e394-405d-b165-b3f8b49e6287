﻿#include <QHBoxLayout>
#include <QVBoxLayout>
#include <QGridLayout>
#include <QScrollArea>

#include "UiApi.h"
#include "KeyConfig.h"
#include "HistoryDataDialog/TrendGraph.h"
#include "TrendTable.h"
#include "SystemTimeManager.h"
#include "SystemSettingManager.h"

#include "SystemConfigManager.h"
#include "ChartManager.h"
#include "HistoryEventManager.h"
#include "RunModeManage.h"
#include "VentModeSettingManager.h"
#include "String/UIStrings.h"
#include "TrendDataManager.h"
#include "TrendChartManager.h"
#include "ModuleTestManager.h"
#include "AGModule.h"

#define CHART_WIDTH 675
#define CHART_HEIGHT 110
#define COMBOBOX_HEIGHT 40

#define RECT_ITEM_SIZE QSize(65, 400)
#define EVENT_LABEL_MINIMUM_WIDTH 150

#define EVENT_LABEL_MARGIN 5
#define TOP_WIDGET_HEIGHT 55
#define BOTTOM_WIDGET_HEIGHT 50
#define CURSOR_WIDHT 50
#define MODEL_LABLE_WIDTH 137
#define IGNORE_H_BAR_TIME_MSEC 30
#define TIMEAXIS_SCALE 10
#define DATE_LABLE_WIDTH MODEL_LABLE_WIDTH
#define TIME_RANGE_LEVEL_LOW 10
#define TIME_RANGE_LEVEL_MIDDLE 30
#define TIME_RANGE_LEVEL_HIGH 60
#define TIME_RANGE_LEVEL_MAX 120
#define SYMPLE_SIZE_L 6
#define SYMPLE_SIZE_M 5
#define SYMPLE_SIZE_NONE 0
const QColor TrendCursorFocusColor = COLOR_BLUE;
const QSize  TrendCursor::sTimeLabelSize = QSize(50, 50);

TrendCursor::TrendCursor(QWidget *parent): QFrame(parent)
{
    setAttribute(Qt::WA_TranslucentBackground, true);
    setAttribute(Qt::WA_TransparentForMouseEvents, true);

    mCursorColor = COLOR_WHITE_GRAY;
    mLineColor = COLOR_WHITE;

    sTimeFormat = "hh:mm";
    mTimeLabel = new QLabel(this);
    StyleSet::SetBackgroundColor(mTimeLabel,COLOR_TRANSPARENT);
    StyleSet::SetTextColor(mTimeLabel,COLOR_WHITE);
    mTimeLabel->setFixedHeight(CURSOR_WIDHT);
    mTimeLabel->setAlignment(Qt::AlignCenter);
    mTimeLabel->raise();
    mTimeLabel->setText("--:--");
    QVBoxLayout *mainLayout = new QVBoxLayout;
    mainLayout->setContentsMargins(0, 0, 0, 0);
    mainLayout->addWidget(mTimeLabel);
    mainLayout->setAlignment(mTimeLabel, Qt::AlignTop);
    setLayout(mainLayout);
}

void TrendCursor::SetTime(QDateTime dateTime)
{
    mTime = dateTime;
    mTimeLabel->setText(mTime.time().toString(sTimeFormat));
}

void TrendCursor::SetFormat(const QString &format)
{
    sTimeFormat = format;
    if (mTimeLabel->text() != "")
    {
        SetTime(mTime);
    }
}

void TrendCursor::SetAlarmPrio(int alarmPrio)
{
    QColor bgColor, foreColor;
    if (alarmPrio == E_ALARMPRIO::ALARMPRIO_HIG)
    {
        bgColor = COLOR_RED;
        foreColor = COLOR_RED;
    }
    else if (alarmPrio == E_ALARMPRIO::ALARMPRIO_MID || alarmPrio == E_ALARMPRIO::ALARMPRIO_LOW)
    {
        bgColor = COLOR_LIGHT_YELLOW;
        foreColor = COLOR_LIGHT_YELLOW;
    }
    else
    {
        bgColor = COLOR_WHITE_GRAY;
        foreColor = COLOR_WHITE;
    }

    mCursorColor = bgColor;
    mLineColor = foreColor;
    repaint();
}

void TrendCursor::paintEvent(QPaintEvent *event)
{
    mCursorColor.setAlpha(50);
    QPainter painter(this);
    painter.fillRect(this->rect(), mCursorColor);

    qreal tLineWidth =1;
    if(hasFocus())
    {
        painter.setPen(QPen{TrendCursorFocusColor, 3});
        painter.drawRect(this->rect());
    }
    painter.setPen(QPen{mLineColor, tLineWidth});
    painter.drawLine(QLine({width() / 2, mTimeLabel->height()}, {width() / 2, height()}));
    QFrame::paintEvent(event);
}

void TrendCursor::focusInEvent(QFocusEvent *)
{
    mIsEditState=false;
    update();
}

void TrendCursor::focusOutEvent(QFocusEvent *)
{
    mIsEditState=false;
    update();
}

void TrendCursor::keyPressEvent(QKeyEvent *ev)
{
    if (KEY_PRESS == ev->key())
    {
        mIsEditState=!mIsEditState;
        update();
    }
    else if(mIsEditState)
    {
        if(ev->key() == KEY_ANTI_CLOSEWISE)
        {
            emit SignalApplyPrevMove();
        }
        else if(ev->key()==KEY_CLOSEWISE)
        {
            emit SignalApplyNextMove();
        }
        else
            ev->ignore();
    }
    else
        ev->ignore();
}

/***************************************************************/

EventLabel::EventLabel(QWidget *parent) : LabelPro(parent)
{
    setAlignment(Qt::AlignCenter);
    StyleSet::SetTextFont(this,FONT_M);
    setContentsMargins(5, 0, 5, 0);
}

void EventLabel::ShowAlarmEvent(int index)
{
    if (mAlarmStringList.size() == 0)
        return;
    auto st = mAlarmStringList[index];
    setText(st.mAlarmString);
    adjustSize();

    QColor bgColor, foreColor;
    if (st.mAlarmPrio == E_ALARMPRIO::ALARMPRIO_HIG)
    {
        bgColor = COLOR_RED;
        foreColor = COLOR_WHITE;
    }
    else if (st.mAlarmPrio == E_ALARMPRIO::ALARMPRIO_MID || st.mAlarmPrio == E_ALARMPRIO::ALARMPRIO_LOW)
    {
        bgColor = COLOR_LIGHT_YELLOW;
        foreColor = COLOR_BLACK;
    }
    else
    {
        bgColor = COLOR_BLACK;
        foreColor = COLOR_WHITE;
    }

    SetLabelBgColor(bgColor);
    SetTextFontColor(foreColor);

    emit SignalCurPrioChanged(st.mAlarmPrio);
}

void EventLabel::SetAlarmStringList(const QVector<DisplayInfoSt> &sl)
{
    mAlarmStringList = sl;
    if(sl.size() == 0)
    {
        mCurIndex = -1;
        setText("");
        adjustSize();
        SetLabelBgColor(COLOR_BLACK);
        emit SignalCurPrioChanged(E_ALARMPRIO::ALARMPRIO_DISABLE);
        hide();
    }
    else
    {
        ShowAlarmEvent(0);
        mCurIndex = 0;
    }
}

void EventLabel::DelColor()
{
    SetLabelBgColor(COLOR_BLACK);
    SetTextFontColor(COLOR_BLACK);
}

void EventLabel::RecoverColor()
{
    if(mCurIndex!=-1)
    {
        auto st = mAlarmStringList[mCurIndex];
        QColor bgColor, foreColor;
        if (st.mAlarmPrio == E_ALARMPRIO::ALARMPRIO_HIG)
        {
            bgColor = COLOR_RED;
            foreColor = COLOR_WHITE;
        }
        else if (st.mAlarmPrio == E_ALARMPRIO::ALARMPRIO_MID || st.mAlarmPrio == E_ALARMPRIO::ALARMPRIO_LOW)
        {
            bgColor = COLOR_LIGHT_YELLOW;
            foreColor = COLOR_BLACK;
        }
        else
        {
            bgColor = COLOR_BLACK;
            foreColor = COLOR_WHITE;
        }
        SetLabelBgColor(bgColor);
        SetTextFontColor(foreColor);
    }
}

/***************************************************************/

TrendGraph::TrendGraph(QWidget *parent) :
    FocusWidget(parent)
{
    InitValue();
    InitUi();
    InitUiText();
    InitConnect();
    mCurType=TrendDataModel::ALL;
    mParamTypeFilter->setValue(mCurType);

    qint64 newTimeTick = QDateTime::currentDateTime().toMSecsSinceEpoch();
    auto result = newTimeTick - (newTimeTick% 60000);
    auto newTime = QDateTime::fromMSecsSinceEpoch(result);
    mTimeAxis->setRange(newTime.addSecs(-10 * 60), newTime); //TBD 常量

    SlotRefurbishTimeFormat();
}

void TrendGraph::InitValue()
{
    mDisabledTypes = TrendDataManager::GetInstance()->GetDisabledParams();
    mScrollDrive = new MyScrollDrive(this);
}

void TrendGraph::InitUi()
{
    mTimer = new QTimer;
    mMainWidget = new FocusWidget(this);
    StyleSet::SetBackgroundColor(mMainWidget,COLOR_BLACK);
    QVBoxLayout *mTopWidgetLayout = new QVBoxLayout;
    mTopWidgetLayout->setContentsMargins(0, 0, 0, 0);
    mTopWidgetLayout->setSpacing(0);


    mModeLabel = new QLabel("", this);
    mDateLabel = new QLabel("", this);

    mModeLabel->setFixedWidth(MODEL_LABLE_WIDTH);
    mModeLabel->setAlignment(Qt::AlignLeft);
    mModeLabel->setContentsMargins(5, 0, 0, 0);
    StyleSet::SetBackgroundColor(mModeLabel,COLOR_TRANSPARENT);
    StyleSet::SetTextFont(mModeLabel,FONT_M,FONT_FAMILY_NINA,true);
    StyleSet::SetTextColor(mModeLabel,COLOR_WHITE);


    mDateLabel->setFixedWidth(DATE_LABLE_WIDTH);
    mDateLabel->setAttribute(Qt::WA_TranslucentBackground);
    mDateLabel->setAlignment(Qt::AlignLeft);
    mDateLabel->setContentsMargins(5, 0, 0, 0);
    StyleSet::SetBackgroundColor(mDateLabel,COLOR_TRANSPARENT);
    StyleSet::SetTextFont(mDateLabel,FONT_M,FONT_FAMILY_NINA,true);
    StyleSet::SetTextColor(mDateLabel,COLOR_WHITE);

    mTopWidgetLayout->addWidget(mModeLabel);
    mTopWidgetLayout->addWidget(mDateLabel);
    mTopWidgetLayout->setAlignment(mModeLabel, Qt::AlignLeft);
    mTopWidgetLayout->setAlignment(mDateLabel, Qt::AlignLeft);
    mTopWidget = new QFrame(this);
    mTopWidget->setFixedHeight(TOP_WIDGET_HEIGHT);
    StyleSet::SetBackgroundColor(mTopWidget,COLOR_BLACK);
    mTopWidget->setLayout(mTopWidgetLayout);
    DEBUG_RUNTIME(InitPlotArea());

    mHorizontalBar = new ScrollBar(Qt::Horizontal, this);
    mHorizontalBar->setTracking(true);
    mHorizontalBar->setMaximum(0);
    mHorizontalBar->setValue(mHorizontalBar->maximum());
    mHorizontalBar->setPageStep(5);

    QVBoxLayout *mainWidgetLayout = new QVBoxLayout;
    mainWidgetLayout->setContentsMargins(0, 0, 0, 0);
    mainWidgetLayout->setSpacing(0);
    mainWidgetLayout->addWidget(mTopWidget);
    mainWidgetLayout->addWidget(mScrollArea);
    mainWidgetLayout->addWidget(mHorizontalBar);
    mMainWidget->setLayout(mainWidgetLayout);//TBD 1500MS 2023.10.17

    mCursorWidget = new TrendCursor(this);
    mCursorWidget->setFixedWidth(65);
    mCursorWidget->setFocusPolicy(Qt::NoFocus);


    mEventLabel = new EventLabel(mTopWidget);
    mEventLabel->SetAlarmStringList({});
    mEventLabel->raise();
    mEventLabel->hide();

    mZoomTimeFilter = new PushButtonCombox(mBottomDocker);
    mZoomTimeFilterDataModel = new ValueStrIntraData;
    QMap<int, QString> stringMap
    {
        {10,        "10"},
        {30,        "30"},
        {60,        "60"},
        {120,       "120"},
    };

    QMutableMapIterator<int, QString> i(stringMap);
    while (i.hasNext())
    {
        i.next();
        i.setValue(i.value() + " " + UIStrings::GetStr(ALL_STRINGS_ENUM::STR_MINUTE));
    }
    mZoomTimeFilterDataModel->SetValueAndStr(stringMap);
    mZoomTimeFilter->setDataModel(mZoomTimeFilterDataModel);
    mParamTypeFilter = new PushButtonCombox(mBottomDocker);
    mParamTypeDataModel = new ValueStrIntraData;
    auto tmpMap = QMap<int, QString>();
    for (int id = TrendDataModel::ALL; id < TrendDataModel::GROUP_TYPE_MAX; ++id)
    {
        if(id==TrendDataModel::FLOWMETER_PARAM&&ConfigManager->GetConfig<int>(SystemConfigManager::MACHINE_MODEL_INDEX) == CENAR_30M)
        {
            continue;
        }
        else if(id==TrendDataModel::EXTERNAL_MODULE_PARAM && ModuleTestManager::GetInstance()->GetAgModuleType() == None
                && !ConfigManager->GetConfig<int>(SystemConfigManager::O2_BOOL_INDEX))
        {
            continue;
        }
        tmpMap.insert(id,TrendDataModel::GetTypeFilterTypeName((TrendDataModel::DATA_FILTER_TYPE)id));
    }
    mParamTypeDataModel->SetValueAndStr(tmpMap);
    mParamTypeFilter->setDataModel(mParamTypeDataModel);


    mPreBtn = new PushButton(UIStrings::GetStr(ALL_STRINGS_ENUM::STR_PRE_EVENT), mBottomDocker);
    mNextBtn = new PushButton(UIStrings::GetStr(ALL_STRINGS_ENUM::STR_NEXT_EVENT), mBottomDocker);
    mZoomTimeFilter->setFixedHeight(COMBOBOX_HEIGHT);
    mParamTypeFilter->setFixedHeight(COMBOBOX_HEIGHT);

    QHBoxLayout *bottomLayout = new QHBoxLayout;
    bottomLayout->setContentsMargins(5, 0, 5, 0);
    bottomLayout->addWidget(mZoomTimeFilter);
    bottomLayout->addWidget(mParamTypeFilter);
    bottomLayout->addStretch();
    bottomLayout->addWidget(mPreBtn);
    bottomLayout->addWidget(mNextBtn);

    mBottomDocker = new FocusWidget(this);
    mBottomDocker->setLayout(bottomLayout);
    mBottomDocker->setFixedHeight(BOTTOM_WIDGET_HEIGHT);


    QVBoxLayout *mainLayout = new QVBoxLayout;


    mMainWidget->setFocusPolicy(Qt::NoFocus);
    mScrollArea->setFocusPolicy(Qt::NoFocus);
    mHorizontalBar->setEnable(false);
    mVerticalBar->setEnable(false);

    mainLayout->setContentsMargins(0, 0, 0, 0);
    mainLayout->setSpacing(0);
    mainLayout->addWidget(mMainWidget);
    mainLayout->addWidget(mBottomDocker);
    setLayout(mainLayout);
}

void TrendGraph::InitPlotArea()
{
    mPlotArea = new QWidget;

    auto mainLayout = new QVBoxLayout;
    mainLayout->setContentsMargins(0, 0, 0, 0);
    mainLayout->setSpacing(0);
    mainLayout->setMargin(0);
    mPlotArea->setLayout(mainLayout);

    for (int i = TREND_PARAM_TYPE_START; i < TREND_PARAM_TYPE::TREND_PARAM_TYPE_END; ++i)
    {
        mPlots[i] = new TrendChart();
        mPlots[i]->setFixedSize(CHART_WIDTH, CHART_HEIGHT);
        TrendChartManager::GetInstance()->RegisterTrend((TREND_PARAM_TYPE)i, mPlots[i]);
    }
    QVector<TREND_PARAM_TYPE> sortAllVe=TrendDataModel::GetTypeVeByFilterType(TrendDataModel::ALL);

    for(auto ite: sortAllVe)
    {
        mainLayout->addWidget(mPlots[ite]);
    }


    mainLayout->addStretch();

    mScrollArea = new QScrollArea(this);
    StyleSet::SetBackgroundColor(mScrollArea,COLOR_BLACK);
    mVerticalBar = new ScrollBar(Qt::Vertical);
    mScrollArea->setFrameShape(QFrame::NoFrame);
    mScrollArea->setContentsMargins(0,0,0,0);
    mScrollArea->setWidget(mPlotArea); //TBD 1500ms 2023.10.17
    mScrollArea->setVerticalScrollBar(mVerticalBar);
    mScrollArea->setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    mScrollArea->setFocusProxy(mVerticalBar);
    mScrollArea->setFixedHeight(CHART_HEIGHT * 3);
    mVerticalBar->setSingleStep(CHART_HEIGHT);
    mVerticalBar->setPageStep(CHART_HEIGHT * 3);


    mStandbyArea = new QWidget(mScrollArea);
    mStandbyArea->setAttribute(Qt::WA_TransparentForMouseEvents, true);
    mStandbyArea->setFixedHeight(CHART_HEIGHT * 3);
    QPalette pa = mStandbyArea->palette();
    pa.setColor(QPalette::Window, QColor(229, 229, 229,100));
    mStandbyArea->setAutoFillBackground(true);
    mStandbyArea->setPalette(pa);
    mStandbyArea->raise();
    mStandbyArea->hide();
}

void TrendGraph::InitConnect()
{
    mScrollDrive->InstallScrollDrive(mPlotArea);

    connect(mTimer,&QTimer::timeout,this,[=](){
        mTimer->stop();
        DEBUG_RUNTIME(RefushTimeAxis());
    });
    connect(TrendDataManager::GetInstance(), &TrendDataManager::SignalDataAdded, this,[=](){
        DEBUG_RUNTIME(SlotOnDataAdded());
    });
    connect(mEventLabel, &EventLabel::SignalCurPrioChanged, mCursorWidget, &TrendCursor::SetAlarmPrio);
    connect(mPreBtn, &QPushButton::clicked, this, &TrendGraph::SlotOnPreBtnClicked);
    connect(mNextBtn, &QPushButton::clicked, this, &TrendGraph::SlotOnNextBtnClicked);
    connect(mParamTypeFilter, &CfgButton::SignalValueChanged, this, &TrendGraph::SlotOnCurTypeChanged);
    connect(mZoomTimeFilter, &CfgButton::SignalValueChanged, this, &TrendGraph::SlotOnZoomChanged);
    connect(this, &TrendGraph::SignalChosenTimeChanged, this,[=](bool isToLower){
        DEBUG_RUNTIME(SlotOnChosenTimeChanged(isToLower));
    } );
    connect(mTimeAxis, &QDateTimeAxis::rangeChanged, this, [=](QDateTime min, QDateTime max){
        DEBUG_RUNTIME(SlotOnTimeRangeChanged(min,max));
    });
    connect(mTimeAxis, &QDateTimeAxis::rangeChanged, this,[=](){
        DEBUG_RUNTIME(RefurbishCursor());
    }, Qt::QueuedConnection);

    foreach (auto chartP, mPlots)
    {
        connect(chartP, &TrendChart::SignalClicked, this, [this](qint64 chosenTime){
            DebugLog << QDateTime::fromMSecsSinceEpoch(chosenTime) << chosenTime << "chosen time1";
            SetChosenTime(QDateTime::fromMSecsSinceEpoch(chosenTime));
        });
    }

    connect(mCursorWidget, &TrendCursor::SignalApplyPrevMove, this, [=]()
    {
        SetChosenTime(mCurChosenTime.addSecs(-60));
    });

    connect(mCursorWidget, &TrendCursor::SignalApplyNextMove, this, [=]()
    {
        SetChosenTime(mCurChosenTime.addSecs(60));
    });


    connect(SettingManager, &SystemSettingManager::SignalSettingChanged, this, [this](int settingId){
        if (settingId == SystemSettingManager::TIMEFORMAT_SETTING) {
            SlotRefurbishTimeFormat();
        }
    });

    connect(mVerticalBar, &QScrollBar::valueChanged, this, [this](int value){
        DebugLog << value << mVerticalBar->singleStep() << value % mVerticalBar->singleStep() << "mVerticalBar, &QScrollBar::valueChanged";
        RefurbishTimeAxisDisplay();
        mCursorWidget->update();
    });

    connect(mVerticalBar, &QScrollBar::sliderReleased, this, [this]{
        if (mVerticalBar->value() % mVerticalBar->singleStep() != 0)
        {
            if (mVerticalBar->value() % mVerticalBar->singleStep() < mVerticalBar->singleStep() / 2)
            {
                mVerticalBar->setValue(mVerticalBar->value() - mVerticalBar->value() % mVerticalBar->singleStep());
            }
            else
            {
                mVerticalBar->setValue(mVerticalBar->value() - mVerticalBar->value() % mVerticalBar->singleStep() + mVerticalBar->singleStep());
            }
        }
    });

    connect(mVerticalBar, &QScrollBar::actionTriggered, this, [this](int){
        qint64 timeTick =mCurChosenTime.toMSecsSinceEpoch();
        foreach (auto chart, mPlots)
        {
            chart->SetChosenTimePoint(timeTick);
        }
        mCursorWidget->update();
    });

    connect(mHorizontalBar, &QScrollBar::valueChanged, this, [=](){
        if(mIsDelayRefurTimeAxis)
        {
            mTimer->start(IGNORE_H_BAR_TIME_MSEC);
        }
        else
        {
            mIsDelayRefurTimeAxis=true;
            RefushTimeAxis();
        }
    }, Qt::DirectConnection);

    connect(TrendDataManager::GetInstance(), &TrendDataManager::SignalDataRemoved, this, [this](QVector<qint64> deletedVec){
        foreach(auto chart,mPlots)
            chart->RemoveFrontPoints(deletedVec);
        RefurbishSlider();
        SetChosenTime(mCurChosenTime);
        RefushTimeAxis();
    });

    connect(mScrollDrive, &MyScrollDrive::SignalMoveAdd, this, [=](int scrollDir)
    {
        if(scrollDir == Qt::Horizontal)
        {
            int times = mZoomTimeFilter->value()/TIME_RANGE_LEVEL_LOW;
            while(times--)
                mHorizontalBar->triggerAction(QSlider::SliderSingleStepAdd);
        }
        else if(scrollDir == Qt::Vertical)
        {
            mVerticalBar->triggerAction(QSlider::SliderSingleStepAdd);
        }
    });

    connect(mScrollDrive, &MyScrollDrive::SignalMoveSub, this, [=](int scrollDir)
    {
        if(scrollDir== Qt::Horizontal)
        {
            int times = mZoomTimeFilter->value()/TIME_RANGE_LEVEL_LOW;
            while(times--)
                mHorizontalBar->triggerAction(QSlider::SliderSingleStepSub);
        }
        else if(scrollDir==Qt::Vertical)
        {
            mVerticalBar->triggerAction(QSlider::SliderSingleStepSub);
        }
    });
}

void TrendGraph::RefushTimeAxis()
{
    auto newestTime = TrendDataManager::GetInstance()->GetNewestDataTime();
    auto newDateTime = newestTime.addSecs(-(mHorizontalBar->maximum() - mHorizontalBar->value()) * 60);
    mTimeAxis->setRange(newDateTime.addSecs(-mZoomTimeFilter->value() * 60), newDateTime);
    mCursorWidget->update();
}

void TrendGraph::RefurbishEventLabelPos()
{
    mEventLabel->DelColor();
    mEventLabel->hide();
    auto cursorLeft = mCursorWidget->geometry().left();
    auto eventLabelXPos1 = cursorLeft + mCursorWidget->width() + EVENT_LABEL_MARGIN;
    if (eventLabelXPos1 + mEventLabel->width() > width())
        mEventLabel->move(cursorLeft - mEventLabel->width() - EVENT_LABEL_MARGIN, EVENT_LABEL_MARGIN);
    else
        mEventLabel->move(eventLabelXPos1, EVENT_LABEL_MARGIN);
    mEventLabel->show();
    mEventLabel->RecoverColor();
}

void TrendGraph::showEvent(QShowEvent *ev)
{
    FocusWidget::showEvent(ev);
    mCursorWidget->raise();
    mCursorWidget->setFixedHeight(mTopWidget->height() + mScrollArea->height());

    foreach (auto chartP, mCurPlots)
        chartP->RedrawXAxisLabels();
    RefurbishTimeAxisDisplay();
    SetChosenTime(mCurChosenTime);
    foreach (auto chart, mPlots)
    {
        chart->update();
    }
}

void TrendGraph::resizeEvent(QResizeEvent *)
{
    auto eventLabelHeight = (TOP_WIDGET_HEIGHT - 2 * EVENT_LABEL_MARGIN);
    mEventLabel->setFixedHeight(eventLabelHeight);

    mCursorWidget->setFixedHeight(mTopWidget->height() + mScrollArea->height());
    SetChosenTime(mCurChosenTime);
    SlotOnTimeRangeChanged(mTimeAxis->min(), mTimeAxis->max());
}

void TrendGraph::SlotOnDataAdded()
{
    RefurbishSlider();
    auto newTime = TrendDataManager::GetInstance()->GetNewestDataTime();
    auto timeAxisMax = mTimeAxis->max();
    if (mHorizontalBar->value() == mHorizontalBar->maximum())
    {
        if (mHorizontalBar->maximum() == 0)
            mTimeAxis->setRange(newTime.addSecs(-60 * mZoomTimeFilter->value()), newTime);
        else
            mHorizontalBar->triggerAction(ScrollBar::SliderAction::SliderToMaximum);
    }
    if (mCurChosenTime < TrendDataManager::GetInstance()->GetOldestDataTime())
    {
        SetChosenTime(TrendDataManager::GetInstance()->GetOldestDataTime());
    }
    SlotOnTimeRangeChanged(mTimeAxis->min(), mTimeAxis->max());
}

void TrendGraph::SlotOnPreBtnClicked()
{
    auto preTime = TrendDataManager::GetInstance()->GetPreAlarmTime(mCurChosenTime.toMSecsSinceEpoch());
    if(preTime!=-1)
    {
        DebugLog << preTime << "preTime" << mCurChosenTime;
        SetChosenTime(QDateTime::fromMSecsSinceEpoch(preTime));
        RefurbishEventLabelPos();
    }
}

void TrendGraph::SlotOnNextBtnClicked()
{
    //AlarmManager::GetInstance()->TriggerAlarm(ALARM_ETCO2_HIGH,1);
    auto nextTime = TrendDataManager::GetInstance()->GetNextAlarmTime(mCurChosenTime.toMSecsSinceEpoch());
    if(nextTime!=-1)
    {
        DebugLog << nextTime << "nextTime" << mCurChosenTime;
        SetChosenTime(QDateTime::fromMSecsSinceEpoch(nextTime));
        RefurbishEventLabelPos();
    }
}

void TrendGraph::SlotOnCurTypeChanged(int type)
{
    int allNum=mPlotArea->layout()->children().size();
    while(allNum>0)
    {
        mPlotArea->layout()->takeAt(0);
        allNum--;
    }

    mCurType=type;

    QVector<TREND_PARAM_TYPE> sortAllVe=TrendDataModel::GetTypeVeByFilterType((TrendDataModel::DATA_FILTER_TYPE)mCurType);

    for(auto ite: sortAllVe)
    {
        mPlotArea->layout()->addWidget(mPlots[ite]);
    }

    mPlotArea->layout()->addItem(new QSpacerItem(20,40,QSizePolicy::Minimum,QSizePolicy::Expanding));

    foreach(auto chartP,mPlots)
    {
        chartP->hide();
        chartP->update();
    }

    int showCount=0;
    for(auto ite: sortAllVe)
    {
        if (mDisabledTypes.indexOf(ite) == -1)
        {
            showCount++;
            mPlots[ite]->show();
        }
    }
    mPlotArea->setFixedHeight(CHART_HEIGHT * showCount);
    RefurbishTimeAxisDisplay();
}

void TrendGraph::SlotOnZoomChanged(int range)
{
    auto curTimestamp = TrendDataManager::GetInstance()->GetNewestDataTime();
    if (TrendDataManager::GetInstance()->GetTrendDataNum() == 0)
    {
        curTimestamp = QDateTime::currentDateTime();
        curTimestamp = QDateTime::fromMSecsSinceEpoch(curTimestamp.toMSecsSinceEpoch() - (curTimestamp.toMSecsSinceEpoch() % 60000));
    }

    auto axisMax = curTimestamp;
    while (true)
    {
        if (mCurChosenTime == QDateTime() || TrendDataManager::GetInstance()->GetTrendDataNum() == 0)
            break;
        if (mCurChosenTime < axisMax.addSecs(-mZoomTimeFilter->value() * 60))
            axisMax = axisMax.addSecs(-60);
        else
            break;
    }


    mTimeAxis->setRange(axisMax.addSecs(-mZoomTimeFilter->value() * 60), axisMax);

    RefurbishSlider();
    auto value = mHorizontalBar->maximum() - (mTimeAxis->max().secsTo(curTimestamp) / 60);
    mHorizontalBar->setValue(value);

    switch (range)
    {
    case TIME_RANGE_LEVEL_LOW:
        foreach (auto chart, mPlots)
        {
            chart->SetSymbolSize(SYMPLE_SIZE_L);
            chart->update();
        }
        break;
    case TIME_RANGE_LEVEL_MIDDLE:
        foreach (auto chart, mPlots)
        {
            chart->SetSymbolSize(SYMPLE_SIZE_M);
            chart->update();
        }
        break;
    case TIME_RANGE_LEVEL_HIGH:
        foreach (auto chart, mPlots)
        {
            chart->SetSymbolSize(SYMPLE_SIZE_NONE);
            chart->update();
        }
        break;
    case TIME_RANGE_LEVEL_MAX:
        foreach (auto chart, mPlots)
        {
            chart->SetSymbolSize(SYMPLE_SIZE_NONE);
            chart->update();
        }
        break;
    }

    SetChosenTime(mCurChosenTime);
}

void TrendGraph::SlotRefurbishTimeFormat()
{
    auto timeFormatEnum = SettingManager->GetIntSettingValue(SystemSettingManager::TIMEFORMAT_SETTING);
    QString format{};
    if (timeFormatEnum == TIMEMODE_12_H)
        format = "APhh:mm";
    else
        format = "hh:mm";

    foreach (auto chartP, mPlots)
    {
        chartP->SetTimeFormat(format);
    }
    mCursorWidget->SetFormat(format);
}

void TrendGraph::SlotOnChosenTimeChanged(bool isToLower)
{
    if (mCurChosenTime < mTimeAxis->min() || mCurChosenTime > mTimeAxis->max())
    {
        SetTimeAxisUpperLimit(mCurChosenTime,isToLower);
    }

    if (mCurPlots.size() == 0)
        return;
    int globalXPos{};
    globalXPos = mCurPlots.first()->ConvertTimePosToGlobalXPos(mCurChosenTime);


    MoveCursor(QPoint(globalXPos, 0));

    auto trendDataSt = TrendDataManager::GetInstance()->GetTrendDataSt(mCurChosenTime);
    auto alarmIdVec = trendDataSt.mAlarmIdList;
    FillEventLabel(alarmIdVec);
    auto runMode = trendDataSt.mRunMode;
    ALL_STRINGS_ENUM nameId = ALL_STRINGS_ENUM::STR_NULL;
    if (runMode == -1 || runMode == MODE_RUN_SELFTEST)
    {
        nameId = ALL_STRINGS_ENUM::STR_DASH;
    }
    else if (MODE_RUN_VENT == runMode)
    {
        unsigned short vent_mode = trendDataSt.mVentMode;
        nameId = VentModeSettingManager::GetInstance()->GetVentModeNameId((E_VENT_MODE_ID)vent_mode);
    }
    else
    {
        nameId = RunModeManage::GetInstance()->GetRunModeName((E_RUNMODE)runMode);
    }
    mModeLabel->setText(UIStrings::GetStr(nameId));
    qint64 timeTick =mCurChosenTime.toMSecsSinceEpoch();
    foreach (auto chart, mPlots)
    {
        chart->SetChosenTimePoint(timeTick);
    }
    QDateTime dateTime;
    TrendDataManager::GetInstance()->GetShowTime(timeTick,&dateTime);
    mDateLabel->setText(SystemTimeManager::GetDateStr( dateTime.date()));
    mCursorWidget->SetTime(dateTime);
}

void TrendGraph::SlotOnTimeRangeChanged(QDateTime min, QDateTime max)
{
    foreach (auto chartP, mCurPlots)
    {
        if(isVisible())
            chartP->SetTimeRange(min, max);
        if (chartP->GetAllData().size() != 0)
        {
            auto scalePair = TrendChartManager::GetInstance()->GetAppropriateScale(mPlots.key(chartP), chartP->GetAllData());
            chartP->SetYScale(scalePair.second);
        }
        chartP->update();
    }
}

void TrendGraph::InitUiText()
{
    auto tmpMap = QMap<int, QString>();
    for (int id = TrendDataModel::ALL; id < TrendDataModel::GROUP_TYPE_MAX; ++id)
    {
        if(id==TrendDataModel::FLOWMETER_PARAM&&ConfigManager->GetConfig<int>(SystemConfigManager::MACHINE_MODEL_INDEX) == CENAR_30M)
            continue;
        else if(id==TrendDataModel::EXTERNAL_MODULE_PARAM && ModuleTestManager::GetInstance()->GetAgModuleType() == None
                && !ConfigManager->GetConfig<int>(SystemConfigManager::O2_BOOL_INDEX))
        {
            continue;
        }
        tmpMap.insert(id,TrendDataModel::GetTypeFilterTypeName((TrendDataModel::DATA_FILTER_TYPE)id));
    }
    mParamTypeDataModel->SetValueAndStr(tmpMap);


    QMap<int, QString> stringMap
    {
        {10,        "10"},
        {30,        "30"},
        {60,        "60"},
        {120,       "120"},
    };

    QMutableMapIterator<int, QString> i(stringMap);
    while (i.hasNext())
    {
        i.next();
        i.setValue(i.value() + " " + UIStrings::GetStr(ALL_STRINGS_ENUM::STR_MINUTE));
    }
    mZoomTimeFilterDataModel->SetValueAndStr(stringMap);

    mPreBtn ->setText(UIStrings::GetStr(ALL_STRINGS_ENUM::STR_PRE_EVENT));
    mNextBtn->setText(UIStrings::GetStr(ALL_STRINGS_ENUM::STR_NEXT_EVENT));
}

void TrendGraph::MoveCursor(QPoint globalP)
{
    if (mPlots.size() < 1)
        return;

    //    if (!mPlots.first()->ContainsInChartPlotArea(globalP))
    //    {
    //        return;
    //    }
    auto cursorLeft = mapFromGlobal(globalP).x() - mCursorWidget->width() / 2;
    mCursorWidget->move(cursorLeft, 0);
    RefurbishEventLabelPos();
}


void TrendGraph::RefurbishCursor()
{
    auto axisMin = mTimeAxis->min();
    auto axisMax = mTimeAxis->max();

    if (TrendDataManager::GetInstance()->GetTrendDataNum() == 0)
    {
        mCursorWidget->hide();
        return;
    }
    else
    {
        mCursorWidget->show();
    }

    if (mCurChosenTime <= axisMin)
    {
        DEBUG_RUNTIME(SetChosenTime(axisMin));
    }
    else if (mCurChosenTime >= axisMax)
    {
        DEBUG_RUNTIME(SetChosenTime(axisMax));
    }
    else
    {

        qint64 curTimeTick = mCurChosenTime.toMSecsSinceEpoch();
        foreach (auto chartP, mPlots)
            if (chartP->geometry().bottom() >= mVerticalBar->value() && chartP->geometry().top() < (mVerticalBar->value() + mVerticalBar->pageStep()))
            {
                if (chartP->isVisible())
                {
                    MoveCursor({chartP->ConvertTimePosToGlobalXPos(curTimeTick), 0});
                    break;
                }
            }
    }
}

void TrendGraph::FillEventLabel(QVector<E_ALARM_ID> idVec)
{
    QVector<EventLabel::DisplayInfoSt> disVec{};
    for (auto id : idVec)
    {
        auto alarmInfoSt = AlarmManager::GetInstance()->GetAlarmInfo(id);
        disVec << EventLabel::DisplayInfoSt{alarmInfoSt.mAlarmPrio, UIStrings::GetStr(alarmInfoSt.mAlarmStrId)};
    }

    mEventLabel->hide();
    mEventLabel->SetAlarmStringList(disVec);
    RefurbishEventLabelPos();
}

void TrendGraph::SetChosenTime(QDateTime timeStamp)
{
    if (TrendDataManager::GetInstance()->GetTrendDataNum() == 0)
    {
        mCursorWidget->hide();
        return;
    }
    else
        mCursorWidget->show();

    if (timeStamp == QDateTime())
        return;
    if (TrendDataManager::GetInstance()->GetTrendDataNum() == 0)
        timeStamp = mTimeAxis->max();
    else
    {
        auto dataMinTime = TrendDataManager::GetInstance()->GetOldestDataTime();
        auto dataMaxTime = TrendDataManager::GetInstance()->GetNewestDataTime();
        if (timeStamp <= dataMinTime)
            timeStamp = dataMinTime;
        else if (timeStamp >= dataMaxTime)
            timeStamp = dataMaxTime;
    }

    bool res=false;
    if(timeStamp<mCurChosenTime)
        res=true;
    else
        res=false;
    mCurChosenTime = timeStamp;
    emit SignalChosenTimeChanged(res);
}

void TrendGraph::RefurbishSlider()
{
    mHorizontalBar->setPageStep(mZoomTimeFilter->value() + 1);
    auto dataNum = TrendDataManager::GetInstance()->GetTrendDataNum();
    auto maxValue = dataNum - mHorizontalBar->pageStep();
    if (maxValue < 0)
        maxValue = 0;
    if(mHorizontalBar->value()==mHorizontalBar->maximum())
    {
        mHorizontalBar->setMaximum(maxValue);
        mHorizontalBar->setValue(mHorizontalBar->maximum());
    }
    else
        mHorizontalBar->setMaximum(maxValue);
}

void TrendGraph::SetTimeAxisUpperLimit(QDateTime dateTime ,bool isToLower)
{
    auto newestTimestamp = TrendDataManager::GetInstance()->GetNewestDataTime();
    if (newestTimestamp != QDateTime())
    {
        mIsDelayRefurTimeAxis=false;
        if(isToLower)
        {
            auto value = mHorizontalBar->maximum() - std::abs(dateTime.addSecs((mZoomTimeFilter->value()/10)*TIMEAXIS_SCALE*60).secsTo(newestTimestamp)) / 60;
            mHorizontalBar->setValue(value);
        }
        else
        {
            auto value = mHorizontalBar->maximum() - std::abs(dateTime.secsTo(newestTimestamp)) / 60;
            mHorizontalBar->setValue(value);
        }
    }
}

void TrendGraph::RefurbishTimeAxisDisplay()
{
    mCurPlots.clear();
    QVector<TREND_PARAM_TYPE> sortAllVe=TrendDataModel::GetTypeVeByFilterType((TrendDataModel::DATA_FILTER_TYPE)mCurType);
    foreach (auto type, sortAllVe)
    {
        auto chartP=mPlots.value(type);
        if (chartP->geometry().bottom() >= mVerticalBar->value() && chartP->geometry().top() < (mVerticalBar->value() + mVerticalBar->pageStep()))
        {
            if (chartP->isVisible())
            {
                mCurPlots << chartP;
                if(mCurPlots.size()==4)
                    break;
            }
        }
    }
    foreach (auto chartP, mCurPlots)
    {
        chartP->SetTimeRange(mTimeAxis->min(), mTimeAxis->max());
        if (chartP->GetAllData().size() != 0)
        {
            auto scalePair = TrendChartManager::GetInstance()->GetAppropriateScale(mPlots.key(chartP), chartP->GetAllData());
            chartP->SetYScale(scalePair.second);
        }
        if (mCurPlots.size() != 0 && chartP == mCurPlots.last())
        {
            chartP->SetDateAxisVisible(true);
            chartP->Redraw();
        }
        else
        {
            chartP->SetDateAxisVisible(false);
            chartP->Redraw();
        }
    }
    update();
}
