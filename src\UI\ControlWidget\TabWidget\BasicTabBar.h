﻿#ifndef BASICTABBAR_H
#define BASICTABBAR_H

#ifdef WIN32
#define STYLE_FATHER QCommonStyle
#endif

#ifdef __linux__
#define STYLE_FATHER QCommonStyle
#endif
#include <QtGui>
#include <QTabBar>
#include <QProxyStyle>

class BasicTabBar : public QTabBar
{
    Q_OBJECT
public:
    explicit BasicTabBar(QWidget *parent = 0);

    //获取选择框所处的标签索引
    int getHoverIndex()const{ return m_HoverTab; }

    void SetBorderMargin(int left, int top, int right, int bottom)
    {m_left = left; m_top = top; m_right = right; m_bottom = bottom;}

    void setTabSpacing(int spacing)
    { m_tabSpacing = spacing; }

    void setOrientation(int orientation)
    { m_orientation = orientation; }

    bool IsPressCurIndex(QPoint pos);


public slots:
    void setHoverTab(int index);
signals:
    void SignalClickeOn(int curIndex);

protected:
    void keyPressEvent(QKeyEvent *ev) { ev->ignore(); }
    void mousePressEvent(QMouseEvent *event);
    void mouseReleaseEvent(QMouseEvent *ev);
    void paintEvent(QPaintEvent *ev);

private:
    int m_HoverTab{};
    QPoint m_PressPoint;
    bool m_PressedOnBar;

    int m_left;
    int m_top;
    int m_right;
    int m_bottom;

    int m_tabSpacing;
    int m_orientation;

    static const QPen sFocusRectPen;
};

#endif // BASICTABBAR_H
