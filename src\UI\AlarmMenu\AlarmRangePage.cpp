﻿#include "AlarmRangePage.h"
#include "AGModule.h"
#include "CfgButton.h"
#include "RangeController.h"
#include "DataLimitManager.h"
#include "VentModeSettingManager.h"
#include "SystemSettingManager.h"
#include "PushButton.h"
#include "String/UIStrings.h"
#include "SystemConfigManager.h"
#include "DataManager.h"
#include "ChartManager.h"
#include "ParamLabelManage.h"
#include "UnitManager.h"
#include "TipDialog.h"
#include "ModuleTestManager.h"

#define ANESTHETIC_GAS_START 7
#define APNEARANGE_DEFAULT 20

AlarmRangePage::AlarmRangePage(QWidget *parent) :
    FocusWidget(parent)
{
    mFiAnaesDatatype = VALUEDATA_ET_AA1;
    mEtAnaesDatatype = VALUEDATA_ET_AA1;
    mAsphyxia = SettingManager->GetIntSettingValue(SystemSettingManager::ASPHYXIA_TIME);

    InitUi();
    InitUiText();
    InitConnect();
}

void AlarmRangePage::InitUi()
{
    mTabWidget = new VerticalTabWidget;
    mMachineLimited = new FocusWidget;

    m_fio2Range = new RangeController;
    initRangeController(m_fio2Range, VALUEDATA_FIO2);
    m_fio2Range->SetUnitId(ParamLabelManage::GetParamInfoSt(VALUEDATA_FIO2).mUnitStrId);

    mPawRange = new RangeController;
    initRangeController(mPawRange, VALUEDATA_PPEAK);
    DataLimitManager::GetInstance()->register_paw_limit_ctrl(mPawRange);
    mPawRange->SetUnitId(UnitManager::GetInstance()->GetCurPressureUnitInfo().mUnitStrId);
    mPawRange->SetLimitValueFontColor(ParamLabelManage::GetParamInfoSt(VALUEDATA_PPEAK).mNameColor);

    m_vteRange = new VtRange;
    initRangeController(m_vteRange, VALUEDATA_VTE);
    m_vteRange->SetUnitId(ParamLabelManage::GetParamInfoSt(VALUEDATA_VTE).mUnitStrId);

    mMvRange = new RangeController;
    initRangeController(mMvRange, VALUEDATA_MV);
    mMvRange->SetUnitId(ParamLabelManage::GetParamInfoSt(VALUEDATA_MV).mUnitStrId);

    mRateRange = new RangeController;
    initRangeController(mRateRange, VALUEDATA_RATE);
    mRateRange->SetUnitId(ParamLabelManage::GetParamInfoSt(VALUEDATA_RATE).mUnitStrId);

    m_apneaRange = new ApneaRange;
    m_apneaRange->enableLowLimit(false);
    m_apneaRange->setName(STR_VALUEPARAM_ASPHY);
    m_apneaRange->SetUnitId(STR_UNIT_S);

            //呼吸窒息，默认值20,范围15-40;由于呼吸窒息是比较特殊的情形,所以不把它当做一般报警参数限制处理;
            //初始化的时候,mCurValue != m_asphxia,需要将值写入设置模式中，但是，发包由待机命令自动完成
            VentModeSettingManager::GetInstance()->SaveSettingdata(SETTINGDATA_ASPHY, mAsphyxia);
    m_apneaRange->setHighMinMax(10, 40); //TBD
    m_apneaRange->setHighValue(mAsphyxia);

    mPrRange = new RangeController;
    initRangeController(mPrRange, VALUEDATA_PR);

    mMachineRestore = new PushButton(UIStrings::GetStr(ALL_STRINGS_ENUM::STR_MAINMENU_DEFAULT));

    QVBoxLayout *leftLayout = new QVBoxLayout;
    leftLayout->setSpacing(3);


    leftLayout->addWidget(mPawRange);
    leftLayout->addWidget(m_vteRange);
    leftLayout->addWidget(mMvRange);
    leftLayout->addWidget(mRateRange);
    leftLayout->addWidget(m_apneaRange);
    leftLayout->addWidget(m_fio2Range);
    leftLayout->addSpacerItem(new QSpacerItem(0,0,QSizePolicy::Expanding,QSizePolicy::Expanding));
    leftLayout->addWidget(mMachineRestore);
    leftLayout->setAlignment(mMachineRestore ,Qt::AlignRight);

    leftLayout->setContentsMargins(0, 0, 0, 15);
    mGasLimited = new FocusWidget;

    m_fico2Range = new RangeController;
    initRangeController(m_fico2Range, VALUEDATA_FICO2, false, true);
    DataLimitManager::GetInstance()->register_CO2FI_limit_ctrl(m_fico2Range);
    m_fico2Range->SetUnitId(ParamLabelManage::GetParamInfoSt(VALUEDATA_FICO2).mUnitStrId);

    m_etco2Range = new RangeController;
    initRangeController(m_etco2Range, VALUEDATA_ETCO2);
    DataLimitManager::GetInstance()->register_CO2ET_limit_ctrl(m_etco2Range);
    m_etco2Range->SetUnitId(ParamLabelManage::GetParamInfoSt(VALUEDATA_ETCO2).mUnitStrId);

    UnitManager::GetInstance()->SetCategoryCurUnit(CONVERTIBLE_UNIT_CATEGORY::CO2_CATEGORY,
                                                   (UNIT_TYPE)SettingManager->GetIntSettingValue(SystemSettingManager::AGUNIT_SETTING));

    mFiN2oRange = new RangeController;
    initRangeController(mFiN2oRange, VALUEDATA_FIN2O);
    mFiN2oRange->SetUnitId(ParamLabelManage::GetParamInfoSt(VALUEDATA_FIN2O).mUnitStrId);

    mEtN2oRange = new RangeController;
    initRangeController(mEtN2oRange, VALUEDATA_ETN2O);
    mEtN2oRange->SetUnitId(ParamLabelManage::GetParamInfoSt(VALUEDATA_ETN2O).mUnitStrId);

    mFiAnaesRange = new RangeController;
    initRangeController(mFiAnaesRange, mFiAnaesDatatype);
    mFiAnaesRange->SetUnitId(ParamLabelManage::GetParamInfoSt(VALUEDATA_FI_AA1).mUnitStrId);

    mEtAnaesRange = new RangeController;
    initRangeController(mEtAnaesRange, mEtAnaesDatatype);
    mEtAnaesRange->SetUnitId(ParamLabelManage::GetParamInfoSt(VALUEDATA_ET_AA1).mUnitStrId);
    mGasRestore = new PushButton(UIStrings::GetStr(STR_MAINMENU_DEFAULT));

    QVBoxLayout *rightLayout = new QVBoxLayout;
    rightLayout->setSpacing(3);

    rightLayout->addWidget(m_fico2Range);
    rightLayout->addWidget(m_etco2Range);
    rightLayout->addWidget(mFiN2oRange);
    rightLayout->addWidget(mEtN2oRange);
    rightLayout->addWidget(mFiAnaesRange);
    rightLayout->addWidget(mEtAnaesRange);
    rightLayout->addSpacerItem(new QSpacerItem(0,0,QSizePolicy::Expanding,QSizePolicy::Expanding));
    rightLayout->addWidget(mGasRestore);
    rightLayout->setAlignment(mGasRestore ,Qt::AlignRight);
    rightLayout->setContentsMargins(0, 0, 0, 15);

    mMachineLimited->setLayout(leftLayout);
    mGasLimited->setLayout(rightLayout);
    mGasLimited->AddFoucusSonWidget(m_fico2Range);
    mGasLimited->AddFoucusSonWidget(m_etco2Range);
    mGasLimited->AddFoucusSonWidget(mFiN2oRange);
    mGasLimited->AddFoucusSonWidget(mEtN2oRange);
    mGasLimited->AddFoucusSonWidget(mFiAnaesRange);
    mGasLimited->AddFoucusSonWidget(mEtAnaesRange);
    mGasLimited->AddFoucusSonWidget(mGasRestore);
    mTabWidget->addTab(mMachineLimited ,UIStrings::GetStr(ALL_STRINGS_ENUM::STR_VENTILATOR_ALARM_LIMIT));


    QVBoxLayout *mainLayout = new QVBoxLayout;
    mainLayout->addWidget(mTabWidget);
    mainLayout->setContentsMargins(0,0,0,0);
    setLayout(mainLayout);
    mRestoreTip = new TipDialog(this);
    UpUi();

    mAllRangeController << m_vteRange;
    mAllRangeController << m_apneaRange;
    mAllRangeController << mMvRange;
    mAllRangeController << mRateRange;
    mAllRangeController << mPrRange;
    mAllRangeController << mPawRange;
    mAllRangeController << m_fio2Range;
    mAllRangeController << m_fico2Range;
    mAllRangeController << m_etco2Range;
    mAllRangeController << mFiN2oRange;
    mAllRangeController << mEtN2oRange;
    mAllRangeController << mFiAnaesRange;
    mAllRangeController << mEtAnaesRange;

}

void AlarmRangePage::translateHeader(QLabel *highLabel, QLabel *lowLabel)
{

}

void AlarmRangePage::UpUi()
{
    //没有AG模块，无效相关设置
    if (ModuleTestManager::GetInstance()->GetAgModuleType() != AG)
    {
        mEtN2oRange->setVisible(false);
        mFiN2oRange->setVisible(false);
        mFiAnaesRange->setVisible(false);
        mEtAnaesRange->setVisible(false);
    }
    else
    {
        //如果没有麻醉气体则隐藏其报警限设置
        if (mEtAnaesDatatype == VALUEDATA_ET_AA1)
        {
            mFiAnaesRange->setVisible(false);
            mEtAnaesRange->setVisible(false);
        }
        else
        {
            mFiAnaesRange->setVisible(true);
            mEtAnaesRange->setVisible(true);
        }
    }

    if (ModuleTestManager::GetInstance()->GetAgModuleType() != None)
    {
        if(mTabWidget->indexOf(mGasLimited)==-1)
            mTabWidget->addTab(mGasLimited ,UIStrings::GetStr(ALL_STRINGS_ENUM::STR_ANESTHETIC_GAS_ALARM_LIMIT));
    }

    //无AG，无CO2,CO2设置无效
    if (ModuleTestManager::GetInstance()->GetAgModuleType() == None)
    {
        m_etco2Range->setVisible(false);
        m_fico2Range->setVisible(false);
    }
    if (!ConfigManager->GetConfig<bool>(SystemConfigManager::O2_BOOL_INDEX))
    {
        m_fio2Range->setVisible(false);
    }
}

//根据DataLimitManager数据，设置各个参数报警限制设置控件；默认情况，上、下限制调节控件可用
void AlarmRangePage::initRangeController(RangeController *ctrl, short id, bool lowEnable, bool highEnable)
{
    ctrl->SetLimitValueFontColor(ParamLabelManage::GetParamInfoSt((MONITOR_PARAM_TYPE)id).mNameColor);

    MONITOR_PARAM_TYPE data_type = (MONITOR_PARAM_TYPE)id;
    DataLimitManager *p_manage = DataLimitManager::GetInstance();
    int low_lowest = p_manage->GetLowLowest(data_type);
    int low_limit = p_manage->GetLowLimit(data_type);
    int low_highest = p_manage->GetLowHighest(data_type);
    int high_lowest = p_manage->GetHighLowest(data_type);
    int high_limit = p_manage->GetHighLimit(data_type);
    int high_highest = p_manage->GetHighHighest(data_type);
    int difference = p_manage->GetDifferenceOfLowHigh(data_type);
    unsigned short digit;
    if (data_type == VALUEDATA_PPEAK)
    {
        digit = UnitManager::GetInstance()->GetCurPressureUnitInfo().digit;
    }
    else
    {
        digit = p_manage->GetLimitDigit(data_type);
    }
    unsigned short step = p_manage->GetChangeStep(data_type);
    unsigned short min_off_flag = p_manage->GetMinOffFlag(data_type);
    unsigned short max_off_flag = p_manage->IsHighLimitClosable(data_type);

    ctrl->enableHighLimit(highEnable);
    ctrl->enableLowLimit(lowEnable);

    if (max_off_flag)
    {//在最低下限OFF
        ctrl->setCloseOnHighest(true);
    }
    if (min_off_flag)
    {//在最高上限OFF
        ctrl->setCloseOnLowest(true);
    }


    if (highEnable && lowEnable)
    {//上、下限控件的势差
        ctrl->setDifferenceOfHighLow(difference);
    }

    ctrl->setStep(step);
    ctrl->setDigit(digit);
    ctrl->setLowMinMax(low_lowest, low_highest);
    ctrl->setHighMinMax(high_lowest, high_highest);
    ctrl->setLowValue(low_limit);
    ctrl->setHighValue(high_limit);
    if (data_type == VALUEDATA_PPEAK)
    {
        ctrl->setName(DataManager::GetParamNameId(VALUEDATA_TOP_PAW));
    }
    else
    {
        ctrl->setName(DataManager::GetParamNameId((int)data_type));
    }
    ctrl->refreshText();
}

//连接报警设置修改处理函数
void AlarmRangePage::InitConnect()
{
    QList<RangeController *> btnList = findChildren<RangeController *>();
    for (int i = 0; i < btnList.size(); i++)
    {
        connect(btnList.at(i), SIGNAL(lowLimitChanged(int)), this, SLOT(onLowLimitChanged(int)));
        connect(btnList.at(i), SIGNAL(highLimitChanged(int)), this, SLOT(onHighLimitChanged(int)));
    }
    connect(AGModule::GetInstance(), &AGModule::SignalAgentTypeChanged, this, &AlarmRangePage::RefurbishAgentType);
    connect(mMachineRestore, &QPushButton::clicked, mRestoreTip, &TipDialog::show);
    connect(mGasRestore, &QPushButton::clicked, mRestoreTip, &TipDialog::show);
    connect(mRestoreTip, &TipDialog::accepted, this, &AlarmRangePage::onSetDefault);
}

//重置默认报警限制
void AlarmRangePage::onSetDefault()
{
    if(mTabWidget->currentWidget() == mMachineLimited)
    {
        SlotMachineRestore();
    }
    else if(mTabWidget->currentWidget() == mGasLimited)
    {
        SlotGasRestore();
    }
}

void AlarmRangePage::SlotMachineRestore()
{
    DataLimitManager *p_manage = DataLimitManager::GetInstance();
    p_manage->SetMachineLimitDefault();

    for (int i = 0; i < mAllRangeController.size(); ++i)
    {
        if(m_fico2Range == mAllRangeController.at(i))
        {
            break;
        }
        if (m_apneaRange == mAllRangeController.at(i))
        {
            mAsphyxia = APNEARANGE_DEFAULT;
            SettingManager->SetSettingValue(SystemSettingManager::ASPHYXIA_TIME, APNEARANGE_DEFAULT);
            m_apneaRange->setHighValue(APNEARANGE_DEFAULT);
        }
        else
        {//重置时，只需要重新读取上、下限
            MONITOR_PARAM_TYPE data_type = (MONITOR_PARAM_TYPE)getControlId(mAllRangeController.at(i));
            short low_limit = p_manage->GetLowLimit(data_type);
            short high_limit = p_manage->GetHighLimit(data_type);
            mAllRangeController.at(i)->ResetHighLowValue(high_limit,low_limit);
        }

    }
}

void AlarmRangePage::SlotGasRestore()
{
    DataLimitManager *p_manage = DataLimitManager::GetInstance();
    p_manage->SetGasLimitDefault();

    for (int i = ANESTHETIC_GAS_START; i < mAllRangeController.size(); ++i)
    {
        MONITOR_PARAM_TYPE data_type = (MONITOR_PARAM_TYPE)getControlId(mAllRangeController.at(i));
        short low_limit = p_manage->GetLowLimit(data_type);
        short high_limit = p_manage->GetHighLimit(data_type);
        mAllRangeController.at(i)->ResetHighLowValue(high_limit,low_limit);
    }
}

//确认参数报警下限修改
void AlarmRangePage::onLowLimitChanged(int limit)
{
    RangeController *senderCtrl = qobject_cast<RangeController *>(sender());
    if (!senderCtrl)
    {
        DebugAssert(0);
        return ;
    }
    if (senderCtrl == m_apneaRange)
    {//室息时间，highlimit来处理
    }
    else
    {//保存设置，并同步更新到其他界面控件
        MONITOR_PARAM_TYPE data_type = (MONITOR_PARAM_TYPE)getControlId(senderCtrl);
        DataLimitManager::GetInstance()->SetLowLimit(data_type, limit);
        ParamLabelManage::GetInstance()->refurbish_alarm_limit(data_type);
    }
}

//确认参数报警上限修改
void AlarmRangePage::onHighLimitChanged(int limit)
{
    RangeController *senderCtrl = qobject_cast<RangeController *>(sender());
    if (!senderCtrl)
    {
        DebugAssert(0);
        return ;
    }
    if (senderCtrl == m_apneaRange)
    {
        mAsphyxia = limit;
        SettingManager->SetSettingValue(SystemSettingManager::ASPHYXIA_TIME, mAsphyxia);
        VentModeSettingManager::GetInstance()->SaveSettingdata(SETTINGDATA_ASPHY, mAsphyxia);
        VentModeSettingManager::GetInstance()->SendPackage();
    }
    else
    {//保存设置，并同步更新到其他界面控件
        MONITOR_PARAM_TYPE data_type = (MONITOR_PARAM_TYPE)getControlId(senderCtrl);
        DataLimitManager::GetInstance()->SetHighLimit(data_type, limit);
        ParamLabelManage::GetInstance()->refurbish_alarm_limit(data_type);
    }
}

void AlarmRangePage::resizeEvent(QResizeEvent *event)
{
    FocusWidget::resizeEvent(event);
}


//麻醉气种改变，更新控件设置
void AlarmRangePage::RefurbishAgentType(unsigned short type)
{
    if (ModuleTestManager::GetInstance()->GetAgModuleType() != AG)
    {
        return;
    }

    auto idPair = AGModule::GetInstance()->AgTypeToParamType(type);
    MONITOR_PARAM_TYPE valuedataFiId = idPair.first;
    MONITOR_PARAM_TYPE valuedataEtId = idPair.second;

    if (mFiAnaesDatatype != valuedataFiId || mEtAnaesDatatype != valuedataEtId)
    {
        mFiAnaesRange->setVisible(false);
        mEtAnaesRange->setVisible(false);
        DataManager::GetInstance()->SaveMonitorData(mFiAnaesDatatype, INVALID_VALUE);
        DataManager::GetInstance()->SaveMonitorData(mEtAnaesDatatype, INVALID_VALUE);
        CloseOldAgentAlarm(mFiAnaesDatatype, mEtAnaesDatatype);

        mFiAnaesDatatype = valuedataFiId;
        mEtAnaesDatatype = valuedataEtId ;

        mFiAnaesRange->Reset();
        mEtAnaesRange->Reset();
        initRangeController(mFiAnaesRange, valuedataFiId);
        initRangeController(mEtAnaesRange, valuedataEtId);
    }
    mFiAnaesRange->setVisible(true);
    mEtAnaesRange->setVisible(true);
    UpUi();
}

void AlarmRangePage::CloseOldAgentAlarm(unsigned short FiAnaesDatatype, unsigned short EtAnaesDatatype)
{
    AlarmManager* p_alarm_mag = AlarmManager::GetInstance();
    DataLimitManager* p_DataLimit_mag = DataLimitManager::GetInstance();

    p_alarm_mag->TriggerAlarm((E_ALARM_ID)p_DataLimit_mag->GetHighAlarmId((MONITOR_PARAM_TYPE)FiAnaesDatatype), 0);
    p_alarm_mag->TriggerAlarm((E_ALARM_ID)p_DataLimit_mag->GetLowAlarmId((MONITOR_PARAM_TYPE)FiAnaesDatatype), 0);
    p_alarm_mag->TriggerAlarm((E_ALARM_ID)p_DataLimit_mag->GetHighAlarmId((MONITOR_PARAM_TYPE)EtAnaesDatatype), 0);
    p_alarm_mag->TriggerAlarm((E_ALARM_ID)p_DataLimit_mag->GetLowAlarmId((MONITOR_PARAM_TYPE)EtAnaesDatatype), 0);
}

unsigned short AlarmRangePage::getControlId(RangeController *ctrl)
{//匹配除了apnea之外的各控件的参数类型
    unsigned short ctrlId;
    if (ctrl == mPawRange)
    {
        ctrlId = VALUEDATA_PPEAK;
    }
    else if (ctrl == m_vteRange)
    {
        ctrlId = VALUEDATA_VTE;
    }
    else if (ctrl == mMvRange)
    {
        ctrlId = VALUEDATA_MV;
    }
    else if (ctrl == mRateRange)
    {
        ctrlId = VALUEDATA_RATE;
    }
    else if (ctrl == mPrRange)
    {
        ctrlId = VALUEDATA_PR;
    }
    else if (ctrl == m_fio2Range)
    {
        ctrlId = VALUEDATA_FIO2;
    }    else if (ctrl == m_fico2Range)
    {
        ctrlId = VALUEDATA_FICO2;
    }
    else if (ctrl == m_etco2Range)
    {
        ctrlId = VALUEDATA_ETCO2;
    }
    else if (ctrl == mFiN2oRange)
    {
        ctrlId = VALUEDATA_FIN2O;
    }
    else if (ctrl == mEtN2oRange)
    {
        ctrlId = VALUEDATA_ETN2O;
    }
    else if (ctrl == mFiAnaesRange)
    {
        ctrlId = mFiAnaesDatatype;
    }
    else if (ctrl == mEtAnaesRange)
    {
        ctrlId = mEtAnaesDatatype;
    }
    else
    {
        DebugAssert(0);
    }
    return ctrlId;
}

void AlarmRangePage::InitUiText()
{
    mMachineRestore->setText(UIStrings::GetStr(ALL_STRINGS_ENUM::STR_MAINMENU_DEFAULT));
    mGasRestore->setText(UIStrings::GetStr(ALL_STRINGS_ENUM::STR_MAINMENU_DEFAULT));
    mTabWidget->setTabText(mTabWidget->indexOf(mMachineLimited) ,
                           UIStrings::GetStr(ALL_STRINGS_ENUM::STR_VENTILATOR_ALARM_LIMIT));
    mTabWidget->setTabText(mTabWidget->indexOf(mGasLimited) ,
                           UIStrings::GetStr(ALL_STRINGS_ENUM::STR_ANESTHETIC_GAS_ALARM_LIMIT));
    mRestoreTip->SetDialogText(UIStrings::GetStr(ALL_STRINGS_ENUM::STR_MAINMENU_DEFAULT) + "？");

    QList<RangeController *> ctrlList = findChildren<RangeController *>();
    for (int i = 0; i < ctrlList.count(); ++i)
    {
        ctrlList.at(i)->InitUiText();
    }
}


void AlarmRangePage::ShowTab(int idx1) {
    if(idx1 >= ALARMRANGEPAGE_INDEX::END || idx1 < 0)
        return;
    switch (idx1){
    case ALARMRANGEPAGE_INDEX::MACHINE_LIMITED:
        mTabWidget->setCurrentIndex(mTabWidget->indexOf(mMachineLimited));
        break;
    case ALARMRANGEPAGE_INDEX::GAS_LIMITED:
        mTabWidget->setCurrentIndex(mTabWidget->indexOf(mGasLimited));
        break;
    default:
        return;
    }
}

void AlarmRangePage::CloseDialog()
{
    mRestoreTip->close();
}
