﻿#ifndef SYSTEMCONFIGMANAGER_H
#define SYSTEMCONFIGMANAGER_H

#include "DBManager/SystemConfigInterface.h"
#include "encryption.h"
#include "../Com/protocol/protocol.h"

#define DESKTOP_WIDTH 1024
#define DESKTOP_HEIGHT 768

//配置软件，1:成功，0:错误
#define SN_LENGTH	4
#define CONFIG_LENGTH	6
//系列号+配置信息+随机数种子+校验码
#define ENC_LENGTH    (SN_LENGTH + CONFIG_LENGTH + 2)

enum STANDARD_SERIAL
{
    STD_CHINA = 0,
    STD_EUROPE,
    STD_USA
};

enum MACHINE_SERIAL
{
    CENAR_30 = 0,
    CENAR_40,
    CENAR_50,
    ELUNA_60,
    ELUNA_70,
    ELUNA_80,
    CENAR_30M,
    OEM,
    MACHINE_SERIAL_MAX
};


class SystemConfigManager : public  QObject
{
    Q_OBJECT
public:
    enum SYSTEM_CONFIG_ENUM
    {
        NETWORK_CONFIG_BOOL_INDEX,  //0
        NETWORK_MONITOR_BOOL_INDEX,

        MACHINE_RULE_STANDARD_INDEX,
        MACHINE_MODEL_INDEX,

        AG_BOOL_INDEX,       //4
        CO2_BOOL_INDEX,
        O2_BOOL_INDEX,
        LOOP_BOOL_INDEX,
        TOUCHSCREEN_BOOL_INDEX,
        TOUCHSCREEN_TYPE_INDEX,
        WAVE_NUM_INDEX,

        ENABLE_FLOWMETER_MODULE,
        GAS_SOURCE_O2_BOOL_INDEX,    //12
        GAS_SOURCE_N2O_BOOL_INDEX,
        GAS_SOURCE_AIR_BOOL_INDEX,

        VCV_BOOL_INDEX,      //15
        PCV_BOOL_INDEX,
        SIMVVC_BOOL_INDEX,
        SIMVPC_BOOL_INDEX,
        PSV_BOOL_INDEX,
        PMODE_BOOL_INDEX,
        PRVC_BOOL_INDEX,
        HLM_BOOL_INDEX,

        MIN_VT_INDEX,     //23

        MANUFACTURE_YEAR_INDEX,       //这一块是出厂日期
        MANUFACTURE_MONTH_INDEX,
        MANUFACTURE_DAY_INDEX,

        SERIAL_MODULE_INDEX,    //27    这一块是产品序列号
        SERIAL_YEAR_INDEX,
        SERIAL_MONTH_INDEX,
        SERIAL_DAY_INDEX,
        SERIAL_NUM_1_INDEX,
        SERIAL_NUM_2_INDEX,
        SERIAL_NUM_3_INDEX,
        SERIAL_NUM_4_INDEX,

        VENT_MODE_NUM_INDEX,   //34

        BIG_SCREEN_BOOL_INDEX,

        ENABLE_LOGO,

        ENABLE_ENGLISH,
        ENABLE_CHINESE,
        ENABLE_RUSSIAN,
        ENABLE_UKRAINIAN,
        ENABLE_FRENCH,

        AG_TYPE_INDEX,
        SYSTEM_CONFIG_ENUM_END
    }; Q_ENUM(SYSTEM_CONFIG_ENUM);

public:
    SystemConfigManager();

    static SystemConfigManager* GetInstance();
    void InitConfig();
    void CheckVentMode();

    template<typename T>
    T GetConfig(SYSTEM_CONFIG_ENUM configEnum)
    {
        return ReadConfigValue(configEnum).value<T>();
    }

    QVector<SystemConfigItemSt> GetAllConfig();


    void SetConfigValue(SYSTEM_CONFIG_ENUM configId, QVariant context);
    void UpdateConfigs(const QVector<SystemConfigItemSt>& configs);

    void RestoreFactoryConfig();

    //返回0校验不成功，解密失败，返回1成功。
    QByteArray BgDecrypt(QByteArray bgCipher);

    //验证系列号是否正确，1:正确，0:错误
    int VerifySn(unsigned char *machSn);
    void ParseConfig(QVector<SystemConfigItemSt> &configs, unsigned char *config);

    ConfigMessage GetMachineConfig();
    void SetMachineConfig(ConfigMessage config);
    bool UpdateByConfigCode(QString code);

    bool IsFullTouch();
    bool IsFullElecFlomter();
    bool IsDoubleBattery();
    static QVariant ReadDefaultValue(SYSTEM_CONFIG_ENUM settingId);
    int GetMachineModeNameId(int machineModelEnum);

private:
    QVariant ReadConfigValue(int id);
private:
    SystemConfigInterface *mSystemConfigDbInterface;
    QVector<SystemConfigItemSt> mCurConfigVec{};
    static QVector<SystemConfigItemSt> sConfigDefault;
};

#endif // SYSTEMCONFIGMANAGER_H

#define ConfigManager (SystemConfigManager::GetInstance())
