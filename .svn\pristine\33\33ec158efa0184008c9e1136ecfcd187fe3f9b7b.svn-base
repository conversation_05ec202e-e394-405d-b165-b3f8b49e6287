﻿#include <QGridLayout>
#include <QTimer>

#include "UiApi.h"
#include "PoweroffConfirmDlg.h"
#include "PushButton.h"
#include "AlarmManager.h"
#include "String/UIStrings.h"
#include "ManageFactory.h"
#include "SystemSoundManager.h"
#include "HistoryEventManager.h"
#include "SystemConfigManager.h"
#include "FlowmeterManager.h"
#include "SystemSettingManager.h"

const int POWEROFF_TIME = 10;
const int PLAY_SOUND_MSEC = 1000;
#define LAST_SECOND_DOTWICE_TIME 280
PoweroffConfirmDlg::PoweroffConfirmDlg(QWidget *parent) :
    FocusWidget(parent)
{
    InitUi();

    mCount = POWEROFF_TIME;
    mCountdownTimer = new QTimer;
    connect(mCountdownTimer, SIGNAL(timeout()), this, SLOT(slotCountdown()));
}

void PoweroffConfirmDlg::InitUi()
{
    StyleSet::SetBackgroundColor(this,COLOR_WHITE);

    mTitle = new QLabel(UIStrings::GetStr(STR_SHUTDOWN_TIME));
    StyleSet::SetTextColor(mTitle,COLOR_DARK_GRAY);
    StyleSet::SetTextFont(mTitle,FONT_XXL,FONT_FAMILY_NINA,true);

    mCountdownLabel = new QLabel(" 10");
    StyleSet::SetTextColor(mCountdownLabel,COLOR_RED);
    StyleSet::SetTextFont(mCountdownLabel,FONT_XXXL);

    mTip = new QLabel(UIStrings::GetStr(STR_SHUTDOWN_TIP));
    StyleSet::SetTextColor(mTip,COLOR_DARK_GRAY);
    StyleSet::SetTextFont(mTip,FONT_XL,FONT_FAMILY_NINA);

    QGridLayout *mainLayout = new QGridLayout;
    mainLayout->setContentsMargins(30, 30, 30, 30);
    mainLayout->setHorizontalSpacing(100);
    mainLayout->addWidget(mTitle, 0, 0, 1, 3);
    mainLayout->addWidget(mCountdownLabel, 1, 1);
    mainLayout->addWidget(mTip, 2, 0, 1, 3);
    mainLayout->setAlignment(mTitle, Qt::AlignHCenter);
    mainLayout->setAlignment(mTip, Qt::AlignHCenter);
    mainLayout->setAlignment(mCountdownLabel, Qt::AlignHCenter);

    setLayout(mainLayout);
}

void PoweroffConfirmDlg::showEvent(QShowEvent *)
{
    mIsFullElectronicFlowmeter = SystemConfigManager::GetInstance()->IsFullElecFlomter();
    if (mIsFullElectronicFlowmeter)
    {
        mSavedControlType = SettingManager->GetIntSettingValue(SystemSettingManager::FLOW_CONTROL_TYPE);
        mSavedBalanceType = SettingManager->GetIntSettingValue(SystemSettingManager::FLOW_CONTROL_BANLANCE_TYPE);
        mSavedTotalValue = SettingManager->GetFloatSettingValue(SystemSettingManager::FLOW_CONTROL_TOTAL_VALUE);
        mSavedO2Value = SettingManager->GetFloatSettingValue(SystemSettingManager::FLOW_CONTROL_O2_VALUE);
        mSavedBalanceValue = SettingManager->GetFloatSettingValue(SystemSettingManager::FLOW_CONTROL_BANLANCE_GAS_VALUE);

        FlowmeterManager::GetInstance()->StopFlow();
    }

    mCountdownTimer->start(PLAY_SOUND_MSEC);
    mOldSoundLevel=ManageFactory::GetInstance()->GetDeviceManage()->GetVolume();
    ManageFactory::GetInstance()->GetDeviceManage()->PasuePlayAlarm(true);
    ManageFactory::GetInstance()->GetDeviceManage()->PasuePlayTestSound(true);
    ManageFactory::GetInstance()->GetDeviceManage()->PasuePlayKnobSound(true);
    ManageFactory::GetInstance()->GetDeviceManage()->SetVolume(DeviceManageBase::LEVEL_MIN);
};

void PoweroffConfirmDlg::hideEvent(QHideEvent *)
{
    ResetCount();
    mCountdownLabel->setText(QString::number(POWEROFF_TIME, 10));
    ManageFactory::GetInstance()->GetDeviceManage()->SetVolume(mOldSoundLevel);
    ManageFactory::GetInstance()->GetDeviceManage()->PasuePlayTestSound(false);
    ManageFactory::GetInstance()->GetDeviceManage()->PasuePlayKnobSound(false);
    ManageFactory::GetInstance()->GetDeviceManage()->PasuePlayAlarm(false);

    if (mIsFullElectronicFlowmeter)
    {
        SettingManager->SetSettingValue(SystemSettingManager::FLOW_CONTROL_TYPE, mSavedControlType);
        SettingManager->SetSettingValue(SystemSettingManager::FLOW_CONTROL_BANLANCE_TYPE, mSavedBalanceType);
        SettingManager->SetSettingValue(SystemSettingManager::FLOW_CONTROL_TOTAL_VALUE, mSavedTotalValue);
        SettingManager->SetSettingValue(SystemSettingManager::FLOW_CONTROL_O2_VALUE, mSavedO2Value);
        SettingManager->SetSettingValue(SystemSettingManager::FLOW_CONTROL_BANLANCE_GAS_VALUE, mSavedBalanceValue);

        FlowmeterManager::GetInstance()->UpValueToFlowModel();

        mIsFullElectronicFlowmeter = false;
    }
}

void PoweroffConfirmDlg::ResetCount()
{
    mCountdownTimer->stop();
    mCount = POWEROFF_TIME;
}


void PoweroffConfirmDlg::slotCountdown()
{
    --mCount;
    mCountdownLabel->setText(QString::number(mCount));

    if (mCount == 0)
    {
        mCountdownTimer->stop();
        ManageFactory::GetInstance()->GetDeviceManage()->OpenDelayShutDown(false);
    }
    else if (mCount == 1)
    {
        HistoryEventManager::GetInstance()->AddHistoryEvent(HistoryEventManager::HISTORY_EVENT_TYPE::OPERATE_EVENT, LOG_SHUT_DOWN);
        SystemSoundManager::GetInstance()->PlayByMaxLevel(SystemSoundManager::SHUTDOWN_DELAY);
        QTimer::singleShot(LAST_SECOND_DOTWICE_TIME,this,[=](){
            SystemSoundManager::GetInstance()->PlayByMaxLevel(SystemSoundManager::SHUTDOWN_DELAY);
        });
    }
    else if (mCount > 1)
    {
        SystemSoundManager::GetInstance()->PlayByMaxLevel(SystemSoundManager::SHUTDOWN_DELAY);
    }

}

void PoweroffConfirmDlg::InitUiText()
{
    mTitle->setText(UIStrings::GetStr(STR_SHUTDOWN_TIME));
    mTip->setText(UIStrings::GetStr(STR_SHUTDOWN_TIP));
}
