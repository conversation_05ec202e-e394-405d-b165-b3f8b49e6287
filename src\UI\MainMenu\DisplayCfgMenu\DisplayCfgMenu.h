﻿#ifndef DISPLAY_CONFIG_H
#define DISPLAY_CONFIG_H

#include <QtGui>

#include "TabFocusWidget.h"
#include "VerticalTabWidget.h"
#include "VolAndBrightCtrlPage.h"
#include "WaveformsSettingPage.h"

class UiSettingPage;

class DisplayCfgMenu : public FocusWidget
{
    Q_OBJECT
public:
    explicit DisplayCfgMenu(QWidget *parent = NULL);
    void InitUiText();

protected:
    void hideEvent(QHideEvent *event);

private:
    void InitUi();
    VerticalTabWidget *mVerticalTabWidget;

    UiSettingPage *mUiSettingPage{};
    VolAndBrightCtrlPage *mVolAndBrightCtrlPage{};
    WaveformsSettingPage *mWaveformsSettingPage{};
};

#endif // DISPLAY_CONFIG_H
