﻿#include "BatteryIcon.h"
#include "BytteryUiFlush.h"
#include "ManageFactory.h"
#include "DebugLogManager.h"
#include "UiApi.h"

#define CHANGE_TIME 1000

#define FULL_ICON       ":/Battery/Full.png"
#define PT50_ICON       ":/Battery/50%.png"
#define PT75_ICON       ":/Battery/75%.png"
#define CHARGING_ICON   ":/Battery/Charging.png"
#define INVALID_ICON    ":/Battery/Invalid.png"
#define LOW_ICON        ":/Battery/Low.png"
#define ZERO_ICON       ":/Battery/Zero.png"
#define NORMAL_LOW_ICON       ":/Battery/NormalLow.png"
#define BATTERYICON_SIZE {30, 40}
#define FONT_HEIGHT 8
#define TEXT_X_POS 23
#define ICON_RECT QRect(0, 0, 22, 40)

//#define OPEN_DEBUG_INFO

#define ICON_ANGLE      90

BatteryIcon::BatteryIcon(QWidget *parent) :
    QFrame(parent)
{
    mCurValue = ManageFactory::GetInstance()->GetDeviceManage()->getCurBatteryPCT();
    mMaxValue = BATTERY_PCT_100;
    mMinValue = 0;

    mBatteryIconMap =
    {
        {BATTERY_PCT_0,      &mZeroIcon},
        {BATTERY_PCT_25,     &m25PtIcon},
        {BATTERY_PCT_50,     &m50PtIcon},
        {BATTERY_PCT_75,     &m75PtIcon},
        {BATTERY_PCT_100,    &mFullIcon},
    };
    setContentsMargins(0, 0, 0, 0);
    SetState(ManageFactory::GetInstance()->GetDeviceManage()->GetBatteryStatus());
}

void BatteryIcon::paintEvent(QPaintEvent *ev)
{
    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing);
    QPainter numberPainter(this);
    QPen pen;
    pen.setStyle(Qt::SolidLine);
    pen.setWidth(4);
    pen.setColor(COLOR_LIGHT_GREEN);
    numberPainter.setPen(pen);
    QFont font;
    font.setPointSize(FONT_HEIGHT);
    font.setFamily("tahoma");
    numberPainter.setFont(font);
    numberPainter.setRenderHint(QPainter::Antialiasing);

    switch (mBatteryState) {
    case DeviceManageBase::BATTERY_ERROR :
        painter.drawPixmap(ICON_RECT, mInvalidIcon, rect());
        break;
    case DeviceManageBase::BATTERY_ING :
    case DeviceManageBase::BATTERY_ING_DOUBLE :
    {
        if(mCurValue != BATTERY_PCT_0)
        {
            painter.drawPixmap(ICON_RECT, *mBatteryIconMap[(BATTERY_LEVEL_ENUM)mCurValue], rect());
        }
        painter.drawPixmap(ICON_RECT, mChargingIcon, rect());
        if(DeviceManageBase::BATTERY_ING_DOUBLE == mBatteryState)
            numberPainter.drawText(TEXT_X_POS, FONT_HEIGHT, "2");
    }
        break;
    case DeviceManageBase::BATTERY_ISFULL :
    case DeviceManageBase::BATTERY_ISFULL_DOUBLE :
    {
        painter.drawPixmap(ICON_RECT, mFullIcon, rect());
        if(DeviceManageBase::BATTERY_ISFULL_DOUBLE == mBatteryState)
            numberPainter.drawText(TEXT_X_POS, FONT_HEIGHT, "2");
    }
        break;
    case DeviceManageBase::BATTERY_OUT :
    case DeviceManageBase::BATTERY_OUT_DOUBLE :
    {
        switch (mCurValue) {
        case BATTERY_PCT_25:
            painter.drawPixmap(ICON_RECT, mLowIcon, rect());
            break;
        case BATTERY_PCT_0:
        case BATTERY_PCT_50:
        case BATTERY_PCT_75:
        case BATTERY_PCT_100:
            painter.drawPixmap(ICON_RECT, *mBatteryIconMap[(BATTERY_LEVEL_ENUM)mCurValue], rect());
            break;
        default:
            break;
        }
        if(DeviceManageBase::BATTERY_OUT_DOUBLE == mBatteryState)
            numberPainter.drawText(TEXT_X_POS, FONT_HEIGHT, "2");
    }
        break;
    default:
        break;
    }
    QFrame::paintEvent(ev);
}



void BatteryIcon::resizeEvent(QResizeEvent *ev)
{
    mZeroIcon = QPixmap(ZERO_ICON).scaled(ev->size(),
                                          Qt::IgnoreAspectRatio,
                                          Qt::SmoothTransformation);

    mLowIcon = QPixmap(LOW_ICON).scaled(ev->size(),
                                        Qt::IgnoreAspectRatio,
                                        Qt::SmoothTransformation);

    m25PtIcon = QPixmap(NORMAL_LOW_ICON).scaled(ev->size(),
                                                Qt::IgnoreAspectRatio,
                                                Qt::SmoothTransformation);

    m50PtIcon = QPixmap(PT50_ICON).scaled(ev->size(),
                                          Qt::IgnoreAspectRatio,
                                          Qt::SmoothTransformation);

    m75PtIcon = QPixmap(PT75_ICON).scaled(ev->size(),
                                          Qt::IgnoreAspectRatio,
                                          Qt::SmoothTransformation);

    mFullIcon = QPixmap(FULL_ICON).scaled(ev->size(),
                                          Qt::IgnoreAspectRatio,
                                          Qt::SmoothTransformation);

    mInvalidIcon = QPixmap(INVALID_ICON).scaled(ev->size(),
                                                Qt::IgnoreAspectRatio,
                                                Qt::SmoothTransformation);

    mChargingIcon = QPixmap(CHARGING_ICON).scaled(ev->size(),
                                                  Qt::IgnoreAspectRatio,
                                                  Qt::SmoothTransformation);
}

void BatteryIcon::SetRange(int minValue, int maxValue)
{
    if(minValue >= maxValue)
    {
        DebugLog<<("mininum value is bigger than or equal to maxinum value");
        return;
    }
    mMaxValue = maxValue;
    mMinValue = minValue;
}

void BatteryIcon::SetVoltage(int value)
{
    if (DeviceManageBase::BATTERY_ERROR == mBatteryState)
    {
        return ;
    }

    if(value >= mMaxValue)
    {
        value = mMaxValue;
    }
    else if(value <= mMinValue)
    {
        value = mMinValue;
    }
    if(mCurValue!=value)
    {
        mCurValue = value;
        QMetaObject::invokeMethod(this, "update", Qt::QueuedConnection);
    }
}

int BatteryIcon::GetVoltage() const
{
    if (DeviceManageBase::BATTERY_ERROR == mBatteryState)
    {
        return -1;
    }
    return mCurValue;
}


void BatteryIcon::SetState(int batteryState)
{
    if (mBatteryState == batteryState)
    {
        return ;
    }
    mBatteryState = batteryState;
#ifdef OPEN_DEBUG_INFO
    DebugLog << "电池状态发生变化，修改为:" << batteryState;
#endif
    QMetaObject::invokeMethod(this, "update", Qt::QueuedConnection);
}



