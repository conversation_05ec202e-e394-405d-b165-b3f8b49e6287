﻿#include <QSvgWidget>

#include "CalibrationMenu.h"
#include "FlowZeroCalPage.h"
#include "O2CalPage.h"
#include "CO2CalPage.h"
#include "String/UIStrings.h"
#include "calibration/CalModuleManage.h"
#include "calibration/FlowZeroCalManage.h"
#include "calibration/o2CalManage.h"
#include "calibration/Co2CalManage.h"
#include "UiConfig.h"
#include "PushButton.h"
#include "SystemConfigManager.h"
#include "ModuleTestManager.h"
#include "LeakTestPage.h"
#include "SelftestInfoTable.h"
#include "String/UIStrings.h"
#include "AGModule.h"

#define WARNING_ICON ":/Item/Test.svg"
#define WARNING_ICON_SIZE QSize(100,100)
CalibrationDlg::CalibrationDlg(QWidget *parent) :
    FocusWidget(parent)
{
    InitUi();
    InitConnect();
}

void CalibrationDlg::InitUi()
{
    QVBoxLayout *Layout = new QVBoxLayout(this);
    mVerticalTabWidget = new VerticalTabWidget(this);
    Layout->addWidget(mVerticalTabWidget);
    Layout->setContentsMargins(0,0,0,0);
    DEBUG_RUNTIME(mFlowZeroCalPage = new FlowZeroCalPage);
    if (ConfigManager->GetConfig<bool>(SystemConfigManager::ENABLE_FLOWMETER_MODULE))
    {
        mVerticalTabWidget->addTab(mFlowZeroCalPage, UIStrings::GetStr(STR_FLOWMETER_ZERO_CAL));
    }

    DEBUG_RUNTIME(mO2CalPage = new O2CalPage);
    if (ConfigManager->GetConfig<bool>(SystemConfigManager::O2_BOOL_INDEX))
    {
        mVerticalTabWidget->addTab(mO2CalPage, UIStrings::GetStr(STR_MAINMENU_O2CAL));
    }

    DEBUG_RUNTIME(mCo2CalPage = new CO2CalPage);
    if (ModuleTestManager::GetInstance()->GetAgModuleType() != None)
    {
        if(ModuleTestManager::GetInstance()->GetAgModuleType() == AG)
        {
            mVerticalTabWidget->addTab(mCo2CalPage, UIStrings::GetStr(STR_AG_ZERO_CAL));
        }
        else if(ModuleTestManager::GetInstance()->GetAgModuleType() == CO2)
        {
            mVerticalTabWidget->addTab(mCo2CalPage, UIStrings::GetStr(STR_CO2_ZERO_CAL));
        }
    }

    DEBUG_RUNTIME(mManualTestMenu = new LeakTestPage(this));
    mVerticalTabWidget->addTab(mManualTestMenu, UIStrings::GetStr(STR_LEAK));

    DEBUG_RUNTIME(mSelftestInfoTablePage = new SelftestInfoTable);
    mVerticalTabWidget->addTab(mSelftestInfoTablePage, UIStrings::GetStr(STR_LATEST_SELFTEST));

    setLayout(Layout);
}

void CalibrationDlg::InitConnect()
{
    connect(mO2CalPage, &O2CalPage::SignalChangeCalState, this, [=](bool CurIsCal){
        emit SignalChangeCalState(CurIsCal);
    }, Qt::QueuedConnection);
    connect(mCo2CalPage, &CO2CalPage::SignalChangeCalState, this, [=](bool CurIsCal){
        emit SignalChangeCalState(CurIsCal);
    }, Qt::QueuedConnection);
    connect(mManualTestMenu, &LeakTestPage::SignalChangeCalState, this, [=](bool CurIsCal){
        emit SignalChangeCalState(CurIsCal);
    }, Qt::QueuedConnection);
    connect(mFlowZeroCalPage, &FlowZeroCalPage::SignalChangeCalState, this, [=](bool CurIsCal){
        emit SignalChangeCalState(CurIsCal);
    }, Qt::QueuedConnection);
}

void CalibrationDlg::InitUiText()
{
    if (mVerticalTabWidget->indexOf(mO2CalPage) != -1)
        mVerticalTabWidget->setTabText(mVerticalTabWidget->indexOf(mO2CalPage),
                                       UIStrings::GetStr(STR_MAINMENU_O2CAL));

    if (mVerticalTabWidget->indexOf(mFlowZeroCalPage) != -1)
        mVerticalTabWidget->setTabText(mVerticalTabWidget->indexOf(mFlowZeroCalPage),
                                               UIStrings::GetStr(STR_FLOWMETER_ZERO_CAL));

    if(mVerticalTabWidget->indexOf(mCo2CalPage) != -1)
    {
        if(ModuleTestManager::GetInstance()->GetAgModuleType() == AG)
        {
            mVerticalTabWidget->setTabText(mVerticalTabWidget->indexOf(mCo2CalPage), UIStrings::GetStr(STR_AG_ZERO_CAL));
        }
        else if(ModuleTestManager::GetInstance()->GetAgModuleType() == CO2)
        {
            mVerticalTabWidget->setTabText(mVerticalTabWidget->indexOf(mCo2CalPage), UIStrings::GetStr(STR_CO2_ZERO_CAL));
        }
    }

    mVerticalTabWidget->setTabText(mVerticalTabWidget->indexOf(mManualTestMenu),
                                           UIStrings::GetStr(ALL_STRINGS_ENUM::STR_LEAK));

    mVerticalTabWidget->setTabText(mVerticalTabWidget->indexOf(mSelftestInfoTablePage),
                                           UIStrings::GetStr(ALL_STRINGS_ENUM::STR_SYSTEM_SELFTEST));

    mFlowZeroCalPage->InitUiText();
    mO2CalPage->InitUiText();
    mCo2CalPage->InitUiText();
    mManualTestMenu->InitUiText();
    mSelftestInfoTablePage->InitUiText();
}


void CalibrationDlg::CheckO2Tab()
{
    //氧浓度校准页面
    //    if ((get_single_soft_config(O2_BOOL_INDEX) && (ModuleTestManager::GetInstance()->GetModuleState(TEST_O2) == ABLE)) && indexOf(mO2CalPage) == -1)
    //    if(ConfigManager->GetConfig<bool>(SystemConfigManager::O2_BOOL_INDEX))
    //    {
    //        mVerticalTabWidget->insertTab(1, mO2CalPage, UIStrings::GetStr(STR_MAINMENU_O2CAL));
    //        O2CalManage::GetInstance()->register_cal_ctrl(mO2CalPage);
    //    }
}
/* ****************************************************  */
CalibrationMenu::CalibrationMenu(QWidget *parent):
    FocusWidget(parent)
{
    setStyleSheet(GetQssFileStr("CalibrationMenu.qss"));
    InitUi();
    InitConnect();
}

void CalibrationMenu::InitUi()
{
    mCalibrationDlg = new CalibrationDlg(this);
    mCalibrationDlg->setObjectName("calibrationDlg");
    connect(mCalibrationDlg, SIGNAL(SignalChangeCalState(bool)), this, SLOT(setExitEnable(bool)));
    mCalibrationDlg->installEventFilter(this);

    mDisablePage = new QWidget;
    mDisableTextLabel = new QLabel(UIStrings::GetStr(STR_CALIB_USE_ONLY_STANBY));
    QSvgWidget *svg = new QSvgWidget(WARNING_ICON);
    svg->setFixedSize(WARNING_ICON_SIZE);
    QHBoxLayout *mDisablePageLayout = new QHBoxLayout;
    mDisablePageLayout->setContentsMargins(0, 0, 0, 0);
    mDisablePageLayout->addStretch();
    mDisablePageLayout->addWidget(svg);
    mDisablePageLayout->addWidget(mDisableTextLabel);
    mDisablePageLayout->addStretch();
    mDisablePage->setLayout(mDisablePageLayout);
    mDisablePage->setFocusPolicy(Qt::NoFocus);

    QVBoxLayout *mainLayout = new QVBoxLayout;
    mainLayout->setContentsMargins(0, 0, 0, 0);
    mainLayout->addWidget(mCalibrationDlg);
    mainLayout->addWidget(mDisablePage);

    setLayout(mainLayout);
}

void CalibrationMenu::InitConnect()
{
    connect(CalModuleManage::GetInstance(), &CalModuleManage::SignalCalEnableChanged, this, [this](bool enable){
        if (enable)
        {
            showCalPage();
        }
        else
        {
            showDisablePage();
        }
    });

    connect(mCalibrationDlg, &CalibrationDlg::SignalChangeCalState, this, [=](bool CurIsCal){
        emit SignalChangeCalState(CurIsCal);
    }, Qt::QueuedConnection);
}

//在校准正在进行时，隐藏退出按钮
void CalibrationMenu::setExitEnable(bool )
{
    //    if (calActive)
    //    {
    //        m_exitBtn->hide();
    //    }
    //    else
    //    {
    //        if (!FlowZeroCalManage::GetInstance()->GetCalState() &&
    //                !O2CalManage::GetInstance()->GetCalState()/*  &&
    //                                            !Co2CalManage::GetInstance()->GetCalState()  */  )
    //        {
    //            m_exitBtn->show();
    //        }
    //    }
}

void CalibrationMenu::showCalPage()
{
    mCalibrationDlg->show();
    mDisablePage->hide();
    
}

void CalibrationMenu::showDisablePage()
{
    mCalibrationDlg->hide();
    mDisablePage->show();
    
}

void CalibrationMenu::InitUiText()
{
    mDisableTextLabel->setText(UIStrings::GetStr(STR_CALIB_USE_ONLY_STANBY));
    mCalibrationDlg->InitUiText();
}

//进入校准页面时，检查校准是否可用
void CalibrationMenu::showEvent(QShowEvent *e)
{
    if (CalModuleManage::GetInstance()->IsCalEnable())
        showCalPage();
    else
        showDisablePage();
    mIsAutoInitFocusControl=true;
    FocusWidget::showEvent(e);
}
