﻿#ifndef _FLOWMETERMANAGER_H_
#define _FLOWMETERMANAGER_H_
#include <QObject>
#include <QMap>
#include <QFile>
#include <QTextStream>

#include "SettingSpinbox/SettingSpinbox.h"

class O2Data : public commonInnerData //能输0但不能输0.1
{
public:
    virtual VALUE_CHECK_TYPE checkValue(qreal val) override;
    virtual void SetRange(int minVal, int maxVal, bool isDumb = false) override;
    void SetZero(bool isZero);
    void SetTrueMin(double min);
private:
    double mTrueMin{2};
    bool mIsZero{true};
};


class FlowmeterView;
class FlowControlButton;

enum E_FLOWMETER_TYPE
{
    FLOWMETER_O2,
    FLOWMETER_AIR,
    FLOWMETER_N2O,
    FLOWMETER_MAX
};

typedef enum FLOWMETER_SCALE
{
    SMALL_SCALE_TYPE,
    //	MIDDLE_SCALE_TYPE,
    LARGE_SCALE_TYPE,
    SCALE_MAX
}FLOWMETER_SCALE_TYPE;

struct FLOWMETER_DISCRIPTOR
{
    unsigned short name_id;
    unsigned short pattern_type;	//0,bar;1,grid
    E_FLOWMETER_TYPE flowmeter_type;
};

enum CONTROL_TYPE
{
    TOTAL_CONTROL,
    DIRECT_FLOW,
};
enum BALANCE_GAS_TYPE
{
    NONE,
    AIR_BALANCE,
    N2O_BALANCE,
};
enum CONTROL_BUTTON_TYPE
{
    AIR,
    N2O,
    O2,
    TOTAL,
    O2PCT,
    MAX_BUTTON_CNT,
};
enum UPDATE_RULE
{
    NONE_RULE,
    RULE_1,             //超下限：(O2<100%)
    RULE_2,             //超上限：硬件流量限制(单管流量 > 12L/min)
    RULE_3,             //超上下限：医疗安全要求(笑气模式下氧气浓度不能低于25%)
    RULE_4,             //超上下限：氧气最小流量技术要求(O2 < 0.2L/min)
};

struct FLOW_LIMIT_INFO
{
    int low_limit;
    int high_limit;
};

struct FlowControlValueSt
{
    double totalValue;
    double o2Value;
    double balanceGasValue;
};

enum FLOWMETER_OPERATE_ENUM
{
    CONTROL_MODE_CHANGE,
    BALANCE_MODE_CHANGE,
    VALUE_CHANGE,
    Quick_Setting
};


//全电子流量计输入数据错误码
enum class DATA_VALIDATOR_ERROR_CODE : short;

class FlowmeterManager : public QObject
{
    Q_OBJECT
public:
    static FlowmeterManager * GetInstance(void);

    static void RefurbishValue(void *wParam, void *lParam);
    void RegisterFlometerWidget(FlowmeterView* wediget);
    void RegisterFlowControlButton(int type, SettingSpinbox* button);
    void RegisterFlowControlButton(int type, FlowControlButton* button);
    void ChangeFlowControlType(int type, bool IsRecord = true);
    bool ChangeFlowControlValue(int type,double value, bool IsRecord = true);
    void ChangeFlowBalanceType(int type, bool IsRecord = true);
    void RefurbishAllFlowControl();
    short RefurbishAllFlowmeter();
    void UpValueToFlowModel();
    void StopFlow();
    bool DataValidator(int type, double value);

signals:

protected:
    int FlowAlarmCheck();
    void AdjustRange(unsigned int paramType);
    void UpOutMaxTip(unsigned int paramType, int rule);
    void UpOutMinTip(unsigned int paramType, int rule);

private slots:
    void slotFlowControlValueChange(int value);
    void SlotStartEdit(bool state);
    void SlotValueChanging(int value);

    void SlotFlowButtonStartEdit();

private:
    FlowmeterManager();
    void SaveSettingData(double totalValue, double o2Value, double balanceValue);
    void CalValidValue(int valueType, int &value);
    bool AdjustPrecision(int valueType, double &value);

    DATA_VALIDATOR_ERROR_CODE DataValidator(int type, double value, int controlType, int balanceType, double totalValue, double o2Value, double balanceValue);

    FlowControlValueSt ProcessValueAfterControlTypeChangeToTotal(double totalValue, double o2Pct);

    FlowControlValueSt ProcessBalanceTypeChangeInDirectControl(int oldBalanceType, int newBalanceType);
    FlowControlValueSt ProcessBalanceTypeChangeInTotalControl(int oldBalanceType, int newBalanceType);
    bool InitValue();
private:
    FlowmeterView *mFlowmeterWidget{};

    QMap<int, SettingSpinbox*> mAllControlButton;
    QMap<int, FlowControlButton*> mAllFlowControlButton;
    short mTotalValue{0};
    QMap<E_FLOWMETER_TYPE, int> mMonitorValues;
    double mO2UserInputValue{100.0};
};

#endif
