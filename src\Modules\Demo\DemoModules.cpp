﻿#include "VentModeSettingManager.h"
#include "DataManager.h"
#include "DemoModules.h"
#include "AGModule.h"
#include "DataManager.h"
#include "VentModeSettingManager.h"
#include "ChartManager.h"
#include "RunModeManage.h"
#include "AlarmManager.h"
static unsigned int s_cur_mode = VENTMODE_FLAG_VCV;
static unsigned int s_cur_mode_index = 0;

//#define OPEN_DEMO_MODULES_DEBUG
#define UP_INTERVAL 20
#define TRIGGER_ALARM_FOREVER_FILE "ALARM_FOREVER_TRIGGER.txt"
static const QStringList DefaultDemoVCVData={"CO2:",
                                            "0,0,0,0,0,0,0,0,0,0,",
                                            "0,0,0,0,0,0,0,0,0,0,",
                                            "0,0,0,0,0,0,0,0,0,0,",
                                            "0,0,0,0,0,0,0,0,0,0,",
                                            "0,0,0,0,0,0,0,0,0,0,",
                                            "0,0,0,0,0,0,0,0,0,0,",
                                            "0,0,0,0,0,0,0,0,0,0,",
                                            "0,0,0,0,0,0,0,0,0,0,",
                                            "0,0,0,0,2,5,11,17,30,40,",
                                            "55,69,88,103,124,138,156,173,190,207,",
                                            "222,235,246,258,268,276,282,293,299,306,",
                                            "312,319,324,329,335,340,345,349,353,355,",
                                            "358,360,360,360,360,360,360,360,360,360,",
                                            "360,360,360,360,360,360,360,360,360,360,",
                                            "360,360,360,360,360,360,360,360,360,360,",
                                            "360,360,360,360,360,360,360,360,360,360,",
                                            "360,360,360,360,360,360,360,360,360,360,",
                                            "360,360,360,360,360,360,360,360,360,358,",
                                            "353,348,341,330,320,308,297,287,274,260,",
                                            "243,222,201,181,164,148,134,122,111,101,",
                                            "91,83,75,67,61,55,49,44,39,34,",
                                            "30,27,23,21,17,15,12,10,7,5,",
                                            "3,2,1,1,1,0,0,0,0,0,",
                                            "0,0,0,0,0,0,0,0,0,0,",
                                            "0,0,0,0,0,0,0,0,0,0;",
                                            
                                            "PAW:",
                                            "22,22,22,22,27,29,31,33,36,38,",
                                            "40,42,44,46,48,50,52,55,57,59,",
                                            "61,63,65,67,69,71,73,76,78,80,",
                                            "82,84,86,88,90,92,95,97,99,101,",
                                            "103,105,107,109,111,114,116,118,120,122,",
                                            "124,126,128,130,133,135,137,139,141,143,",
                                            "145,147,149,152,154,156,158,160,162,164,",
                                            "166,168,170,173,175,177,179,181,183,185,",
                                            "187,189,192,194,196,198,200,152,118,84,",
                                            "70,60,57,53,51,49,47,45,43,41,",
                                            "39,38,37,36,35,34,33,32,31,30,",
                                            "29,28,27,26,25,24,23,23,22,22,",
                                            "22,22,22,22,22,22,22,22,22,22,",
                                            "22,22,22,22,22,22,22,22,22,22,",
                                            "22,22,22,22,22,22,22,22,22,22,",
                                            "22,22,22,22,22,22,22,22,22,22,",
                                            "22,22,22,22,22,22,22,22,22,22,",
                                            "22,22,22,22,22,22,22,22,22,22,",
                                            "22,22,22,22,22,22,22,22,22,22,",
                                            "22,22,22,22,22,22,22,22,22,22,",
                                            "22,22,22,22,22,22,22,22,22,22,",
                                            "22,22,22,22,22,22,22,22,22,22,",
                                            "22,22,22,22,22,22,22,22,22,22,",
                                            "22,22,22,22,22,22,22,22,22,22,",
                                            "22,22,22,22,22,22,22,22,22,22;",
                                            
                                            "FLOW:",
                                            "4,6,4,20,500,900,1200,1500,1659,1800,",
                                            "1800,1800,1800,1800,1800,1800,1800,1800,1800,1800,",
                                            "1800,1800,1800,1800,1800,1800,1800,1800,1800,1800,",
                                            "1800,1800,1800,1800,1800,1800,1800,1800,1800,1800,",
                                            "1800,1800,1800,1800,1800,1800,1800,1800,1800,1800,",
                                            "1800,1800,1800,1800,1800,1800,1800,1800,1800,1800,",
                                            "1800,1800,1800,1800,1800,1800,1800,1800,1800,1800,",
                                            "1800,1800,1800,1800,1800,1800,1800,1800,1800,1800,",
                                            "1800,1800,1800,1800,1800,1800,-816,-5738,-8023,-7677,",
                                            "-7186,-6800,-6500,-6300,-6000,-5800,-5600,-5440,-5200,-5000,",
                                            "-4800,-4600,-4430,-4200,-4000,-3900,-3700,-3500,-3300,-3100,",
                                            "-2900,-2700,-2500,-2300,-2100,-2000,-1800,-1700,-1500,-1300,",
                                            "-1200,-1000,-900,-800,-600,-400,-300,-160,-100,-70,",
                                            "-50,-37,-19,-12,-14,-23,-27,4,-14,-22,",
                                            "-16,-7,-3,13,-13,-17,-6,-15,-15,-3,",
                                            "-11,-4,4,-8,-10,1,-1,-4,-2,-2,",
                                            "-3,1,1,7,3,-6,-16,-8,8,9,",
                                            "14,6,-12,-13,8,6,11,9,-7,-12,",
                                            "-3,4,14,9,9,-6,-6,8,4,9,",
                                            "9,4,-5,-9,11,9,9,9,1,-5,",
                                            "-6,8,4,4,4,6,6,6,1,-12,",
                                            "-13,5,6,11,9,9,9,14,14,9,",
                                            "9,9,-9,-11,-7,0,4,4,9,9,",
                                            "9,9,14,14,4,0,-7,-10,-6,0,",
                                            "1,4,4,4,4,4,4,4,4,4;",
                                            
                                            "VOLUME:",
                                            "0,0,0,0,0,0,0,6,13,19,",
                                            "25,31,38,44,50,56,63,69,75,81,",
                                            "88,94,100,106,113,119,125,131,138,144,",
                                            "150,156,163,169,175,181,188,194,200,206,",
                                            "213,219,225,231,238,244,250,256,263,269,",
                                            "275,281,288,294,300,306,313,319,325,331,",
                                            "338,344,350,356,363,369,375,381,388,394,",
                                            "400,406,413,419,425,431,438,444,450,456,",
                                            "463,469,475,481,488,494,500,486,469,451,",
                                            "433,414,392,371,350,330,311,292,274,257,",
                                            "240,225,210,195,181,168,156,144,133,122,",
                                            "112,103,94,87,79,73,66,59,53,47,",
                                            "42,36,30,25,20,15,12,8,5,2,",
                                            "0,0,0,0,0,0,0,0,0,0,",
                                            "0,0,0,0,0,0,0,0,0,0,",
                                            "0,0,0,0,0,0,0,0,0,0,",
                                            "0,0,0,0,0,0,0,0,0,0,",
                                            "0,0,0,0,0,0,0,0,0,0,",
                                            "0,0,0,0,0,0,0,0,0,0,",
                                            "0,0,0,0,0,0,0,0,0,0,",
                                            "0,0,0,0,0,0,0,0,0,0,",
                                            "0,0,0,0,0,0,0,0,0,0,",
                                            "0,0,0,0,0,0,0,0,0,0,",
                                            "0,0,0,0,0,0,0,0,0,0,",
                                            "0,0,0,0,0,0,0,0,0,0;",
                                            
                                            "AG_TYPE:4,4,4;",
                                            "VALUEDATA_VTE:500,500,500;",
                                            "VALUEDATA_VTI:500,500,500;",
                                            "VALUEDATA_MV:600,600,600;",
                                            "VALUEDATA_RATE:12,12,12;",
                                            "VALUEDATA_R:20,20,20;",
                                            "VALUEDATA_C:20,20,20;",
                                            "VALUEDATA_C20C:100,100,100;",
                                            "VALUEDATA_FSPN:0,0,0;",
                                            "VALUEDATA_MVSPN:0,0,0;",
                                            "VALUEDATA_IE:110,110,110;",
                                            "VALUEDATA_PPEAK:20,20,20;",
                                            "VALUEDATA_TOP_PAW:40,40,40;",
                                            "VALUEDATA_PPLAT:20,20,20;",
                                            "VALUEDATA_PEEP:0,0,0;",
                                            "VALUEDATA_PMEAN:9,9,9;",
                                            "VALUEDATA_FIO2:45,45,45;",
                                            "VALUEDATA_FICO2:0,0,0;",
                                            "VALUEDATA_ETCO2:36,36,36;",
                                            "VALUEDATA_FIN2O:0,0,0;",
                                            "VALUEDATA_ETN2O:0,0,0;",
                                            "VALUEDATA_FI_AA1:-99999,-99999,-99999;",
                                            "VALUEDATA_FI_AA2:-99999,-99999,-99999;",
                                            "VALUEDATA_FIHAL:3,3,3;",
                                            "VALUEDATA_FIENF:3,3,3;",
                                            "VALUEDATA_FISEV:3,3,3;",
                                            "VALUEDATA_FIISO:3,3,3;",
                                            "VALUEDATA_FIDES:3,3,3;",
                                            "VALUEDATA_ET_AA1:-99999,-99999,-99999;",
                                            "VALUEDATA_ET_AA2:-99999,-99999,-99999;",
                                            "VALUEDATA_ETHAL:2,2,2;",
                                            "VALUEDATA_ETENF:2,2,2;",
                                            "VALUEDATA_ETSEV:2,2,2;",
                                            "VALUEDATA_ETISO:2,2,2;",
                                            "VALUEDATA_ETDES:2,2,2;",
                                            "VALUEDATA_MAC:20,20,20;",
                                            "VALUEDATA_SPO2:99,99,99;",
                                            "VALUEDATA_PR:70,70,70;",
                                            "VALUEDATA_FLOWMETER_O2:200,200,200;",
                                            "VALUEDATA_FLOWMETER_AIR:0,0,0;",
                                            "VALUEDATA_FLOWMETER_N2O:0,0,0;"};

static const QStringList DefaultDemoHLMData={
                                           "CO2:",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,2,5,11,17,",
                                           "30,40,55,69,88,103,124,138,156,173,",
                                           "190,207,222,235,246,258,268,276,282,293,",
                                           "299,306,312,319,324,329,334,337,338,340,",
                                           "340,342,342,343,345,346,347,348,349,349,",
                                           "350,351,352,353,353,353,353,354,354,354,",
                                           "354,354,355,356,356,356,356,356,356,357,",
                                           "357,357,357,358,358,358,358,358,359,359,",
                                           "359,360,360,360,360,360,360,360,360,360,",
                                           "360,360,360,360,360,360,360,360,360,360,",
                                           "360,358,353,348,341,330,320,308,297,287,",
                                           "274,260,243,222,201,181,164,148,134,122,",
                                           "111,101,91,83,75,67,61,55,49,44,",
                                           "39,34,30,27,23,21,17,15,12,10,",
                                           "7,5,3,2,1,1,1,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0;",

                                           "PAW:",
                                           "60,60,60,60,60,60,60,60,60,60,",
                                           "60,60,60,60,60,60,60,60,60,60,",
                                           "60,60,60,60,60,60,60,60,60,60,",
                                           "60,60,60,60,60,60,60,60,60,60,",
                                           "60,60,60,60,60,60,60,60,60,60,",
                                           "60,60,60,60,60,60,60,60,60,60,",
                                           "60,60,60,60,60,60,60,60,60,60,",
                                           "60,60,60,60,60,60,60,60,60,60,",
                                           "60,60,60,60,60,60,60,60,60,60,",
                                           "60,60,60,60,60,60,60,60,60,60,",
                                           "60,60,60,60,60,60,60,60,60,60,",
                                           "60,60,60,60,60,60,60,60,60,60,",
                                           "60,60,60,60,60,60,60,60,60,60,",
                                           "60,60,60,60,60,60,60,60,60,60,",
                                           "60,60,60,60,60,60,60,60,60,60,",
                                           "60,60,60,60,60,60,60,60,60,60,",
                                           "60,60,60,60,60,60,60,60,60,60,",
                                           "60,60,60,60,60,60,60,60,60,60,",
                                           "60,60,60,60,60,60,60,60,60,60,",
                                           "60,60,60,60,60,60,60,60,60,60,",
                                           "60,60,60,60,60,60,60,60,60,60,",
                                           "60,60,60,60,60,60,60,60,60,60,",
                                           "60,60,60,60,60,60,60,60,60,60,",
                                           "60,60,60,60,60,60,60,60,60,60,",
                                           "60,60,60,60,60,60,60,60,60,60;",

                                           "FLOW:",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0;",

                                           "VOLUME:",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0;",

                                           "AG_TYPE:4,4,4;",
                                           "VALUEDATA_VTE:0,0,0;",
                                           "VALUEDATA_VTI:0,0,0;",
                                           "VALUEDATA_MV:0,0,0;",
                                           "VALUEDATA_RATE:0,0,0;",
                                           "VALUEDATA_R:0,0,0;",
                                           "VALUEDATA_C:0,0,0;",
                                           "VALUEDATA_C20C:100,100,100;",
                                           "VALUEDATA_FSPN:0,0,0;",
                                           "VALUEDATA_MVSPN:0,0,0;",
                                           "VALUEDATA_IE:0,0,0;",
                                           "VALUEDATA_PPEAK:6,6,6;",
                                           "VALUEDATA_TOP_PAW:40,40,40;",
                                           "VALUEDATA_PPLAT:6,6,6;",
                                           "VALUEDATA_PEEP:2,2,2;",
                                           "VALUEDATA_PMEAN:6,6,6;",
                                           "VALUEDATA_FIO2:21,21,21;",
                                           "VALUEDATA_FICO2:0,0,0;",
                                           "VALUEDATA_ETCO2:36,36,36;",
                                           "VALUEDATA_FIN2O:0,0,0;",
                                           "VALUEDATA_ETN2O:0,0,0;",
                                           "VALUEDATA_FI_AA1:-99999,-99999,-99999;",
                                           "VALUEDATA_FI_AA2:-99999,-99999,-99999;",
                                           "VALUEDATA_FIHAL:3,3,3;",
                                           "VALUEDATA_FIENF:3,3,3;",
                                           "VALUEDATA_FISEV:3,3,3;",
                                           "VALUEDATA_FIISO:3,3,3;",
                                           "VALUEDATA_FIDES:3,3,3;",
                                           "VALUEDATA_ET_AA1:-99999,-99999,-99999;",
                                           "VALUEDATA_ET_AA2:-99999,-99999,-99999;",
                                           "VALUEDATA_ETHAL:2,2,2;",
                                           "VALUEDATA_ETENF:2,2,2;",
                                           "VALUEDATA_ETSEV:2,2,2;",
                                           "VALUEDATA_ETISO:2,2,2;",
                                           "VALUEDATA_ETDES:2,2,2;",
                                           "VALUEDATA_MAC:20,20,20;",
                                           "VALUEDATA_SPO2:99,98,98;",
                                           "VALUEDATA_PR:70,70,70;",
                                           "VALUEDATA_FLOWMETER_O2:200,200,200;",
                                           "VALUEDATA_FLOWMETER_AIR:0,0,0;",
                                           "VALUEDATA_FLOWMETER_N2O:0,0,0;"};

static const QStringList DefaultDemoPmodeData={
                                              "CO2:",
                                              "0,0,0,0,0,0,0,0,0,0,",
                                              "0,0,0,0,0,0,0,0,0,0,",
                                              "0,0,0,0,0,0,0,0,0,0,",
                                              "0,0,0,0,0,0,0,0,0,0,",
                                              "0,0,0,0,0,0,0,0,0,0,",
                                              "0,0,0,0,0,0,0,0,0,0,",
                                              "0,0,0,0,0,0,0,0,0,0,",
                                              "0,0,0,0,0,0,0,0,0,0,",
                                              "0,0,0,0,2,5,11,17,30,40,",
                                              "55,69,88,103,124,138,156,173,190,207,",
                                              "222,235,246,258,268,276,282,293,299,306,",
                                              "312,319,324,329,335,340,345,349,353,355,",
                                              "358,360,360,360,360,360,360,360,360,360,",
                                              "360,360,360,360,360,360,360,360,360,360,",
                                              "360,360,360,360,360,360,360,360,360,360,",
                                              "360,360,360,360,360,360,360,360,360,360,",
                                              "360,360,360,360,360,360,360,360,360,360,",
                                              "360,360,360,360,360,360,360,360,360,358,",
                                              "353,348,341,330,320,308,297,287,274,260,",
                                              "243,222,201,181,164,148,134,122,111,101,",
                                              "91,83,75,67,61,55,49,44,39,34,",
                                              "30,27,23,21,17,15,12,10,7,5,",
                                              "3,2,1,1,1,0,0,0,0,0,",
                                              "0,0,0,0,0,0,0,0,0,0,",
                                              "0,0,0,0,0,0,0,0,0,0;",

                                              "PAW:",
                                              "22,22,22,27,33,36,38,42,49,56,",
                                              "61,67,72,78,83,88,93,98,102,106,",
                                              "111,114,118,121,124,126,129,132,133,135,",
                                              "138,140,141,142,144,145,146,147,147,148,",
                                              "149,149,150,150,150,150,150,150,150,150,",
                                              "150,150,150,150,150,150,150,150,150,150,",
                                              "150,150,150,150,150,150,150,150,150,150,",
                                              "150,150,150,150,150,150,150,150,150,150,",
                                              "150,150,150,150,150,145,140,129,120,111,",
                                              "103,95,87,79,72,66,60,55,50,44,",
                                              "41,37,34,31,29,27,24,24,22,22,",
                                              "22,22,22,22,22,22,22,22,22,22,",
                                              "22,22,22,22,22,22,22,22,22,22,",
                                              "22,22,22,22,22,22,22,22,22,22,",
                                              "22,22,22,22,22,22,22,22,22,22,",
                                              "22,22,22,22,22,22,22,22,22,22,",
                                              "22,22,22,22,22,22,22,22,22,22,",
                                              "22,22,22,22,22,22,22,22,22,22,",
                                              "22,22,22,22,22,22,22,22,22,22,",
                                              "22,22,22,22,22,22,22,22,22,22,",
                                              "22,22,22,22,22,22,22,22,22,22,",
                                              "22,22,22,22,22,22,22,22,22,22,",
                                              "22,22,22,22,22,22,22,22,22,22,",
                                              "22,22,22,22,22,22,22,22,22,22,",
                                              "22,22,22,22,22,22,22,22,22,22;",

                                              "FLOW:",
                                              "0,0,346,1972,6000,7456,7800,7700,7650,7550,",
                                              "7400,7250,7100,6900,6700,6500,6300,6100,5900,5700,",
                                              "5500,5300,5100,4800,4583,4300,4000,3800,3500,3300,",
                                              "3100,2800,2600,2300,2108,1900,1700,1550,1300,1150,",
                                              "1000,800,700,600,550,500,490,400,300,280,",
                                              "200,120,100,0,0,0,0,0,0,0,",
                                              "0,0,0,0,0,0,0,0,0,0,",
                                              "0,0,0,0,0,0,0,0,0,0,",
                                              "0,0,0,0,0,-1595,-7219,-12062,-12188,-11476,",
                                              "-10928,-10396,-9939,-9503,-8818,-8153,-7673,-7146,-6574,-6028,",
                                              "-5563,-4989,-4462,-4111,-3708,-3267,-2913,-2800,-2013,-1505,",
                                              "-1039,-700,-400,-141,-39,0,0,0,0,0,",
                                              "0,0,0,0,0,0,0,0,0,0,",
                                              "0,0,0,0,0,0,0,0,0,0,",
                                              "0,0,0,0,0,0,0,0,0,0,",
                                              "0,0,0,0,0,0,0,0,0,0,",
                                              "0,0,0,0,0,0,0,0,0,0,",
                                              "0,0,0,0,0,0,0,0,0,0,",
                                              "0,0,0,0,0,0,0,0,0,0,",
                                              "0,0,0,0,0,0,0,0,0,0,",
                                              "0,0,0,0,0,0,0,0,0,0,",
                                              "0,0,0,0,0,0,0,0,0,0,",
                                              "0,0,0,0,0,0,0,0,0,0,",
                                              "0,0,0,0,0,0,0,0,0,0,",
                                              "0,0,0,0,0,0,0,0,0,0;",

                                              "VOLUME:",
                                              "0,0,0,5,28,41,55,75,110,143,",
                                              "167,192,215,239,263,285,308,330,351,372,",
                                              "391,410,427,444,459,474,487,500,512,522,",
                                              "532,541,550,558,565,571,577,583,588,592,",
                                              "596,597,599,600,601,602,603,604,605,606,",
                                              "607,608,609,610,610,610,610,610,610,610,",
                                              "610,610,610,610,610,610,610,610,610,610,",
                                              "610,610,610,610,610,610,610,610,610,610,",
                                              "610,610,610,610,610,608,590,539,498,460,",
                                              "423,388,354,322,292,264,238,214,192,171,",
                                              "152,135,120,106,93,82,72,70,49,40,",
                                              "30,23,16,10,5,0,0,0,0,0,",
                                              "0,0,0,0,0,0,0,0,0,0,",
                                              "0,0,0,0,0,0,0,0,0,0,",
                                              "0,0,0,0,0,0,0,0,0,0,",
                                              "0,0,0,0,0,0,0,0,0,0,",
                                              "0,0,0,0,0,0,0,0,0,0,",
                                              "0,0,0,0,0,0,0,0,0,0,",
                                              "0,0,0,0,0,0,0,0,0,0,",
                                              "0,0,0,0,0,0,0,0,0,0,",
                                              "0,0,0,0,0,0,0,0,0,0,",
                                              "0,0,0,0,0,0,0,0,0,0,",
                                              "0,0,0,0,0,0,0,0,0,0,",
                                              "0,0,0,0,0,0,0,0,0,0,",
                                              "0,0,0,0,0,0,0,0,0,0;",

                                              "AG_TYPE:4,4,4;",
                                              "VALUEDATA_VTE:610,610,610;",
                                              "VALUEDATA_VTI:610,610,610;",
                                              "VALUEDATA_MV:732,732,732;",
                                              "VALUEDATA_RATE:12,12,12;",
                                              "VALUEDATA_R:20,20,20;",
                                              "VALUEDATA_C:20,20,20;",
                                              "VALUEDATA_C20C:100,100,100;",
                                              "VALUEDATA_FSPN:0,0,0;",
                                              "VALUEDATA_MVSPN:0,0,0;",
                                              "VALUEDATA_IE:110,110,110;",
                                              "VALUEDATA_PPEAK:15,15,15;",
                                              "VALUEDATA_TOP_PAW:40,40,40;",
                                              "VALUEDATA_PPLAT:15,15,15;",
                                              "VALUEDATA_PEEP:2,2,2;",
                                              "VALUEDATA_PMEAN:9,9,9;",
                                              "VALUEDATA_FIO2:45,45,45;",
                                              "VALUEDATA_FICO2:0,0,0;",
                                              "VALUEDATA_ETCO2:36,36,36;",
                                              "VALUEDATA_FIN2O:0,0,0;",
                                              "VALUEDATA_ETN2O:0,0,0;",
                                              "VALUEDATA_FI_AA1:-99999,-99999,-99999;",
                                              "VALUEDATA_FI_AA2:-99999,-99999,-99999;",
                                              "VALUEDATA_FIHAL:3,3,3;",
                                              "VALUEDATA_FIENF:3,3,3;",
                                              "VALUEDATA_FISEV:3,3,3;",
                                              "VALUEDATA_FIISO:3,3,3;",
                                              "VALUEDATA_FIDES:3,3,3;",
                                              "VALUEDATA_ET_AA1:-99999,-99999,-99999;",
                                              "VALUEDATA_ET_AA2:-99999,-99999,-99999;",
                                              "VALUEDATA_ETHAL:2,2,2;",
                                              "VALUEDATA_ETENF:2,2,2;",
                                              "VALUEDATA_ETSEV:2,2,2;",
                                              "VALUEDATA_ETISO:2,2,2;",
                                              "VALUEDATA_ETDES:2,2,2;",
                                              "VALUEDATA_MAC:20,20,20;",
                                              "VALUEDATA_SPO2:99,98,98;",
                                              "VALUEDATA_PR:70,70,70;",
                                              "VALUEDATA_FLOWMETER_O2:200,200,200;",
                                              "VALUEDATA_FLOWMETER_AIR:0,0,0;",
                                              "VALUEDATA_FLOWMETER_N2O:0,0,0;"};

static const QStringList DefaultDemoPCVData={
                                           "CO2:",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,2,5,11,17,30,40,",
                                           "55,69,88,103,124,138,156,173,190,207,",
                                           "222,235,246,258,268,276,282,293,299,306,",
                                           "312,319,324,329,335,340,345,349,353,355,",
                                           "358,360,360,360,360,360,360,360,360,360,",
                                           "360,360,360,360,360,360,360,360,360,360,",
                                           "360,360,360,360,360,360,360,360,360,360,",
                                           "360,360,360,360,360,360,360,360,360,360,",
                                           "360,360,360,360,360,360,360,360,360,360,",
                                           "360,360,360,360,360,360,360,360,360,358,",
                                           "353,348,341,330,320,308,297,287,274,260,",
                                           "243,222,201,181,164,148,134,122,111,101,",
                                           "91,83,75,67,61,55,49,44,39,34,",
                                           "30,27,23,21,17,15,12,10,7,5,",
                                           "3,2,1,1,1,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0;",

                                           "PAW:",
                                           "22,22,22,27,33,36,38,42,49,56,",
                                           "61,67,72,78,83,88,93,98,102,106,",
                                           "111,114,118,121,124,126,129,132,133,135,",
                                           "138,140,141,142,144,145,146,147,147,148,",
                                           "149,149,150,150,150,150,150,150,150,150,",
                                           "150,150,150,150,150,150,150,150,150,150,",
                                           "150,150,150,150,150,150,150,150,150,150,",
                                           "150,150,150,150,150,150,150,150,150,150,",
                                           "150,150,150,150,150,145,140,129,120,111,",
                                           "103,95,87,79,72,66,60,55,50,44,",
                                           "41,37,34,31,29,27,24,24,22,22,",
                                           "22,22,22,22,22,22,22,22,22,22,",
                                           "22,22,22,22,22,22,22,22,22,22,",
                                           "22,22,22,22,22,22,22,22,22,22,",
                                           "22,22,22,22,22,22,22,22,22,22,",
                                           "22,22,22,22,22,22,22,22,22,22,",
                                           "22,22,22,22,22,22,22,22,22,22,",
                                           "22,22,22,22,22,22,22,22,22,22,",
                                           "22,22,22,22,22,22,22,22,22,22,",
                                           "22,22,22,22,22,22,22,22,22,22,",
                                           "22,22,22,22,22,22,22,22,22,22,",
                                           "22,22,22,22,22,22,22,22,22,22,",
                                           "22,22,22,22,22,22,22,22,22,22,",
                                           "22,22,22,22,22,22,22,22,22,22,",
                                           "22,22,22,22,22,22,22,22,22,22;",

                                           "FLOW:",
                                           "0,0,346,1972,6000,7456,7800,7700,7650,7550,",
                                           "7400,7250,7100,6900,6700,6500,6300,6100,5900,5700,",
                                           "5500,5300,5100,4800,4583,4300,4000,3800,3500,3300,",
                                           "3100,2800,2600,2300,2108,1900,1700,1550,1300,1150,",
                                           "1000,800,700,600,550,500,490,400,300,280,",
                                           "200,120,100,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,-1595,-7219,-12062,-12188,-11476,",
                                           "-10928,-10396,-9939,-9503,-8818,-8153,-7673,-7146,-6574,-6028,",
                                           "-5563,-4989,-4462,-4111,-3708,-3267,-2913,-2800,-2013,-1505,",
                                           "-1039,-700,-400,-141,-39,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0;",

                                           "VOLUME:",
                                           "0,0,0,5,28,41,55,75,110,143,",
                                           "167,192,215,239,263,285,308,330,351,372,",
                                           "391,410,427,444,459,474,487,500,512,522,",
                                           "532,541,550,558,565,571,577,583,588,592,",
                                           "596,597,599,600,601,602,603,604,605,606,",
                                           "607,608,609,610,610,610,610,610,610,610,",
                                           "610,610,610,610,610,610,610,610,610,610,",
                                           "610,610,610,610,610,610,610,610,610,610,",
                                           "610,610,610,610,610,608,590,539,498,460,",
                                           "423,388,354,322,292,264,238,214,192,171,",
                                           "152,135,120,106,93,82,72,70,49,40,",
                                           "30,23,16,10,5,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0;",

                                           "AG_TYPE:4,4,4;",
                                           "VALUEDATA_VTE:610,610,610;",
                                           "VALUEDATA_VTI:610,610,610;",
                                           "VALUEDATA_MV:732,732,732;",
                                           "VALUEDATA_RATE:12,12,12;",
                                           "VALUEDATA_R:20,20,20;",
                                           "VALUEDATA_C:20,20,20;",
                                           "VALUEDATA_C20C:100,100,100;",
                                           "VALUEDATA_FSPN:0,0,0;",
                                           "VALUEDATA_MVSPN:0,0,0;",
                                           "VALUEDATA_IE:110,110,110;",
                                           "VALUEDATA_PPEAK:15,15,15;",
                                           "VALUEDATA_TOP_PAW:40,40,40;",
                                           "VALUEDATA_PPLAT:15,15,15;",
                                           "VALUEDATA_PEEP:2,2,2;",
                                           "VALUEDATA_PMEAN:9,9,9;",
                                           "VALUEDATA_FIO2:45,45,45;",
                                           "VALUEDATA_FICO2:0,0,0;",
                                           "VALUEDATA_ETCO2:36,36,36;",
                                           "VALUEDATA_FIN2O:0,0,0;",
                                           "VALUEDATA_ETN2O:0,0,0;",
                                           "VALUEDATA_FI_AA1:-99999,-99999,-99999;",
                                           "VALUEDATA_FI_AA2:-99999,-99999,-99999;",
                                           "VALUEDATA_FIHAL:3,3,3;",
                                           "VALUEDATA_FIENF:3,3,3;",
                                           "VALUEDATA_FISEV:3,3,3;",
                                           "VALUEDATA_FIISO:3,3,3;",
                                           "VALUEDATA_FIDES:3,3,3;",
                                           "VALUEDATA_ET_AA1:-99999,-99999,-99999;",
                                           "VALUEDATA_ET_AA2:-99999,-99999,-99999;",
                                           "VALUEDATA_ETHAL:2,2,2;",
                                           "VALUEDATA_ETENF:2,2,2;",
                                           "VALUEDATA_ETSEV:2,2,2;",
                                           "VALUEDATA_ETISO:2,2,2;",
                                           "VALUEDATA_ETDES:2,2,2;",
                                           "VALUEDATA_MAC:20,20,20;",
                                           "VALUEDATA_SPO2:99,98,98;",
                                           "VALUEDATA_PR:70,70,70;",
                                           "VALUEDATA_FLOWMETER_O2:200,200,200;",
                                           "VALUEDATA_FLOWMETER_AIR:0,0,0;",
                                           "VALUEDATA_FLOWMETER_N2O:0,0,0;"};

static const QStringList DefaultDemoPRVCData={
                                            "CO2:",
                                            "0,0,0,0,0,0,0,0,0,0,",
                                            "0,0,0,0,0,0,0,0,0,0,",
                                            "0,0,0,0,0,0,0,0,0,0,",
                                            "0,0,0,0,0,0,0,0,0,0,",
                                            "0,0,0,0,0,0,0,0,0,0,",
                                            "0,0,0,0,0,0,0,0,0,0,",
                                            "0,0,0,0,0,0,0,0,0,0,",
                                            "0,0,0,0,0,0,0,0,0,0,",
                                            "0,0,0,0,2,5,11,17,30,40,",
                                            "55,69,88,103,124,138,156,173,190,207,",
                                            "222,235,246,258,268,276,282,293,299,306,",
                                            "312,319,324,329,335,340,345,349,353,355,",
                                            "358,360,360,360,360,360,360,360,360,360,",
                                            "360,360,360,360,360,360,360,360,360,360,",
                                            "360,360,360,360,360,360,360,360,360,360,",
                                            "360,360,360,360,360,360,360,360,360,360,",
                                            "360,360,360,360,360,360,360,360,360,360,",
                                            "360,360,360,360,360,360,360,360,360,358,",
                                            "353,348,341,330,320,308,297,287,274,260,",
                                            "243,222,201,181,164,148,134,122,111,101,",
                                            "91,83,75,67,61,55,49,44,39,34,",
                                            "30,27,23,21,17,15,12,10,7,5,",
                                            "3,2,1,1,1,0,0,0,0,0,",
                                            "0,0,0,0,0,0,0,0,0,0,",
                                            "0,0,0,0,0,0,0,0,0,0;",

                                            "PAW:",
                                            "22,22,22,27,33,36,38,42,49,56,",
                                            "61,67,72,78,83,88,93,98,102,106,",
                                            "111,114,118,121,124,126,129,132,133,135,",
                                            "138,140,141,142,144,145,146,147,147,148,",
                                            "149,149,150,150,150,150,150,150,150,150,",
                                            "150,150,150,150,150,150,150,150,150,150,",
                                            "150,150,150,150,150,150,150,150,150,150,",
                                            "150,150,150,150,150,150,150,150,150,150,",
                                            "150,150,150,150,150,145,140,129,120,111,",
                                            "103,95,87,79,72,66,60,55,50,44,",
                                            "41,37,34,31,29,27,24,24,22,22,",
                                            "22,22,22,22,22,22,22,22,22,22,",
                                            "22,22,22,22,22,22,22,22,22,22,",
                                            "22,22,22,22,22,22,22,22,22,22,",
                                            "22,22,22,22,22,22,22,22,22,22,",
                                            "22,22,22,22,22,22,22,22,22,22,",
                                            "22,22,22,22,22,22,22,22,22,22,",
                                            "22,22,22,22,22,22,22,22,22,22,",
                                            "22,22,22,22,22,22,22,22,22,22,",
                                            "22,22,22,22,22,22,22,22,22,22,",
                                            "22,22,22,22,22,22,22,22,22,22,",
                                            "22,22,22,22,22,22,22,22,22,22,",
                                            "22,22,22,22,22,22,22,22,22,22,",
                                            "22,22,22,22,22,22,22,22,22,22,",
                                            "22,22,22,22,22,22,22,22,22,22;",

                                            "FLOW:",
                                            "0,0,284,1616,4918,6111,6393,6311,6270,6189,",
                                            "6066,5943,5820,5656,5492,5328,5164,5000,4836,4672,",
                                            "4508,4344,4180,3934,3757,3525,3279,3115,2869,2705,",
                                            "2541,2295,2131,1885,1728,1557,1393,1270,1066,943,",
                                            "820,656,574,492,451,410,402,328,246,230,",
                                            "164,98,82,0,0,0,0,0,0,0,",
                                            "0,0,0,0,0,0,0,0,0,0,",
                                            "0,0,0,0,0,0,0,0,0,0,",
                                            "0,0,0,0,0,-1307,-5917,-9887,-9990,-9407,",
                                            "-8957,-8521,-8147,-7789,-7228,-6683,-6289,-5857,-5389,-4941,",
                                            "-4560,-4089,-3657,-3370,-3039,-2678,-2388,-2087,-1650,-1234,",
                                            "-852,-462,-234,-116,-32,0,0,0,0,0,",
                                            "0,0,0,0,0,0,0,0,0,0,",
                                            "0,0,0,0,0,0,0,0,0,0,",
                                            "0,0,0,0,0,0,0,0,0,0,",
                                            "0,0,0,0,0,0,0,0,0,0,",
                                            "0,0,0,0,0,0,0,0,0,0,",
                                            "0,0,0,0,0,0,0,0,0,0,",
                                            "0,0,0,0,0,0,0,0,0,0,",
                                            "0,0,0,0,0,0,0,0,0,0,",
                                            "0,0,0,0,0,0,0,0,0,0,",
                                            "0,0,0,0,0,0,0,0,0,0,",
                                            "0,0,0,0,0,0,0,0,0,0,",
                                            "0,0,0,0,0,0,0,0,0,0,",
                                            "0,0,0,0,0,0,0,0,0,0;",

                                            "VOLUME:",
                                            "0,0,0,4,23,34,45,61,90,117,",
                                            "137,157,176,196,216,234,252,270,288,305,",
                                            "320,336,350,364,376,389,399,410,420,428,",
                                            "436,443,451,457,463,468,473,478,482,485,",
                                            "489,489,491,492,493,493,494,495,496,497,",
                                            "498,498,499,500,500,500,500,500,500,500,",
                                            "500,500,500,500,500,500,500,500,500,500,",
                                            "500,500,500,500,500,500,500,500,500,500,",
                                            "500,500,500,500,500,498,475,442,408,377,",
                                            "347,318,290,264,239,216,195,175,157,140,",
                                            "125,111,98,87,76,67,59,49,40,33,",
                                            "25,19,13,8,4,0,0,0,0,0,",
                                            "0,0,0,0,0,0,0,0,0,0,",
                                            "0,0,0,0,0,0,0,0,0,0,",
                                            "0,0,0,0,0,0,0,0,0,0,",
                                            "0,0,0,0,0,0,0,0,0,0,",
                                            "0,0,0,0,0,0,0,0,0,0,",
                                            "0,0,0,0,0,0,0,0,0,0,",
                                            "0,0,0,0,0,0,0,0,0,0,",
                                            "0,0,0,0,0,0,0,0,0,0,",
                                            "0,0,0,0,0,0,0,0,0,0,",
                                            "0,0,0,0,0,0,0,0,0,0,",
                                            "0,0,0,0,0,0,0,0,0,0,",
                                            "0,0,0,0,0,0,0,0,0,0,",
                                            "0,0,0,0,0,0,0,0,0,0;",

                                            "AG_TYPE:4,4,4;",
                                            "VALUEDATA_VTE:500,500,500;",
                                            "VALUEDATA_VTI:500,500,500;",
                                            "VALUEDATA_MV:600,600,600;",
                                            "VALUEDATA_RATE:12,12,12;",
                                            "VALUEDATA_R:20,20,20;",
                                            "VALUEDATA_C:20,20,20;",
                                            "VALUEDATA_C20C:100,100,100;",
                                            "VALUEDATA_FSPN:0,0,0;",
                                            "VALUEDATA_MVSPN:0,0,0;",
                                            "VALUEDATA_IE:110,110,110;",
                                            "VALUEDATA_PPEAK:15,15,15;",
                                            "VALUEDATA_TOP_PAW:40,40,40;",
                                            "VALUEDATA_PPLAT:15,15,15;",
                                            "VALUEDATA_PEEP:2,2,2;",
                                            "VALUEDATA_PMEAN:9,9,9;",
                                            "VALUEDATA_FIO2:45,45,45;",
                                            "VALUEDATA_FICO2:0,0,0;",
                                            "VALUEDATA_ETCO2:36,36,36;",
                                            "VALUEDATA_FIN2O:0,0,0;",
                                            "VALUEDATA_ETN2O:0,0,0;",
                                            "VALUEDATA_FI_AA1:-99999,-99999,-99999;",
                                            "VALUEDATA_FI_AA2:-99999,-99999,-99999;",
                                            "VALUEDATA_FIHAL:3,3,3;",
                                            "VALUEDATA_FIENF:3,3,3;",
                                            "VALUEDATA_FISEV:3,3,3;",
                                            "VALUEDATA_FIISO:3,3,3;",
                                            "VALUEDATA_FIDES:3,3,3;",
                                            "VALUEDATA_ET_AA1:-99999,-99999,-99999;",
                                            "VALUEDATA_ET_AA2:-99999,-99999,-99999;",
                                            "VALUEDATA_ETHAL:2,2,2;",
                                            "VALUEDATA_ETENF:2,2,2;",
                                            "VALUEDATA_ETSEV:2,2,2;",
                                            "VALUEDATA_ETISO:2,2,2;",
                                            "VALUEDATA_ETDES:2,2,2;",
                                            "VALUEDATA_MAC:20,20,20;",
                                            "VALUEDATA_SPO2:99,98,98;",
                                            "VALUEDATA_PR:70,70,70;",
                                            "VALUEDATA_FLOWMETER_O2:200,200,200;",
                                            "VALUEDATA_FLOWMETER_AIR:0,0,0;",
                                            "VALUEDATA_FLOWMETER_N2O:0,0,0;"};

static const QStringList DefaultDemoPSVData={
                                           "CO2:",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,2,5,11,17,30,40,",
                                           "55,69,88,103,124,138,156,173,190,207,",
                                           "222,235,246,258,268,276,282,293,299,306,",
                                           "312,319,324,329,335,340,345,349,353,355,",
                                           "358,360,360,360,360,360,360,360,360,360,",
                                           "360,360,360,360,360,360,360,360,360,360,",
                                           "360,360,360,360,360,360,360,360,360,360,",
                                           "360,360,360,360,360,360,360,360,360,360,",
                                           "360,360,360,360,360,360,360,360,360,360,",
                                           "360,360,360,360,360,360,360,360,360,358,",
                                           "353,348,341,330,320,308,297,287,274,260,",
                                           "243,222,201,181,164,148,134,122,111,101,",
                                           "91,83,75,67,61,55,49,44,39,34,",
                                           "30,27,23,21,17,15,12,10,7,5,",
                                           "3,2,1,1,1,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0;",

                                           "PAW:",
                                           "22,22,22,27,33,36,38,42,49,56,",
                                           "61,67,72,78,83,88,93,98,102,106,",
                                           "111,114,118,121,124,126,129,132,133,135,",
                                           "138,140,141,142,144,145,146,147,147,148,",
                                           "149,149,150,150,150,150,150,150,150,150,",
                                           "150,150,150,150,150,150,150,150,150,150,",
                                           "150,150,150,150,150,150,150,150,150,150,",
                                           "150,150,150,150,150,150,150,150,150,150,",
                                           "150,150,150,150,150,145,140,129,120,111,",
                                           "103,95,87,79,72,66,60,55,50,44,",
                                           "41,37,34,31,29,27,24,24,22,22,",
                                           "22,22,22,22,22,22,22,22,22,22,",
                                           "22,22,22,22,22,22,22,22,22,22,",
                                           "22,22,22,22,22,22,22,22,22,22,",
                                           "22,22,22,22,22,22,22,22,22,22,",
                                           "22,22,22,22,22,22,22,22,22,22,",
                                           "22,22,22,22,22,22,22,22,22,22,",
                                           "22,22,22,22,22,22,22,22,22,22,",
                                           "22,22,22,22,22,22,22,22,22,22,",
                                           "22,22,22,22,22,22,22,22,22,22,",
                                           "22,22,22,22,22,22,22,22,22,22,",
                                           "22,22,22,22,22,22,22,22,22,22,",
                                           "22,22,22,22,22,22,22,22,22,22,",
                                           "22,22,22,22,22,22,22,22,22,22,",
                                           "22,22,22,22,22,22,22,22,22,22;",

                                           "FLOW:",
                                           "0,0,346,1972,6000,7456,7800,7700,7650,7550,",
                                           "7400,7250,7100,6900,6700,6500,6300,6100,5900,5700,",
                                           "5500,5300,5100,4800,4583,4300,4000,3800,3500,3300,",
                                           "3100,2800,2600,2300,2108,1900,1700,1550,1300,1150,",
                                           "1000,800,700,600,550,500,490,400,300,280,",
                                           "200,120,100,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,-1595,-7219,-12062,-12188,-11476,",
                                           "-10928,-10396,-9939,-9503,-8818,-8153,-7673,-7146,-6574,-6028,",
                                           "-5563,-4989,-4462,-4111,-3708,-3267,-2913,-2546,-2013,-1505,",
                                           "-1039,-564,-286,-141,-39,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0;",

                                           "VOLUME:",
                                           "0,0,0,5,28,41,55,75,110,143,",
                                           "167,192,215,239,263,285,308,330,351,372,",
                                           "391,410,427,444,459,474,487,500,512,522,",
                                           "532,541,550,558,565,571,577,583,588,592,",
                                           "596,597,599,600,601,602,603,604,605,606,",
                                           "607,608,609,610,610,610,610,610,610,610,",
                                           "610,610,610,610,610,610,610,610,610,610,",
                                           "610,610,610,610,610,610,610,610,610,610,",
                                           "610,610,610,610,610,608,590,539,498,460,",
                                           "423,388,354,322,292,264,238,214,192,171,",
                                           "152,135,120,106,93,82,72,70,49,40,",
                                           "30,23,16,10,5,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0,",
                                           "0,0,0,0,0,0,0,0,0,0;",

                                           "AG_TYPE:4,4,4;",
                                           "VALUEDATA_VTE:610,610,610;",
                                           "VALUEDATA_VTI:610,610,610;",
                                           "VALUEDATA_MV:732,732,732;",
                                           "VALUEDATA_RATE:12,12,12;",
                                           "VALUEDATA_R:20,20,20;",
                                           "VALUEDATA_C:20,20,20;",
                                           "VALUEDATA_C20C:100,100,100;",
                                           "VALUEDATA_FSPN:0,0,0;",
                                           "VALUEDATA_MVSPN:0,0,0;",
                                           "VALUEDATA_IE:110,110,110;",
                                           "VALUEDATA_PPEAK:15,15,15;",
                                           "VALUEDATA_TOP_PAW:40,40,40;",
                                           "VALUEDATA_PPLAT:15,15,15;",
                                           "VALUEDATA_PEEP:2,2,2;",
                                           "VALUEDATA_PMEAN:9,9,9;",
                                           "VALUEDATA_FIO2:45,45,45;",
                                           "VALUEDATA_FICO2:0,0,0;",
                                           "VALUEDATA_ETCO2:36,36,36;",
                                           "VALUEDATA_FIN2O:0,0,0;",
                                           "VALUEDATA_ETN2O:0,0,0;",
                                           "VALUEDATA_FI_AA1:-99999,-99999,-99999;",
                                           "VALUEDATA_FI_AA2:-99999,-99999,-99999;",
                                           "VALUEDATA_FIHAL:3,3,3;",
                                           "VALUEDATA_FIENF:3,3,3;",
                                           "VALUEDATA_FISEV:3,3,3;",
                                           "VALUEDATA_FIISO:3,3,3;",
                                           "VALUEDATA_FIDES:3,3,3;",
                                           "VALUEDATA_ET_AA1:-99999,-99999,-99999;",
                                           "VALUEDATA_ET_AA2:-99999,-99999,-99999;",
                                           "VALUEDATA_ETHAL:2,2,2;",
                                           "VALUEDATA_ETENF:2,2,2;",
                                           "VALUEDATA_ETSEV:2,2,2;",
                                           "VALUEDATA_ETISO:2,2,2;",
                                           "VALUEDATA_ETDES:2,2,2;",
                                           "VALUEDATA_MAC:20,20,20;",
                                           "VALUEDATA_SPO2:99,98,98;",
                                           "VALUEDATA_PR:70,70,70;",
                                           "VALUEDATA_FLOWMETER_O2:200,200,200;",
                                           "VALUEDATA_FLOWMETER_AIR:0,0,0;",
                                           "VALUEDATA_FLOWMETER_N2O:0,0,0;"};

static const QStringList DefaultDemoSIMV_PCVData={
                                               "CO2:",
                                               "0,0,0,0,0,0,0,0,0,0,",
                                               "0,0,0,0,0,0,0,0,0,0,",
                                               "0,0,0,0,0,0,0,0,0,0,",
                                               "0,0,0,0,0,0,0,0,0,0,",
                                               "0,0,0,0,0,0,0,0,0,0,",
                                               "0,0,0,0,0,0,0,0,0,0,",
                                               "0,0,0,0,0,0,0,0,0,0,",
                                               "0,0,0,0,0,0,0,0,0,0,",
                                               "0,0,0,0,2,5,11,17,30,40,",
                                               "55,69,88,103,124,138,156,173,190,207,",
                                               "222,235,246,258,268,276,282,293,299,306,",
                                               "312,319,324,329,335,340,345,349,353,355,",
                                               "358,360,360,360,360,360,360,360,360,360,",
                                               "360,360,360,360,360,360,360,360,360,360,",
                                               "360,360,360,360,360,360,360,360,360,360,",
                                               "360,360,360,360,360,360,360,360,360,360,",
                                               "360,360,360,360,360,360,360,360,360,360,",
                                               "360,360,360,360,360,360,360,360,360,358,",
                                               "353,348,341,330,320,308,297,287,274,260,",
                                               "243,222,201,181,164,148,134,122,111,101,",
                                               "91,83,75,67,61,55,49,44,39,34,",
                                               "30,27,23,21,17,15,12,10,7,5,",
                                               "3,2,1,1,1,0,0,0,0,0,",
                                               "0,0,0,0,0,0,0,0,0,0,",
                                               "0,0,0,0,0,0,0,0,0,0;",

                                               "PAW:",
                                               "22,22,22,27,33,36,38,42,49,56,",
                                               "61,67,72,78,83,88,93,98,102,106,",
                                               "111,114,118,121,124,126,129,132,133,135,",
                                               "138,140,141,142,144,145,146,147,147,148,",
                                               "149,149,150,150,150,150,150,150,150,150,",
                                               "150,150,150,150,150,150,150,150,150,150,",
                                               "150,150,150,150,150,150,150,150,150,150,",
                                               "150,150,150,150,150,150,150,150,150,150,",
                                               "150,150,150,150,150,145,140,129,120,111,",
                                               "103,95,87,79,72,66,60,55,50,44,",
                                               "41,37,34,31,29,27,24,24,22,22,",
                                               "22,22,22,22,22,22,22,22,22,22,",
                                               "22,22,22,22,22,22,22,22,22,22,",
                                               "22,22,22,22,22,22,22,22,22,22,",
                                               "22,22,22,22,22,22,22,22,22,22,",
                                               "22,22,22,22,22,22,22,22,22,22,",
                                               "22,22,22,22,22,22,22,22,22,22,",
                                               "22,22,22,22,22,22,22,22,22,22,",
                                               "22,22,22,22,22,22,22,22,22,22,",
                                               "22,22,22,22,22,22,22,22,22,22,",
                                               "22,22,22,22,22,22,22,22,22,22,",
                                               "22,22,22,22,22,22,22,22,22,22,",
                                               "22,22,22,22,22,22,22,22,22,22,",
                                               "22,22,22,22,22,22,22,22,22,22,",
                                               "22,22,22,22,22,22,22,22,22,22;",

                                               "FLOW:",
                                               "0,0,346,1972,6000,7456,7800,7700,7650,7550,",
                                               "7400,7250,7100,6900,6700,6500,6300,6100,5900,5700,",
                                               "5500,5300,5100,4800,4583,4300,4000,3800,3500,3300,",
                                               "3100,2800,2600,2300,2108,1900,1700,1550,1300,1150,",
                                               "1000,800,700,600,550,500,490,400,300,280,",
                                               "200,120,100,0,0,0,0,0,0,0,",
                                               "0,0,0,0,0,0,0,0,0,0,",
                                               "0,0,0,0,0,0,0,0,0,0,",
                                               "0,0,0,0,0,-1595,-7219,-12062,-12188,-11476,",
                                               "-10928,-10396,-9939,-9503,-8818,-8153,-7673,-7146,-6574,-6028,",
                                               "-5563,-4989,-4462,-4111,-3708,-3267,-2913,-2546,-2013,-1505,",
                                               "-1039,-564,-286,-141,-39,0,0,0,0,0,",
                                               "0,0,0,0,0,0,0,0,0,0,",
                                               "0,0,0,0,0,0,0,0,0,0,",
                                               "0,0,0,0,0,0,0,0,0,0,",
                                               "0,0,0,0,0,0,0,0,0,0,",
                                               "0,0,0,0,0,0,0,0,0,0,",
                                               "0,0,0,0,0,0,0,0,0,0,",
                                               "0,0,0,0,0,0,0,0,0,0,",
                                               "0,0,0,0,0,0,0,0,0,0,",
                                               "0,0,0,0,0,0,0,0,0,0,",
                                               "0,0,0,0,0,0,0,0,0,0,",
                                               "0,0,0,0,0,0,0,0,0,0,",
                                               "0,0,0,0,0,0,0,0,0,0,",
                                               "0,0,0,0,0,0,0,0,0,0;",

                                               "VOLUME:",
                                               "0,0,0,5,28,41,55,75,110,143,",
                                               "167,192,215,239,263,285,308,330,351,372,",
                                               "391,410,427,444,459,474,487,500,512,522,",
                                               "532,541,550,558,565,571,577,583,588,592,",
                                               "596,597,599,600,601,602,603,604,605,606,",
                                               "607,608,609,610,610,610,610,610,610,610,",
                                               "610,610,610,610,610,610,610,610,610,610,",
                                               "610,610,610,610,610,610,610,610,610,610,",
                                               "610,610,610,610,610,608,590,539,498,460,",
                                               "423,388,354,322,292,264,238,214,192,171,",
                                               "152,135,120,106,93,82,72,70,49,40,",
                                               "30,23,16,10,5,0,0,0,0,0,",
                                               "0,0,0,0,0,0,0,0,0,0,",
                                               "0,0,0,0,0,0,0,0,0,0,",
                                               "0,0,0,0,0,0,0,0,0,0,",
                                               "0,0,0,0,0,0,0,0,0,0,",
                                               "0,0,0,0,0,0,0,0,0,0,",
                                               "0,0,0,0,0,0,0,0,0,0,",
                                               "0,0,0,0,0,0,0,0,0,0,",
                                               "0,0,0,0,0,0,0,0,0,0,",
                                               "0,0,0,0,0,0,0,0,0,0,",
                                               "0,0,0,0,0,0,0,0,0,0,",
                                               "0,0,0,0,0,0,0,0,0,0,",
                                               "0,0,0,0,0,0,0,0,0,0,",
                                               "0,0,0,0,0,0,0,0,0,0;",

                                               "AG_TYPE:4,4,4;",
                                               "VALUEDATA_VTE:610,610,610;",
                                               "VALUEDATA_VTI:610,610,610;",
                                               "VALUEDATA_MV:732,732,732;",
                                               "VALUEDATA_RATE:12,12,12;",
                                               "VALUEDATA_R:20,20,20;",
                                               "VALUEDATA_C:20,20,20;",
                                               "VALUEDATA_C20C:100,100,100;",
                                               "VALUEDATA_FSPN:0,0,0;",
                                               "VALUEDATA_MVSPN:0,0,0;",
                                               "VALUEDATA_IE:110,110,110;",
                                               "VALUEDATA_PPEAK:15,15,15;",
                                               "VALUEDATA_TOP_PAW:40,40,40;",
                                               "VALUEDATA_PPLAT:15,15,15;",
                                               "VALUEDATA_PEEP:2,2,2;",
                                               "VALUEDATA_PMEAN:9,9,9;",
                                               "VALUEDATA_FIO2:45,45,45;",
                                               "VALUEDATA_FICO2:0,0,0;",
                                               "VALUEDATA_ETCO2:36,36,36;",
                                               "VALUEDATA_FIN2O:0,0,0;",
                                               "VALUEDATA_ETN2O:0,0,0;",
                                               "VALUEDATA_FI_AA1:-99999,-99999,-99999;",
                                               "VALUEDATA_FI_AA2:-99999,-99999,-99999;",
                                               "VALUEDATA_FIHAL:3,3,3;",
                                               "VALUEDATA_FIENF:3,3,3;",
                                               "VALUEDATA_FISEV:3,3,3;",
                                               "VALUEDATA_FIISO:3,3,3;",
                                               "VALUEDATA_FIDES:3,3,3;",
                                               "VALUEDATA_ET_AA1:-99999,-99999,-99999;",
                                               "VALUEDATA_ET_AA2:-99999,-99999,-99999;",
                                               "VALUEDATA_ETHAL:2,2,2;",
                                               "VALUEDATA_ETENF:2,2,2;",
                                               "VALUEDATA_ETSEV:2,2,2;",
                                               "VALUEDATA_ETISO:2,2,2;",
                                               "VALUEDATA_ETDES:2,2,2;",
                                               "VALUEDATA_MAC:20,20,20;",
                                               "VALUEDATA_SPO2:99,98,98;",
                                               "VALUEDATA_PR:70,70,70;",
                                               "VALUEDATA_FLOWMETER_O2:200,200,200;",
                                               "VALUEDATA_FLOWMETER_AIR:0,0,0;",
                                               "VALUEDATA_FLOWMETER_N2O:0,0,0;"};

static const QStringList DefaultDemoSIMV_VCVData={
                                               "CO2:",
                                               "0,0,0,0,0,0,0,0,0,0,",
                                               "0,0,0,0,0,0,0,0,0,0,",
                                               "0,0,0,0,0,0,0,0,0,0,",
                                               "0,0,0,0,0,0,0,0,0,0,",
                                               "0,0,0,0,0,0,0,0,0,0,",
                                               "0,0,0,0,0,0,0,0,0,0,",
                                               "0,0,0,0,0,0,0,0,0,0,",
                                               "0,0,0,0,0,0,0,0,0,0,",
                                               "0,0,0,0,2,5,11,17,30,40,",
                                               "55,69,88,103,124,138,156,173,190,207,",
                                               "222,235,246,258,268,276,282,293,299,306,",
                                               "312,319,324,329,335,340,345,349,353,355,",
                                               "358,360,360,360,360,360,360,360,360,360,",
                                               "360,360,360,360,360,360,360,360,360,360,",
                                               "360,360,360,360,360,360,360,360,360,360,",
                                               "360,360,360,360,360,360,360,360,360,360,",
                                               "360,360,360,360,360,360,360,360,360,360,",
                                               "360,360,360,360,360,360,360,360,360,358,",
                                               "353,348,341,330,320,308,297,287,274,260,",
                                               "243,222,201,181,164,148,134,122,111,101,",
                                               "91,83,75,67,61,55,49,44,39,34,",
                                               "30,27,23,21,17,15,12,10,7,5,",
                                               "3,2,1,1,1,0,0,0,0,0,",
                                               "0,0,0,0,0,0,0,0,0,0,",
                                               "0,0,0,0,0,0,0,0,0,0;",

                                               "PAW:",
                                               "22,22,22,22,27,29,31,33,36,38,",
                                               "40,42,44,46,48,50,52,55,57,59,",
                                               "61,63,65,67,69,71,73,76,78,80,",
                                               "82,84,86,88,90,92,95,97,99,101,",
                                               "103,105,107,109,111,114,116,118,120,122,",
                                               "124,126,128,130,133,135,137,139,141,143,",
                                               "145,147,149,152,154,156,158,160,162,164,",
                                               "166,168,170,173,175,177,179,181,183,185,",
                                               "187,189,192,194,196,198,200,152,118,84,",
                                               "70,60,57,53,51,49,47,45,43,41,",
                                               "39,38,37,36,35,34,33,32,31,30,",
                                               "29,28,27,26,25,24,23,23,22,22,",
                                               "22,22,22,22,22,22,22,22,22,22,",
                                               "22,22,22,22,22,22,22,22,22,22,",
                                               "22,22,22,22,22,22,22,22,22,22,",
                                               "22,22,22,22,22,22,22,22,22,22,",
                                               "22,22,22,22,22,22,22,22,22,22,",
                                               "22,22,22,22,22,22,22,22,22,22,",
                                               "22,22,22,22,22,22,22,22,22,22,",
                                               "22,22,22,22,22,22,22,22,22,22,",
                                               "22,22,22,22,22,22,22,22,22,22,",
                                               "22,22,22,22,22,22,22,22,22,22,",
                                               "22,22,22,22,22,22,22,22,22,22,",
                                               "22,22,22,22,22,23,22,22,22,22,",
                                               "22,22,22,22,22,22,22,22,22,22;",

                                               "FLOW:",
                                               "4,6,4,20,500,900,1200,1500,1659,1800,",
                                               "1800,1800,1800,1800,1800,1800,1800,1800,1800,1800,",
                                               "1800,1800,1800,1800,1800,1800,1800,1800,1800,1800,",
                                               "1800,1800,1800,1800,1800,1800,1800,1800,1800,1800,",
                                               "1800,1800,1800,1800,1800,1800,1800,1800,1800,1800,",
                                               "1800,1800,1800,1800,1800,1800,1800,1800,1800,1800,",
                                               "1800,1800,1800,1800,1800,1800,1800,1800,1800,1800,",
                                               "1800,1800,1800,1800,1800,1800,1800,1800,1800,1800,",
                                               "1800,1800,1800,1800,1800,1800,-816,-5738,-8023,-7677,",
                                               "-7186,-6800,-6500,-6300,-6000,-5800,-5600,-5440,-5200,-5000,",
                                               "-4800,-4600,-4430,-4200,-4000,-3900,-3700,-3500,-3300,-3100,",
                                               "-2900,-2700,-2500,-2300,-2100,-2000,-1800,-1700,-1500,-1300,",
                                               "-1200,-1000,-900,-800,-600,-400,-300,-160,-100,-70,",
                                               "-50,-37,-19,-12,-14,-23,-27,4,-14,-22,",
                                               "-16,-7,-3,13,-13,-17,-6,-15,-15,-3,",
                                               "-11,-4,4,-8,-10,1,-1,-4,-2,-2,",
                                               "-3,1,1,7,3,-6,-16,-8,8,9,",
                                               "14,6,-12,-13,8,6,11,9,-7,-12,",
                                               "-3,4,14,9,9,-6,-6,8,4,9,",
                                               "9,4,-5,-9,11,9,9,9,1,-5,",
                                               "-6,8,4,4,4,6,6,6,1,-12,",
                                               "-13,5,6,11,9,9,9,14,14,9,",
                                               "9,9,-9,-11,-7,0,4,4,9,9,",
                                               "9,9,14,14,4,0,-7,-10,-6,0,",
                                               "1,4,4,4,4,4,4,4,4,4;",

                                               "VOLUME:",
                                               "0,0,0,0,0,0,0,6,13,19,",
                                               "25,31,38,44,50,56,63,69,75,81,",
                                               "88,94,100,106,113,119,125,131,138,144,",
                                               "150,156,163,169,175,181,188,194,200,206,",
                                               "213,219,225,231,238,244,250,256,263,269,",
                                               "275,281,288,294,300,306,313,319,325,331,",
                                               "338,344,350,356,363,369,375,381,388,394,",
                                               "400,406,413,419,425,431,438,444,450,456,",
                                               "463,469,475,481,488,494,500,486,469,451,",
                                               "433,414,392,371,350,330,311,292,274,257,",
                                               "240,225,210,195,181,168,156,144,133,122,",
                                               "112,103,94,87,79,73,66,59,53,47,",
                                               "42,36,30,25,20,15,12,8,5,2,",
                                               "0,0,0,0,0,0,0,0,0,0,",
                                               "0,0,0,0,0,0,0,0,0,0,",
                                               "0,0,0,0,0,0,0,0,0,0,",
                                               "0,0,0,0,0,0,0,0,0,0,",
                                               "0,0,0,0,0,0,0,0,0,0,",
                                               "0,0,0,0,0,0,0,0,0,0,",
                                               "0,0,0,0,0,0,0,0,0,0,",
                                               "0,0,0,0,0,0,0,0,0,0,",
                                               "0,0,0,0,0,0,0,0,0,0,",
                                               "0,0,0,0,0,0,0,0,0,0,",
                                               "0,0,0,0,0,0,0,0,0,0,",
                                               "0,0,0,0,0,0,0,0,0,0;",

                                               "AG_TYPE:4,4,4;",
                                               "VALUEDATA_VTE:500,500,500;",
                                               "VALUEDATA_VTI:500,500,500;",
                                               "VALUEDATA_MV:600,600,600;",
                                               "VALUEDATA_RATE:12,12,12;",
                                               "VALUEDATA_R:20,20,20;",
                                               "VALUEDATA_C:20,20,20;",
                                               "VALUEDATA_C20C:100,100,100;",
                                               "VALUEDATA_FSPN:0,0,0;",
                                               "VALUEDATA_MVSPN:0,0,0;",
                                               "VALUEDATA_IE:110,110,110;",
                                               "VALUEDATA_PPEAK:20,20,20;",
                                               "VALUEDATA_TOP_PAW:40,40,40;",
                                               "VALUEDATA_PPLAT:20,20,20;",
                                               "VALUEDATA_PEEP:0,0,0;",
                                               "VALUEDATA_PMEAN:9,9,9;",
                                               "VALUEDATA_FIO2:45,45,45;",
                                               "VALUEDATA_FICO2:0,0,0;",
                                               "VALUEDATA_ETCO2:36,36,36;",
                                               "VALUEDATA_FIN2O:0,0,0;",
                                               "VALUEDATA_ETN2O:0,0,0;",
                                               "VALUEDATA_FI_AA1:-99999,-99999,-99999;",
                                               "VALUEDATA_FI_AA2:-99999,-99999,-99999;",
                                               "VALUEDATA_FIHAL:3,3,3;",
                                               "VALUEDATA_FIENF:3,3,3;",
                                               "VALUEDATA_FISEV:3,3,3;",
                                               "VALUEDATA_FIISO:3,3,3;",
                                               "VALUEDATA_FIDES:3,3,3;",
                                               "VALUEDATA_ET_AA1:-99999,-99999,-99999;",
                                               "VALUEDATA_ET_AA2:-99999,-99999,-99999;",
                                               "VALUEDATA_ETHAL:2,2,2;",
                                               "VALUEDATA_ETENF:2,2,2;",
                                               "VALUEDATA_ETSEV:2,2,2;",
                                               "VALUEDATA_ETISO:2,2,2;",
                                               "VALUEDATA_ETDES:2,2,2;",
                                               "VALUEDATA_MAC:20,20,20;",
                                               "VALUEDATA_SPO2:99,99,99;",
                                               "VALUEDATA_PR:70,70,70;",
                                               "VALUEDATA_FLOWMETER_O2:200,200,200;",
                                               "VALUEDATA_FLOWMETER_AIR:0,0,0;",
                                               "VALUEDATA_FLOWMETER_N2O:0,0,0;"};


static const QStringList CURVE_TYPE_NAME = {"CO2:","PAW:","FLOW:","VOLUME:","AG_TYPE:",
                                            "VALUEDATA_VTE:","VALUEDATA_VTI:","VALUEDATA_MV:", "VALUEDATA_RATE:","VALUEDATA_R:","VALUEDATA_C:",
                                            "VALUEDATA_C20C:","VALUEDATA_FSPN:","VALUEDATA_MVSPN:","VALUEDATA_IE:","VALUEDATA_PPEAK:",
                                            "VALUEDATA_TOP_PAW:","VALUEDATA_PPLAT:","VALUEDATA_PEEP:","VALUEDATA_PMEAN:","VALUEDATA_FIO2:",
                                            "VALUEDATA_FICO2:","VALUEDATA_ETCO2:","VALUEDATA_FIN2O:","VALUEDATA_ETN2O:","VALUEDATA_FI_AA1:",
                                            "VALUEDATA_FI_AA2:","VALUEDATA_FIHAL:","VALUEDATA_FIENF:","VALUEDATA_FISEV:","VALUEDATA_FIISO:",
                                            "VALUEDATA_FIDES:","VALUEDATA_ET_AA1:","VALUEDATA_ET_AA2:","VALUEDATA_ETHAL:","VALUEDATA_ETENF:",
                                            "VALUEDATA_ETSEV:","VALUEDATA_ETISO:","VALUEDATA_ETDES:","VALUEDATA_MAC:","VALUEDATA_SPO2:",
                                            "VALUEDATA_PR:", "VALUEDATA_FLOWMETER_O2:", "VALUEDATA_FLOWMETER_AIR:","VALUEDATA_FLOWMETER_N2O:",
                                           };
const QMap<int,QString> cAllFileName={{VENTMODE_FLAG_VCV,"DemoDataVCV.txt"},
                                      {VENTMODE_FLAG_PCV,"DemoDataPCV.txt"},
                                      {VENTMODE_FLAG_PMODE,"DemoDataP-mode.txt"},
                                      {VENTMODE_FLAG_VCVSIMV,"DemoDataSIMV-VC.txt"},
                                      {VENTMODE_FLAG_PCVSIMV,"DemoDataSIMV-PC.txt"},
                                      {VENTMODE_FLAG_PSV,"DemoDataPSV.txt"},
                                      {VENTMODE_FLAG_PRVC,"DemoDataPRVC.txt"},
                                      {VENTMODE_FLAG_HLM,"DemoDataHLM.txt"}};

static const QMap<int, QStringList> sModeDefaultDataMap ={{VENTMODE_FLAG_VCV, DefaultDemoVCVData},
                                                          {VENTMODE_FLAG_PCV, DefaultDemoPCVData},
                                                          {VENTMODE_FLAG_PMODE, DefaultDemoPmodeData},
                                                          {VENTMODE_FLAG_VCVSIMV, DefaultDemoSIMV_VCVData},
                                                          {VENTMODE_FLAG_PCVSIMV, DefaultDemoSIMV_PCVData},
                                                          {VENTMODE_FLAG_PSV, DefaultDemoPSVData},
                                                          {VENTMODE_FLAG_PRVC, DefaultDemoPRVCData},
                                                          {VENTMODE_FLAG_HLM, DefaultDemoHLMData}};
DemoThread::DemoThread(QObject *parent):
    QThread(parent)
{
    for(int i=CO2_CURVE;i<=VALUEDATA_FLOWMETER_N2O;i++)
    {
        for(auto ite:cAllFileName.keys())
            mDemoData[ite]<<QVector<int>();
        mDemoSendIndex << 0;
    }
}




void DemoThread::run()
{
    for(auto ite =cAllFileName.begin();ite!=cAllFileName.end();ite++)
        InitDemoData(ite.key(),ite.value());

    //------------------------------ for test------------------------------
    QMap<int, ALL_STRINGS_ENUM> strIdMap
    {
        {PAW_CURVE, STR_GRAPHNAME_PAW	 },
        {FLOW_CURVE, STR_GRAPHNAME_FLOW },
        {VOLUME_CURVE, STR_GRAPHNAME_VOL	 },
        {CO2_CURVE, STR_GRAPHNAME_CO2	 },
    };
    for (int i = E_VENT_MODE_ID::VENTMODE_FLAG_VCV; i < E_VENT_MODE_ID::VENTMODE_FLAG_MAX; ++i)
    {
        auto modeStrId = VentModeSettingManager::GetInstance()->GetVentModeNameId((E_VENT_MODE_ID)i);
        QString output = UIStrings::GetStr(modeStrId) + ": ";
        for (int j = CO2_CURVE; j <= VOLUME_CURVE; ++j)
        {
            output += UIStrings::GetStr(strIdMap[j]) + "-" + QString::number(mDemoData[i][j].size()) + "   ";
        }
#ifdef OPEN_DEMO_MODULES_DEBUG
        DebugLog << output << "\n";
#endif
    }
    //-------------------------------------------------------------------

    connect(RunModeManage::GetInstance(), &RunModeManage::SignalModeChanged,
            this, &DemoThread::SlotOnRunModeChanged);
    connect(VentModeSettingManager::GetInstance(), &VentModeSettingManager::SignalModeChanged,
            this, [this]{
        ResetOffset();
    });

    mDataRefurbishTimer = new QTimer();
    connect(mDataRefurbishTimer, &QTimer::timeout, this, &DemoThread::RefurbishData, Qt::QueuedConnection);
    mDataRefurbishTimer->start(UP_INTERVAL);

    exec();
}

void DemoThread::InitDemoData(int modeType, QString FileName)
{
    QString filePath = QDir::currentPath()+"/DemoData/"+FileName;
    QFile file(filePath);
    QString allValue = sModeDefaultDataMap[modeType].join("");
    QStringList allValueList = allValue.split(";");
    InitDataForStrList(modeType,allValueList);
    if(!file.open(QIODevice::ReadOnly))
    {
        QDir dir;
        dir.mkpath( QDir::currentPath()+"/DemoData/");
        if(file.open(QIODevice::WriteOnly))
        {
            QTextStream steram(&file);
            for(auto ite: sModeDefaultDataMap[modeType])
                steram<<ite<<endl;
        }
        file.close();
        allValue=sModeDefaultDataMap[modeType].join("");
    }
    else
    {
        allValue = file.readAll();
        allValue.remove("\r");
        allValue.remove("\n");
        allValue.remove("\t");
        allValue.remove(" ");
        allValueList = allValue.split(";");
        InitDataForStrList(modeType,allValueList);
    }
}

void DemoThread::InitDataForStrList(int modeType, QStringList value)
{
    for(auto ite : value)
    {
        if(ite.isEmpty())
            continue;
        for(int j=CO2_CURVE; j<=DATA_TYPE::VALUEDATA_FLOWMETER_N2O; j++)
        {
            QString typeName = ite.left(ite.indexOf(":")+1);
            if(typeName==CURVE_TYPE_NAME[j])
            {
                ite =ite.remove(CURVE_TYPE_NAME[j]);
                QStringList valueList= ite.split(",");
                mDemoData[modeType][j].clear();
                foreach (auto value, valueList)
                {
                    bool isSuccess = false;
                    int pushValue = value.toInt(&isSuccess);
                    if(!isSuccess)
                    {
                        auto nameStrId = DataManager::GetParamNameId(j - VALUEDATA_VTE);
#ifdef OPEN_DEMO_MODULES_DEBUG
                        DebugLog << QString("Wrong demo data: %1 %2 @WangXiang").arg(UIStrings::GetStr(nameStrId), value);
#endif
                        pushValue = value.toInt(&isSuccess, 16);
                    }
                    if(isSuccess)
                        mDemoData[modeType][j].push_back(pushValue);
                }
                break;
            }
        }
    }
}

void DemoThread::RefurbishData()
{
    static int upCount=0;
    if(upCount>=(1000/UP_INTERVAL))
    {
        UpTriggerAlarmId();
        for(int i=0;i<mTriggerAlarmId.length();i++)
        {
            AlarmManager::GetInstance()->ForeverTriggerAlarm((E_ALARM_ID)mTriggerAlarmId[i],true);
        }
        upCount=0;
    }
    upCount++;
    if (RunModeManage::GetInstance()->GetCurRunMode() != MODE_RUN_VENT)
    {
        return;
    }
    static int isSendAgData =0;
    auto ventMode = VentModeSettingManager::GetInstance()->GetCurMode();
    PutCurveData(ventMode);
    PutValueData(ventMode);
    ++isSendAgData;
    if(isSendAgData == 40)
    {
        RefurbishAg(ventMode);
        isSendAgData = 0;
    }


    //        for(int i = DemoThread::DATA_TYPE::VALUEDATA_FLOWMETER_O2; i <= DemoThread::DATA_TYPE::VALUEDATA_FLOWMETER_N2O;i++)
    //        {
    //            auto data = mDemoData[VENTMODE_FLAG_VCV][i][mDemoSendIndex[i]];
    //            DataManager::GetInstance()->SaveMonitorData((i - DemoThread::VALUEDATA_VTE), data);
    //            if(++mDemoSendIndex[i]>=mDemoData[VENTMODE_FLAG_VCV][i].count())
    //                mDemoSendIndex[i]=0;
    //        }
}



void DemoThread::CreateDemoDataFile()
{
    for(auto ite =cAllFileName.begin();ite!=cAllFileName.end();ite++)
        InitDemoData(ite.key(),ite.value());
}

void DemoThread::PutCurveData(int ventMode)
{
    auto modeData = mDemoData[ventMode];

    DataManager::GetInstance()->UpdateWaveData(DataManager::WAVE_CO2, modeData[CO2_CURVE][mDemoSendIndex[CO2_CURVE]], 0);
    if(++mDemoSendIndex[CO2_CURVE] >= modeData[CO2_CURVE].count())
        mDemoSendIndex[CO2_CURVE] = 0;
    DataManager::GetInstance()->UpdateWaveData(DataManager::WAVE_PAW, modeData[PAW_CURVE][mDemoSendIndex[PAW_CURVE]], 0);
    if(++mDemoSendIndex[PAW_CURVE] >= modeData[PAW_CURVE].count())
        mDemoSendIndex[PAW_CURVE] = 0;
    DataManager::GetInstance()->UpdateWaveData(DataManager::WAVE_FLOW, modeData[FLOW_CURVE][mDemoSendIndex[FLOW_CURVE]], 0);
    if(++mDemoSendIndex[FLOW_CURVE] >= modeData[FLOW_CURVE].count())
        mDemoSendIndex[FLOW_CURVE]=0;
    DataManager::GetInstance()->UpdateWaveData(DataManager::WAVE_VOL, modeData[VOLUME_CURVE][mDemoSendIndex[VOLUME_CURVE]], 0);
    if(++mDemoSendIndex[VOLUME_CURVE] >= modeData[VOLUME_CURVE].count())
    {
        mDemoSendIndex[VOLUME_CURVE] = 0;
        emit SignalCurWavePeriodEnd();
    }
}
void DemoThread::PutValueData(int ventMode)
{
    T_MASTER_SET sMasterSet;
    VentModeSettingManager::GetInstance()->GetSettingdata(sMasterSet);

    for(int i = DemoThread::DATA_TYPE::VALUEDATA_VTE; i <= DemoThread::DATA_TYPE::VALUEDATA_FLOWMETER_N2O;i++)
    {
        auto data = mDemoData[ventMode][i][mDemoSendIndex[i]];

        if (i >= DemoThread::DATA_TYPE::VALUEDATA_FIHAL && i <= DemoThread::DATA_TYPE::VALUEDATA_ETDES)
        {
            auto idPair = AGModule::GetInstance()->AgTypeToParamType(mDemoData[ventMode][AG_TYPE][mDemoSendIndex[i]]);
            if ((i - DemoThread::VALUEDATA_VTE) != idPair.first && i - DemoThread::VALUEDATA_VTE != idPair.second )
            {
                data = INVALID_VALUE;
            }
        }
        //如果peep设置值为off,则peep监测值无效
        if (i == DemoThread::VALUEDATA_PEEP && sMasterSet.Peep < 4)
        {
            data = INVALID_VALUE;
        }
        DataManager::GetInstance()->SaveMonitorData((i - DemoThread::VALUEDATA_VTE), data);
        if(++mDemoSendIndex[i]>=mDemoData[ventMode][i].count())
            mDemoSendIndex[i]=0;
    }
}


void DemoThread::RefurbishAg(int mode)
{
    /*
    ANESGAS_NULL = 0,
    ANESGAS_HAL,
    ANESGAS_ENF,
    ANESGAS_ISO,
    ANESGAS_SEV,
    ANESGAS_DES
*/
    int agType = mDemoData[mode][AG_TYPE][mDemoSendIndex[AG_TYPE]];
    mDemoSendIndex[AG_TYPE]++;
    if(++mDemoSendIndex[AG_TYPE] >= mDemoData[mode][AG_TYPE].count())
        mDemoSendIndex[AG_TYPE] = 0;
    static int count = 0;
    count++;
    if(count % 10 ==0)
        AGModule::GetInstance()->DemoSetAgType(agType);
}

void DemoThread::SlotOnRunModeChanged()
{
    if (RunModeManage::GetInstance()->GetCurRunMode() == MODE_RUN_VENT)
    {
        ResetOffset();
    }
    else
    {
    }
}


void DemoThread::ResetOffset()
{
    for (int j = DemoThread::CO2_CURVE; j <= DemoThread::VALUEDATA_FLOWMETER_N2O; ++j)
    {
        mDemoSendIndex[j] = 0;
    }
}

void DemoThread::UpTriggerAlarmId()
{
    QString filePath = QDir::currentPath()+"/DemoData/"+TRIGGER_ALARM_FOREVER_FILE;
    QFileInfo fileInfo(filePath);
#ifdef OPEN_DEMO_MODULES_DEBUG
    DebugLog<<fileInfo.lastModified();
#endif
    if(!fileInfo.exists())
    {
        for(int i=0;i<mTriggerAlarmId.length();i++)
        {
            AlarmManager::GetInstance()->ForeverTriggerAlarm((E_ALARM_ID)mTriggerAlarmId[i],false);
        }
        mTriggerAlarmId.clear();
        QFile file(filePath);
        if(file.open(QIODevice::WriteOnly))
        {
            QTextStream steram(&file);
            for(int i=ALARM_FIO2_HIGH;i<ALARM_END;i++)
                steram<<i<<"="<<"false"<<endl;
        }
        file.close();
    }
    else if(!mTriggerFileLastUpTime.isValid()||fileInfo.lastModified()!=mTriggerFileLastUpTime)
    {
        mTriggerFileLastUpTime=fileInfo.lastModified();
        for(int i=0;i<mTriggerAlarmId.length();i++)
        {
            AlarmManager::GetInstance()->ForeverTriggerAlarm((E_ALARM_ID)mTriggerAlarmId[i],false);
        }
        mTriggerAlarmId.clear();
        QSettings settings(filePath, QSettings::IniFormat);
        QStringList keys = settings.allKeys();

        bool needUpdate = false;
        int alarmEndValue = ALARM_END;
        
        foreach (const QString& key, keys)
        {
            bool conversionOk = false;
            int alarmId = key.toInt(&conversionOk);
            if (conversionOk && alarmId >= alarmEndValue)
            {
                settings.remove(key);
                needUpdate = true;
            }
        }
        
        for (int i = ALARM_FIO2_HIGH; i < alarmEndValue; i++)
        {
            if (!settings.contains(QString::number(i)))
            {
                settings.setValue(QString::number(i), false);
                needUpdate = true;
            }
        }
        
        if (needUpdate)
        {
            settings.sync();
            keys = settings.allKeys();
        }

        foreach (const QString& key, keys)
        {
            bool conversionOk = false;
            int alarmId = key.toInt(&conversionOk);
            if (conversionOk && alarmId < alarmEndValue)
            {
                bool value = settings.value(key).toBool();
                if(value)
                    mTriggerAlarmId<<alarmId;
            }
        }
    }
}

