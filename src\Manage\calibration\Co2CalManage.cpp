﻿#include "calibration/Co2CalManage.h"
#include "AGModule.h"
#include "MainMenu/CalibrationMenu/CO2CalPage.h"

#include "calibration/CalModuleManage.h"
#include "SystemTimeManager.h"
#include "CalDemoModule.h"
#include "ModuleTestManager.h"
#include "SystemConfigManager.h"
#include "SystemSettingManager.h"
#include "ModuleTestManager.h"

#define WAITFOR_CO2_CAL_TIME    30  //30sec

Co2CalManage::Co2CalManage()
{
    mCalStatus = AG_ZERO_STATUS_NORMAL;

    connect(ModuleTestManager::GetInstance(), &ModuleTestManager::SignalModuleStateChanged, this, [this](unsigned short moduleId, unsigned short state){
        auto mode = TEST_AG_CO2;

        if(moduleId == mode && state != ABLE)
        {
            if(mCalStatus == AG_ZERO_STATUS_IN_PROGRESS)
            {
                SaveCalResult(AG_ZERO_STATUS_DISABLE);
            }
            mCalStatus = AG_ZERO_STATUS_DISABLE;
            this->DisableCo2Cal();
        }
    });
}

void Co2CalManage::StartCo2Cal()
{
    if (mCalStatus == AG_ZERO_STATUS_NORMAL)
    {
        mInUserCalibration = true;
        
        if (DEMO_MODE == SettingManager->
                GetIntSettingValue(SystemSettingManager::SOFT_MODE_SETTING))
        {//demo演示
            CalDemoModule::GetInstance()->ProcessCalCommand(CAL_TYPE_CO2, 0);
        }
        else
        {
            AGModule::GetInstance()->StartZeroCal();
        }
    }
}

//co2校零模块不能执行取消操作
void Co2CalManage::EndCo2Cal()
{
    mCalStatus = AG_ZERO_STATUS_NORMAL;
}

int Co2CalManage::GetCalTimeLimit()
{
    return WAITFOR_CO2_CAL_TIME;
}

void Co2CalManage::DisableCo2Cal()
{
    if(mCalStatus == AG_ZERO_STATUS_IN_PROGRESS)
    {
        mCalStatus = AG_ZERO_STATUS_DISABLE;
        mInUserCalibration = false;
    }
    emit SignalStatusChanged(AG_ZERO_STATUS_DISABLE);
}

//接收校零结果
void Co2CalManage::SaveCalResult(unsigned char agCalResult)
{
    if (mCalStatus == agCalResult)
    {
        return;
    }

    if (mInUserCalibration)
    {
        //正常成功步骤 normal -> in_process -> normal
        if(AG_ZERO_STATUS_NORMAL == agCalResult && mCalStatus == AG_ZERO_STATUS_IN_PROGRESS)
        {
            agCalResult = AG_ZERO_STATUS_DONE;
            EndCo2Cal();

            mInUserCalibration = false;
        }
        //校准过程中拔掉适配器    ||  校准超时失败
        else if (agCalResult == AG_ZERO_STATUS_DISABLE && mCalStatus == AG_ZERO_STATUS_IN_PROGRESS)
        {
            agCalResult = AG_ZERO_STATUS_FAIL;
            mCalStatus = AG_ZERO_STATUS_DISABLE;

            mInUserCalibration = false;
        }
        else
        {
            //上位机与校零模块阶段同步
            mCalStatus = agCalResult;
        }
    }
    else
    {
        //上位机与校零模块阶段同步
        mCalStatus = agCalResult;
    }


    switch (agCalResult)
    {
    case AG_ZERO_STATUS_DONE:
        if (ModuleTestManager::GetInstance()->GetAgModuleType() == AG)
        {
            CalModuleManage::GetInstance()->UpdateCalResult(CAL_TYPE::CAL_TYPE_AG, CAL_RESULT::CAL_SUCCESS);
        }
        else if(ModuleTestManager::GetInstance()->GetAgModuleType() == CO2)
        {
            CalModuleManage::GetInstance()->UpdateCalResult(CAL_TYPE::CAL_TYPE_CO2, CAL_RESULT::CAL_SUCCESS);
        }
        break;
    case AG_ZERO_STATUS_FAIL:
        if (ModuleTestManager::GetInstance()->GetAgModuleType() == AG)
        {
            CalModuleManage::GetInstance()->UpdateCalResult(CAL_TYPE::CAL_TYPE_AG, CAL_RESULT::CAL_FAIL);
        }
        else if(ModuleTestManager::GetInstance()->GetAgModuleType() == CO2)
        {
            CalModuleManage::GetInstance()->UpdateCalResult(CAL_TYPE::CAL_TYPE_CO2, CAL_RESULT::CAL_FAIL);
        }
        break;
    }

    emit SignalStatusChanged((AG_ZERO_CAL_RESULT)agCalResult);
}


