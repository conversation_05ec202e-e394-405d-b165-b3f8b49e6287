//2025-06-04 14:34:25
//Generated by BAIGE
#pragma once
#include <QStringList>
#include "StringEnum.h"

namespace Ukrainian
{
    QStringList UiStr
    {

		u8R"()", //STR_NULL
		u8R"(VCV)", //STR_LABEL_VCV
		u8R"(PCV)", //STR_LABEL_PCV
		u8R"(PSV)", //STR_LABEL_PSV
		u8R"(SIMV-VC)", //STR_LABEL_SIMVVCV
		u8R"(SIMV-PC)", //STR_LABEL_SIMVPCV
		u8R"(PRVC)", //STR_LABEL_PRVC
		u8R"(P-режим)", //STR_LABEL_PMODE
		u8R"(HLM)", //STR_LABEL_HLM
		u8R"(CPB)", //STR_LABEL_CPB
		u8R"(МЕХАНІЧНИЙ)", //STR_MODE_MECHANICAL
		u8R"(ACGO)", //STR_MODE_ACGO
		u8R"(Реж оч)", //STR_MODE_STANDBY
		u8R"(Ручний)", //STR_MODE_MANUAL
		u8R"(Швидкість)", //STR_VALUEPARAM_RATE
		u8R"(fspn)", //STR_VALUEPARAM_FSPN
		u8R"(I:E)", //STR_VALUEPARAM_IE
		u8R"(VTe)", //STR_VALUEPARAM_VTE
		u8R"(VTi)", //STR_VALUEPARAM_VTI
		u8R"(MV)", //STR_VALUEPARAM_MV
		u8R"(MVspn)", //STR_VALUEPARAM_MVSPN
		u8R"(Ppeak)", //STR_VALUEPARAM_PPEAK
		u8R"(Pplat)", //STR_VALUEPARAM_PPLAT
		u8R"(PEEP)", //STR_VALUEPARAM_PEEP
		u8R"(Pmean)", //STR_VALUEPARAM_PMEAN
		u8R"(Pmin)", //STR_VALUEPARAM_PMIN
		u8R"(R)", //STR_VALUEPARAM_R
		u8R"(C)", //STR_VALUEPARAM_C
		u8R"(FiO2)", //STR_VALUEPARAM_FIO2
		u8R"(EtCO2)", //STR_VALUEPARAM_ETCO2
		u8R"(FiCO2)", //STR_VALUEPARAM_FICO2
		u8R"(PR)", //STR_VALUEPARAM_PR
		u8R"(AA)", //STR_VALUEPARAM_AG_NULL
		u8R"(FiAA)", //STR_VALUEPARAM_AG_FINULL
		u8R"(FiN2O)", //STR_VALUEPARAM_FIN2O
		u8R"(FiHAL)", //STR_VALUEPARAM_FIHAL
		u8R"(FiENF)", //STR_VALUEPARAM_FIENF
		u8R"(FiSEV)", //STR_VALUEPARAM_FISEV
		u8R"(FiISO)", //STR_VALUEPARAM_FIISO
		u8R"(FiDES)", //STR_VALUEPARAM_FIDES
		u8R"(EtAA)", //STR_VALUEPARAM_AG_ETNULL
		u8R"(EtN2O)", //STR_VALUEPARAM_ETN2O
		u8R"(EtHAL)", //STR_VALUEPARAM_ETHAL
		u8R"(EtENF)", //STR_VALUEPARAM_ETENF
		u8R"(EtSEV)", //STR_VALUEPARAM_ETSEV
		u8R"(EtISO)", //STR_VALUEPARAM_ETISO
		u8R"(EtDES)", //STR_VALUEPARAM_ETDES
		u8R"(Апное)", //STR_VALUEPARAM_ASPHY
		u8R"(VT)", //STR_SETTINGPARAM_VT
		u8R"(Швидкість)", //STR_SETTINGPARAM_RATE
		u8R"(I:E)", //STR_SETTINGPARAM_IE
		u8R"(Tip:Ti)", //STR_SETTINGPARAM_TITP
		u8R"(Pinsp)", //STR_SETTINGPARAM_PINSP
		u8R"(CPAP)", //STR_SETTINGPARAM_CPAP
		u8R"(Plimit)", //STR_SETTINGPARAM_PLIMIT
		u8R"(PEEP)", //STR_SETTINGPARAM_PEEP
		u8R"(Ftrig)", //STR_SETTINGPARAM_FTRIG
		u8R"(Ptrig)", //STR_SETTINGPARAM_PTRIG
		u8R"(Tinsp)", //STR_SETTINGPARAM_TINSP
		u8R"(Slope)", //STR_SETTINGPARAM_SLOPE
		u8R"(Psupp)", //STR_SETTINGPARAM_PSUPP
		u8R"(Кінцевий рівень)", //STR_SETTINGPARAM_INENDLEVEL
		u8R"(VTe)", //STR_SETTINGLIMIT_VTE
		u8R"(Paw)", //STR_SETTINGLIMIT_PAW
		u8R"(FiO2)", //STR_SETTINGLIMIT_FIO2
		u8R"(Швидкість)", //STR_SETTINGLIMIT_RATE
		u8R"(MV)", //STR_SETTINGLIMIT_MV
		u8R"(1/(хв.л))", //STR_UNIT_1PERMINL
		u8R"(с)", //STR_UNIT_S
		u8R"(смH2O)", //STR_UNIT_CMH2O
		u8R"(смH2O/(л/с))", //STR_UNIT_CMH2OLS
		u8R"(мл/смH2O)", //STR_UNIT_MLCMH2O
		u8R"(мм рт.ст.)", //STR_UNIT_MMHG
		u8R"(%)", //STR_UNIT_PERCENTAGE
		u8R"(л)", //STR_UNIT_L
		u8R"(мл)", //STR_UNIT_ML
		u8R"(л/хв)", //STR_UNIT_LPERMIN
		u8R"(хв)", //STR_UNIT_MIN
		u8R"(уд/хв)", //STR_UNIT_BPM
		u8R"(кг)", //STR_UNIT_KG
		u8R"(фунт)", //STR_UNIT_LB
		u8R"(м)", //STR_UNIT_M
		u8R"(фут)", //STR_UNIT_FT
		u8R"(гПа)", //STR_UNIT_HPA
		u8R"(кПа)", //STR_UNIT_KPA
		u8R"(мБар)", //STR_UNIT_MBAR
		u8R"(---)", //STR_DASH
		u8R"(Insp.)", //STR_INSP
		u8R"(Exp.)", //STR_EXP
		u8R"(MAC)", //STR_MAC
		u8R"(Paw)", //STR_GRAPHNAME_PAW
		u8R"(Потік)", //STR_GRAPHNAME_FLOW
		u8R"(Об'єм)", //STR_GRAPHNAME_VOL
		u8R"(CO2)", //STR_GRAPHNAME_CO2
		u8R"(SPO2)", //STR_GRAPHNAME_SPO2
		u8R"(C%10 / C)", //STR_GRAPHNAME_C20C
		u8R"(Vol-Flow)", //STR_GRAPHNAME_VF
		u8R"(Paw-Vol)", //STR_GRAPHNAME_PV
		u8R"(Flow-Paw)", //STR_GRAPHNAME_FP
		u8R"(Vol-CO2)", //STR_GRAPHNAME_VCO2
		u8R"(O2)", //STR_GASNAME_O2
		u8R"(N2O)", //STR_GASNAME_N2O
		u8R"(CO2)", //STR_GASNAME_CO2
		u8R"(Повіт)", //STR_GASNAME_AIR
		u8R"(Pinsp)", //STR_CPAP
		u8R"(Phy-тривога)", //STR_BIO_ALARM
		u8R"(Встановити час)", //STR_SET_TIME
		u8R"(VENTURA)", //STR_OEM
		u8R"(ELUNA-60)", //STR_ELUNA_60
		u8R"(ELUNA-70)", //STR_ELUNA_70
		u8R"(ELUNA-80)", //STR_ELUNA_80
		u8R"(CENAR-30)", //STR_CENAR_30
		u8R"(CENAR-40)", //STR_CENAR_40
		u8R"(CENAR-50)", //STR_CENAR_50
		u8R"(PSV рівень)", //STR_PSV_LEVEL
		u8R"(backup)", //STR_BACKUP
		u8R"(SN-)", //STR_SN_SHORT_CUT
		u8R"()", //STR_BLANK
		u8R"(НАЗАД)", //STR_BACK
		u8R"(ВВЕДЕННЯ)", //STR_ENTER
		u8R"(Тест на герметичність недоступний. причиною є: 
1. ручний режим, або 
2. відкритий ACGO, або 
3. витратоміри не вимкнені.)", //STR_DISABLE_TEST
		u8R"(Виконайте тест наступним чином:)", //STR_LABEL_BST_HEAD
		u8R"(1.Під'єднайте Y-образний патрубок до заглушки тесту на герметичність, як показано на рисунку.
2.Тримайте перемикач ручної/механічної вентиляції в механічному положенні.
3.Натисніть кнопку промивання O2, щоб повністю заповнити сильфон.
4.Відрегулюйте всі витратоміри на нуль. 
5.Натисніть кнопку ‘Почати’ для тестування.)", //STR_LABEL_BST_INIT
		u8R"(Тест)", //STR_TEST
		u8R"(Тест на герметичність триває.
Зачекайте близько 1 хв.)", //STR_LABEL_BST_TESTING
		u8R"(Невдалий тест герметичн.
Перевірте схему й спробуйте ще раз.)", //STR_LABEL_BST_FAIL
		u8R"(Відповідність)", //STR_COMPLIANCE
		u8R"(Тест герметичн.)", //STR_LEAK
		u8R"(Невдача)", //STR_FAIL
		u8R"(Пройдено)", //STR_QULIFIED
		u8R"(Невдача)", //STR_UNQULIFIED
		u8R"(Остан. тест)", //STR_RecentSTT
		u8R"(Остан. калібр.)", //STR_RECENT_CAL
		u8R"(Загал. потік)", //STR_TOTAL
		u8R"(Вихід)", //STR_EXIT
		u8R"(OK)", //STR_CONFIRM
		u8R"(Скасувати)", //STR_CANCEL
		u8R"(Почати)", //STR_START
		u8R"(Скинути)", //STR_RESET
		u8R"(Сигнали)", //STR_LABEL_WAVEFORMS
		u8R"(Петлі)", //STR_LABEL_SPIROMETRY
		u8R"(Графічні тренди)", //STR_LABEL_TREND
		u8R"(Усі значення)", //STR_LABEL_VALUES
		u8R"(Зберегти петлю)", //STR_LOOP_BUTTON
		u8R"(Відновити)", //STR_LOOP_BUTTONRELEASE
		u8R"(Витратомір)", //STR_LABEL_FLOWMETER
		u8R"(Тривога!)", //STR_ALARM
		u8R"(повідомл.)", //STR_MESSAGE
		u8R"(Гол. меню)", //STR_MAINMENU
		u8R"(Нуль)", //STR_MAINMENU_ZERO
		u8R"(За замовчув.)", //STR_MAINMENU_DEFAULT
		u8R"(Калібрування)", //STR_CALIBRATION
		u8R"(Калібрування недоступне в ручному режимі або з увімкненим перемикачем ACGO)", //STR_CAL_DIABLE
		u8R"(Калібрування)", //STR_MAINMENU_CAL
		u8R"(Успішне калібрування)", //STR_MAINMENU_CALDONE
		u8R"(Невдале калібрування)", //STR_MAINMENU_CALFAIL
		u8R"(Виконайте калібрування наступним чином)", //STR_MAINMENU_CAL_TIP
		u8R"(Калібрування витратоміра)", //STR_MAINMENU_FLOWCAL
		u8R"(1.Переконайтеся, що ви перебуваєте в режимі очікування.
2.Установіть всі витратоміри на нуль.
3.Натисніть кнопку ‘Почати’.)", //STR_MAINMENU_FLOWCAL_TXT2
		u8R"(Вибрати 'калібрування')", //STR_MAINMENU_FLOWCAL_TXT3
		u8R"(Йде калібрування нуля, це може зайняти 1-2 хв.)", //STR_MAINMENU_FLOWCAL_TXT4
		u8R"(Калібрування датчика O2)", //STR_MAINMENU_O2CAL
		u8R"(Кал.21%)", //STR_MAINMENU_21PO2CAL
		u8R"(100%Кал.)", //STR_MAINMENU_100PO2CAL
		u8R"(Готово)", //STR_MAINMENU_CAL_FIN
		u8R"(1.Переконайтеся, що ви перебуваєте в режимі очікування.
2.Вимкніть всі потоки свіжого газу.
3.Відкрийте заглушку й зворотний клапан вдиху, як показано на рисунку.
4.Натисніть кнопку 'Почати', щоб виконати калібрування концентрації кисню 21%.)", //STR_MAINMENU_O2CAL_TXT3
		u8R"(Йде калібрування датчика O2, зачекайте кілька хвилин...)", //STR_MAINMENU_CAL_IN_PROCESS
		u8R"(Калібрування 21% завершено, установіть заглушку й зворотний клапан для вдиху, як показано, а потім виберіть ‘100%Кал.’ або ‘Готово’.)", //STR_MAINMENU_CAL_21_DONE
		u8R"(Калібрування концентрації 100% O2 завершено.)", //STR_MAINMENU_CAL_100_DONE
		u8R"(Калібрування 21% O2 не вдалося; поверніться до системи дихального контуру, як показано на рисунку.)", //STR_MAINMENU_CAL_21_FAIL
		u8R"(Не вдалося виконати калібрування 100% O2.)", //STR_MAINMENU_CAL_100_FAIL
		u8R"(Калібрування O2 завершено, перевірте систему дихального контуру.)", //STR_MAINMENU_CAL_FINISH
		u8R"(AG/CO2)", //STR_MAINMENU_AGCO2CAL
		u8R"(1.Переконайтеся, що ви в режимі очікування.
2.Зберігайте концентрацію CO2 такою ж, як в атмосфері.)", //STR_MAINMENU_AGCO2CAL_TXT1_TITLE
		u8R"(Модуль AG/CO2 нагрівається)", //STR_MAINMENU_CAL_STATUS_DISABLE
		u8R"(Занулення...)", //STR_MAINMENU_CAL_STATUS_IN_PROG
		u8R"(Успішне калібрування нуля)", //STR_MAINMENU_CAL_STATUS_ZERODONE
		u8R"(Помилка калібрування нуля)", //STR_MAINMENU_CAL_STATUS_FAILED
		u8R"(Останнє калібрування нуля)", //STR_LATEST_ZERO_CAL_TIME
		u8R"(Датчик потоку)", //STR_MAINMENU_SENSORCAL
		u8R"(Датчик тиску)", //STR_MAINMENU_SENSORCAL_TXT3
		u8R"(система)", //STR_MAINMENU_SYSSETTING
		u8R"(Загальні налаштування)", //STR_MAINMENU_EQUIPSET
		u8R"(Гучність системи)", //STR_MAINMENU_EQUIPSET_TXT2
		u8R"(Увімкн.)", //STR_ON
		u8R"(Вимкн.)", //STR_OFF
		u8R"(Одиниця тиску)", //STR_PAW_UNIT
		u8R"(Одиниця CO2)", //STR_CO2_UNIT
		u8R"(Час)", //STR_MAINMENU_TIMEDATE
		u8R"(Дата)", //STR_MAINMENU_TIMEDATE_DATE
		u8R"(Час)", //STR_MAINMENU_TIMEDATE_TIME
		u8R"(Час(AM))", //STR_MAINMENU_TIMEDATE_12HOUR_AM
		u8R"(Час(PM))", //STR_MAINMENU_TIMEDATE_12HOUR_PM
		u8R"(Формат дати)", //STR_DATE_FORMAT
		u8R"(Формат часу)", //STR_TIME_FORMAT
		u8R"(Р - М - Д)", //STR_MAINMENU_YMD
		u8R"(М - Д - Р)", //STR_MAINMENU_MDY
		u8R"(Д - М - Р)", //STR_MAINMENU_DMY
		u8R"(12год)", //STR_TIME_FORMAT_12H
		u8R"(24год)", //STR_TIME_FORMAT_24H
		u8R"(Змінити тип тригера)", //STR_MAINMENU_TRIGSET
		u8R"(IP)", //STR_MAINMENU_IP
		u8R"(Тип тригера)", //STR_MAINMENU_TRIGSET_TXT1
		u8R"(Дисплей)", //STR_MAINMENU_DISPSETTING
		u8R"(Швидко)", //STR_MAINMENU_WAVESET_HIGH_SPEED
		u8R"(Нормально)", //STR_MAINMENU_WAVESET_MID_SPEED
		u8R"(Повільно)", //STR_MAINMENU_WAVESET_LOW_SPEED
		u8R"(Петля)", //STR_MAINMENU_LOOPSET
		u8R"(Значення)", //STR_MAINMENU_VALUE
		u8R"(Графічні тренди)", //STR_MAINMENU_TRENDSET
		u8R"(Час тренду)", //STR_MAINMENU_TRENDSET_TXT4
		u8R"(Сис. інфо)", //STR_MAINMENU_SYSINFO
		u8R"(Версія)", //STR_MAINMENU_VER
		u8R"(Назва)", //STR_MAINMENU_VER_TITLE1
		u8R"(ПЗ хоста)", //STR_MAINMENU_VER_TITLE1_TXT1
		u8R"(Версія)", //STR_MAINMENU_VER_TITLE2
		u8R"(Дата)", //STR_MAINMENU_VER_TITLE3
		u8R"(Конфіг)", //STR_MAINMENU_CONFIG
		u8R"(Серійний номер)", //STR_MAINMENU_CONFIG_TXT1
		u8R"(Перевірка сист)", //STR_MAINMENU_SELFTEST
		u8R"(Тест датчика потоку O2)", //STR_MAINMENU_SELFTEST_TXT1
		u8R"(Тест датчика повітря)", //STR_MAINMENU_SELFTEST_TXT2
		u8R"(Тест датчика потоку видиху)", //STR_MAINMENU_SELFTEST_TXT3
		u8R"(Тест датчика тиску)", //STR_MAINMENU_SELFTEST_TXT4
		u8R"(Тест клапана PEEP)", //STR_MAINMENU_SELFTEST_TXT5
		u8R"(Тест клапана безпеки)", //STR_MAINMENU_SELFTEST_TXT6
		u8R"(Тест датчика O2)", //STR_MAINMENU_SELFTEST_TXT7
		u8R"(Витік (мл/хв))", //STR_MAINMENU_SELFTEST_TXT8
		u8R"(Відповідність(мл/смH2O))", //STR_MAINMENU_SELFTEST_TXT9
		u8R"(Опір контуру (смH2O/л/с))", //STR_MAINMENU_SELFTEST_TXT10
		u8R"(Готово)", //STR_FINISH
		u8R"(Інженер)", //STR_MAINMENU_EGRSETTING
		u8R"(Інженер. налашт.)", //STR_EGNEER_SETTING
		u8R"(Журнал операцій)", //STR_OPER_LOG
		u8R"(Інженер. калібр.)", //STR_EG_CAL
		u8R"(Інфо конфіг.)", //STR_PRODUCT_CFG
		u8R"(Калібр. потоку)", //STR_FLOW_CAL
		u8R"(Калібр. тиску)", //STR_PRESS_CAL
		u8R"(Очікування)", //STR_STANDBY
		u8R"(Перейти в режим очікув.?)", //STR_STANDBY_STRING1
		u8R"(Час)", //STR_ALARMSET_TIME
		u8R"(Пункти тривоги)", //STR_ALARMSET_ALARMITEM
		u8R"(Пріоритет)", //STR_ALARMSET_PRIORITY
		u8R"(Низький)", //STR_ALARMSET_LOW
		u8R"(Високий)", //STR_ALARMSET_HIGH
		u8R"(Налашт трв)", //STR_ALARMSET
		u8R"(Межа тривоги)", //STR_ALARMSET_ALARMLIMIT
		u8R"(Журнал тривог)", //STR_ALARMSET_ALARMLOG
		u8R"(Високий FiO2!!)", //STR_ALARMINFO_HIGH_FIO2
		u8R"(FiO2 занизький!!!)", //STR_ALARMINFO_LOW_FIO2
		u8R"(Високий PR!!)", //STR_ALARMINFO_HIGH_PR
		u8R"(Низький PR!!)", //STR_ALARMINFO_LOW_PR
		u8R"(MV зависокий!!)", //STR_ALARMINFO_HIGH_MV
		u8R"(MV занизький!!)", //STR_ALARMINFO_LOW_MV
		u8R"(Швидкість зависока!)", //STR_ALARMINFO_HIGH_RATE
		u8R"(Швидкість занизька!)", //STR_ALARMINFO_LOW_RATE
		u8R"(VTe зависокий!!)", //STR_ALARMINFO_HIGH_VTE
		u8R"(VTe занизький!!)", //STR_ALARMINFO_LOW_VTE
		u8R"(Paw занизький!!)", //STR_ALARMINFO_LOW_PAW
		u8R"(Paw зависокий!!!)", //STR_ALARMINFO_HIGH_PAW
		u8R"(Межа тиску)", //STR_PRESSURE_REACH_LIMIT
		u8R"(FiN2O зависокий!!!)", //STR_ALARMINFO_HIGH_FIN2O
		u8R"(FiN2O занизький!)", //STR_ALARMINFO_LOW_FIN2O
		u8R"(EtN2O зависокий!!!)", //STR_ALARMINFO_HIGH_ETN2O
		u8R"(EtN2O занизький!)", //STR_ALARMINFO_LOW_ETN2O
		u8R"(FiCO2 зависокий!!!)", //STR_ALARMINFO_HIGH_FICO2
		u8R"(FiCO2 занизький!)", //STR_ALARMINFO_LOW_FICO2
		u8R"(EtCO2 зависокий!!!)", //STR_ALARMINFO_HIGH_ETCO2
		u8R"(EtCO2 занизький!!)", //STR_ALARMINFO_LOW_ETCO2
		u8R"(Високий SPO2!!)", //STR_ALARMINFO_HIGH_SPO2
		u8R"(Низький SPO2!!!)", //STR_ALARMINFO_LOW_SPO2
		u8R"(FiHAL зависокий!!)", //STR_ALARMINFO_HIGH_FIHAL
		u8R"(FiHAL занизький!)", //STR_ALARMINFO_LOW_FIHAL
		u8R"(FiENF зависокий!!)", //STR_ALARMINFO_HIGH_FIENF
		u8R"(FiENF занизький!)", //STR_ALARMINFO_LOW_FIENF
		u8R"(FiSEV зависокий!!)", //STR_ALARMINFO_HIGH_FISEV
		u8R"(FiSEV занизький!)", //STR_ALARMINFO_LOW_FISEV
		u8R"(FiISO зависокий!!)", //STR_ALARMINFO_HIGH_FIISO
		u8R"(FiISO занизький!)", //STR_ALARMINFO_LOW_FIISO
		u8R"(FiDES зависокий!!)", //STR_ALARMINFO_HIGH_FIDES
		u8R"(FiDES занизький!)", //STR_ALARMINFO_LOW_FIDES
		u8R"(EtHAL зависокий!!)", //STR_ALARMINFO_HIGH_ETHAL
		u8R"(EtHAL занизький!)", //STR_ALARMINFO_LOW_ETHAL
		u8R"(EtENF зависокий!!)", //STR_ALARMINFO_HIGH_ETENF
		u8R"(EtENF занизький!)", //STR_ALARMINFO_LOW_ETENF
		u8R"(EtSEV зависокий!!)", //STR_ALARMINFO_HIGH_ETSEV
		u8R"(EtSEV занизький!)", //STR_ALARMINFO_LOW_ETSEV
		u8R"(EtISO зависокий!!)", //STR_ALARMINFO_HIGH_ETISO
		u8R"(EtISO занизький!)", //STR_ALARMINFO_LOW_ETISO
		u8R"(EtDES зависокий!!)", //STR_ALARMINFO_HIGH_ETDES
		u8R"(EtDES занизький!)", //STR_ALARMINFO_LOW_ETDES
		u8R"(Зависокий потік O2!)", //STR_ALARMINFO_O2_FLOW_HIGH
		u8R"(Зависокий потік N2O!)", //STR_ALARMINFO_N2O_FLOW_HIGH
		u8R"(Зависокий потік повітря!)", //STR_ALARMINFO_AIR_FLOW_HIGH
		u8R"(Зависокий потік!)", //STR_ALARMINFO_FLOW_HIGH
		u8R"(Ненормальне співвідношення O2 і N2O!!!)", //STR_ALARMINFO_O2_N2O_UNUSUAL_RATIO
		u8R"(Низька батарея!!)", //STR_TECHALARM_BATTERYLOW
		u8R"(Акумулятор розряджений, вимикання!!!)", //STR_TECHALARM_BATTERYEMPTY
		u8R"(Немає живлення!)", //STR_TECHALARM_PLUGOUT
		u8R"(Помилка живлення!)", //STR_TECHALARM_POWERERR
		u8R"(Відкрити ACGO!!)", //STR_TECHALARM_ACGO
		u8R"(Постійний тиск у дихальних шляхах зависокий!!!)", //STR_ALARMINFO_GASHIGH
		u8R"(Постійний тиск у дихальних шляхах занизький!!)", //STR_ALARMINFO_GASLOW
		u8R"(Недосконалий монтаж системи контурів!!!)", //STR_TECHALARM_GASLOOPERR
		u8R"(Тиск приводного газу низький!!!)", //STR_TECHALARM_GASSOURCELOW
		u8R"(Низький тиск у джерелі кисню!!!)", //STR_TECHALARM_O2_GASSOURCELOW
		u8R"(Негативний тиск у дихальних шляхах!!!)", //STR_ALARM_NEGPRESS
		u8R"(Тайм-аут!!)", //STR_TECHALARM_ADDO2TIMEOUT
		u8R"(Помилка прямої подачі кисню!!!)", //STR_TECHALARM_ADDO2ERR
		u8R"(Каністра поглинача CO2 не встановлена!!!)", //STR_TECHALARM_BYPASSERR
		u8R"(Апное!!)", //STR_TECHALARM_APNEAALARM
		u8R"(Час апное > 120 секунд!!!)", //STR_TECHALARM_APNEA_120_ALARM
		u8R"(Несправність датчика потоку!!!)", //STR_FLOW_SENSOR_ERR_ALARM
		u8R"(Висока температура мат. плати!!!)", //STR_TECHALARM_FAN_STOP
		u8R"(Датчик кисню від'єднано!!)", //STR_TECHALARM_O2_SENSOR_ERR
		u8R"(Припинено зв'язок з модулем моніторингу!!!)", //STR_TECHALARM_MINOTORING_MODULE_ERR
		u8R"(Помилка датчика тиску!!!)", //STR_TECHALARM_PRESSURE_SENSOR_ERR
		u8R"(Оглядовий клапан від'єднано!!!)", //STR_TECHALARM_EXP_VALVE_ERR
		u8R"(Дихальний клапан заблоковано!!!)", //STR_TECHALARM_EXP_VALVE_CLOSE_OFF
		u8R"(Помилка дихального клапана!!!)", //STR_TECHALARM_INSP_VALVE_ERR
		u8R"(Помилка нагрівального модуля!)", //STR_TECHALARM_HEATING_MODULE_ERR
		u8R"(Не встановлено адаптер датчика кисню!)", //STR_TECHALARM_AG_ADAPTOR_ERR
		u8R"(Час вийшов!)", //STR_TECHALARM_TIME_UP
		u8R"(Від'єднаний датчик SPO2!!)", //STR_TECHALARM_SPO2_BREAKOFF
		u8R"(Концентрація AX за межами діапазону!!)", //STR_TECHALARM_AX_OUT_OF_RANGE
		u8R"(Концентрація CO2 за межами діапазону!!)", //STR_TECHALARM_CO2_OUT_OF_RANGE
		u8R"(Концентрація N2O за межами діапазону!!)", //STR_TECHALARM_N2O_OUT_OF_RANGE
		u8R"(Температура датчика AG за межами діапазону!!)", //STR_TECHALARM_TMP_OUT_OF_RANGE
		u8R"(Тиск датчика AG за межами діапазону!!)", //STR_TECHALARM_PRESS_OUT_OF_RANGE
		u8R"(Датчик кисню потребує калібрування!!)", //STR_TECHALARM_AG_NEED_RECAIL
		u8R"(Змішаний анестезійний засіб!)", //STR_TECHALARM_AG_AGENT_MIX
		u8R"(Дані датчика AG неточні!!)", //STR_TECHALARM_AG_UNSPECIFIED_ACCURACY
		u8R"(Помилка програмного забезпечення датчика AG!!!)", //STR_TECHALARM_AG_SW_ERR
		u8R"(Помилка апаратного забезпечення датчика AG!!!)", //STR_TECHALARM_AG_HW_ERR
		u8R"(Помилка в роботі датчика AG!!!)", //STR_TECHALARM_MOTO_ERR
		u8R"(Втрачено дані калібрування датчика AG!!)", //STR_TECHALARM_AG_LOST_CAIL
		u8R"(Необхідно замінити адаптер датчика AG!!)", //STR_TECHALARM_AG_REPLACE
		u8R"(Ручний перемикач)", //STR_TIP_MANUAL
		u8R"(Вис)", //STR_HIGH
		u8R"(Сер)", //STR_MID
		u8R"(Низ)", //STR_LOW
		u8R"(Таймер)", //STR_TIMER1
		u8R"(Часщо минув)", //STR_TIMER2
		u8R"(Перезавантаження)", //STR_TIMER3
		u8R"(Відкрити)", //STR_OPEN
		u8R"(Закрити)", //STR_CLOSE
		u8R"(Деяку фізичну тривогу припинено)", //STR_BIO_ALARM_CLOSE
		u8R"(Межа досяжності параметра)", //STR_PARAM_LIMITED
		u8R"(Перевірка сист.)", //STR_SYSTEM_SELFTEST
		u8R"(Повторити)", //STR_RETRY
		u8R"(Ігнор.)", //STR_IGNORE
		u8R"(Тест модуля моніторингу)", //STR_SELFTEST_MONITORING
		u8R"(Тест змінн. струму)", //STR_SELFTEST_AC
		u8R"(Тест батареї)", //STR_SELFTEST_BATTERY
		u8R"(Тест часу)", //STR_SELFTEST_SYS_TIME
		u8R"(Тест витратоміра)", //STR_SELFTEST_FLOWMETER
		u8R"(Тест модуля AG/CO2)", //STR_SELFTEST_AG_CO2
		u8R"(Тест датчика O2)", //STR_SELFTEST_O2
		u8R"(Тест модуля SPO2)", //STR_SELFTEST_SPO2
		u8R"(Тест сенсор. екрана)", //STR_SELFTEST_TOUCHSCREEN
		u8R"(Випробування тиску подачі газу)", //STR_SELFTEST_GAS_SOURCE
		u8R"(Завантаження бази даних)", //STR_SELFTEST_DATABASE
		u8R"(Випробування клапана PEEP)", //STR_SELFTEST_EXHALATION_VALUE
		u8R"(Тест інгаляційного клапана)", //STR_SELFTEST_INHALATION_VALUE
		u8R"(Втрата зв'язку з витратоміром)", //STR_FLOWMETER_STOP
		u8R"(Невдача калібр. датчика моніторингу)", //STR_MONIT_SENSOR_CAL_FAIL
		u8R"(Точність моніторингу невисока)", //STR_MONITOR_ACCURACY_LOW
		u8R"(Помилка датчика витратоміра O2)", //STR_O2_FM_SENSOR_ERROR
		u8R"(Помилка датчика витратоміра N2O)", //STR_N2O_FM_SENSOR_ERROR
		u8R"(Помилка датчика витратоміра повітря)", //STR_AIR_FM_SENSOR_ERROR
		u8R"(Апаратна помилка витратоміра)", //STR_FM_HARDWARE_ERROR
		u8R"(Датчик SPO2 від'єднано.)", //STR_SPO2_SENSOR_UNUNITED
		u8R"(Від’єднано датчик AG)", //STR_AG_ERROR
		u8R"(Від’єднано датчик O2)", //STR_O2_CONCENTRATION_ERROR
		u8R"(Сенсорний екран натиснуто)", //STR_TOUCH_SCREEN_HOLDBACK
		u8R"(Помилка сист. часу)", //STR_SYS_TIME_ERR
		u8R"(Надмірні витоки в контурі)", //STR_BREATH_LEAK_HIGH
		u8R"(Помилка батареї)", //STR_BATTERYERR
		u8R"(Результат)", //STR_RESULT
		u8R"(Пройдено)", //STR_PASS
		u8R"(Робоча подія)", //STR_OPER_EVENT
		u8R"(Детальна інформація)", //STR_DETAIL_INFO
		u8R"(Увімкнення)", //STR_SYS_START
		u8R"(Виберіть режим)", //STR_RUNMODE_CHAGNE
		u8R"(Змінено режим вентиляції)", //STR_VENTMODE_CHAGNE
		u8R"(Змінено верхню межу тривоги)", //STR_VENT_PARAM_CHANGE
		u8R"(Змінити верхню межу тривоги)", //STR_HIGH_ALARM_LIMIT_CHANGE
		u8R"(Змінити нижню межу тривоги)", //STR_LOW_ALARM_LIMIT_CHANGE
		u8R"(Змінено парам. режиму HLM)", //STR_HLM_PARAM_CHANGE
		u8R"(Модель продукту)", //STR_PRODUCT_MODE
		u8R"(Серійний номер)", //STR_SN
		u8R"(Дата виробництва)", //STR_P_DATE
		u8R"(Версія ПЗ)", //STR_S_VERION
		u8R"(Пропустити)", //STR_SKIP
		u8R"(Натисніть кнопку ‘Start’, щоб розпочати калібрування.)", //STR_CAL_START
		u8R"(Виконується калібрування потоку...)", //STR_CAL_IN_FLOW_PROC
		u8R"(Помилка калібрування потоку.)", //STR_CAL_FLOW_FAIL
		u8R"(Калібрування потоку виконано успішно.)", //STR_CAL_FLOW_DONE
		u8R"(Калібрування тиску...)", //STR_CAL_IN_PEEP_PROC
		u8R"(Успішне калібр. тиску.)", //STR_CAL_PEEP_DONE
		u8R"(Невдале калібр. тиску.)", //STR_CAL_PEEP_FAIL
		u8R"(Експорт записів)", //STR_EXPRT_DATA
		u8R"(Експорт даних)", //STR_READ_CAL
		u8R"(Натисніть кнопку 'Start', щоб почати експорт даних калібрування.)", //STR_EXPRT_START
		u8R"(Калібр. витратоміра)", //STR_FLOWMETER_CAL
		u8R"(Input 0 to start Calibration)", //STR_FLOWMETER_CAL_START
		u8R"(Cal data)", //STR_FM_CAL_DATA
		u8R"(Data calibration failed. Please try this point again.)", //STR_FM_CAL_FAIL
		u8R"(Current point done, please input the next point.)", //STR_FM_CAL_DONE_NEXT
		u8R"(Flowmeter calibration is completed, please select 'OK' or 'CANCEL'.)", //STR_FM_CAL_FINISH
		u8R"(Flowmeter calibration is in progress...)", //STR_FM_CAL_WAIT
		u8R"(Flowmeter Calibration Successful.)", //STR_FM_CAL_SUCCEED
		u8R"(IP-адреса)", //STR_IPADDR
		u8R"(Маска)", //STR_NETMASK
		u8R"(Шлюз)", //STR_GATEWAY
		u8R"(MAC)", //STR_MACADDR
		u8R"(Підтвердити)", //STR_CONFIRM_NET
		u8R"(.)", //STR_DOT
		u8R"(KEYCODE)", //STR_KEYCODE
		u8R"(ОНОВЛЕННЯ)", //STR_FIRMUPDATE
		u8R"(Демо)", //STR_SOFT_MODE
		u8R"(Порада: Перезапустіть систему!)", //STR_SOFT_MODE_SWITCHED_TIP
		u8R"(Демо режим)", //STR_SOFT_DEMO_MODE
		u8R"(Режим роботи)", //STR_SOFT_RUN_MODE
		u8R"(Калібр.)", //STR_CALI
		u8R"(Калібрування триває...  Натисніть програмну клавішу для скасування)", //STR_CALI_PROC
		u8R"(Конфіг. ПЗ)", //STR_SOFT_CFG_TITLE
		u8R"(Помилка коду конфігурації)", //STR_SOFT_CFG_INPUT_TIP
		u8R"(Конфігурація не вдалася)", //STR_SOFT_CFG_FAIL
		u8R"(Конфігурацію виконано успішно)", //STR_SOFT_CFG_SUCCEED
		u8R"(Яскравість екрану)", //STR_MAINMENU_EQUIPSET_LCD
		u8R"(Потік O2 ненормальний!)", //STR_O2_FLOW_INEXACT
		u8R"(Потік балансового газу ненормальний!)", //STR_BANLANCE_FLOW_INEXACT
		u8R"(Немає свіжого газу!!)", //STR_FLOW_TOO_LOW
		u8R"(Помилка постачання O2!)", //STR_O2_PRESS_LOW
		u8R"(Помилка постачання N2O!!)", //STR_N2O_PRESS_LOW
		u8R"(Помилка постачання повітря!!)", //STR_AIR_PRESS_LOW
		u8R"(Датчик O2 не читає дані калібрування!!)", //STR_FLOWMETER_O2_SENSOR_ERROR
		u8R"(Датчик N2O не читає дані калібрування!!)", //STR_FLOWMETER_N2O_SENSOR_ERROR
		u8R"(Датчик повітря не читає дані калібрування!!)", //STR_FLOWMETER_AIR_SENSOR_ERROR
		u8R"(Система керування резервним копіюванням увімкнена!!)", //STR_BACKUP_FLOW_CONTROL_OPEN
		u8R"(Історія)", //STR_HISTORY_DATA
		u8R"(Налашт.)", //STR_SETTINGS
		u8R"(Заморозити)", //STR_FREEZING
		u8R"(Трив вимк)", //STR_ALARM_MUTE
		u8R"(Інфо петлі)", //STR_LOOPINFO
		u8R"(Остан. перевірка сист.)", //STR_LATEST_SELFTEST
		u8R"(Попер. налаштув.)", //STR_PRE_STEP
		u8R"(Наступ. налаштув.)", //STR_NEXT_STEP
		u8R"(Готово)", //STR_ACCOMPLISH
		u8R"(Стоп)", //STR_STOP
		u8R"(Продовж.)", //STR_CONTINUE
		u8R"(Ви впевнені,що хочете пропустити перевірку системи?)", //STR_SELFTEST_SKIP_TIP
		u8R"(Тест герметичності)", //STR_MANUAL_LEAK_CHECK
		u8R"(1. Переконайтеся, що система перебуває в режимі очікування й що перемикач ручної/механічної вентиляції знаходиться в Ручному положенні.
2. Під’єднайте ручний мішок до порту ручного мішка.
3. Під’єднайте Y-образний патрубок до заглушки для перевірки герметичності, щоб перекрити вихід повітря.
4. Відрегулюйте ручку клапана APL на 70 см вод.ст.
5. Відрегулюйте швидкість потоку O2 до 0,15 л/хв.
6. Натисніть кнопку промивання O2, щоб підвищити тиск у дихальних шляхах приблизно до 30 см вод.ст.
7. Відпустіть кнопку промивання O2. Спостерігайте за манометром тиску в дихальних шляхах.)", //STR_MANUAL_LEAK_CHECK_TIP
		u8R"(Тест газового контуру)", //STR_GAS_CIRCUIT_CHECK
		u8R"(1. Переконайтеся, що дихальна система правильно під’єднана, а дихальні трубопроводи не пошкоджені. Переконайтеся, що дихальна система зафіксована.
2. Перевірте, чи правильно встановлена каністра з поглиначем CO2.
3. Перевірте, чи не змінюється колір натрію хлориду, якщо так, негайно замініть натрій хлориду.)", //STR_GAS_CIRCUIT_CHECK_TIP
		u8R"(Тест витратоміра)", //STR_FLOWMETER_CHECK
		u8R"(1. Переконайтеся, що система газопостачання під’єднана належним чином і тиск в ній нормальний. Показання витратоміра дорівнюють нулю, коли ручка витратоміра встановлена на нуль.
2. Переконайтеся, що внутрішня стінка однотрубного витратоміра не забруднена.)", //STR_FLOWMETER_CHECK_TIP
		u8R"(Перевірка випарника)", //STR_ANESTHETIC_GAS_EVAPORATOR_CHECK
		u8R"(1. Переконайтеся, що випарник знаходиться в правильному заблокованому положенні.
2. Переконайтеся, що випарник відрегульований на 0.
3. Переконайтеся, що випарник заповнений до нормального рівня.
4. Переконайтеся, що випарник надійно заповнений і заблокований.)", //STR_ANESTHETIC_GAS_EVAPORATOR_CHECK_TIP
		u8R"(Секундомір)", //STR_TIMER
		u8R"(Тайм звр відлік)", //STR_COUNTDOWN
		u8R"(Межа тривоги вентилятора)", //STR_VENTILATOR_ALARM_LIMIT
		u8R"(Межа тривоги AG)", //STR_ANESTHETIC_GAS_ALARM_LIMIT
		u8R"(Сигнал тривоги)", //STR_CUR_ALARM
		u8R"(4 криві)", //STR_FOUR_WAVE
		u8R"(3 криві)", //STR_THREE_WAVE
		u8R"(Петлі)", //STR_LOOP
		u8R"(Контроль свіжого газу)", //STR_FLOW_CONTROL_MENU
		u8R"(Швидкий набір)", //STR_QUICK_SET
		u8R"(Режим керування)", //STR_CONTROL_MODE
		u8R"(Загал. потік)", //STR_TOTAL_FLOW_CONTROL
		u8R"(Прямий потік)", //STR_SINGLE_PIPE_FLOW_CONTROL
		u8R"(Балансовий газ)", //STR_BALANCE_FLOW
		u8R"(Графічні тренди)", //STR_TREND_GRAPH
		u8R"(Хвилини)", //STR_MINUTE
		u8R"(Попер. подія)", //STR_PRE_EVENT
		u8R"(Наступ. подія)", //STR_NEXT_EVENT
		u8R"(Список трендів)", //STR_TREND_TABLE
		u8R"(ВСІ)", //STR_ALL_PARAMS
		u8R"(Парам. тиску)", //STR_PRESSURE_PARAMS
		u8R"(Парам. гучності)", //STR_VOLUME_PARAMS
		u8R"(Парам. часу)", //STR_TIME_PARAMS
		u8R"(Парам. витратоміра)", //STR_FLOWMETER_PARAMS
		u8R"(Парам. Ag)", //STR_AG_PARAMS
		u8R"(Деталі Петлі)", //STR_LOOP_DETAIL
		u8R"(Зберегти Петлю)", //STR_SAVE_LOOP
		u8R"(Видалити)", //STR_DELETE_LOOP
		u8R"(Показати посилання)", //STR_SWITCH_REFER_LOOP
		u8R"(Порівняти петлі)", //STR_COMPARE_LOOP
		u8R"(Петля V-F)", //STR_LOOP_VF
		u8R"(Петля P-V)", //STR_LOOP_PV
		u8R"(Петля F-P)", //STR_LOOP_FP
		u8R"(Підтвердити це як базову петлю?)", //STR_CONFIRM_REF_LOOP_TIP
		u8R"(Підтвердити видалення петлі?)", //STR_CONFIRM_DELETE_LOOP_TIP
		u8R"(Підтвердити як контрастну петлю?)", //STR_CONFIRM_COMPARE_LOOP_TIP
		u8R"(Історія подій)", //STR_HISTORY_EVENT
		u8R"(Дата)", //STR_DATE
		u8R"(Всі події)", //STR_ALL_EVENT
		u8R"(Висока тривога)", //STR_HIGH_LEVEL_ALARM
		u8R"(Середня тривога)", //STR_MID_LEVEL_ALARM
		u8R"(Низька тривога)", //STR_LOW_LEVEL_ALARM
		u8R"(Підказка)", //STR_PROMPT
		u8R"(Виконати)", //STR_OPERATE
		u8R"(Успішно)", //STR_SUCCESS
		u8R"(Гучність/Яскравість)", //STR_VOL_BRIGHT
		u8R"(Налаштув. екрана)", //STR_UI_SETTING
		u8R"(Обслуговування)", //STR_FACTORY_MAINTENANCE
		u8R"(Мова)", //STR_LANGUAGE
		u8R"(Russian)", //STR_LANGUAGE_RUS
		u8R"(Chinese)", //STR_LANGUAGE_CHN
		u8R"(English)", //STR_LANGUAGE_ENG
		u8R"(Ukrainian)", //STR_LANGUAGE_UKR
		u8R"(French)", //STR_LANGUAGE_FRE
		u8R"(Протокол)", //STR_PROTOCAL
		u8R"(Налаштув. протоколу)", //STR_PROTOCAL_SETTINGS
		u8R"(Послідовний протокол)", //STR_SERIAL_PROTOCAL
		u8R"(Немає)", //STR_NONE
		u8R"(Швидкість передачі даних)", //STR_BAUD_RATE
		u8R"(Біти даних)", //STR_DATA_BITS
		u8R"(Парність)", //STR_VERIFY
		u8R"(Непарні)", //STR_ODD
		u8R"(Парні)", //STR_EVEN
		u8R"(Стоп-біти)", //STR_STOP_BIT
		u8R"(Мережевий протокол)", //STR_NETWORK_PROTOCAL
		u8R"(Маска підмережі)", //STR_SUBNET_MASK
		u8R"(Стан)", //STR_LINK_STATE
		u8R"(Під’єднано)", //STR_CONNECTED
		u8R"(Від’єднано)", //STR_DISCONNECTED
		u8R"(Інтервал)", //STR_SEND_INTERVAL
		u8R"(Цільовий IP)", //STR_TARGET_IP
		u8R"(Цільовий порт)", //STR_TARGET_PORT
		u8R"(Підтвердити під’єднання?)", //STR_CONFIRM_CONNECT_TIP
		u8R"(Підтвердити від’єднання?)", //STR_CONFIRM_DISCONNECT_TIP
		u8R"(Сист. інфо)", //STR_SYSTEM_INFO
		u8R"(Інфо конфіг)", //STR_CONFIG_INFO
		u8R"(Активовано)", //STR_CONFIGURED
		u8R"(Лінія Plimit)", //STR_PLIMIT_LINE
		u8R"(Увімкн.)", //STR_SHOW
		u8R"(Вимкн.)", //STR_HIDE
		u8R"(Намал. хвилю)", //STR_DRAW_CURVE
		u8R"(Крива)", //STR_DRAW_LINE
		u8R"(Заповн.)", //STR_FILL_AREA
		u8R"(Швидкість розгортки)", //STR_SWEEP_SPEED
		u8R"(Нуль витратомір)", //STR_FLOWMETER_ZERO_CAL
		u8R"(Почати вентиляцію)", //STR_START_VENTILATION
		u8R"(Назад)", //STR_RETURN
		u8R"(Вибрати базову петлю)", //STR_SWITCH_BASE_LOOP
		u8R"(Виявлено лише одну батарею)", //STR_ONLY_ONE_BATTER
		u8R"(Пошкоджено базу даних!!)", //STR_DB_FAIL
		u8R"(Датчик AG нагрівається)", //STR_AG_WARMING
		u8R"(Датчик CO2 нагрівається)", //STR_CO2_WARMING
		u8R"(Таймер досяг максимальної межі)", //STR_TIMER_MAX
		u8R"(Відлік закінчився)", //STR_COUNTDOWN_END
		u8R"(Калібрування доступне лише в режимі очікування)", //STR_CALIB_USE_ONLY_STANBY
		u8R"(Увійти в обслуговування)", //STR_ENTER_MAINTENANCE
		u8R"(Обслуговування доступне лише в режимі очікування)", //STR_MAINTENANCE_USE_ONLY_STANBY
		u8R"(Завод налашт)", //STR_P_SETTINGS
		u8R"(Віднов завод налашт)", //STR_RESTORE_FACTORY_SETTING
		u8R"(Злам)", //STR_BREAKDOWN
		u8R"(Нормально)", //STR_NORMAL
		u8R"(Заверш. видалення)", //STR_END_DELETE
		u8R"(Заверш. перемик.)", //STR_END_SWITCH
		u8R"(Виконайте калібрування нуля наступним чином)", //STR_ZERO_CAL_TIP
		u8R"(Змінити режим вент.)", //STR_CHANGE_VENT_MODE
		u8R"(Перев врх межі)", //STR_OUT_OF_UPPER_LIMIT
		u8R"(Перев нижн межі)", //STR_OUT_OF_LOWER_LIMIT
		u8R"(Введене значення неправильне)", //STR_FORMAT_ERROR
		u8R"(Неправильне значення)", //STR_INVALID_VALUE
		u8R"(Калібрування O2 скасовано; поверніться до системи дихального контуру, як показано на рисунку.)", //STR_O2_CAL_CANCEL
		u8R"(Очікування)", //STR_HOTKEY_STANDBY
		u8R"(Затримка вимкнення)", //STR_SHUTDOWN_DELAY
		u8R"(Газ)", //STR_EXTERNAL_MODULE
		u8R"(Механіка дихання)", //STR_BREATHING_MECHANICS_PARAM
		u8R"(Вент. функція)", //STR_VENTILATORY_FUNCTION_PARAM
		u8R"(Змінити пароль)", //STR_CHANGE_PASSWORD
		u8R"(Введіть новий пароль)", //STR_ENTER_NEW_PASSWORD
		u8R"(Підтвердити новий пароль)", //STR_NEW_PASSWORD_CONFIRM
		u8R"(Підтвердити зміну)", //STR_CONFIRM_CHANGE
		u8R"(*Пароль може містити від нуля до шести символів,усі символи мають бути цифрами.)", //STR_PASSWORD_TIP
		u8R"(Помилка формату введення пароля)", //STR_PASSWORD_INPUT_FAULT_TIP
		u8R"(Підтвердити щоб змінити пароль?)", //STR_PASSWORD_CHANGE_TIP
		u8R"(Головна плата управління)", //STR_MAIN_CONTROL_BOARD
		u8R"(Плата монітора)", //STR_MONITOR_BOARD
		u8R"(U-Boot)", //STR_U_BOOT
		u8R"(Файлова система Linux)", //STR_LINUX_FILE_SYSTEM
		u8R"(Ядро Linux)", //STR_LINUX_KERNEL
		u8R"(Плата витратоміра)", //STR_FLOWMETER_BOARD
		u8R"(Плата живлення)", //STR_POWER_BOARD
		u8R"(Vt650)", //STR_VT_650
		u8R"(VtPlus)", //STR_VT_PLUS
		u8R"(VtMini)", //STR_VT_MINI
		u8R"(Модель пристрою для калібрування)", //STR_VT_MODEL_CHOICE
		u8R"(Прослуховування)", //STR_AUDITION
		u8R"(Введіть ваш пароль)", //STR_ENTER_PASSWORD
		u8R"(Тест сенсорної панелі)", //STR_TOUCH_PAD
		u8R"(Сенсорний екран)", //STR_TOUCH_SCREEN
		u8R"(Немає свіжого газу)", //STR_FLOW_TOO_LOW_PROMPT
		u8R"(Змінити)", //STR_CHANGE
		u8R"(Почати вентиляцію.Поточний режим вентиляції)", //STR_LOG_ENTER_VENT_MODE
		u8R"(Затримка вимкнення)", //STR_LOG_SHUTDOWN_DELAY
		u8R"(Скасувати затримку вимкнення)", //STR_LOG_CANCEL_SHUTDOWN_DELAY
		u8R"(Механічна вентиляція)", //STR_VENT
		u8R"(Межа тривоги за замовчуванням)", //STR_ALARM_LIMIT_RESTORE_DEFAULT
		u8R"(Змінити режим керування)", //STR_CHANGE_CONTROL_MODE
		u8R"(Змінити режим балансу)", //STR_CHANGE_BALANCE_MODE
		u8R"(Змінити значення потоку)", //STR_CHANGE_FLOW_VALUE
		u8R"(Оновити до)", //STR_UPGRADE
		u8R"(CENAR-30M)", //STR_CENAR_30M
		u8R"(Датчик AG)", //STR_SELFTEST_AG
		u8R"(Датчик CO2)", //STR_SELFTEST_CO2
		u8R"(Змінити параметр режиму вентиляції)", //STR_CHANGE_VENT_MODE_PARAM
		u8R"(Змінити дату)", //STR_CHANGE_SYSTEM_DATE
		u8R"(Змінити час)", //STR_CHANGE_SYSTEM_TIME
		u8R"(Помилка пароля)", //STR_PASSWORD_ERROR_TIP
		u8R"(Два паролі не збігаються)", //STR_PASSWORD_DONOT_MATCH
		u8R"(Змінити одиницю Paw)", //STR_SWITCH_PAW_UNIT
		u8R"(Одиниця виміру концентрації CO2 перемикача)", //STR_SWITCH_CO2_UNIT
		u8R"(AM)", //STR_TIME_AM
		u8R"(PM)", //STR_TIME_PM
		u8R"(Відновлення граничної межі тривоги апарата ШВЛ за замовчуванням)", //STR_VENTILATOR_LIMIT_RESTORE_DEFAULT
		u8R"(Межа анестетика за замовчуванням)", //STR_ANESTHETIC_GAS_LIMIT_RESTORE_DEFAULT
		u8R"(Налаштування таймера не може перевищувати)", //STR_TIMER_LIMIT
		u8R"(Перевищує межу тривоги)", //STR_INPUT_ALARM_LIMIT
		u8R"(Код конфігурації)", //STR_CFG_CODE
		u8R"(Калібр. PV витратоміра)", //STR_PV_CAL
		u8R"(Залишилось до вимкнення)", //STR_SHUTDOWN_TIME
		u8R"(Щоб скасувати завершення роботи, увімкніть системний перемикач знову)", //STR_SHUTDOWN_TIP
		u8R"(Деактивовано)", //STR_NOT_CONFIG
		u8R"(Введення)", //STR_IN
		u8R"(Вихід)", //STR_OUT
		u8R"(Від’єднано датчик CO2)", //STR_CO2_ERROR
		u8R"(Increase Flow)", //STR_ADD_FLOW
		u8R"(Decrease Flow)", //STR_REDUCE_FLOW
		u8R"(НАДІСЛАТИ)", //STR_SEND
		u8R"(Заводські налаштування відновлено,перезапустіть машину)", //STR_RESTORE_TIP
		u8R"(Змініть IP-адресу на ************ для калібрування витрати.Натисніть OK, щоб змінити IP-адресу.)", //STR_FLOW_TIP
		u8R"(Зачекайте,виконується занулення модуля AG)", //STR_AG_ZEROING_TIP
		u8R"(Калібрування недійсне в ручному режимі або режимі ACGO)", //STR_MENUAL_ACGO_STATE_TIP
		u8R"(Зв'язок із датчиком потоку O2 втрачено)", //STR_O2_FM_SENSOR_COMMUNICATION_STOP
		u8R"(Помилка датчика потоку балансового газу)", //STR_BALANCE_GAS_FM_SENSOR_ERROR
		u8R"(Зв’язок із датчиком системи керування резервним потоком втрачено)", //STR_BACKUP_FLOW_CONTROL_COMMUNICATION_STOP
		u8R"(Адаптер датчика CO2 не встановлено!)", //STR_TECHALARM_CO2_ADAPTOR_ERR
		u8R"(1. Переконайтеся, що система газопостачання під'єднана правильно, тиск в нормі, а потік знаходиться на найнижчому рівні, коли ручка витратоміра закрита (мінімальне значення)2. Переконайтеся, що поплавок знаходиться в найнижчій точці, коли ручка витратоміра знаходиться на позначці 0,
2. Переконайтеся, що на внутрішній стінці скляної трубки витратоміра немає бруду.)", //STR_FLOWMETER_CHECK_TIP_FOR_80
		u8R"(Ручний/ACGO режим блокує тестування витоку)", //STR_MENUAL_ACGO_LEAK_STATE_TIP
		u8R"(1. Переконайтеся, що система газопостачання під’єднана правильно, тиск в нормі, а потік знаходиться на найнижчому рівні, коли ручка витратоміра закрита (мінімальне значення)
2.Переконайтеся, що внутрішня стінка однотрубного витратоміра чиста.)", //STR_FLOWMETER_CHECK_TIP_FOR_C30M
		u8R"(Калібр.я нуля датчик AG)", //STR_AG_ZERO_CAL
		u8R"(Калібр. нуля датчик CO2)", //STR_CO2_ZERO_CAL
		u8R"(Виконується калібрування нуля датчика CO2, зачекайте)", //STR_CO2_ZEROING_TIP
		u8R"(Перев)", //STR_OVER
		u8R"(Врх межа трив)", //STR_ALARM_HIGH_LIMIT
		u8R"(Ниж межа трв)", //STR_ALARM_LOW_LIMIT
		u8R"(Прямий)", //STR_DIRECT_FLOW
		u8R"(Please enter the calibration value!)", //STR_EMPTY_INPUT_TIP
		u8R"(Please enter the valid calibration value!)", //STR_INVALID_INPUT_TIP
		u8R"(C)", //STR_SERIAL_C
		u8R"(E)", //STR_SERIAL_E
		u8R"(Калібрування 21%)", //STR_21PO2CAL
		u8R"(Калібрування 100%)", //STR_100PO2CAL
		u8R"(Налаштув. Сигнали)", //STR_WAVEFORMS_SETTINGS
		u8R"(Вимкнути)", //STR_SHUT_DOWN
		u8R"(Поп екзамен)", //STR_PREOPERATIVE_CHECK
		u8R"(Конц СО2 завелика)", //STR_CO2_CANNOT_ZERO_TIP
		u8R"(Gas source configuration)", //STR_FLOWMETER_GAS_CONFIG
		u8R"(Flowmeter board type)", //STR_FLOWMETER_SENSOR_TYPE
		u8R"(Software update)", //STR_SOFTWARE_UPDATE
		u8R"(Update switch)", //STR_UPDATE_SWITCH
		u8R"(Update type)", //STR_UPDATE_TYPE
		u8R"(Previous version)", //STR_SRC_VERSION
		u8R"(Target version)", //STR_TARGET_VERSION
		u8R"(Update progress)", //STR_PROGRESS
		u8R"(Software needs to restart to complete the update, it will automatically restart later!)", //STR_UPDATE_FINISH_TIP
		u8R"(Restart immediately)", //STR_RESTART_NOW
		u8R"(Communication error during software transfer.)", //STR_UPDATE_COM_ERROR_TIP
		u8R"(Software verification failed during transfer; requesting retransmission.)", //STR_UPDATE_CRC_ERROR_TIP
		u8R"(Online configuration in progress...)", //STR_MACHINE_CONFIGURING
		u8R"(Device tree)", //STR_DEVICE_TREE
		u8R"(Custom update)", //STR_CUSTOM_UPDATE
		u8R"(Logo png)", //STR_LOGO_PNG
		u8R"(Logo bin)", //STR_LOGO_BIN
		u8R"(Daemon version)", //STR_DAEMON_VERSION
		u8R"(Online configuration completed)", //STR_UPDATE_CONFIG
		u8R"(Custom product model configuration completed)", //STR_UPDATE_PRODUCT_MODEL
		u8R"(Custom program)", //STR_UPDATE_CUSTOM_STEP
		u8R"(Software boot logo upgrade completed)", //STR_UPDATE_LOGO
		u8R"(Uboot boot logo upgrade completed)", //STR_UPDATE_LOGO_BIN
		u8R"(AG module type)", //STR_AG_MODULE_TYPE
		u8R"(Mainstream)", //STR_MAIN_STREAM
		u8R"(Sidestream)", //STR_SIDE_STREAM
		u8R"(Check AG sampling line!)", //STR_TECHALARM_AG_SAMPLING_ERR
		u8R"(Check CO2 sampling line!)", //STR_TECHALARM_CO2_SAMPLING_ERR
		u8R"(Need To Replace CO2 Sensor Adapter!!)", //STR_TECHALARM_CO2_REPLACE
		u8R"(AG sampling line clogged!!)", //STR_TECHALARM_AG_SAMPLING_CLOGGED
		u8R"(CO2 sampling line clogged!!)", //STR_TECHALARM_CO2_SAMPLING_CLOGGED
		u8R"(O2 flow rate cannot be lower than 0.2 L/min)", //STR_FLOWMETER_O2_FLOW_TIPS
		u8R"(O2 concentration cannot be lower than 25%)", //STR_FLOWMETER_O2_CONCENTRATION_TIPS

    };
}

