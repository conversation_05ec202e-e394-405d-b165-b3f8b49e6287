﻿#ifndef PARAMLABELBASE_H
#define PARAMLABELBASE_H

#include <QtWidgets/QWidget>
#include <QtWidgets/QLabel>
#include <QColor>


class LabelPro : public QLabel
{
    Q_OBJECT
public:
    explicit LabelPro(QWidget *parent=nullptr, Qt::WindowFlags f=Qt::WindowFlags());
    explicit LabelPro(const QString &text, QWidget *parent=nullptr, Qt::WindowFlags f=Qt::WindowFlags());

    void SetLabelBgColor(const QColor &color);

    void SetTextFontColor(const QColor& color);

    void SetTextFontSize(const size_t fontSize);

    void SetTextFontBold(bool flag);

//private:
    void SetTextFontFamily(QString family);
};


class ParamLabelBase : public QWidget
{
    Q_OBJECT

public:
    explicit ParamLabelBase(QWidget *parent = NULL);
    virtual ~ParamLabelBase() {}

    void SetRange(QString min,QString max);
    virtual void SetValue(QString value);
    virtual void SetName(QString name);
    void SetUnit(QString Unit);

    void SetNameColor(const QColor&);
    void SetUnitColor(const QColor&);
    void SetValueColor(const QColor&);
    void SetValueBackgroundColor(const QColor& color);
    void SetScaleColor(const QColor&);
    void SetLowLimitColor(const QColor&);
    void SetHighLimitColor(const QColor&);
    void SetValueBackgroundBlinkColor(const QColor&);


    void SetLabelBgColor(const QColor &);
    void SetIsOpenValueBackgroundBlink(bool isOpen);
    void SetLabelMinimumWidthByValueDigit(int digit);
    void SetLimitVisible();

    virtual void SetLabelScale(int) = 0;

signals:
    void SignalClicked();



protected:
    QSize CalculateLabelTextSize(const QLabel &, QString text);
    void ShowLimitOffIcon(QLabel&);

    void mousePressEvent(QMouseEvent *) override;

protected:
    LabelPro *mNameLabel{};
    LabelPro *mUnitLabel{};
    LabelPro *mValueLabel{};
    LabelPro *mHighLimitLabel{};
    LabelPro *mLowLimitLabel{};

    QColor mValueLabelColor{};
    QColor mValueLabelBlinkColor = Qt::transparent;

    QTimer *mTimer =  nullptr;
private:
    void InitUi();
};
#endif // PARAMLABELBASE_H
