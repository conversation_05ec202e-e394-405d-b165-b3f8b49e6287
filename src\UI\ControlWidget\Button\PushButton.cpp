#include "UiApi.h"
#include "PushButton.h"
#include "UiConfig.h"
#include "RichTextStyle.h"
#include "KeyConfig.h"


#define DEFALUT_WIDTH 135
#define DEFALUT_HEGIHT 42

#define FOCUS_BORDER_COLOR COLOR_BRIGHT_BLUE
#define BACK_ICON_PATH ":/button/grayBackGround1.png"

PushButton::PushButton(QWidget *parent) :
    QPushButton(parent)
{
    setFixedSize(DEFALUT_WIDTH,DEFALUT_HEGIHT);
    StyleSet::SetTextFont(this,FONT_M);
    NormalStyle();
    connect(this,&QPushButton::pressed,this,[=]()
    {
        PressStyle();
        emit SignalPress();
    });
    connect(this,&QPushButton::released,this,[=]()
    {
        NormalStyle();
        emit SignalRelease();
    });
}

PushButton::PushButton(const QString &text, QWidget *parent) :
    QPushButton(text, parent)
{
    setFixedSize(DEFALUT_WIDTH,DEFALUT_HEGIHT);
    StyleSet::SetTextFont(this,FONT_M);
    NormalStyle();
    connect(this,&QPushButton::pressed,this,[=]()
    {
        PressStyle();
        emit SignalPress();
    });
    connect(this,&QPushButton::released,this,[=]()
    {
        NormalStyle();
        emit SignalRelease();
    });
}

void PushButton::focusInEvent(QFocusEvent *ev)
{
    FocusStyle();
    emit SignalFocusIn();
    QPushButton::focusInEvent(ev);
}

void PushButton::focusOutEvent(QFocusEvent *ev)
{
    if(isEnabled())
    {
        NormalStyle();
    }
    else
    {
        DisableStyle();
    }
    emit SignalFocusOut();
    QPushButton::focusOutEvent(ev);
}

void PushButton::changeEvent(QEvent *event)
{
    if(event->type()==QEvent::EnabledChange)
    {
        if(isEnabled())
        {
            NormalStyle();
            if(qobject_cast<QWidget*>(parent())!=NULL)
                qobject_cast<QWidget*>(parent())->setFocusPolicy(Qt::StrongFocus);
        }
        else
        {
            DisableStyle();
            clearFocus();
        }
    }
    QPushButton::changeEvent(event);
}

void PushButton::keyPressEvent(QKeyEvent *ev)
{
    if (KEY_PRESS == ev->key())
    {
        animateClick();
    }
    else if(ev->key()!=Qt::Key_Enter&&ev->key()!=Qt::Key_Return)
    {
        QPushButton::keyPressEvent(ev);
    }
    else
        ev->ignore();
}

void PushButton::paintEvent(QPaintEvent *event)
{
    QPushButton::paintEvent(event);
    if(hasFocus())
    {
        QPainter p(this);
        p.setRenderHint(QPainter::Antialiasing);
        QPen pen(QColor(FOCUS_BORDER_COLOR),5);
        p.setPen(pen);
        p.drawRoundedRect(0,0,width(),height(),2,2);
    }
}

void PushButton::DisableStyle()
{
    StyleSet::SetTextColor(this,COLOR_DARK_GRAY);
    StyleSet::SetBackGroundIcon(this,BACK_ICON_PATH);
}

void PushButton::NormalStyle()
{
    StyleSet::SetTextColor(this,COLOR_BLACK);
    StyleSet::SetBackGroundIcon(this,BACK_ICON_PATH);
}

void PushButton::FocusStyle()
{

}

void PushButton::PressStyle()
{
    StyleSet::SetTextColor(this,COLOR_WHITE);
    StyleSet::SetBackgroundColor(this,COLOR_DARK_GRAY);
    StyleSet::SetBackGroundIcon(this,"");
}



/* **************************************  */
GasButton::GasButton(QWidget *parent) :
    PushButton(parent)
{
    setStyle(new RichTextStyle);
}

GasButton::GasButton(const QString &text, QWidget *parent) :
    PushButton(text, parent)
{
    setStyle(new RichTextStyle);
}


