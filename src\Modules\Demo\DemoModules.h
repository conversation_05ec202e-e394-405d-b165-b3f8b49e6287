﻿#ifndef __DEMOMODULES_H__
#define __DEMOMODULES_H__

#include <QtGui>

class DemoThread : public QThread
{
    Q_OBJECT
    enum DATA_TYPE
    {
        CO2_CURVE,
        PAW_CURVE,
        FLOW_CURVE,
        VOLUME_CURVE,

        AG_TYPE,

        VALUEDATA_VTE,
        VALUEDATA_VTI,
        VALUEDATA_MV,
        VALUEDATA_RATE,
        VALUEDATA_R,
        VALUEDATA_C,
        VALUEDATA_C20C,
        VALUEDATA_FSPN,
        VALUEDATA_MVSPN,
        VALUEDATA_IE,
        VALUEDATA_PPEAK,
        VALUEDATA_TOP_PAW,
        VALUEDATA_PPLAT,
        VALUEDATA_PEEP,
        VALUEDATA_PMEAN,

        VALUEDATA_FIO2,
        VALUEDATA_FICO2,
        VALUEDATA_ETCO2,
        VALUEDATA_FIN2O,
        VALUEDATA_ETN2O,
        VALUEDATA_FI_AA1,
        VALUEDATA_FI_AA2,
        VALUEDATA_FIHAL ,
        VALUEDATA_FIENF  ,
        VALUEDATA_FISEV  ,
        VALUEDATA_FIISO ,
        VALUEDATA_FIDES  ,
        VALUEDATA_ET_AA1,
        VALUEDATA_ET_AA2,
        VALUEDATA_ETHAL,
        VALUEDATA_ETENF ,
        VALUEDATA_ETSEV,
        VALUEDATA_ETISO ,
        VALUEDATA_ETDES,
        VALUEDATA_MAC,
        VALUEDATA_SPO2,
        VALUEDATA_PR,
        VALUEDATA_FLOWMETER_O2,
        VALUEDATA_FLOWMETER_AIR,
        VALUEDATA_FLOWMETER_N2O,
    };
public:
    static DemoThread *GetInstance()
    {
        static DemoThread sInstance;
        return &sInstance;
    }

    void CreateDemoDataFile();
signals:
    void SignalCurWavePeriodEnd();

private slots:
    void PutCurveData(int mode);
    void PutValueData(int mode);
    void RefurbishAg(int mode);
    void SlotOnRunModeChanged();


protected:
    void run();
private:
    void InitDemoData(int modeType,QString FileName);
    explicit DemoThread(QObject *parent = NULL);
    void InitDataForStrList(int modeType,QStringList value);
    void RefurbishData();
    void ResetOffset();
    void UpTriggerAlarmId();
private:
    QMap<int,QVector<QVector<int>>> mDemoData;
    QVector<int> mDemoSendIndex;
    QTimer *mDataRefurbishTimer{};
    QVector<int> mTriggerAlarmId;
    QDateTime mTriggerFileLastUpTime;
};

#endif // __DEMOMODULES_H__

