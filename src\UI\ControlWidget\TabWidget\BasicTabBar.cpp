﻿#include "BasicTabBar.h"
#include "UiApi.h"
#include "DebugLogManager.h"
#define HOVER_BORDER_PEN_SIZE   3
#define INVALID_TAB_INDEX       -1
#define HOVER_BORDER_RADIUS     5



const QPen BasicTabBar::sFocusRectPen = QPen(COLOR_BRIGHT_BLUE, HOVER_BORDER_PEN_SIZE);

BasicTabBar::BasicTabBar(QWidget *parent) :
    QTabBar(parent)
{
    m_HoverTab = 0;
    m_PressedOnBar = false;
    m_PressPoint = QPoint(0, 0);

    m_left = 0;
    m_top = 0;
    m_right = 0;
    m_bottom = 0;
    m_tabSpacing = 0;
    m_orientation = Qt::Horizontal;
}

bool BasicTabBar::IsPressCurIndex(QPoint pos)
{
    if(tabAt(pos) == currentIndex())
        return true;
    else
        return false;
}

//在点击左键时，记录点击的标签索引，真正的mousePressEvent后再发出
void BasicTabBar::mousePressEvent(QMouseEvent *ev)
{
    int clickedTab = tabAt(ev->pos());
    if (Qt::LeftButton == ev->button() && clickedTab != currentIndex())
    {
        m_PressedOnBar = true;
        m_PressPoint = ev->pos();
        setHoverTab(clickedTab);
    }
    if(clickedTab<0)
        clickedTab=currentIndex();
    emit SignalClickeOn(clickedTab);
}


//防止鼠标在窗体外点击，拖动到窗体内释放导致的错误
void BasicTabBar::mouseReleaseEvent(QMouseEvent *ev)
{
    int clickedTab = tabAt(ev->pos());
    if(m_PressedOnBar)
    {
        m_PressedOnBar = false;
        QMouseEvent m_mouseEv = QMouseEvent(QEvent::MouseButtonPress, m_PressPoint,
                                            Qt::LeftButton, Qt::LeftButton, Qt::NoModifier);
        //        setHoverTab(INVALID_TAB_INDEX);
        QTabBar::mousePressEvent(&m_mouseEv);
    }
}

void BasicTabBar::paintEvent(QPaintEvent *ev)
{
    QTabBar::paintEvent(ev);
    if(hasFocus())
    {
        QPainter painter(this);
        if((m_HoverTab >= 0) && (m_HoverTab != currentIndex()))
        {
            painter.setRenderHint(QPainter::Antialiasing);
            painter.setPen(sFocusRectPen);
            QRect borderRect = tabRect(m_HoverTab).adjusted(m_left + 1, m_top + 1, -m_right - 1, -m_bottom - 1);
            painter.drawRoundedRect(borderRect, HOVER_BORDER_RADIUS, HOVER_BORDER_RADIUS);
        }
    }
}

void BasicTabBar::setHoverTab(int index)
{
    if(index<0 || index>count()-1)
        return;
    m_HoverTab = index;
    update();
}
