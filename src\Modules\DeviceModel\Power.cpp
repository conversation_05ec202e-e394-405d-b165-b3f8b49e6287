﻿#include <unistd.h>
#include "Power.h"
#include "UiConfig.h"
#include "SystemSettingManager.h"
#include "UpgradeManager/UpgradeManager.h"

#ifdef __linux__
#define SERIALNAME "/dev/ttymxc1"
#else
#define SERIALNAME "com7"
#endif


#define BAUDRATE (9600)
#define STOPBIT QSerialPort::OneStop
#define PARITY QSerialPort::EvenParity

Power* Power::mObject {};
Power::Power(QObject *parent) : LowerProxyBase(parent)
{
    mSerialPort.setPortName(SERIALNAME);
    mSerialPort.setBaudRate(BAUDRATE);
    mSerialPort.setStopBits(STOPBIT);
    mSerialPort.setParity(PARITY);

    mPackDir = 0x15;

    if(!SetCommIODevice(&mSerialPort))
        DebugLog<<"Power serial connect error";
}

void Power::UnpackVersion(QByteArray data)
{
    if ( !data.isEmpty() )
    {
        UpgradeManager::GetInstance()->SetMachineVersion(POWER_BOARD, data);
    }
}

Power *Power::GetInstance()
{
    if(mObject==NULL)
        mObject= new Power;
    return mObject;
}

void Power::CloseSerial()
{
    if(mSerialPort.isOpen())
        mSerialPort.close();
}

Power::~Power()
{

}

StPowerInof Power::getCurPowerInfo()
{
    mMutex.lock();
    StPowerInof tempCurPowerInfo = mCurPowerInfo;
    mMutex.unlock();
    return tempCurPowerInfo;
}

bool Power::SendQueryVersion()
{
    QByteArray sendData;
    sendData.push_back((char)0);
    return SendData(POWER_QUERY_VERSION_ID,sendData);
}

void Power::UnpackData(int msgType,QByteArray recvData)
{
    if(msgType==POWER_INFO)
     {
         mMutex.lock();
         mCurPowerInfo.mPower1PCT = recvData[0];
         mCurPowerInfo.mPower2PCT = recvData[1];
         mCurPowerInfo.mPower1Statu = recvData.at(2)==255?0:recvData[2];
         mCurPowerInfo.mPower2Statu = recvData.at(3)==255?0:recvData[3];
         mCurPowerInfo.mAcStatu = recvData[4];
         mMutex.unlock();
     }
    else if(msgType==POWER_QUERY_VERSION_ID)
    {
        UnpackVersion(recvData);
    }

}
