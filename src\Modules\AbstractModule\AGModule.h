#ifndef AGMODULE_H
#define AGMODULE_H

#include <QSerialPort>
#include "LowerProxyBase.h"
#include "DataManager.h"
#include "SpiIODevice.h"
#include <QTimer>

enum E_WARMING_STATUS
{
    NO_CONNECT_AG,
    WARMING_UP,
    WARMING_FINISH
};

class AGModule : public CommProxyBase
{
    Q_OBJECT
public:
    static AGModule *GetInstance()
    {
        static AGModule mInstance;
        return  &mInstance;
    }

    void DemoSetAgType(int agType);
    void StartZeroCal();
    int  GetCurAgentType();
    int  GetSecondAgentType();
    void CloseSerial();

    QPair<MONITOR_PARAM_TYPE, MONITOR_PARAM_TYPE> AgTypeToParamType(unsigned int agTypeId);

    void ResetWarmingStatus();

signals:
    void SignalAgentTypeChanged(int newType);

protected:
    virtual  void SlotReadData();

private:
    explicit AGModule(QObject *parent = NULL);
    ~AGModule();

    void UpPack();

    bool isGasValid(unsigned char value);

private:
    char *mAgRecevBuff;
    int   mAgZeroCalFlag;

    int mCurAgent1Type;
    int mCurAgent2Type;
    int mPrevRegError1;
    int mPrevRegError2;
    int mPrevRegError3;
    unsigned char mPreAgStatus = AG_ZERO_STATUS_NORMAL;
    QIODevice * mIoPort;
    double mAgentMac[6]={1,0.77,1.7,1.15,2.1,7.3};
    E_WARMING_STATUS mWarmingStatus = NO_CONNECT_AG;
    QTimer* mWarmingTimer;

    bool mIsFirstConnect = true;
};

#endif // AGMODULE_H
