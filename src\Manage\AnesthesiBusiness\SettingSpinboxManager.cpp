﻿
#include "String/UIStrings.h"
#include "VentModeSettingManager.h"
#include "UnitManager.h"
#include "SettingSpinboxManager.h"
#include "ModeSheetBase.h"
#include "SettingSpinbox/SettingSpinbox.h"
#include "UnitManager.h"
#include "SystemConfigManager.h"
#include "DataLimitManager.h"

//每种spinbox的最大数目
#define MAX_SPN_COUNT_EACH_TYPE 7
#define MIN_VT_VALUE 15

#define VT_INPUT_REGEX "^[0-9]\\d{4}$"

QMap<int, ALL_MODESET_SPN_DISCRIPTOR> SettingSpinboxManager::mAllSpnInfo
{   /* key(settingdata_id)  */            /* settingdata_id  */                  /* name_id  */         /*unit category*/ /*Default unit*/ /*unit*/      /*value*/  /* prio_level*/   /* max  *//* min  */   /* scale  */  /* digit  */  /* step  */      /* str_min  */   /* str_max  */
    {SETTINGDATA_VT,                     { SETTINGDATA_VT,                     STR_SETTINGPARAM_VT,      NONE_CATEGORY,      U_ML,        U_ML,             500,    MODESET_LEVEL_5,  1500,     MIN_VT_VALUE,   1,        DIGIT_0,            5,              NULL,           NULL}},
    {SETTINGDATA_RATE,                   { SETTINGDATA_RATE,                   STR_SETTINGPARAM_RATE,    NONE_CATEGORY,      U_BPM,       U_BPM,            12,		MODESET_LEVEL_0,  100,      4,              1,        DIGIT_0,            1,              NULL,           NULL}},
    {SETTINGDATA_IE,                     { SETTINGDATA_IE,                     STR_SETTINGPARAM_IE,      NONE_CATEGORY,      U_MAX,       U_MAX,            110,  	MODESET_LEVEL_1,  190,      70,             1,        DIGIT_1,            5,              NULL,           NULL}},
    {SETTINGDATA_TITP,                   { SETTINGDATA_TITP,                   STR_SETTINGPARAM_TITP,    NONE_CATEGORY,      U_PERCENT,   U_PERCENT,        0,      MODESET_LEVEL_3,   60,      0,              1,        DIGIT_0,            5,              (char *)"OFF",  NULL}},
    {SETTINGDATA_TINSP,                  { SETTINGDATA_TINSP,                  STR_SETTINGPARAM_TINSP,   NONE_CATEGORY,      U_SECOND,    U_SECOND,         17,	    MODESET_LEVEL_2,  100,      1,              0.1,      DIGIT_1,            1,              NULL,           NULL}},
    {SETTINGDATA_SLOPE,                  { SETTINGDATA_SLOPE,                  STR_SETTINGPARAM_SLOPE,   NONE_CATEGORY,      U_SECOND,    U_SECOND,         20,	    MODESET_LEVEL_4,  200,      0,              0.01,     DIGIT_2,            5,              NULL,           NULL}},
    {SETTINGDATA_PINSP,                  { SETTINGDATA_PINSP,                  STR_SETTINGPARAM_PINSP,   PRESSURE_CATEGORY,  U_CMH2O,     U_CMH2O,          15,		MODESET_LEVEL_2,   70,      5,              1,        DIGIT_0,            1,              NULL,           NULL}},
    {SETTINGDATA_PLIMIT,                 { SETTINGDATA_PLIMIT,                 STR_SETTINGPARAM_PLIMIT,  PRESSURE_CATEGORY,  U_CMH2O,     U_CMH2O,          40,		MODESET_LEVEL_1,  100,      10,             1,        DIGIT_0,            1,              NULL,           NULL}},
    {SETTINGDATA_PEEP,                   { SETTINGDATA_PEEP,                   STR_SETTINGPARAM_PEEP,    PRESSURE_CATEGORY,  U_CMH2O,     U_CMH2O,          3,		MODESET_LEVEL_0,   30,      3,              1,        DIGIT_0,            1,              (char *)"OFF",  NULL}},
    {SETTINGDATA_PSUPP,                  { SETTINGDATA_PSUPP,                  STR_SETTINGPARAM_PSUPP,   PRESSURE_CATEGORY,  U_CMH2O,     U_CMH2O,          10,		MODESET_LEVEL_3,   70,      4,              1,        DIGIT_0,            1,              (char *)"OFF",  NULL}},
    {SETTINGDATA_IN_END_LEVEL,           { SETTINGDATA_IN_END_LEVEL,           STR_PSV_LEVEL,            NONE_CATEGORY,      U_PERCENT,   U_PERCENT,        25,     MODESET_LEVEL_MAX, 65,      5,              1,        DIGIT_0,            5,              NULL,           NULL}},\
    {SETTINGDATA_FLOW_TRIGGERVALUE,      { SETTINGDATA_FLOW_TRIGGERVALUE,      STR_SETTINGPARAM_FTRIG,   NONE_CATEGORY,      U_LPERMIN,   U_LPERMIN,        30,     MODESET_LEVEL_MAX, 150,     0,              0.1,      DIGIT_1,            5,              (char *)"OFF",  NULL}},
    {SETTINGDATA_PRESSURE_TRIGGERVALUE,  { SETTINGDATA_PRESSURE_TRIGGERVALUE,  STR_SETTINGPARAM_PTRIG,   PRESSURE_CATEGORY,  U_CMH2O,     U_CMH2O,          -2,     MODESET_LEVEL_MAX, 0,       -20,            1,        DIGIT_0,            1,              NULL,           (char *)"OFF"}},
    {SETTINGDATA_CPAP,                   { SETTINGDATA_CPAP,                   STR_SETTINGPARAM_CPAP,    PRESSURE_CATEGORY,  U_CMH2O,     U_CMH2O,          6,      MODESET_LEVEL_2,   40,      5,              1,        DIGIT_0,            1,              NULL,           NULL}},
    {SETTINGDATA_ASPHY,                  { SETTINGDATA_ASPHY,                  STR_BIO_ALARM,            NONE_CATEGORY,      U_MAX,       U_MAX,            0,      MODESET_LEVEL_MAX, 1,       0,              1,        DIGIT_0,            1,              (char *)"OFF",  (char *)"ON"}},
    {SETTINGDATA_MAX,                    {0,0,NONE_CATEGORY,0,0,0,MODESET_LEVEL_MAX,MODESET_LEVEL_MAX,0,0}}
};

static SettingSpinbox *sAllSpn[SETTINGDATA_MAX][MAX_SPN_COUNT_EACH_TYPE];

SettingSpinboxManager::SettingSpinboxManager()
{
    mAllSpnInfo[SETTINGDATA_VT].min_value = ConfigManager->GetConfig<short>(SystemConfigManager::MIN_VT_INDEX);

    connect(UnitManager::GetInstance(), &UnitManager::SignalCategoryUnitChanged, this, [this](CONVERTIBLE_UNIT_CATEGORY paramType, UNIT_TYPE , UNIT_TYPE newUnit){
        if (paramType == CONVERTIBLE_UNIT_CATEGORY::PRESSURE_CATEGORY)
        {
            SetUnitScheme(newUnit);
        }
    });
    SetUnitScheme(UnitManager::GetInstance()->GetCurPressureUnitType());
}

ALL_MODESET_SPN_DISCRIPTOR SettingSpinboxManager::GetModesetSpnDiscriptor(unsigned short index)
{
    if (index < SETTINGDATA_VT || SETTINGDATA_MAX <= index)
    {
        DebugAssert(0);
    }

    return mAllSpnInfo[index];
}

void SettingSpinboxManager::SetTrigMode(unsigned short trig_mode)
{
    int changedSpnType;
    int changedToSpnType;
    unsigned short changed_spn_count = 0;
    unsigned short changed_to_spn_count = 0;
    switch (trig_mode)
    {
    case TRIG_FLAG_FTRIG:
        changedSpnType = SETTINGDATA_PRESSURE_TRIGGERVALUE;
        changedToSpnType = SETTINGDATA_FLOW_TRIGGERVALUE;
        break;
    case TRIG_FLAG_PTRIG:
        changedSpnType = SETTINGDATA_FLOW_TRIGGERVALUE;
        changedToSpnType = SETTINGDATA_PRESSURE_TRIGGERVALUE;
        break;
    default:
        DebugAssert(0);
        return ;
        break;
    }
    for(int i=0;i<mAllModeSheet.count();i++)
    {
        mAllModeSheet[i]->UpTrigMode(trig_mode);
    }
    for (int i = 0; i < MAX_SPN_COUNT_EACH_TYPE; ++i)
    {//被改变的数量
        if (NULL == sAllSpn[changedSpnType][i])
        {
            break;
        }
        ++changed_spn_count;
    }
    for (int i = 0; i < MAX_SPN_COUNT_EACH_TYPE; ++i)
    {//已存在的目标类型的spn的数量
        if (NULL == sAllSpn[changedToSpnType][i])
        {
            break;
        }
        ++changed_to_spn_count;
    }
    for (int i = 0; i < changed_spn_count; ++i)
    {//转化成目标类型spn
        if (MAX_SPN_COUNT_EACH_TYPE == changed_to_spn_count)
        {//没有位置添加
            DebugAssert(0);
            break;
        }
        //        sAllSpn[changedToSpnType][changed_to_spn_count] = sAllSpn[changedSpnType][i];
        //改变spn属性，注册时会向[changedToSpnType]中添加
        RegisterModesetSpn(changedToSpnType, sAllSpn[changedSpnType][i]);
        //设置值
        T_MASTER_SET master_data;

        VentModeSettingManager::GetInstance()->GetSettingdata(master_data);
        short value =0;
        if(trig_mode==TRIG_FLAG_FTRIG)
            value = VentModeSettingManager::GetInstance()->GetDataFromSet(&master_data, SETTINGDATA_FLOW_TRIGGERVALUE);
        if(trig_mode==TRIG_FLAG_PTRIG)
            value = VentModeSettingManager::GetInstance()->GetDataFromSet(&master_data, SETTINGDATA_PRESSURE_TRIGGERVALUE);
        if (sAllSpn[changedToSpnType][i] ==NULL)
            DebugAssert(0);
        sAllSpn[changedToSpnType][i]->setValue(value);
        sAllSpn[changedSpnType][i]=NULL;
        ++changed_to_spn_count;
    }
}

void SettingSpinboxManager::SetUnitScheme(unsigned short unitType)
{
    UnitInfoSt pressureUnitInfo = UnitManager::GetInfoSt((UNIT_TYPE)unitType);
    double scale = 1.0;

    for (int i = 0; i < SETTINGDATA_MAX; ++i)
    {//修改描述表
        if (UnitManager::GetInstance()->InCategory(CONVERTIBLE_UNIT_CATEGORY::PRESSURE_CATEGORY, (UNIT_TYPE)mAllSpnInfo[i].mUnitId))
        {
            switch (pressureUnitInfo.digit)
            {
            case DIGIT_0:
                break;
            case DIGIT_1:
                scale = 0.1;
                break;
            case DIGIT_2:
                scale = 0.01;
                break;
            case DIGIT_3:
                scale = 0.001;
                break;
            case DIGIT_4:
                scale = 0.0001;
                break;
            default:
                break;
            }
            mAllSpnInfo[i].zoom_scale = scale;
            mAllSpnInfo[i].digit = pressureUnitInfo.digit;
            mAllSpnInfo[i].mUnitId = unitType;
            for (int j = 0; j < MAX_SPN_COUNT_EACH_TYPE; ++j)
            {
                if (sAllSpn[i][j])
                {
                    sAllSpn[i][j]->setUnit(pressureUnitInfo.mUnitStrId);
                    sAllSpn[i][j]->setDigit(pressureUnitInfo.digit);
                    sAllSpn[i][j]->setZoomScale(scale);
                    sAllSpn[i][j]->refreshText();
                }
            }
        }
    }
}


int SettingSpinboxManager::RegisterModesetSpn(unsigned short spnType, SettingSpinbox *spn)
{
    bool add_success = false;
    if (SETTINGDATA_MAX <= spnType || NULL == spn)
    {
        DebugAssert(0);
        return -1;
    }
    for (int i = 0; i < MAX_SPN_COUNT_EACH_TYPE; ++i)
    {
        if (sAllSpn[spnType][i] == spn)
        {//已注册，则不进行注册
            return -1;
        }
    }
    for (int i = 0; i < MAX_SPN_COUNT_EACH_TYPE; ++i)
    {
        if (NULL == sAllSpn[spnType][i])
        {//寻找空位进行注册
            sAllSpn[spnType][i] = spn;
            add_success = true;
            break;
        }
    }
    if (!add_success)
    {//没有位置注册
        DebugLog<<("register setting spinbox failure");
        return -1;
    }

    InitModeSetSpn(spnType, spn);

    return true;
}

void SettingSpinboxManager::InitModeSetSpn(unsigned short spnType, SettingSpinbox *spn)
{
    spn->setSettingDataId(mAllSpnInfo[spnType].setting_data_id);
    spn->setName(mAllSpnInfo[spnType].name_id);

    if((UNIT_TYPE)mAllSpnInfo[spnType].mUnitId != U_MAX)
    {
        auto unitStrId = UnitManager::GetStrId((UNIT_TYPE)mAllSpnInfo[spnType].mUnitId);
        spn->setUnit(unitStrId);
    }
    else
        spn->setUnit(ALL_STRINGS_ENUM::STR_NULL);

    if (NULL != mAllSpnInfo[spnType].str_min)
    {
        spn->setMinStr(mAllSpnInfo[spnType].str_min);
    }
    else
        spn->setMinStr("");
    if (NULL != mAllSpnInfo[spnType].str_max)
    {
        spn->setMaxStr(mAllSpnInfo[spnType].str_max);
    }
    else
        spn->setMaxStr("");

    spn->setZoomScale(mAllSpnInfo[spnType].zoom_scale);
    spn->setStep(mAllSpnInfo[spnType].step);
    spn->setBaseMinMax(mAllSpnInfo[spnType].min_value, mAllSpnInfo[spnType].max_value);
    spn->SetRange(mAllSpnInfo[spnType].min_value, mAllSpnInfo[spnType].max_value, true);

    spn->setDigit(mAllSpnInfo[spnType].digit);
    spn->setValue(mAllSpnInfo[spnType].value);
    if(spnType == SETTINGDATA_VT)
        spn->SetPopupInputRegex(VT_INPUT_REGEX);
    if(spnType == SETTINGDATA_ASPHY)
    {
        if(ConfigManager->IsFullTouch())
            spn->SetPopupType(PopupAdapter::CHOICE_POPUB);
    }
    spn->refreshText();
}

void SettingSpinboxManager::InitAllRegisteredSpn()
{
    for (int i = 0; i < SETTINGDATA_MAX; i++)
    {
        for (int j = 0; j < MAX_SPN_COUNT_EACH_TYPE; j++)
        {
            if (sAllSpn[i][j])
            {
                InitModeSetSpn(i, sAllSpn[i][j]);
            }
        }
    }
}

void SettingSpinboxManager::GetParamInitRange(int paramType, short &min, short &max)
{
    max =mAllSpnInfo[paramType].max_value;
    min =mAllSpnInfo[paramType].min_value;
}

int SettingSpinboxManager::RegisterModeSheetBase(ModeSheetBase *curMode)
{
    mAllModeSheet << curMode;
    return 1;
}

