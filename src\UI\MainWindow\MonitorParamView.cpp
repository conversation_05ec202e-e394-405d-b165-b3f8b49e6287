﻿#include <QVBoxLayout>
#include "UiApi.h"
#include "MonitorParamView.h"
#include "SystemConfigManager.h"
#include "DataLimitManager.h"
#include "SystemSettingManager.h"
#include "ParamLabel/ParamLabelLayoutVertical.h"
#include "ParamLabel/ParamLabelLayoutHorizontal.h"


#define MARGIN 2
#define HIGHLIGHT_PARAM_BG_COLOR COLOR_T_LIGHT_GRAY

enum E_MONITOR_PARAM_POS
{
    PARAMLABEL_TOPAREA_TOP,
    PARAMLABEL_TOPAREA_MID,
    PARAMLABEL_TOPAREA_BOTTOM,
    PARAMLABEL_MIDAREA_TOP,
    PARAMLABEL_MIDAREA_MID,
    PARAMLABEL_MIDAREA_BOTTOM,
    PARAMLABEL_BOTTOMAREA_TOP,
    PARAMLABEL_BOTTOMAREA_MID,
    PARAMLABEL_BOTTOMAREA_BOTTOM,
    MONITOR_PARAM_MAX
};

MonitorParamView::MonitorParamView(QWidget *parent) : QFrame(parent)
{
    InitUi();
    connect(SettingManager, &SystemSettingManager::SignalSettingChanged, this, [this](int id)
    {
        if (id >= SystemSettingManager::VALUE_UPAREA_1 && id <= SystemSettingManager::VALUE_DOWNAREA_3)
        {
            short value = SettingManager->GetIntSettingValue((SystemSettingManager::SYSTEM_SETTING_ENUM)id);
            ChangeParam(id, value);
        }
    });
}

void MonitorParamView::InitUi()
{
    QFrame *topParamBlock = new QFrame;
    StyleSet::SetBackgroundColor(topParamBlock,COLOR_BLACK);

    auto topBlockLayout = new QVBoxLayout;
    topBlockLayout->setSpacing(0);
    topBlockLayout->setContentsMargins(MARGIN, 0, MARGIN, 0);
    topParamBlock->setLayout(topBlockLayout);

    QFrame *midParamBlock = new QFrame;
    StyleSet::SetBackgroundColor(midParamBlock,COLOR_BLACK);
    auto midBlockLayout = new QVBoxLayout;
    midBlockLayout->setSpacing(0);
    midBlockLayout->setContentsMargins(MARGIN, 0, MARGIN, 0);
    midParamBlock->setLayout(midBlockLayout);

    QFrame *lowParamBlock = new QFrame;
    StyleSet::SetBackgroundColor(lowParamBlock,COLOR_BLACK);
    auto lowBlockLayout = new QVBoxLayout;
    lowBlockLayout->setSpacing(0);
    lowBlockLayout->setContentsMargins(MARGIN, 0, MARGIN, 0);
    lowParamBlock->setLayout(lowBlockLayout);

    for (int i = 0; i < MONITOR_PARAM_MAX;  i++)
    {
        mParamLabels << new ParamLabelLayoutHorizontal;

        if (i < PARAMLABEL_MIDAREA_TOP)
        {

            topBlockLayout->addWidget(mParamLabels.last());
        }
        else if (i < PARAMLABEL_BOTTOMAREA_TOP)
        {
            midBlockLayout->addWidget(mParamLabels.last());
        }
        else
        {
            lowBlockLayout->addWidget(mParamLabels.last());
        }

        if (i % 3 == 0)
            mParamLabels.last()->SetLabelScale(ParamLabelLayoutHorizontal::LABEL_SCALE_ENUM::EXTRA);
        else
            mParamLabels.last()->SetLabelScale(ParamLabelLayoutHorizontal::LABEL_SCALE_ENUM::LARGE);
    }
    ParamLabelManage *p_param_manage = ParamLabelManage::GetInstance();
    SystemSettingManager *p_conf_manage = SettingManager;

    for(int i = SystemSettingManager::VALUE_UPAREA_1,j=PARAMLABEL_TOPAREA_TOP;i<=SystemSettingManager::VALUE_DOWNAREA_3;i++,j++)
    {
        MONITOR_PARAM_TYPE id = (MONITOR_PARAM_TYPE)p_conf_manage->GetIntSettingValue((SystemSettingManager::SYSTEM_SETTING_ENUM)i);
        p_param_manage->RegisterParamView(id, mParamLabels[j]);
    }
    QVBoxLayout *layoutMain = new QVBoxLayout;
    layoutMain->setMargin(0);
    layoutMain->setSpacing(2);
    layoutMain->setContentsMargins(0, 0, 0, 0);
    layoutMain->addWidget(topParamBlock);
    layoutMain->addWidget(midParamBlock);
    layoutMain->addWidget(lowParamBlock);

    setLayout(layoutMain);
}

QVector<int> MonitorParamView::GetpartitionXpos()
{
    QVector<int> re;
    for(int i=0;i<mPartitionSpacerItemVe.count();i++)
    {
        re.push_back(mPartitionSpacerItemVe[i]->geometry().center().y());
    }
    return  re;
}

//高亮或取消参数背景高亮
void MonitorParamView::HighlightParam(unsigned short pos, bool onOff)
{
    if (onOff)
    {
        mParamLabels[pos - SystemSettingManager::VALUE_UPAREA_1 + PARAMLABEL_TOPAREA_TOP]->SetLabelBgColor(HIGHLIGHT_PARAM_BG_COLOR);
    }
    else
    {
        mParamLabels[pos - SystemSettingManager::VALUE_UPAREA_1 + PARAMLABEL_TOPAREA_TOP]->SetLabelBgColor(Qt::transparent);
    }
}

void MonitorParamView::ChangeParam(short pos, short paramType)
{
    auto concreteLabel = mParamLabels[pos - SystemSettingManager::VALUE_UPAREA_1];
    ParamLabelManage::GetInstance()->RegisterParamView((MONITOR_PARAM_TYPE)paramType, concreteLabel);
}

void MonitorParamView::InitUiText()
{
}

