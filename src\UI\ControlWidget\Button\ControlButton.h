﻿#ifndef CONTROLBUTTON_H
#define CONTROLBUTTON_H

#include <QPushButton>

class CloseButton : public QPushButton
{
    Q_OBJECT
public:
    CloseButton(QWidget* parent = NULL);

protected:
    void paintEvent(QPaintEvent *event);
    void keyPressEvent(QKeyEvent *event);
};


class SureButton : public CloseButton
{
    Q_OBJECT
public:
    SureButton(QWidget* parent = NULL);
};

#endif // CONTROLBUTTON_H
