﻿#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QHBoxLayout>
#include <QSizePolicy>
#include <QMap>
#include <QtDebug>

#include "UiApi.h"
#include "ParamLabelLayoutHorizontal.h"



static constexpr unsigned int MINIMUM_WIDTH = 110;

const QMap<int, ParamLabelLayoutHorizontal::LabelScaleInfoSt> ParamLabelLayoutHorizontal::sScaleInfoMap
{
    { ParamLabelLayoutHorizontal::LABEL_SCALE_ENUM::MIN,  {35, MINIMUM_WIDTH, 12, 30}},
    { ParamLabelLayoutHorizontal::LABEL_SCALE_ENUM::SMALL,  {35, MINIMUM_WIDTH, 15, 30}},
    { ParamLabelLayoutHorizontal::LABEL_SCALE_ENUM::MID,    {40, MINIMUM_WIDTH, 15, 40}},
    { ParamLabelLayoutHorizontal::LABEL_SCALE_ENUM::LARGE,  {48, MINIMUM_WIDTH, 15, 48}},
    { ParamLabelLayoutHorizontal::LABEL_SCALE_ENUM::EXTRA,  {65, MINIMUM_WIDTH, 15, 65}}
};

ParamLabelLayoutHorizontal::ParamLabelLayoutHorizontal(QWidget *parent) : ParamLabelBase(parent)
{
    InitUi();
}

void ParamLabelLayoutHorizontal::InitUi()
{
    mNameLabel->SetTextFontBold(true);
    mHighLimitLabel->SetTextFontBold(true);
    mLowLimitLabel->SetTextFontBold(true);

    QSize limitSize = CalculateLabelTextSize(*mHighLimitLabel, "XXX.");
    mHighLimitLabel->setFixedWidth(limitSize.width());
    mLowLimitLabel->setFixedWidth(limitSize.width());

    mNameLabel->setSizePolicy(QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Fixed);
    mUnitLabel->setSizePolicy(QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Fixed);
    mValueLabel->setSizePolicy(QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

    QVBoxLayout *layoutLeft = new QVBoxLayout;
    layoutLeft->setContentsMargins(0, 0, 0, 0);
    layoutLeft->setSpacing(0);
    layoutLeft->addWidget(mNameLabel);
    layoutLeft->addWidget(mUnitLabel);
    layoutLeft->addStretch();
    mNameLabel->setAlignment(Qt::AlignLeft);
    mUnitLabel->setAlignment(Qt::AlignLeft);

    QVBoxLayout *layoutRight = new QVBoxLayout;
    layoutRight->setContentsMargins(0, 0, 0, 0);
    layoutRight->setSpacing(0);
    layoutRight->addWidget(mHighLimitLabel);
    layoutRight->addStretch();
    layoutRight->addWidget(mLowLimitLabel);
    mHighLimitLabel->setAlignment(Qt::AlignHCenter);
    mLowLimitLabel->setAlignment(Qt::AlignHCenter);

    QHBoxLayout *layoutMain = new QHBoxLayout;
    layoutMain->setContentsMargins(2, 0, 0, 0);
    layoutMain->setSpacing(1);
    layoutMain->addLayout(layoutLeft);
    layoutMain->addSpacing(4);
    layoutMain->addStretch();
    layoutMain->addWidget(mValueLabel);
    layoutMain->addLayout(layoutRight);
    layoutMain->setStretchFactor(layoutLeft, 1);
    layoutMain->setStretchFactor(mValueLabel, 8);
    layoutMain->setStretchFactor(layoutRight, 1);
    mValueLabel->setAlignment(Qt::AlignRight | Qt::AlignVCenter);


    StyleSet::SetBackgroundColor(this,COLOR_BLACK);

    StyleSet::SetBackgroundColor(mValueLabel,COLOR_TRANSPARENT);
    StyleSet::SetBackgroundColor(mNameLabel,COLOR_TRANSPARENT);
    StyleSet::SetBackgroundColor(mUnitLabel,COLOR_TRANSPARENT);
    StyleSet::SetBackgroundColor(mLowLimitLabel,COLOR_TRANSPARENT);
    StyleSet::SetBackgroundColor(mHighLimitLabel,COLOR_TRANSPARENT);


    setLayout(layoutMain);
}


void ParamLabelLayoutHorizontal::SetLabelScale(int scale)
{
    LabelScaleInfoSt st = sScaleInfoMap[scale];
    setFixedHeight(st.fixedHeight);
    setMinimumWidth(st.minimumWidth);

    mNameLabel->SetTextFontSize(st.normalFontSize);
    mUnitLabel->SetTextFontSize(st.normalFontSize);
    mHighLimitLabel->SetTextFontSize(st.normalFontSize);
    mLowLimitLabel->SetTextFontSize(st.normalFontSize);
    mValueLabel->SetTextFontSize(st.valueFontSize);
}
