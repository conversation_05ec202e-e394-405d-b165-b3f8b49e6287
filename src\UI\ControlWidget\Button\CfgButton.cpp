﻿#include "CfgButton.h"
#include "UiConfig.h"
#include "KeyConfig.h"
#include "String/UIStrings.h"

const int LEFT_ARROW_MARGIN = 3;     //左箭头距离边框的距离
const int RIGHT_ARROW_MARGIN = 5;    //右箭头距离边框的距离

TristateBtn::TristateBtn(QWidget *parent) :
    QLabel(parent), mState(e_Normal)
{
    NormalStyle();
    setFocusPolicy(Qt::StrongFocus);
    setAlignment(Qt::AlignCenter);
}

void TristateBtn::focusInEvent(QFocusEvent *)
{
    setFocusState();
}

void TristateBtn::focusOutEvent(QFocusEvent *)
{
    setNormalState();
}

void TristateBtn::mouseReleaseEvent(QMouseEvent *ev)
{
    if(ev->type()==QEvent::MouseButtonRelease)
    {
        if(e_Edit == mState)
        {
            setFocusState();
        }
        else
        {
            setEditState();
        }
    }
}

void TristateBtn::keyPressEvent(QKeyEvent *ev)
{
    switch(ev->key())
    {
    case KEY_PRESS:
        //处于编辑状态则确认设置，退出编辑状态
        if(e_Edit == mState)
        {
            setFocusState();
        }
        else    //开始编辑
        {
            setEditState();
        }
        break;
    default:
        ev->ignore();
        break;
    }
}

void TristateBtn::changeEvent(QEvent *event)
{
    if(event->type()==QEvent::EnabledChange)
    {
        if(isEnabled())
        {
            NormalStyle();
            update();
        }
        else
        {
            DisableStyle();
            update();
        }
    }
    QLabel::changeEvent(event);
}

void TristateBtn::setNormalState()
{
    int preState = mState;
    mState = e_Normal;
    if (m_preState != mState)
    {
        m_preState = mState;
        NormalStyle();
        update();
    }
    if(preState==e_Edit)
        emit SignalEndEdit();
}

void TristateBtn::setFocusState()
{
    int preState = mState;
    mState = e_Focus;
    if (m_preState != mState)
    {
        m_preState = mState;
        FocusStyle();
        update();
    }
    if(preState==e_Edit)
        emit SignalEndEdit();
}

void TristateBtn::setEditState()
{
    if(mEnableEdit)
    {
        mState = e_Edit;
        if (m_preState != mState)
        {
            m_preState = mState;
            EditStyle();
            update();
        }
        emit SignalBeginEdit();
    }
}

void TristateBtn::setEnableEdit(bool newEnableEdit)
{
    mEnableEdit = newEnableEdit;
}

void TristateBtn::NormalStyle()
{
    setProperty("state", "normal");
    style()->polish(this);
}

void TristateBtn::FocusStyle()
{
    setProperty("state", "focus");
    style()->polish(this);
}

void TristateBtn::EditStyle()
{
    setProperty("state", "edit");
    style()->polish(this);
}

void TristateBtn::DisableStyle()
{
    NormalStyle();
}


/* **************************************************  */
CfgButton::CfgButton(QWidget *parent, bool IsCloseRefreshPopup) :
    TristateBtn(parent), mInnerData(new commonInnerData), mIsCloseAutoRefrenshText(IsCloseRefreshPopup)
{
    InitPopupAdapter();
    InitConnectDateModel();
    InitUiText();
}

void CfgButton::SetPopupType(int type)
{
    DebugAssert(mPopup);
    mPopup->SetPopupType(type);
}

bool CfgButton::DataValidator(QString value,QString& errorStr)
{
    bool reValue = false;
    mInnerData->StringTovalue(value);
    int valueCheckRe = mInnerData->GetPrevStrToValueCheckeRe();
    if(valueCheckRe==IntraData::VALID)
        reValue = true;
    if(!reValue)
        errorStr = GetErrorValueTipStr(valueCheckRe);
    return reValue;
}

QString CfgButton::GetErrorValueTipStr(int checkFailType)
{
    return mErrorValueTipStr[checkFailType];
}

void CfgButton::setText(QString Text)
{
    QLabel::setText(Text);
    mPopup->UpPopupText(Text);
}

void CfgButton::setValue(int val)
{
    val = val + step() > mInnerData->maxValue() ? mInnerData->maxValue() : val;
    val = val < mInnerData->minValue() ? mInnerData->minValue() : val;
    mInnerData->setValue(val);
    refreshText();
}

void CfgButton::setDumbValue(int val)
{
    mInnerData->setDumbValue(val);
    refreshText();
}


QStringList CfgButton::GetAllValueStr()
{
    if (qobject_cast<ValueStrIntraData *>(mInnerData))
        return qobject_cast<ValueStrIntraData *>(mInnerData)->GetStrList();

    int value = minValue();
    QStringList reValue;

    if(mPopup->POPUB_TYPE::CHOICE_POPUB == mPopup->getPopupType())
    {
        while(value <= mInnerData->maxValue())
        {
            if (!valueToStr(value).isEmpty())
                reValue.push_back(valueToStr(value));
            value+=mInnerData->step();
        }
    }
    return reValue;
}

void CfgButton::SetRange(int minValue, int maxValue, bool dumb)
{
    mInnerData->SetRange(minValue, maxValue, dumb);
    refreshText();
    emit SignalRangeChanged();
}

void CfgButton::setStep(int step)
{
    mInnerData->setStep(step);
}

void CfgButton::setITimeTipStr(const QString &str)
{
    mPopup->SlotShowTip(str);
}

int CfgButton::step() const
{
    return mInnerData->step();
}

int CfgButton::value() const
{
    return mInnerData->value();
}

int CfgButton::minValue() const
{
    return mInnerData->minValue();
}

int CfgButton::maxValue() const
{
    return mInnerData->maxValue();
}

void CfgButton::setDataModel(IntraData *model)
{
    if (model == mInnerData)
    {
        return;
    }
    delete mInnerData;
    mInnerData = model;
    InitConnectDateModel();
}

void CfgButton::SetErrorValueTipStr(IntraData::VALUE_CHECK_TYPE checkFailType, QString str)
{
    mErrorValueTipStr[checkFailType] = str;
}

void CfgButton::SetPopupInputRegex(QString s)
{
    mPopup->SetPopupInputRegex(s);
}

void CfgButton::focusInEvent(QFocusEvent *ev)
{
    if(state()==e_Edit)
    {
       mInnerData->RestoreValue();
       refreshText();
    }

    TristateBtn::focusInEvent(ev);
}

void CfgButton::focusOutEvent(QFocusEvent *ev)
{
    mInnerData->RestoreValue();
    refreshText();
    TristateBtn::focusOutEvent(ev);
}
void CfgButton::paintEvent(QPaintEvent *ev)
{
    TristateBtn::paintEvent(ev);
    QPainter painter(this);
    drawTriangle(&painter);
    switch(state())
    {
    case e_Edit:
        if(mInnerData->value() > mInnerData->minValue())
        {
            drawLeftArrow(&painter);
        }
        if(mInnerData->value() < mInnerData->maxValue())
        {
            drawRightArrow(&painter);
        }
        else
        {
            break;
        }
        break;
    case e_Focus:
        painter.setPen(QPen(COLOR_BRIGHT_BLUE, 4));
        drawRect(&painter);
        break;
    default:
        break;
    }
}

void CfgButton::mouseReleaseEvent(QMouseEvent *ev)
{
    if(ev->type()==QEvent::MouseButtonRelease)
    {
        emit clicked();
        if(e_Edit == state())
        {
            mInnerData->RestoreValue();
            refreshText();
        }
        else
        {
            mInnerData->BackupCurValue();
        }
    }
    TristateBtn::mouseReleaseEvent(ev);
}

void CfgButton::keyPressEvent(QKeyEvent *ev)
{
    switch(ev->key())
    {
    case KEY_PRESS:
        //处于编辑状态则确认设置，退出编辑状态
        if(e_Edit == state())
        {
            mInnerData->confirmChange();
            mInnerData->RestoreValue();
            refreshText();
            TristateBtn::keyPressEvent(ev);
        }
        else    //开始编辑
        {
            emit clicked();
            mInnerData->BackupCurValue();
            TristateBtn::keyPressEvent(ev);
        }
        break;
    case KEY_ANTI_CLOSEWISE:
        if(e_Edit == state())
        {
            mInnerData->decrease();
        }
        else
        {
            ev->ignore();
        }
        break;
    case KEY_CLOSEWISE:
        if(e_Edit == state())
        {
            mInnerData->increase();
        }
        else
        {
            ev->ignore();
        }
        break;
    default:
        ev->ignore();
        break;
    }
    refreshText();
}

void CfgButton::hideEvent(QHideEvent *)
{
    if(state()==e_Edit)
    {
        focusOutEvent(nullptr);
    }
    //mPopup->close();
}

void CfgButton::showEvent(QShowEvent *)
{
    refreshText();
}

void CfgButton::drawLeftArrow(QPainter *painter) const
{
    int y = (rect().height() - fontMetrics().height())/2;
    painter->drawStaticText(LEFT_ARROW_MARGIN, y, QStaticText("<"));
}

void CfgButton::drawRightArrow(QPainter *painter) const
{
    int y = (rect().height() - fontMetrics().height())/2;
    int arrowWidth = fontMetrics().boundingRect("<").width();
    painter->drawStaticText(width() - RIGHT_ARROW_MARGIN - arrowWidth, y, QStaticText(">"));
}

void CfgButton::drawTriangle(QPainter *) const
{
    return;
}

void CfgButton::drawRect(QPainter *painter) const
{
    painter->setRenderHint(QPainter::Antialiasing);
    painter->drawRect(rect());
    return;
}

void CfgButton::InitConnectDateModel()
{
    connect(mInnerData, &IntraData::SignalValueChanged, this, [=](int value)
    {
        refreshText();
        emit SignalValueChanged(value);
    });
    connect(mInnerData, &IntraData::SignalValueChangedTemporary, this, [=](int value)
    {
        mPopup->SlotShowTip(mErrorValueTipStr[IntraData::VALID]);
        refreshText();
        emit SignalValueChangedTemporary(value);
    });
}

void CfgButton::InitPopupAdapter()
{
    mPopup = new PopupAdapter(this);
    mPopup->SetCheckValueFunc([this](QString value,QString& errorStr) {
        return DataValidator(value, errorStr);
    });
    connect(mPopup, &PopupAdapter::SignalNeedUpPopupText,this, &CfgButton::SlotPopupNeedTextData);
    connect(mPopup, &PopupAdapter::SignalValueChanged,this, &CfgButton::SlotPopupValueChanged);
    connect(this, &TristateBtn::SignalBeginEdit, mPopup, &PopupAdapter::show);
    connect(this, &TristateBtn::SignalEndEdit, mPopup, &PopupAdapter::close, Qt::DirectConnection);
}

QString CfgButton::valueToStr(int value)
{
    QString strText = mInnerData->valueToString(value);
    return strText;
}

int CfgButton::strToValue(QString str)
{
    int value = mInnerData->StringTovalue(str);
    return value;
}

void CfgButton::SlotPopupValueChanged(QString value)
{
    if(!value.isEmpty())
    {
        int neeValue = strToValue(value);
        if(neeValue != INVALID_VALUE)
            setValue(neeValue);
    }
}

void CfgButton::SlotPopupNeedTextData()
{
    mPopup->UpPopupListText(GetAllValueStr());
    mPopup->UpPopupText(valueToStr(mInnerData->value()));
}

void CfgButton::SlotSetValue(int val) {
    if (val != value()) {
        setValue(val);
    }
}

void CfgButton::refreshText()
{
    if (mIsCloseAutoRefrenshText)
        setText(valueToStr(mInnerData->value()));
    mPopup->UpPopupText(valueToStr(mInnerData->value()));
}

void CfgButton::InitUiText()
{
    mErrorValueTipStr[IntraData::VALID]= QString("");
    mErrorValueTipStr[IntraData::OUT_MAX]= UIStrings::GetStr(STR_OUT_OF_UPPER_LIMIT);
    mErrorValueTipStr[IntraData::OUT_MIN]= UIStrings::GetStr(STR_OUT_OF_LOWER_LIMIT);
    mErrorValueTipStr[IntraData::FORMAL_ERROR]= UIStrings::GetStr(STR_FORMAT_ERROR);
    mErrorValueTipStr[IntraData::INVALID]= UIStrings::GetStr(STR_INVALID_VALUE);
}

void CfgButton::setIsCloseRefershPopup(bool mySet)
{
    mIsCloseAutoRefrenshText = mySet;
}

