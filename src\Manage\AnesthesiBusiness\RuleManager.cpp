﻿#include <math.h>

#include "DebugLogManager.h"
#include "VentModeSettingManager.h"
#include "AlarmManager.h"
#include "DataLimitManager.h"
#include "RuleManager.h"
#include "SettingSpinboxManager.h"

#define MAX(a,b) ((a>b)?a:b)
#define MIN(a,b) ((a<b)?a:b)

#define  RULE_ITEM_MAX	7
#define  MAX_MODE_NUM   9
#define  MAX_VALUE_DEFALUT	2000       //数值所能表示的最大限值
#define  MIN_VALUE_DEFALUT  (0-MAX_VALUE_DEFALUT)
int mDefaultParam= RuleManager::NONE_RULE;
//IE转换
/* ***********************************************************************
 将IE值转化成(I+E)/I的比值;由 (E - I) * 10  + 100 = value 计算得如下
 if(value > 100)
 {
    I = 1;
    E = value / 10 - 9;
   (I＋E)/I = (value / 10 - 8) / 1;
 }
 if(value < 100)
 {
    E = 1;
    I = (110 - value) / 10;
   (I+ E)/I = (120 - value) /( 110 - value)
 }
 ***********************************************************************  */
void IE_value_to_percent(const short  value, float * IE_percent)
{
    if(value >= 100)
    {
        *IE_percent = ((float)value / 10 - 8);
    }
    if(value < 100)
    {
        *IE_percent =  (float)(120 - value) /( 110 - value);
    }
}

void IE_value_to_percent_double(const short  value, double * IE_percent)
{
    if(value >= 100)
    {
        *IE_percent = ((double)value / 10 - 8);
    }
    if(value < 100)
    {
        *IE_percent =  (double)(120 - value) /( 110 - value);
    }
}
/* ***********************************************************************
 由IE_percent = (I +Ｅ) / I 可得：
 当I>E,IE_percent < 2 ，value < 100;
 value = 100 + 10 * ((IE_percent - 2)/(IE_percent - 1)) = 110 - 10/(IE_percent - 1)
 当I<E,IE_percent > 2, value > 100
 value = IE_percent * 10 + 80
***********************************************************************  */
void IE_percent_to_vlaue(float IE_max, float IE_min, short * max, short * min)
{
    if(IE_max < 1.25)
    {
        DebugAssert(0);
    }

    //如果超出最小判断范围
    IE_min = (float)((IE_min < 1.25) ? 1.25 : IE_min);

    if (IE_max <= 2)
    {//范围全部落在[0,2]
        *min = (short)ceil(110 - (float)10 / (IE_min - 1));
        *max = (short)floor(110 - (float)10 / (IE_max - 1));
    }
    else if (IE_min >= 2)
    {//范围全部落在[2,~]
        *min = (short)ceil(IE_min * 10 + 80);
        *max = (short)floor(IE_max * 10 + 80);
    }
    else
    {//范围分别落在[0,2]~[2,~],根据函数线性递增特性得出IE 和value是同时递增的;
        *min = (short)ceil(110 - (float)10 / (IE_min - 1));
        *max = (short)floor(IE_max * 10 + 80);
    }
}

/* **********************************************************************  */
/*  参数限值规则，共十二条:
 如果有相对应的参数，则需要符合限制
 rule 3:PEEP <= Plimit – 5
 rule 5:PEEP <= Pinsp – 5
 rule 6:60 / Rate / (I + E) * E > 0.5  (Rate>I:E)
 rule 7: 1350 <= VT * 60 * 100 / (Tinsp * (100 - Tip:Ti)) <= 100000  (Tip:Ti > VT > Tinsp)
 (由于tinsp带一位小数，将其扩大10倍，135 <= VT * 60 / Tinsp * (100 - Tip:Ti) / 100 <= 10000)

 rule 8: 1350 <= VT * Rate * (I + E) * 100 / (I * (100 - Tip:Ti)) <= 100000 （Tip:Ti>VT>Rate>I:E）


 rule 9:Slope <= 60 / Rate /( (I + E) / I)  (Slope > Rate >I:E)

(由于slope带两位小数，将其扩大100倍，为Slope <= 6000 / Rate /( (I + E) / I))
 rule 10:0.5 < 60 / Rate – Tinsp  (Rate > Tinsp)
 ( 5< 600/Rate - Tinsp)
 rule 11:Slope <= Tinsp  (Slope > Tinsp)
 ( Slope <= Tinsp / 10 )
//rule 12:Pinsp <= Plimt - 5
***********************************************************************  */

int param_rule_3(E_SETTING_DATA_ID param_data_id, T_MASTER_SET sMasterSet, short *max, short *min)
{ //rule 3:PEEP <= Plimit – 5  (PEEP => Plimit)
    short  peep;
    short  plimit;
    peep = sMasterSet.Peep;
    plimit = sMasterSet.Plimit;

    switch(param_data_id)
    {
    case SETTINGDATA_PEEP:
        *max = plimit - 5;
        *min = 0;
        break;
    case SETTINGDATA_PLIMIT:
        *max = MAX_VALUE_DEFALUT;
        *min = peep + 5;
        break;
    default:
        return 0;
        break;
    }
    return 1;
}
int param_rule_5(E_SETTING_DATA_ID param_data_id, T_MASTER_SET sMasterSet, short *max, short *min)
{//rule 5:PEEP <= Pinsp – 5
    short  peep;
    short  pinsp;
    peep = sMasterSet.Peep;
    pinsp = sMasterSet.Pinsp;

    switch(param_data_id)
    {
    case SETTINGDATA_PEEP:
    {
        ALL_MODESET_SPN_DISCRIPTOR p_spn_disc = SettingSpinboxManager::GetInstance()->GetModesetSpnDiscriptor(SETTINGDATA_PEEP);

        if (pinsp - 5 < p_spn_disc.min_value)
            *max = p_spn_disc.min_value;
        else
            *max = pinsp - 5;
        *min = 0;
    }
        break;
    case SETTINGDATA_PINSP:
        *max = MAX_VALUE_DEFALUT;
        *min = peep + 5;
        break;
    default:
        return 0;
        break;
    }
    return 1;
}


int param_rule_6(E_SETTING_DATA_ID param_data_id, T_MASTER_SET sMasterSet, short *max, short *min)
{//rule 6:60 / (Rate * (I + E) / E) > 0.5  (Rate>I:E)
    short rate;
    float ie_percent;
    float p_value_max;
    float p_value_min;
    IE_value_to_percent(sMasterSet.IE, &ie_percent);

    switch(param_data_id)
    {
    case SETTINGDATA_RATE:
        *max = (short)floor((float)(60 * 2 * (ie_percent - 1)) / ie_percent - 0.001);
        *min = 0;
        break;
    case SETTINGDATA_IE:
    {
        rate = sMasterSet.Rate;

        p_value_min = (float)((float)120 / (120 - rate) + 0.01); ////减0.001为了保证整除时， 不等式也成立
        p_value_max = MAX_VALUE_DEFALUT;

        IE_percent_to_vlaue(p_value_max, p_value_min, max, min);
        break;
    }
    default:
        return 0;
        break;
    }

    return 1;
}

int param_rule_7(E_SETTING_DATA_ID param_data_id, T_MASTER_SET sMasterSet, short *max, short *min)
{//1350 <= VT * 60 * 100 / (Tinsp * (100 - Tip:Ti)) <= 100000  (Tip:Ti > VT > Tinsp)  simv-vcv
    //tinsp带一位精度，需要除去
    short  vt;
    short  tinsp;
    short  titp;

    vt = sMasterSet.VT;

    titp = sMasterSet.TiTp;

    tinsp = sMasterSet.Tinsp;

    switch (param_data_id)
    {
    case SETTINGDATA_VT:
        *max = (short)floor((float)(10000 * tinsp * (100 - titp))/ (60 * 100));
        *min = (short)ceil((float)(135 * tinsp * (100 - titp))/ (60 * 100));
        break;
    case SETTINGDATA_TINSP:
        *max = (short)floor((float)(vt * 60 * 100)/(135 * (100 - titp)));
        *min = (short)ceil((float)(vt * 60 * 100) /(10000 * (100 - titp)));
        break;
    case SETTINGDATA_TITP:
        *max = MAX(0, 100 - (short)ceil((float)(vt * 60 * 100 *10)/(tinsp * 100000)));
        *min = MAX(0, 100 - (short)floor((float)(vt * 60 * 100 *10)/(tinsp * 1350)));
        break;
    default:
        return 0;
        break;
    }
    return 1;
}

int param_rule_8(E_SETTING_DATA_ID param_data_id, T_MASTER_SET sMasterSet, short *max, short *min)
{//1350 <= VT * Rate * (I + E) * 100 / (I * (100 - Tip:Ti)) <= 100000 vcv
    short  vt;
    short  rate;
    double ie_percent; //float精度丢失导致计算值错误
    short  titp;
    float p_value;
    float p_value_max;
    float p_value_min;

    vt = sMasterSet.VT;

    rate = sMasterSet.Rate;

    IE_value_to_percent_double(sMasterSet.IE, &ie_percent);

    titp = sMasterSet.TiTp;

    switch(param_data_id)
    {
    case SETTINGDATA_VT:
        *min = (short)ceil((float)1350 * (100 - titp)/( rate * ie_percent * 100));
        *max = (short)floor((float)100000 * (100 - titp)/(rate * ie_percent * 100));
        break;
    case SETTINGDATA_RATE:
        *min = (short)ceil((float)1350*(100 - titp) /(vt * ie_percent * 100));

        *max = (short)floor((float)100000 * (100 - titp)/(vt * ie_percent * 100));

        break;
    case SETTINGDATA_IE:

        p_value_min = (float)1350 * (100 - titp)/(vt * rate * 100);

        p_value_max = (float)100000 * (100 - titp)/(vt * rate * 100);

        IE_percent_to_vlaue(p_value_max, p_value_min, max, min);
        break;
    case SETTINGDATA_TITP:

        p_value = (float)(vt * rate * ie_percent * 100) / 1350;

        *min = (short)MAX(0, ceil((float)100 - p_value));

        p_value =  (float)(vt * rate * ie_percent * 100) / 100000;

        *max = (short)MAX(0, floor((float)(100 - p_value)));

        break;
    default:
        return 0;
        break;
    }

    return 1;
}

int param_rule_9(E_SETTING_DATA_ID param_data_id, T_MASTER_SET sMasterSet, short *max, short *min)
{//	rule 9:Slope <= 6000 / Rate /( (I + E) / I)  (Slope > Rate >I:E)
    short  slope;
    short  rate;
    float ie_percent;
    float p_value_max;
    float p_value_min;

    slope = sMasterSet.Slope;

    rate =  sMasterSet.Rate;

    IE_value_to_percent(sMasterSet.IE, &ie_percent);

    if (0 == slope && param_data_id != SETTINGDATA_SLOPE)
    {//如果slope 为0，任意的数都能满足

        *max = MAX_VALUE_DEFALUT;
        *min = 0;
        return 1;
    }

    switch(param_data_id)
    {
    case SETTINGDATA_SLOPE:

        *max = (short)floor(6000 * 1.0 / (rate * ie_percent) - 0.01);//减0.001为了保证整除时， 不等式也成立
        *min = 0;
        break;
    case SETTINGDATA_RATE:

        *max = (short)floor(6000 * 1.0 / (slope * ie_percent) - 0.01); //减0.001为了保证整除时， 不等式也成立 ???
        *min = 0;
        break;
    case SETTINGDATA_IE:{

        p_value_max = (float)(6000 * 1.0 / (slope * rate) - 0.01);		//减0.001为了保证整除时， 不等式也成立

        p_value_min = 0;

        IE_percent_to_vlaue(p_value_max, p_value_min, max, min);

        break;
    }
    default:
        return 0;
        break;
    }
    return 1;
}

int param_rule_10(E_SETTING_DATA_ID param_data_id, T_MASTER_SET sMasterSet, short *max, short *min)
{//5 < 600/Rate - Tinsp  (Rate > Tinsp)
    short  rate;
    short  tinsp;

    rate = sMasterSet.Rate;
    tinsp = sMasterSet.Tinsp;

    switch(param_data_id)
    {
    case SETTINGDATA_RATE:
        *max = (short)floor((float)600 / (5 + tinsp) - 0.001);
        *min = 0;
        break;
    case SETTINGDATA_TINSP:
        *max = (short)floor((float)600 / rate - 5 - 0.001); //加0.001为了保证整除时， 不等式也成立
        *min = 0;
        break;
    default:
        return 0;
        break;
    }

    return 1;
}

int param_rule_11(E_SETTING_DATA_ID param_data_id, T_MASTER_SET sMasterSet, short *max, short *min)
{//rule 11:Slope <= 10 * Tinsp (Slope > Tinsp)

    switch(param_data_id)
    {
    case SETTINGDATA_SLOPE:
        *max = 10 * sMasterSet.Tinsp;

        *min = 0;
        break;
    case SETTINGDATA_TINSP:
        *min = (short)ceil((float)sMasterSet.Slope / 10);

        *max = MAX_VALUE_DEFALUT;
        break;
    default:
        return 0;
        break;
    }

    return 1;
}
int param_rule_12(E_SETTING_DATA_ID param_data_id, T_MASTER_SET sMasterSet, short *max, short *min)
{//rule 12:Pinsp <= Plimt - 5

    switch(param_data_id)
    {
    case SETTINGDATA_PINSP:
        *max = sMasterSet.Plimit - 5;
        *min = 0;
        break;
    case SETTINGDATA_PLIMIT:
        *min = sMasterSet.Pinsp + 5;
        *max = MAX_VALUE_DEFALUT;
        break;
    default:
        return 0;
        break;
    }

    return 1;
}
/* ***********************************************************************
Function Name :GetParamMaxMin()
params: 1:setting_data id; 2: sMasterSet; 3: param_max; 3: param_min
return: ;
***********************************************************************  */

bool RuleManager::GetParmRangeByPriority(int ParamId, T_MASTER_SET sMasterSet, short &param_min, short &param_max)
{
    param_min=9999;
    param_max=-9999;
    QVector<int> DynamicCalParamIdVe;
    for(int i=SETTINGDATA_VT;i<SETTINGDATA_MAX;i++)
    {
        if(SettingSpinboxManager::GetInstance()->GetModesetSpnDiscriptor(i).prio_level>
                SettingSpinboxManager::GetInstance()->GetModesetSpnDiscriptor(ParamId).prio_level)
        {
            DynamicCalParamIdVe<<i;
        }
    }
    std::function<void(int,int ,short&,short&)> caclParamRage;
    caclParamRage = [&](int calStep,int ParamId,short &param_min,short &param_max)
    {
        short deafaultMin,deafaultMax;
        SettingSpinboxManager::GetInstance()->GetParamInitRange(DynamicCalParamIdVe[calStep],deafaultMin,deafaultMax);
        VentModeSettingManager::GetInstance()->SaveToSpecificSet(&sMasterSet,(E_SETTING_DATA_ID)DynamicCalParamIdVe[calStep],deafaultMin);
        if(calStep==DynamicCalParamIdVe.length()-1)
        {
            short calMin,calMax;
            if(GetParamMaxMin(ParamId,sMasterSet,calMin,calMax))
            {
                param_max=param_max<calMax?calMax:param_max;
                param_min=param_min>calMin?calMin:param_min;
            }
            VentModeSettingManager::GetInstance()->SaveToSpecificSet(&sMasterSet,(E_SETTING_DATA_ID)DynamicCalParamIdVe[calStep],deafaultMax);
            if(GetParamMaxMin(ParamId,sMasterSet,calMin,calMax))
            {
                param_max=param_max<calMax?calMax:param_max;
                param_min=param_min>calMin?calMin:param_min;
            }
        }
        else
        {
            caclParamRage(calStep+1,ParamId,param_min,param_max);
            VentModeSettingManager::GetInstance()->SaveToSpecificSet(&sMasterSet,(E_SETTING_DATA_ID)DynamicCalParamIdVe[calStep],deafaultMax);
            caclParamRage(calStep+1,ParamId,param_min,param_max);
        }

    };
    if(DynamicCalParamIdVe.length()>0)
        caclParamRage(0,ParamId,param_min,param_max);
    else
        GetParamMaxMin(ParamId,sMasterSet,param_min,param_max);
    return true;
}

bool RuleManager::GetParamMaxMin(unsigned short param_data_id, T_MASTER_SET sMasterSet,
                                 short &param_min, short &param_max, int &minRule, int &maxRule)
{
    SettingSpinboxManager::GetInstance()->GetParamInitRange(param_data_id,param_min,param_max);
    if((sMasterSet.VentMode == VENTMODE_FLAG_VCVSIMV || sMasterSet.VentMode == VENTMODE_FLAG_PCVSIMV) && param_data_id == SETTINGDATA_RATE)
    {
        param_min = 4;
        param_max = 40;
    }
    short  max = param_max;
    short  min = param_min;
    maxRule=minRule=NONE_RULE;
    switch(param_data_id)
    {
    case SETTINGDATA_FLOW_TRIGGERVALUE:
    {
        return true;
    }
        break;
    case SETTINGDATA_PRESSURE_TRIGGERVALUE:
    {
        return true;
    }
        break;
    }
    if(mModelRuleMap.find(sMasterSet.VentMode)!=mModelRuleMap.end())
    {
        foreach(auto ruleType, mModelRuleMap[sMasterSet.VentMode])
        {
            if(mRuleFuctionMap.value(ruleType,NULL)==NULL)
                continue;
            mRuleFuctionMap[ruleType]((E_SETTING_DATA_ID)param_data_id, sMasterSet, &max, &min);
            if(max < param_max)
            {
                maxRule = ruleType;
                param_max = max;
            }
            if(min > param_min)
            {
                minRule = ruleType;
                param_min = min;
            }
        }
    }
    if(param_max<param_min)
        return false;
    return true;
}


RuleManager::RuleManager()
{
    mModelRuleMap[VENTMODE_FLAG_VCV]<<RULE_3<<RULE_6<<RULE_8;
    mModelRuleMap[VENTMODE_FLAG_PCV]<<RULE_5<<RULE_6<<RULE_9;
    mModelRuleMap[VENTMODE_FLAG_VCVSIMV]<<RULE_3<<RULE_7<<RULE_10;
    mModelRuleMap[VENTMODE_FLAG_PCVSIMV]<<RULE_5<<RULE_10<<RULE_11;
    mModelRuleMap[VENTMODE_FLAG_PSV]<<RULE_5<<RULE_6<<RULE_9;
    mModelRuleMap[VENTMODE_FLAG_PRVC]<<RULE_3<<RULE_9;
    mModelRuleMap[VENTMODE_FLAG_PMODE]<<RULE_3<<RULE_5<<RULE_6<<RULE_12;

    mRuleFuctionMap[RULE_3]=param_rule_3;
    mRuleFuctionMap[RULE_5]=param_rule_5;
    mRuleFuctionMap[RULE_6]=param_rule_6;
    mRuleFuctionMap[RULE_7]=param_rule_7;
    mRuleFuctionMap[RULE_8]=param_rule_8;
    mRuleFuctionMap[RULE_9]=param_rule_9;
    mRuleFuctionMap[RULE_10]=param_rule_10;
    mRuleFuctionMap[RULE_11]=param_rule_11;
    mRuleFuctionMap[RULE_12]=param_rule_12;
}



