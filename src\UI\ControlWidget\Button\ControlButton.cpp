﻿#include "ControlButton.h"
#include "UiApi.h"
#include "KeyConfig.h"
#include <QProxyStyle>

#define CLOSE_ICON_PATH ":/button/CloseIcon.png"
#define SURE_ICON_PATH ":/button/SureIcon.png"

#define DEFALUT_HEIGHT 38
#define DEFALUT_WIDTH 50
//菜单标签容器
CloseButton::CloseButton(QWidget* parent):QPushButton(parent)
{
    setFixedSize(DEFALUT_WIDTH,DEFALUT_HEIGHT);
    StyleSet::SetBackgroundColor(this,COLOR_TRANSPARENT);
    StyleSet::SetBackGroundIcon(this,CLOSE_ICON_PATH,StyleSet::KEEP_CENTER);
    StyleSet::SetBorder(this,COLOR_TRANSPARENT);
}

void CloseButton::paintEvent(QPaintEvent *event)
{
    QPushButton::paintEvent(event);
    if(hasFocus())
    {
        QPainter p(this);
        p.setRenderHint(QPainter::Antialiasing);
        Q<PERSON>en pen(COLOR_BRIGHT_BLUE,4);
        p.set<PERSON>en(pen);
        p.drawRoundedRect(0,0,width(),height(),2,2);
    }
}

void CloseButton::keyPressEvent(QKeyEvent *event)
{
    if (KEY_PRESS == event->key())
    {
        animateClick();
    }
    else
    {
        QPushButton::keyPressEvent(event);
    }
}

SureButton::SureButton(QWidget* parent):CloseButton(parent)
{
    setFixedSize(DEFALUT_WIDTH,DEFALUT_HEIGHT);
    StyleSet::SetBackgroundColor(this,COLOR_TRANSPARENT);
    StyleSet::SetBackGroundIcon(this,SURE_ICON_PATH,StyleSet::KEEP_CENTER);
    StyleSet::SetBorder(this,COLOR_TRANSPARENT);
}
