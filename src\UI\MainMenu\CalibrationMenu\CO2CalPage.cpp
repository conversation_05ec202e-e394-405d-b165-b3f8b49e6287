﻿#include <QHBoxLayout>
#include <QVBoxLayout>
#include <QGridLayout>
#include "UiApi.h"
#include "AGModule.h"
#include "CO2CalPage.h"
#include "PushButton.h"
#include "calibration/Co2CalManage.h"
#include "ModuleTestManager.h"
#include "KeyConfig.h"
#include "CountdownProgressBar.h"
#include "String/UIStrings.h"
#include "SystemConfigManager.h"
#include "TipDialog.h"

#define IMAGE_HEIGHT    340
#define IMAGE_WEIGHT    290
#define PAGE_HEIGHT     462
#define PAGE_WEIGHT     558
#define TIP_IMAGE_PATH ":/Calibration/cali.png"

#define TIP_IMAGE_WIDTH 180
#define TIP_IMAGE_HEIGHT 300
#define RESULT_WIDGET_HEIGHT 150
#define RESULT_LABLE_HEIGHT 24
CO2CalPage::CO2CalPage(QWidget* parent ) : FocusWidget(parent)
{
    mCalStateTextId = STR_NULL;
    mCalResultTextId = STR_DASH;
    mCalStepTextId = STR_MAINMENU_AGCO2CAL_TXT1_TITLE;

    InitUi();
    InitUiText();

    RefurbishCalResult();

    connect(mCalProgressBar, &CountdownProgressBar::SignalCountdownEnd, this, [this]{
        Co2CalManage::GetInstance()->SaveCalResult(AG_ZERO_STATUS_DISABLE);
        Co2CalManage::GetInstance()->EndCo2Cal();
        DisplayCalTip(AG_ZERO_STATUS_FAIL);
    });

    connect(Co2CalManage::GetInstance(), &Co2CalManage::SignalStatusChanged, this, &CO2CalPage::DisplayCalTip, Qt::DirectConnection);
}

void CO2CalPage::InitUi()
{
    mCalTip = new ModalTipDialog(this);

    mCalModuleStateTip = new QLabel;
    mCalModuleStateTip->setText(UIStrings::GetStr(ALL_STRINGS_ENUM::STR_AG_ERROR));
    mCalModuleStateTip->hide();


    mCalStepTipTitle = new QLabel(UIStrings::GetStr(ALL_STRINGS_ENUM::STR_ZERO_CAL_TIP) + ":");
    mCalStepTipTitle->setObjectName("mCalStepTipTitle");
    mCalStepTipTitle->setFixedHeight(35);//TBD 常量
    mCalStepTipTitle->setContentsMargins(5, 0, 0, 0);
    mCalStepTipTitle->setAlignment(Qt::AlignVCenter);
    StyleSet::SetTextFont(mCalStepTipTitle, mCalStepTipTitle->font().pointSize(), FONT_FAMILY_TAHOMA);

    mCalStepTipText = new QTextEdit(this);
    mCalStepTipText->setFocusPolicy(Qt::NoFocus);
    mCalStepTipText->setFrameStyle(QFrame::Box);
    mCalStepTipText->setLineWidth(0);
    StyleSet::SetTextFont(mCalStepTipText,FONT_M);
    StyleSet::SetBackgroundColor(mCalStepTipText,COLOR_WHITE_GRAY);
    mCalStepTipText->setReadOnly(true);
    mCalStepTipText->setTextInteractionFlags(Qt::NoTextInteraction);
    mCalTipImage = new QLabel;
    mCalTipImage->setFixedWidth(TIP_IMAGE_WIDTH);
    mCalTipImage->setFixedHeight(TIP_IMAGE_HEIGHT);
    StyleSet::SetBackgroundColor(mCalTipImage,COLOR_TRANSPARENT);
    mCalTipImage->setPixmap(QPixmap(TIP_IMAGE_PATH));
    mCalTipImage->setScaledContents(true);
    mCalTipImage->setContentsMargins(20, 30, 10, 30);
    mCalProgressBar = new CountdownProgressBar;
    mCalProgressBar->SetCountTime(20);   //TBD 校准限时
    mCalProgressBar->hide();
    mCalResultTitle = new QLabel;
    mCalResultTitle->setFixedHeight(RESULT_LABLE_HEIGHT);
    StyleSet::SetTextColor(mCalResultTitle,COLOR_BLACK);
    StyleSet::SetTextFont(mCalResultTitle, FONT_M);

    mCalResult = new QLabel;
    mCalResult->setFixedHeight(RESULT_LABLE_HEIGHT);
    mPreCalTime = new DateTimeWidget(this, Qt::Horizontal);
    mPreCalTime->setFixedHeight(RESULT_LABLE_HEIGHT);
    mPreCalTime->SetDateColor(COLOR_BLACK);
    mPreCalTime->SetTimeColor(COLOR_BLACK);

    mCalButton = new PushButton(UIStrings::GetStr(ALL_STRINGS_ENUM::STR_START));
    connect(mCalButton, &PushButton::clicked, this, &CO2CalPage::SlotOnCalBtnClicked);


    auto resultLayout = new QVBoxLayout;
    resultLayout->setContentsMargins(13, 0, 0, 0);
    resultLayout->addWidget(mCalResultTitle);
    resultLayout->addWidget(mCalResult);
    resultLayout->addWidget(mPreCalTime);
    resultLayout->setAlignment(mCalResult, Qt::AlignBottom);
    resultLayout->setAlignment(mPreCalTime, Qt::AlignBottom);
    resultLayout->addStretch();
    mResultBlock = new QFrame;
    mResultBlock->setLayout(resultLayout);

    StyleSet::SetBackgroundColor(mCalResultTitle,COLOR_WHITE_GRAY);
    StyleSet::SetBackgroundColor(mCalResult,COLOR_WHITE_GRAY);
    StyleSet::SetBackgroundColor(mPreCalTime,COLOR_WHITE_GRAY);
    StyleSet::SetBackgroundColor(mResultBlock,COLOR_WHITE_GRAY);
    StyleSet::SetTextFont(mCalResult, FONT_M);

    auto centerLayout = new QHBoxLayout;
    centerLayout->addWidget(mCalStepTipText);
    centerLayout->addWidget(mCalTipImage);

    auto bhLayout = new QHBoxLayout;
    bhLayout->addWidget(mCalModuleStateTip);
    bhLayout->addWidget(mCalButton);
    bhLayout->setAlignment(mCalModuleStateTip, Qt::AlignLeft);
    bhLayout->setAlignment(mCalButton, Qt::AlignRight);
    QVBoxLayout *bottomLayout = new QVBoxLayout;
    bottomLayout->addWidget(mResultBlock);
    bottomLayout->addStretch();
    bottomLayout->addWidget(mCalProgressBar);
    bottomLayout->addLayout(bhLayout);
    bottomLayout->setAlignment(mCalButton, Qt::AlignRight | Qt::AlignBottom);



    auto mainLayout = new QVBoxLayout;
    mainLayout->setContentsMargins(0, 0, 0, 0);
    mainLayout->setSpacing(0);
    mainLayout->addWidget(mCalStepTipTitle);
    mainLayout->addLayout(centerLayout);
    mainLayout->addSpacing(2);
    mainLayout->addLayout(bottomLayout);
    setLayout(mainLayout);
    StyleSet::SetBackgroundColor(this,COLOR_WHITE_GRAY);
}

//显示校零各阶段提示
void CO2CalPage::DisplayCalTip(unsigned short calStatus)
{
    switch (calStatus)
    {
    case AG_ZERO_STATUS_FAIL:
        mCalProgressBar->hide();
        if(Co2CalManage::GetInstance()->GetCalState() == AG_ZERO_STATUS_DISABLE)
        {
            mCalModuleStateTip->show();
            mCalButton->setEnabled(false);
        }
        else
        {
            mCalModuleStateTip->hide();
            mCalButton->setEnabled(true);
        }

        mCalTip->close();
        break;
    case AG_ZERO_STATUS_NORMAL:
        mCalProgressBar->hide();
        mCalButton->setEnabled(true);

        mCalModuleStateTip->hide();
        mCalTip->close();
        mCalStateTextId = STR_NULL;
        break;
    case AG_ZERO_STATUS_DISABLE:
    {
        ALL_STRINGS_ENUM oldDisableStateTextId = mCalStateTextId;
        oldDisableStateTextId = ModuleTestManager::GetInstance()->GetAgDisableStateTextId();
        if(oldDisableStateTextId != STR_NULL && oldDisableStateTextId != mCalStateTextId)
        {
            mCalStateTextId = oldDisableStateTextId;
            mCalModuleStateTip->setText(UIStrings::GetStr(mCalStateTextId));
        }
        RefurbishCalResult();
        mCalProgressBar->hide();
        mCalProgressBar->ResetTimer();
        mCalTip->close();
        mCalModuleStateTip->show();
        mCalButton->setEnabled(false);
    }
        break;
    case AG_ZERO_STATUS_IN_PROGRESS:
        mCalButton->setEnabled(false);
        break;
    case AG_ZERO_STATUS_DONE:
        mCalProgressBar->SetBarFinish();
        mCalProgressBar->hide();
        if(Co2CalManage::GetInstance()->GetCalState() == AG_ZERO_STATUS_DISABLE)
        {
            mCalModuleStateTip->show();
            mCalButton->setEnabled(false);
        }
        else
        {
            mCalModuleStateTip->hide();
            mCalButton->setEnabled(true);
        }
        mCalTip->close();
        break;
    default:
        break;
    }

    if(calStatus != AG_ZERO_STATUS_DISABLE)
    {
        SetStepText(UIStrings::GetStr(mCalStepTextId));
        if (calStatus != AG_ZERO_STATUS_IN_PROGRESS)
        {
            mCalProgressBar->ResetTimer();
            RefurbishCalResult();
        }
        else
        {
            mResultBlock->hide();
        }
    }
}


void CO2CalPage::SetStepText(QString text) {
    mCalStepTipText->clear();
    QTextDocument *doc = mCalStepTipText->document();
    QTextCursor cursor(doc);

    QTextBlockFormat blockFormat;
    blockFormat.setAlignment(Qt::AlignTop);
    blockFormat.setTopMargin(0);
    blockFormat.setBottomMargin(3);
    blockFormat.setLeftMargin(10);
    blockFormat.setRightMargin(3);
    blockFormat.setLineHeight(3, QTextBlockFormat::LineDistanceHeight);

    QFont tempFont;
    tempFont.setFamily(FONT_FAMILY_TAHOMA);
    tempFont.setPixelSize(FONT_M);
    mCalStepTipText->setFont(tempFont);


    cursor.insertBlock(blockFormat);
    cursor.insertText(text);
    cursor.insertBlock();


    mCalStepTipText->setDocument(doc);
    mCalStepTipText->setTextCursor(cursor);

}

void CO2CalPage::SetCalResult(QDateTime calTime, CAL_RESULT result)
{
    switch(result)
    {
    case CAL_RESULT::CAL_SUCCESS:
        mCalResultTextId = STR_SUCCESS;
        StyleSet::SetTextColor(mCalResult,COLOR_LIGHT_GREEN);
        break;
    case CAL_RESULT::CAL_FAIL:
        mCalResultTextId = STR_FAIL;
        StyleSet::SetTextColor(mCalResult,COLOR_RED);
        break;
    case CAL_RESULT::CAL_CANCEL:
        mCalResultTextId = STR_CANCEL;
        StyleSet::SetTextColor(mCalResult,COLOR_YELLOW);
        break;
    }

    mCalResult->setText(UIStrings::GetStr(mCalResultTextId));
    mPreCalTime->SetDateTime(calTime);
}

void CO2CalPage::RefurbishCalResult()
{
    QPair<qint64, CalInfoSt> calResult;
    if (ModuleTestManager::GetInstance()->GetAgModuleType() == AG)
    {
        calResult = CalModuleManage::GetInstance()->GetLatestCalResult(CAL_TYPE::CAL_TYPE_AG);
    }
    else if(ModuleTestManager::GetInstance()->GetAgModuleType() == CO2)
    {
        calResult = CalModuleManage::GetInstance()->GetLatestCalResult(CAL_TYPE::CAL_TYPE_CO2);
    }
    if (calResult.first == -1)
    {
        mResultBlock->hide();
        return;
    }
    else
    {
        mResultBlock->show();
    }

    SetCalResult(QDateTime::fromSecsSinceEpoch(calResult.first), (CAL_RESULT)calResult.second.mCalResult);
}

void CO2CalPage::InitUiText()
{
    mCalStepTipTitle->setText(UIStrings::GetStr(STR_ZERO_CAL_TIP) + ":");
    SetStepText(UIStrings::GetStr(mCalStepTextId));
    mCalResultTitle->setText(UIStrings::GetStr(ALL_STRINGS_ENUM::STR_LATEST_ZERO_CAL_TIME) + ": ");
    mCalResult->setText(UIStrings::GetStr(mCalResultTextId));
    mCalButton->setText(UIStrings::GetStr(STR_START));
    if (ModuleTestManager::GetInstance()->GetAgModuleType() == AG)
    {
        mCalTip->SetDialogText(UIStrings::GetStr(STR_AG_ZEROING_TIP));
    }
    else if (ModuleTestManager::GetInstance()->GetAgModuleType() == CO2)
    {
        mCalTip->SetDialogText(UIStrings::GetStr(STR_CO2_ZEROING_TIP));
    }
    mCalModuleStateTip->setText(UIStrings::GetStr(mCalStateTextId));

}

void CO2CalPage::showEvent(QShowEvent *event)
{
    emit SignalChangeCalState(true);
    FocusWidget::showEvent(event);
}

void CO2CalPage::hideEvent(QHideEvent *event)
{
    emit SignalChangeCalState(false);
    FocusWidget::hideEvent(event);
}

//如果AG/CO2模块有效，且不是在校零状态，开始校零
void CO2CalPage::SlotOnCalBtnClicked()
{
    if (Co2CalManage::GetInstance()->GetCalState() == AG_ZERO_STATUS_NORMAL)
    {
        Co2CalManage::GetInstance()->StartCo2Cal();

        mCalProgressBar->ResetTimer();
        mCalProgressBar->show();
        mCalProgressBar->StartCount();

        mCalModuleStateTip->hide();
        mCalButton->setEnabled(false);
        mResultBlock->hide();

        mCalTip->exec();
    }
    else
    {
        DisplayCalTip(AG_ZERO_STATUS_DISABLE);
    }
}
