﻿#ifndef DBINIT_H
#define DBINIT_H

#include <QString>
#include <QVariant>

enum class ALARM_ID_ENUM : unsigned short
{
    //呼吸机参数
    VALUEDATA_ENUM_BEGIN,
    VALUEDATA_VTE = VALUEDATA_ENUM_BEGIN,
    VALUEDATA_VTI,
    VALUEDATA_MV,
    VALUEDATA_RATE,
    VALUEDATA_R,
    VALUEDATA_C,
    VALUEDATA_C20C,
    VALUEDATA_FSPN,
    VALUEDATA_MVSPN,
    VALUEDATA_IE,

    VALUEDATA_PPEAK,
    VALUEDATA_TOP_PAW,
    VALUEDATA_PPLAT,
    VALUEDATA_PEEP,
    VALUEDATA_PMEAN,


    //O2,CO2参数
    VALUEDATA_FIO2,
    VALUEDATA_FICO2,
    VALUEDATA_ETCO2,
    VALUEDATA_FIN2O,
    VALUEDATA_ETN2O,
    //麻醉气体参数
    VALUEDATA_FI_AA1,
    VALUEDATA_FI_AA2,

    VALUEDATA_FIHAL,
    VALUEDATA_FIENF,
    VALUEDATA_FISEV,
    VALUEDATA_FIISO,
    VALUEDATA_FIDES,

    VALUEDATA_ET_AA1,
    VALUEDATA_ET_AA2,

    VALUEDATA_ETHAL,
    VALUEDATA_ETENF,
    VALUEDATA_ETSEV,
    VALUEDATA_ETISO,
    VALUEDATA_ETDES,

    VALUEDATA_MAC,	//tbd

    //血氧参数
    VALUEDATA_SPO2,
    VALUEDATA_PR,

    VALUEDATA_FLOWMETER_O2,
    VALUEDATA_FLOWMETER_AIR,
    VALUEDATA_FLOWMETER_N2O,
    VALUEDATA_ENUM_END,
};

//数据库所有表初始列定义
//仪器历史数据
extern const QString gCreateMachineHistoryDataTableQuery;

extern const QString gCreateTrendHistoryDataTableQuery;

//--报警限值
extern const QString gCreateAlarmLimitTableQuery;

//环图历史数据
extern const QString gCreateLoopHistoryDataTableQuery;

//系统设置
extern const QString gCreateSystemSettingTableQuery;

//系统配置
extern const QString gCreateSystemConfigTableQuery;

//数据库日志
extern const QString gCreateDatabaseLogTable;

extern const QStringList gTableCreateList;

//各id对应的名字
extern const QStringList gSystemSettingIdNameTable;
extern const QStringList gSystemConfigIdNameTable;
extern const QStringList gAlarmIdNameTable;


//系统配置默认值
extern const QVariant gSystemConfigDefaultValues[];

//报警限默认值
extern const QPair<int, int> gAlarmLimitDefaultValues[];

extern QMap<QString, QString> gMappingTableNameToTableCreateQuery;
#endif

