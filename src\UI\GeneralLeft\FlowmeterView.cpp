﻿#include <QHBoxLayout>
#include <QVBoxLayout>
#include <QGridLayout>

#include "UiApi.h"

#include "FlowmeterView.h"
#include "String/UIStrings.h"
#include "SystemConfigManager.h"
#include "SystemSettingManager.h"
#include "FlowmeterManager.h"
#include "SettingSpinbox/SettingSpinbox.h"
#include "HorizontalTabWidget.h"

#include "MainWindow/MainWindow.h"
#include "FlowControlDialog.h"
#include "FlowmeterWidget.h"

#include "ParamLabel/ParamLabelLayoutHorizontal.h"
#include "FlowmeterManager.h"
#include "ParamLabelManage.h"

#define WIDGET_SPACING  2

#define FLOWMETER_SIZE 80,300

#define SETTING_BOX_HEIGHT 94
#define BLACK_WHITE_BACKGROUND_PIXMAP   (":/flowmeter/AirBackground.png")
#define BLUE_BACKGROUND_PIXMAP    (":/flowmeter/O2Background.png")
#define WHITE_BACKGROUND_PIXMAP   (":/flowmeter/N2oBackground.png")

#define DEFAULT_WIDTH 545
#define FLOW_VALUE_DIGIT 2

static QMap<int, ALL_STRINGS_ENUM> sFlowNameMap
{
    {FLOWMETER_O2,   STR_GASNAME_O2},
    {FLOWMETER_N2O,  STR_GASNAME_N2O},
    {FLOWMETER_AIR,  STR_GASNAME_AIR}
};

FlowmeterView::FlowmeterView(QWidget* parent):FocusWidget(parent)
{
    InitValue();
    InitUi();
    InitConnect();
}

void FlowmeterView::SetRange(int type,int minValue, int maxValue)
{
    if(mFlowmeterWidgets.find(type)!=mFlowmeterWidgets.end())
    {
        mFlowmeterWidgets[type]->SetRange(minValue,maxValue);
    }
}



void FlowmeterView::RefurbishValue(E_FLOWMETER_TYPE type, short flowValue)
{
    mFlowmeterWidgets[type]->SetValue(flowValue);

    double totalValue{};
    bool invalidFlag{false};
    for (auto &item : mFlowmeterWidgets)
    {
        if (item->isVisible())
        {
            if(item->GetValue() != INVALID_VALUE)
            {
                totalValue += item->GetValue();
            }
            else
            {
                invalidFlag = true;
            }
        }
    }
    if (invalidFlag)
        mTotalLabel->SetValue("---");
    else
        mTotalLabel->SetValue(QString::number(totalValue / 100, 'f', FLOW_VALUE_DIGIT));
}


void FlowmeterView::InitUiText()
{
    mTotalLabel->SetName(UIStrings::GetStr(STR_TOTAL));
    mTotalLabel->SetUnit(UnitManager::GetNameStr(U_LPERMIN));

    QList<SettingSpinbox *> boxList = findChildren<SettingSpinbox *>();
    for (int i = 0; i < boxList.count(); ++i)
    {
        boxList.at(i)->InitUiText();
    }

    for (int i = 0; i < E_FLOWMETER_TYPE::FLOWMETER_MAX; ++i)
    {
        mFlowmeterWidgets[static_cast<E_FLOWMETER_TYPE>(i)]->SetName(UIStrings::GetStr(sFlowNameMap[i]));
        mFlowmeterWidgets[static_cast<E_FLOWMETER_TYPE>(i)]->SetUnit(UIStrings::GetStr(STR_UNIT_LPERMIN));
    }
}

bool FlowmeterView::eventFilter(QObject *watched, QEvent *event)
{
    if (event->type() == QEvent::MouseButtonPress)
    {
        if (dynamic_cast<FlowmeterWidget *>(watched) != nullptr || dynamic_cast<ParamLabelLayoutHorizontal *>(watched) != nullptr
                || dynamic_cast<FlowParamLabel *>(watched) != nullptr)
        {
            if (ConfigManager->IsFullElecFlomter())
            {
                MainWindow::GetInstance()->showDialog(MainWindow::DLG_FLOWMETER_CONTROL);
            }
        }
    }
    return false;
}

void FlowmeterView::InitValue()
{
}


struct FlowmeterDisplayInfoSt
{
    QString mPatternUrl;
    QColor mColor;
    QColor mScaleColor;
};


static FlowmeterDisplayInfoSt displayScheme1{BLUE_BACKGROUND_PIXMAP, COLOR_BLUE, COLOR_WHITE};
static FlowmeterDisplayInfoSt displayScheme2{WHITE_BACKGROUND_PIXMAP, COLOR_WHITE, QColor(134, 134, 134)};
static FlowmeterDisplayInfoSt displayScheme3{BLACK_WHITE_BACKGROUND_PIXMAP, COLOR_DARK_GRAY, COLOR_WHITE};

static QMap<int, FlowmeterDisplayInfoSt> sFlowInfoMap
{
    {FLOWMETER_O2,   displayScheme1},
    {FLOWMETER_N2O,  displayScheme2},
    {FLOWMETER_AIR,  displayScheme3}
};




void FlowmeterView::InitUi()
{
    StyleSet::SetBackgroundColor(this,COLOR_BLACK);
    setFocusPolicy(Qt::NoFocus);

    switch(ConfigManager->GetConfig<int>(SystemConfigManager::MACHINE_RULE_STANDARD_INDEX))
    {
    case STD_CHINA:
        break;
    case STD_EUROPE:
        sFlowInfoMap[FLOWMETER_O2] = displayScheme2;
        sFlowInfoMap[FLOWMETER_N2O] = displayScheme1;
        sFlowInfoMap[FLOWMETER_AIR] = displayScheme3;
        break;
    case STD_USA:
        //        flowmeter_color[FLOWMETER_O2] = &flowmter_color[FM_GREEN];    //green
        //        flowmeter_color[FLOWMETER_N2O] = &flowmter_color[FM_BLUE];    //蓝色
        //        flowmeter_color[FLOWMETER_AIR] = &flowmter_color[FM_YELLOW];  //黄色

        //tbd
        sFlowInfoMap[FLOWMETER_O2] = displayScheme2;
        sFlowInfoMap[FLOWMETER_N2O] = displayScheme1;
        sFlowInfoMap[FLOWMETER_AIR] = displayScheme3;
        break;
    }

    for (int i = 0; i < E_FLOWMETER_TYPE::FLOWMETER_MAX; ++i)
    {
        mFlowmeterWidgets[static_cast<E_FLOWMETER_TYPE>(i)] = new FlowmeterWidget(this);
        mFlowmeterWidgets[static_cast<E_FLOWMETER_TYPE>(i)]->setVisible(false);

        mFlowmeterWidgets[static_cast<E_FLOWMETER_TYPE>(i)]->SetName(UIStrings::GetStr(sFlowNameMap[i]));
        mFlowmeterWidgets[static_cast<E_FLOWMETER_TYPE>(i)]->SetUnit(UIStrings::GetStr(STR_UNIT_LPERMIN));
        mFlowmeterWidgets[static_cast<E_FLOWMETER_TYPE>(i)]->SetColor(sFlowInfoMap[i].mColor);
    }

    QVector<int> idVec{};

    idVec << FLOWMETER_O2;
    if ( ConfigManager->GetConfig<bool>(SystemConfigManager:: GAS_SOURCE_N2O_BOOL_INDEX ) )
    {//带N2O电子流量计
        idVec << FLOWMETER_N2O;
        if (ConfigManager->GetConfig<bool>(SystemConfigManager:: GAS_SOURCE_AIR_BOOL_INDEX ) )
        {//带AIR电子流量计 ---氧笑空
            idVec << FLOWMETER_AIR;
        }
    }
    else
    {//不带N2O电子流量计
        if ( ConfigManager->GetConfig<bool>(SystemConfigManager:: GAS_SOURCE_AIR_BOOL_INDEX ) )
        {//带AIR电子流量计     --------氧空
            idVec << FLOWMETER_AIR;
        }
    }

    auto flowmeterLayout = new QHBoxLayout;
    flowmeterLayout->setContentsMargins(WIDGET_SPACING, 0, WIDGET_SPACING, 0);
    flowmeterLayout->setSpacing(WIDGET_SPACING);
    foreach (auto id, idVec)
    {
        if (!ConfigManager->GetConfig<bool>(SystemConfigManager::ENABLE_FLOWMETER_MODULE))
        {
            break;
        }

        if(sFlowInfoMap.find(id) != sFlowInfoMap.end())
        {
            mFlowmeterWidgets[id]->installEventFilter(this);
            mFlowmeterWidgets[id]->setVisible(true);
            mFlowmeterWidgets[id]->SetPattern(QPixmap(sFlowInfoMap[id].mPatternUrl));
            flowmeterLayout->addWidget(mFlowmeterWidgets[id]);
        }
        else
        {
            DebugLog<<("invalidate flowmeter\n");
            DebugAssert(0);
        }
    }

    mTotalLabel = new ParamLabelLayoutHorizontal;
    mTotalLabel->installEventFilter(this);
    mTotalLabel->SetLabelBgColor(COLOR_BLACK);
    mTotalLabel->SetNameColor(COLOR_BLUE);
    mTotalLabel->SetUnitColor(COLOR_DARK_GRAY);
    mTotalLabel->SetValueColor(COLOR_BLUE);

    mTotalLabel->SetLimitVisible();
    mTotalLabel->SetName(UIStrings::GetStr(STR_TOTAL));
    mTotalLabel->SetUnit(UnitManager::GetNameStr(U_LPERMIN));
    mTotalLabel->SetValue("---");
    mTotalLabel->SetLabelScale(ParamLabelLayoutHorizontal::LABEL_SCALE_ENUM::SMALL);
    mTotalLabel->setFixedWidth(160);

    if (!ConfigManager->GetConfig<bool>(SystemConfigManager::ENABLE_FLOWMETER_MODULE))
    {
        mTotalLabel->hide();
    }

    if (ConfigManager->IsFullElecFlomter())
    {
        mTotalFlow = new SettingSpinbox;
        mTotalFlow->setFixedHeight(SETTING_BOX_HEIGHT);
        FlowmeterManager::GetInstance()->RegisterFlowControlButton(CONTROL_BUTTON_TYPE::TOTAL,mTotalFlow);
        mO2TotalFlow = new SettingSpinbox;
        mO2TotalFlow->setFixedHeight(SETTING_BOX_HEIGHT);
        FlowmeterManager::GetInstance()->RegisterFlowControlButton(CONTROL_BUTTON_TYPE::O2,mO2TotalFlow);
        mO2Percent = new SettingSpinbox;
        mO2Percent->setFixedHeight(SETTING_BOX_HEIGHT);
        FlowmeterManager::GetInstance()->RegisterFlowControlButton(CONTROL_BUTTON_TYPE::O2PCT,mO2Percent);
        mAirFlow = new SettingSpinbox;
        mAirFlow->setFixedHeight(SETTING_BOX_HEIGHT);
        FlowmeterManager::GetInstance()->RegisterFlowControlButton(CONTROL_BUTTON_TYPE::AIR,mAirFlow);
        mN2OTotalFlow = new SettingSpinbox;
        mN2OTotalFlow->setFixedHeight(SETTING_BOX_HEIGHT);
        FlowmeterManager::GetInstance()->RegisterFlowControlButton(CONTROL_BUTTON_TYPE::N2O,mN2OTotalFlow);
    }

    QHBoxLayout* controlLayout = new QHBoxLayout;
    controlLayout->setContentsMargins(0, 0, 0, 0);
    controlLayout->setSpacing(WIDGET_SPACING);
    controlLayout->addWidget(mTotalFlow);
    controlLayout->addWidget(mO2TotalFlow);
    controlLayout->addWidget(mO2Percent);
    controlLayout->addWidget(mAirFlow);
    controlLayout->addWidget(mN2OTotalFlow);


    auto controls = new FocusWidget(this);
    controls->setLayout(controlLayout);
    controls->setFixedHeight(SETTING_BOX_HEIGHT);
    controls->setFocusPolicy(Qt::NoFocus);


    QVBoxLayout *mainLayout = new QVBoxLayout;

    if(SystemConfigManager::GetInstance()->GetConfig<int>(SystemConfigManager::MACHINE_MODEL_INDEX) == CENAR_30M)
    {
        for(int i = 0; i < 5; i++)
        {
            mParamLabels << new ParamLabelLayoutHorizontal;
            mParamLabels[i]->SetLabelScale(ParamLabelLayoutHorizontal::LABEL_SCALE_ENUM::MID);
            mParamLabels[i]->setVisible(false);
        }

        RefurbishParamLabel();

        mainLayout->setSpacing(0);
        mainLayout->setContentsMargins(4,2,2,2);
        for(int i = 0; i < mParamLabels.size(); i++)
        {
            mainLayout->addWidget(mParamLabels[i]);
        }
    }
    else
    {
        mainLayout->setContentsMargins(3, 3, 3, 2);
        mainLayout->setSpacing(0);
        mainLayout->addLayout(flowmeterLayout);
        mainLayout->addSpacing(12);
        mainLayout->addWidget(mTotalLabel);
        mainLayout->addSpacing(12);
        mainLayout->setAlignment(mTotalLabel, Qt::AlignHCenter);

        if (ConfigManager->IsFullElecFlomter())
            mainLayout->addWidget(controls);
        else
            controls->hide();
    }

    setLayout(mainLayout);
}

void FlowmeterView::InitConnect()
{
    FlowmeterManager::GetInstance()->RegisterFlometerWidget(this);

    connect(SettingManager,
            &SystemSettingManager::SignalSettingChanged,
            this,
            [this](int settingId){
        if (settingId == SystemSettingManager::FLOW_CONTROL_BANLANCE_TYPE && ConfigManager->IsFullElecFlomter())
        {
            auto type = SettingManager->GetIntSettingValue(SystemSettingManager::FLOW_CONTROL_BANLANCE_TYPE);
            SlotOnBlanceTypeChanged(type);
        }
    });
    if (ConfigManager->IsFullElecFlomter())
        SlotOnBlanceTypeChanged(SettingManager->GetIntSettingValue(SystemSettingManager::FLOW_CONTROL_BANLANCE_TYPE));

    connect(SettingManager, &SystemSettingManager::SignalSettingChanged, this, [this](int id)
    {
        if (id >= SystemSettingManager::VALUE_UPAREA_1 && id <= SystemSettingManager::VALUE_DOWNAREA_3)
        {
            RefurbishParamLabel();
        }
    });

    connect(RunModeManage::GetInstance(), &RunModeManage::SignalModeChanged, this, [=](E_RUNMODE pre , E_RUNMODE now){
        bool isVisble = false;
        if(now == MODE_RUN_STANDBY)
        {
            isVisble = false;
        }
        else
        {
            isVisble = true;
        }
        for (int i = 0; i < mParamLabels.size(); i++)
        {
            mParamLabels[i]->setVisible(isVisble);
        }
    });
}

void FlowmeterView::RefurbishUi()
{
    int cnt = 0;
    foreach (auto i, mFlowmeterWidgets)
    {
        if (i->isVisible())
            ++cnt;
    }

    if (cnt > 2)
    {
        foreach (auto i, mFlowmeterWidgets)
        {
            i->SetOnlyBig(true);
        }
    }
    else
    {
        foreach (auto i, mFlowmeterWidgets)
        {
            i->SetOnlyBig(false);
        }
    }

    int singleWidth = 0;
    if(cnt == 2 || cnt == 1)
    {
        singleWidth = (width() - WIDGET_SPACING * 3) / 2;
    }
    else if(cnt == 3)
    {
        singleWidth = (width() - WIDGET_SPACING * 4) / 3;
    }
    foreach (auto i, mFlowmeterWidgets)
    {
        if (i->isVisible())
        {
            i->setFixedWidth(singleWidth);
        }
    }
}

void FlowmeterView::RefurbishParamLabel()
{
    QVector<MONITOR_PARAM_TYPE> mMonitorParamVec;
    mMonitorParamVec << VALUEDATA_VTE << VALUEDATA_VTI << VALUEDATA_MV << VALUEDATA_RATE << VALUEDATA_R << VALUEDATA_C << VALUEDATA_FSPN << VALUEDATA_MVSPN << VALUEDATA_IE
                     << VALUEDATA_PPEAK << VALUEDATA_PPLAT << VALUEDATA_PEEP << VALUEDATA_PMEAN;
    for (int i = SystemSettingManager::VALUE_UPAREA_1; i <= SystemSettingManager::VALUE_DOWNAREA_3; i++)
    {
        short value = SettingManager->GetIntSettingValue((SystemSettingManager::SYSTEM_SETTING_ENUM)i);
        if (mMonitorParamVec.contains((MONITOR_PARAM_TYPE)value))
        {
            mMonitorParamVec.removeAll((MONITOR_PARAM_TYPE)value);
        }
    }

    for (int i = 0; i < mMonitorParamVec.size(); ++i)
    {
        if(i >= mParamLabels.size())
            continue;
        MONITOR_PARAM_TYPE param = mMonitorParamVec[i];
        ParamLabelManage::GetInstance()->RegisterParamView(param, mParamLabels[i]);
    }
}

void FlowmeterView::showEvent(QShowEvent *)
{
    RefurbishUi();
}



void FlowmeterView::SlotOnBlanceTypeChanged(int type)
{
    switch(type)
    {
    case BALANCE_GAS_TYPE::NONE:
        mFlowmeterWidgets[FLOWMETER_O2]->setVisible(true);
        mFlowmeterWidgets[FLOWMETER_N2O]->setVisible(false);
        mFlowmeterWidgets[FLOWMETER_AIR]->setVisible(false);
        break;
    case BALANCE_GAS_TYPE::AIR_BALANCE:
        mFlowmeterWidgets[FLOWMETER_O2]->setVisible(true);
        mFlowmeterWidgets[FLOWMETER_N2O]->setVisible(false);
        mFlowmeterWidgets[FLOWMETER_AIR]->setVisible(true);
        break;
    case BALANCE_GAS_TYPE::N2O_BALANCE:
        mFlowmeterWidgets[FLOWMETER_O2]->setVisible(true);
        mFlowmeterWidgets[FLOWMETER_N2O]->setVisible(true);
        mFlowmeterWidgets[FLOWMETER_AIR]->setVisible(false);
        break;
    }

    RefurbishUi();
}
