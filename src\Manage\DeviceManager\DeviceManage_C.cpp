﻿#include <QKeyEvent>
#include "DeviceManage_C.h"
#include "AlarmManager.h"
#include "AlarmLed.h"
#include "Lcd.h"
#include "Battery.h"
#include "Key.h"
#include "Knob.h"
#include "audio.h"
#include "Inputscan.h"
#include "KeyConfig.h"
#include "SystemSoundManager.h"
#include "SystemSettingManager.h"
#include "gpio.h"
DeviceManage_C* CurDevice {};
DeviceManage_C::DeviceManage_C()
{
    CurDevice = this;
}


void DeviceManage_C::Init()
{
    register_key_event(excuteKeyEvent);
    register_knob_event(excuteKnobEvent);
    SetEnableAudio(true);
    AlarmManager::GetInstance()->DisableAnAlarm(PROMPT_ONLY_ONE_BATTERY);
}


int DeviceManage_C::GetBatteryStatus()
{
    return  battery_status();
}
#define BATTERY_DIGTS 100
#define AD_PCT100 1600
#define AD_PCT75 1512
#define AD_PCT50 1446
#define AD_PCT25 1412
#define AD_PCT0 1300
double DeviceManage_C::GetBatteryVoltage()
{
    return  battery_voltage();
}

int DeviceManage_C::getCurBatteryPCT()
{
    double cuBatVoltage =  battery_voltage();
    int curAD = cuBatVoltage*BATTERY_DIGTS;
    if(curAD>AD_PCT75)
        return PCT_100;
    else if(curAD>AD_PCT50)
        return PCT_75;
    else if(curAD>AD_PCT25)
        return PCT_50;
    else if(curAD>AD_PCT0)
        return PCT_25;
    else
        return PCT_0;
}

double DeviceManage_C::GetAcStatu()
{
    return ac_status();
}

int DeviceManage_C::GetVoltageStatu()
{
    return voltage_status();
}

void DeviceManage_C::run()
{
    for(int i = LED_1;i<LED_TYPE_MAX;i++)
    {
        mAllLedObject[i].mCurState=LED_STATU_NONE;
        mAllLedObject[i].mCntrolState=LED_OP_NONE;
        mAllLedObject[i].mBlinkOnMs=0;
        mAllLedObject[i].mBlinkOffMs=0;
    }
    mKnobThread.start();
    while(1)
    {
        ExcuteLedEvent();
#ifdef ARM
        key_scan();
#endif

#ifdef KEY_DEBUG
        int eventType = KEY_1_PRESS;
        while(1)
        {
            _sleep(200);
            excuteKnobEvent(KNOB_LEFT);
            //excuteKeyEvent(eventType++);
            //            if(eventType>=KEY_10_RELEASE)
            //                eventType = KEY_1_PRESS;
        }

#endif
    }
}

int DeviceManage_C::excuteKeyEvent(int eventType)
{
    int keyNumber=-1, keyEvnetType=-1;
    switch (eventType) {
    case KEY_1_PRESS:
    {
        keyNumber =KEY_SOUND; keyEvnetType=QEvent::KeyPress;
    }
        break;
    case KEY_1_RELEASE:
    {
        keyNumber =KEY_SOUND; keyEvnetType=QEvent::KeyRelease;
    }
        break;
    case KEY_2_PRESS:
    {
        keyNumber =KEY_MAIN_WINDOW; keyEvnetType=QEvent::KeyPress;
    }
        break;
    case KEY_2_RELEASE:
    {
        keyNumber =KEY_MAIN_WINDOW; keyEvnetType=QEvent::KeyRelease;
    }
        break;
    case KEY_3_PRESS:
    {
        keyNumber =KEY_RESERVE_3; keyEvnetType=QEvent::KeyPress;
    }
        break;
    case KEY_3_RELEASE:
    {
        keyNumber =KEY_RESERVE_3; keyEvnetType=QEvent::KeyPress;
    }
        break;
    case KEY_4_PRESS:
    {
        keyNumber =KEY_STANDBY_CONFIRM; keyEvnetType=QEvent::KeyPress;
    }
        break;
    case KEY_4_RELEASE:
    {
        keyNumber =KEY_STANDBY_CONFIRM; keyEvnetType=QEvent::KeyRelease;
    }
        break;
    case KEY_5_PRESS:
    {
        keyNumber =KEY_ALARM_MENU; keyEvnetType=QEvent::KeyPress;
    }
        break;
    case KEY_5_RELEASE:
    {
        keyNumber =KEY_ALARM_MENU; keyEvnetType=QEvent::KeyRelease;
    }
        break;
    case KEY_6_PRESS:
    {
        keyNumber =KEY_MAIN_MENU; keyEvnetType=QEvent::KeyPress;
    }
        break;
    case KEY_6_RELEASE:
    {
        keyNumber =KEY_MAIN_MENU; keyEvnetType=QEvent::KeyRelease;
    }
        break;
    case KEY_7_PRESS:
    {
        keyNumber =KEY_FREEZE; keyEvnetType=QEvent::KeyPress;
    }
        break;
    case KEY_7_RELEASE:
    {
        keyNumber =KEY_FREEZE; keyEvnetType=QEvent::KeyRelease;
    }
        break;
    case KEY_8_PRESS:
    {
        keyNumber =KEY_RESERVE_1; keyEvnetType=QEvent::KeyPress;
    }
        break;
    case KEY_8_RELEASE:
    {
        keyNumber =KEY_RESERVE_1; keyEvnetType=QEvent::KeyPress;
    }
        break;
    case KEY_9_PRESS:
    {
        keyNumber =KEY_RESERVE_2; keyEvnetType=QEvent::KeyPress;
    }
        break;
    case KEY_9_RELEASE:
    {
        keyNumber =KEY_RESERVE_2; keyEvnetType=QEvent::KeyPress;
    }
        break;
    case KEY_10_PRESS:
    {
        keyNumber =KEY_SYS_TIMER; keyEvnetType=QEvent::KeyPress;
    }
        break;
    case KEY_10_RELEASE:
    {
        keyNumber =KEY_SYS_TIMER; keyEvnetType=QEvent::KeyRelease;
    }
        break;
    case KEY_11_PRESS:
    {
    }
        break;
    case KEY_11_RELEASE:
    {
    }
        break;
    case KEY_12_PRESS:
    {
    }
        break;
    case KEY_12_RELEASE:
    {
    }
    default:
        break;
    }
    SendKeyEnvent(keyEvnetType,keyNumber);
    return true;
}
int DeviceManage_C::excuteKnobEvent(int eventType)
{
    int keyNumber=-1, keyEvnetType=-1;
    switch (eventType) {
    case KNOB_PRESS:
    {
        keyNumber =KEY_PRESS; keyEvnetType=QEvent::KeyPress;
        SendKeyEnvent(keyEvnetType,keyNumber);
    }
        break;
    case KNOB_RELEASE:
    {
        keyNumber =KEY_PRESS; keyEvnetType=QEvent::KeyRelease;
        SendKeyEnvent(keyEvnetType,keyNumber);
    }
        break;
    case KNOB_LEFT:
    {
        keyNumber =KEY_ANTI_CLOSEWISE; keyEvnetType=QEvent::KeyPress;
        SendKeyEnvent(keyEvnetType,keyNumber);
        keyNumber =KEY_ANTI_CLOSEWISE; keyEvnetType=QEvent::KeyRelease;
        SendKeyEnvent(keyEvnetType,keyNumber);
    }
        break;
    case KNOB_RIGHT:
    {
        keyNumber =KEY_CLOSEWISE; keyEvnetType=QEvent::KeyPress;
        SendKeyEnvent(keyEvnetType,keyNumber);
        keyNumber =KEY_CLOSEWISE; keyEvnetType=QEvent::KeyRelease;
        SendKeyEnvent(keyEvnetType,keyNumber);
    }
        break;
    default:
        break;
    }
    return true;
}

void DeviceManage_C::SendKeyEnvent(int action, int key)
{
    static bool isFirst =true;
    if(isFirst)
    {
        isFirst=false;
        return;
    }
    emit CurDevice->signalKeyEvent(action,key);

    if(action==QEvent::KeyRelease)
    {
        if(key ==KEY_PRESS||(key>=KEY_MAIN_WINDOW&&key<=KEY_SOUND))
            CurDevice->playKeyaudio(BIANBIAN);
        else
            CurDevice->playKeyaudio(DINGDONG);
    }


}
void KnobThread::run()
{
    while (1) {
        knob_scan();
    }
}
