﻿#include "DBInit.h"
#include "protocol.h"
#include "SystemSettingManager.h"
#include "SystemConfigManager.h"



const QString gCreateMachineHistoryDataTableQuery = R"(
    CREATE TABLE HistoryData (
        HistoryDataId       INTEGER PRIMARY KEY AUTOINCREMENT,
        Time                INTEGER,
        WorkModel           INTEGER,
        VTI                 INTEGER,
        VTE                 INTEGER,
        MV                  INTEGER,
        Rate                INTEGER,
        Fspn                INTEGER,
        Pplat               INTEGER,
        Ppeak               INTEGER,
        Peep                INTEGER,
        EtCo2               INTEGER,
        C                   INTEGER,
        R                   INTEGER,
        MVSpn               INTEGER,
        FiCo2               INTEGER
    )
)";


const QString gCreateTrendHistoryDataTableQuery = R"(
    CREATE TABLE TrendHistoryData (
        DefaultPK           INTEGER PRIMARY KEY AUTOINCREMENT,
        Time                INTEGER,
        VTI                 INTEGER,
        VTE                 INTEGER,
        MV                  INTEGER,
        Rate                INTEGER,
        Fspn                INTEGER,
        Pplat               INTEGER,
        Ppeak               INTEGER,
        Peep                INTEGER,
        EtCo2               INTEGER,
        C                   INTEGER,
        R                   INTEGER,
        MVSpn               INTEGER,
        FiCo2               INTEGER
    )
)";

//--报警限值
const QString gCreateAlarmLimitTableQuery = R"(
    CREATE TABLE AlarmLimit (
        DataType        TEXT PRIMARY KEY NOT NULL,
        LowLimit        INTEGER,
        HighLimit       INTEGER
    )
)";

const QString gCreateHistoryEventTableQuery = R"(
    CREATE TABLE HistoryEvent (
        DefaultPK               INTEGER PRIMARY KEY AUTOINCREMENT,
        EventTime               INTEGER,
        EventType               INTEGER,
        EventId                   INTEGER,

        parameter1              INTEGER,
        parameter2              INTEGER,
        parameter3              INTEGER,
        parameter4              INTEGER,
        parameter5              INTEGER
    )
)";

//环图历史数据
const QString gCreateLoopHistoryDataTableQuery = R"(
    CREATE TABLE LoopHistory (
        DefaultPK           INTEGER PRIMARY KEY AUTOINCREMENT,
        Time                INTEGER,
        ValueX              TEXT,
        ValueY              TEXT,
        HistoryDataId       INTEGER,
        LoopType            INTEGER,

        CONSTRAINT fk_loophistorydata_historydata
        FOREIGN KEY (HistoryDataId)
        REFERENCES HistoryData(HistoryDataId)
    )
)";

//系统设置
const QString gCreateSystemSettingTableQuery = R"(
    CREATE TABLE SystemSetting (
        SettingId   TEXT PRIMARY KEY NOT NULL,
        Value       TEXT
    )
)";

//系统配置
const QString gCreateSystemConfigTableQuery = R"(
    CREATE TABLE SystemConfig (
        ConfigId    TEXT PRIMARY KEY NOT NULL,
        Value       TEXT
    )
)";

//TBD
//const QString gCreateDatabaseLogTable = R"(
//    CREATE TABLE DatabaseLog (
//        LogId     INT  PRIMARY KEY NOT NULL AUTOINCREMENT,
//        DbVersion TEXT,
//        Log       TEXT
//    )
//)";

const QString gCreateDbInfoTableQuery = R"(
    CREATE TABLE DbInfo (
        DbVersion TEXT,
        CurSoftwareVersion  TEXT
   )
)";

QMap<QString, QString> gMappingTableNameToTableCreateQuery;



const QStringList gTableCreateList
{
    gCreateAlarmLimitTableQuery,
            gCreateTrendHistoryDataTableQuery,
            gCreateMachineHistoryDataTableQuery,
            gCreateLoopHistoryDataTableQuery,
            gCreateSystemSettingTableQuery,
            gCreateSystemConfigTableQuery,
            gCreateDbInfoTableQuery,
            gCreateHistoryEventTableQuery,
};




const QHash<QString, int> gTableNumLimitMap
{
    {"AlarmLimit",                         1000            }          ,
    {"TrendHistoryData",                10000                     }           ,
    {"HistoryData",                  1000                 }           ,
    {"LoopHistory",                       30            }           ,
    {"SystemSetting",                  1000              }           ,
    {"SystemConfig",                    1000              }           ,
    {"DbInfo",                           2       }           ,
    {"HistoryEvent",                    1000                   }           ,
};

const QStringList gSystemSettingIdNameTable =
{
    "LANGUAGE_SETTING",
    "SOFT_MODE_SETTING",
    "VOLUME_SETTING",
    "PUNIT_SETTING",
    "CPB_SETTING",
    "TIMEFORMAT_SETTING",
    "DATEFORMAT_SETTING",
    "SYSTEM_TIME_Y",
    "SYSTEM_TIME_M",
    "SYSTEM_TIME_D",
    "TRIG_SETTING",
    "LOOP_POSITION_LEFT",
    "LOOP_POSITION_RIGHT",
    "TREND_GRAPH1",
    "TREND_GRAPH2",
    "TREND_GRAPH3",
    "TREND_PERIOD",
    "VALUE_UPAREA_1",
    "VALUE_UPAREA_2",
    "VALUE_UPAREA_3",
    "VALUE_MIDAREA_1",
    "VALUE_MIDAREA_2",
    "VALUE_MIDAREA_3",
    "VALUE_DOWNAREA_1",
    "VALUE_DOWNAREA_2",
    "VALUE_DOWNAREA_3",
    "WAVE_MID",
    "WAVE_BOTTOM",
    "WAVE_SPEED",
    "WAVE_DRAWMODE",
    "ASPHYXIA_TIME",
    "N2O_CONSISTENCY",
    "O2_CONSISTENCY",
    "AGUNIT_SETTING",
    "LCD_DEVICE_NAME",
    "FLOW_CONTROL_TYPE",
    "FLOW_CONTROL_TOTAL_VALUE",
    "FLOW_CONTROL_O2_VALUE",
    "FLOW_CONTROL_BANLANCE_TYPE",
    "FLOW_CONTROL_BANLANCE_GAS_VALUE",
    "LOOP_LOOPDETAIL_POSITION_LEFT",
    "LOOP_LOOPDETAIL_POSITION_RIGHT",
    "LOOP_AG"
};




const QStringList gAlarmIdNameTable =
{
    "VALUEDATA_VTE",
    "VALUEDATA_VTI",
    "VALUEDATA_MV",
    "VALUEDATA_RATE",
    "VALUEDATA_R",
    "VALUEDATA_C",
    "VALUEDATA_C20C",
    "VALUEDATA_FSPN",
    "VALUEDATA_MVSPN",
    "VALUEDATA_IE",

    "VALUEDATA_PPEAK",
    "VALUEDATA_TOP_PAW",
    "VALUEDATA_PPLAT",
    "VALUEDATA_PEEP",
    "VALUEDATA_PMEAN",

    //O2,CO2参数
    "VALUEDATA_FIO2",
    "VALUEDATA_FICO2",
    "VALUEDATA_ETCO2",
    "VALUEDATA_FIN2O",
    "VALUEDATA_ETN2O",

    //麻醉气体参数
    "VALUEDATA_FI_AA1",
    "VALUEDATA_FI_AGENT2",
    "VALUEDATA_FIHAL",
    "VALUEDATA_FIENF",
    "VALUEDATA_FISEV",
    "VALUEDATA_FIISO",
    "VALUEDATA_FIDES",

    "VALUEDATA_ET_AA1",
    "VALUEDATA_ET_AGENT2",
    "VALUEDATA_ETHAL",
    "VALUEDATA_ETENF",
    "VALUEDATA_ETSEV",
    "VALUEDATA_ETISO",
    "VALUEDATA_ETDES",

    "VALUEDATA_MAC",
    //血氧参数
    "VALUEDATA_SPO2",
    "VALUEDATA_PR",
    //流量计参数
    "VALUEDATA_FLOWMETER_O2",
    "VALUEDATA_FLOWMETER_N2O",
    "VALUEDATA_FLOWMETER_AIR",
};



const QVariant gSystemConfigDefaultValues[] =
{
    /* unsigned short network_config_bool  */       0,
    /* unsigned short network_monitor_bool  */      1,

    /* unsigned short machine_rule_standard  */     STD_CHINA,
    /* unsigned short machine_model  */             CENAR_30,

    //附件(存在为1，否则为0)
    /* unsigned short ag_bool  */                   0,
    /* unsigned short co2_bool  */                  0,
    /* unsigned short o2_bool  */                   0,
    /* unsigned short spo2_bool  */                 0,
    /* unsigned short loop_bool  */                 0,
    /* unsigned short touch_screen_bool  */         0,
    /* unsigned short touch_screen_type  */  	   0,

    /* unsigned short wave_num   */                 3,

    /* unsigned short enableFlowmeterModule  */     1,

    //气源(存在为1，否则为0)
    /* unsigned short gas_source_o2_bool  */        1,
    /* unsigned short gas_source_n2o_bool  */       0,
    /* unsigned short gas_source_air_bool  */       0,

    //呼吸模式(1为带该呼吸模式)
    /* unsigned short vcv_bool  */                  1,
    /* unsigned short pcv_bool  */                  1,
    /* unsigned short psv_bool  */                  0,
    /* unsigned short simvvc_bool  */               0,
    /* unsigned short simvpc_bool  */               0,
    /* unsigned short prvc_bool  */                 0,
    /* unsigned short pmode_bool  */                0,
    /* unsigned short hlm_bool  */                  0,

    /* unsigned short min_vt  */                    40,

    //出厂日期
    /* unsigned short manufacture_year  */          2013,
    /* unsigned short manufacture_month  */         6,
    /* unsigned short manufacture_day  */           6,

    //产品系列号
    /* unsigned short serial_moudle  */         "C3",        //C3
    /* unsigned short serial_year  */               13,        //13
    /* unsigned short serial_month  */              6,        //06
    /* unsigned short serial_num_1  */              0,          //0
    /* unsigned short serial_num_2  */              0,           //0
    /* unsigned short serial_num_3  */              0,           //0
    /* unsigned short serial_num_4  */              0,           //0

    /*VENT_MODE_NUM_INDEX*/               0,
    /*BIG_SCREEN_BOOL_INDEX*/              0,
    // 开机logo
    /* unsigned short enable_logo;  */  			1,

    //支持语言
    /* unsigned short enable_english;  */  			1,
    /* unsigned short enable_chinese;  */  			0,
    /* unsigned short enable_russian;  */  			0
};





const QPair<int, int> gAlarmLimitDefaultValues[]=
{
    /* VALUEDATA_VTE  */  		{100,		780},
    /* VALUEDATA_VTI  */  		{INVALID_VALUE,		INVALID_VALUE},
    /* VALUEDATA_MV  */  		{30,		90},
    /* VALUEDATA_RATE  */  		{5,		    50},
    /* VALUEDATA_R  */  			{INVALID_VALUE,		INVALID_VALUE},
    /* VALUEDATA_C  */  			{INVALID_VALUE,		INVALID_VALUE},
    /* VALUEDATA_C20C  */  		{INVALID_VALUE,		INVALID_VALUE},
    /* VALUEDATA_FSPN  */  		{INVALID_VALUE,		INVALID_VALUE},
    /* VALUEDATA_MVSPN  */  		{INVALID_VALUE,		INVALID_VALUE},
    /* VALUEDATA_IE  */  		{INVALID_VALUE,		INVALID_VALUE},

    /* VALUEDATA_PPEAK  */  		{2,			40},
    /* VALUEDATA_TOP_PAW  */  	{2,			40},
    /* VALUEDATA_PPLAT  */  		{INVALID_VALUE,		INVALID_VALUE},
    /* VALUEDATA_PEEP  */  		{INVALID_VALUE,		INVALID_VALUE},
    /* VALUEDATA_PMEAN  */  		{INVALID_VALUE,		INVALID_VALUE},

    //O2,CO2参数
    /* VALUEDATA_FIO2  */  		{18,		100},
    /* VALUEDATA_FICO2  */  		{0,			4},
    /* VALUEDATA_ETCO2  */  		{15,		50},
    /* VALUEDATA_FIN2O  */  		{0,			53},
    /* VALUEDATA_ETN2O  */  		{0,			55},

    //麻醉气体参数
    /* VALUEDATA_FI_AA1  */  	{INVALID_VALUE,		INVALID_VALUE},
    /* VALUEDATA_FI_AGENT2  */  	{0,			180},
    /* VALUEDATA_FIHAL  */  		{0,			50},
    /* VALUEDATA_FIENF  */  		{0,			50},
    /* VALUEDATA_FISEV  */  		{0,			80},
    /* VALUEDATA_FIISO  */  		{0,			50},
    /* VALUEDATA_FIDES  */  		{0,			150},

    /* VALUEDATA_ET_AA1  */  	{INVALID_VALUE,		INVALID_VALUE},
    /* VALUEDATA_ET_AGENT2  */  	{0,			180},
    /* VALUEDATA_ETHAL  */  		{0,			50},
    /* VALUEDATA_ETENF  */  		{0,			50},
    /* VALUEDATA_ETSEV  */  		{0,			70},
    /* VALUEDATA_ETISO  */  		{0,			50},
    /* VALUEDATA_ETDES  */  		{0,			100},

    /* VALUEDATA_MAC  */  		{INVALID_VALUE,		INVALID_VALUE},
    //血氧参数
    /* VALUEDATA_SPO2  */  		{ 90,        100},
    /* VALUEDATA_PR  */  		{ 30,		 250},
    //流量计参数
    /* VALUEDATA_FLOWMETER_O2  */   {INVALID_VALUE,	INVALID_VALUE},
    /* VALUEDATA_FLOWMETER_N2O  */  {INVALID_VALUE,	INVALID_VALUE},
    /* VALUEDATA_FLOWMETER_AIR  */  { INVALID_VALUE,	INVALID_VALUE}
};
