﻿#include "DataManager.h"
#include "DebugLogManager.h"
#include "String/UIStrings.h"
#include "SystemTimeManager.h"
#include "SystemConfigManager.h"
#include "ModuleTestManager.h"
#include "AGModule.h"

//最大最小有效值为超出允许范围20%
const QMap<MONITOR_PARAM_TYPE, MonitorParamInfoSt> DataManager::sParamDefaultMap
{                                                                        /* mMaxValidValue  */   /* mMinValidValue  */ /*mExponent*/
    {VALUEDATA_VTE,     {STR_VALUEPARAM_VTE, 	U_ML,       NONE_CATEGORY,          2500,	0,   0}},
    {VALUEDATA_VTI,     {STR_VALUEPARAM_VTI, 	U_ML,       NONE_CATEGORY,          2500,	0,   0}},
    {VALUEDATA_MV ,     {STR_VALUEPARAM_MV,  	U_LPERMIN,	NONE_CATEGORY,          6000,	0,   -2}},
    {VALUEDATA_RATE,    {STR_VALUEPARAM_RATE,   U_BPM,		NONE_CATEGORY,          105,    0,   0}},

    {VALUEDATA_R,       {STR_VALUEPARAM_R,  	U_CMH2OLS, 	NONE_CATEGORY,          200,	0,   0}},
    {VALUEDATA_C,       {STR_VALUEPARAM_C,  	U_MLCMH2O, 	NONE_CATEGORY,          200,	0,   0}},
    {VALUEDATA_C20C,    {STR_GRAPHNAME_C20C,	U_MAX,		NONE_CATEGORY,          2400,	0,  -2}},
    {VALUEDATA_FSPN,    {STR_VALUEPARAM_FSPN,   U_BPM,      NONE_CATEGORY,          100,	0,   0}},
    {VALUEDATA_MVSPN,   {STR_VALUEPARAM_MVSPN,  U_LPERMIN,  NONE_CATEGORY,          6000,	0,  -2}},
    {VALUEDATA_IE,      {STR_VALUEPARAM_IE,     U_MAX,		NONE_CATEGORY,          209,	63,  0}},


    {VALUEDATA_PPEAK,    {STR_VALUEPARAM_PPEAK,  U_CMH2O,   PRESSURE_CATEGORY,      120,	  -20,  0}},
    {VALUEDATA_TOP_PAW,  {STR_SETTINGLIMIT_PAW,  U_CMH2O,   PRESSURE_CATEGORY,      120,	  -20,  0}},
    {VALUEDATA_PPLAT,    {STR_VALUEPARAM_PPLAT,  U_CMH2O,   PRESSURE_CATEGORY,      120,	  -20,  0}},
    {VALUEDATA_PEEP,     {STR_VALUEPARAM_PEEP,   U_CMH2O,   PRESSURE_CATEGORY,      70,         0,  0}},
    {VALUEDATA_PMEAN,    {STR_VALUEPARAM_PMEAN,  U_CMH2O,   PRESSURE_CATEGORY,      120,	  -20,  0}},

    //O2,CO2参数
    {VALUEDATA_FIO2 ,     {STR_VALUEPARAM_FIO2, 	U_PERCENT, NONE_CATEGORY,       100,	18, 0}},
    {VALUEDATA_FICO2,     {STR_VALUEPARAM_FICO2,	U_PERCENT, CO2_CATEGORY,        150,    0,  -1}},
    {VALUEDATA_ETCO2,     {STR_VALUEPARAM_ETCO2,	U_PERCENT, CO2_CATEGORY,        150,    0,  -1}},
    {VALUEDATA_FIN2O,     {STR_VALUEPARAM_FIN2O,	U_PERCENT, NONE_CATEGORY,       100,	0,  0}},
    {VALUEDATA_ETN2O,     {STR_VALUEPARAM_ETN2O,	U_PERCENT, NONE_CATEGORY,       100,	0,  0}},

    //麻醉气体参数
    {VALUEDATA_FI_AA1,    {STR_VALUEPARAM_AG_FINULL,	U_PERCENT,	NONE_CATEGORY, 	1200,  0,   -1}},
    {VALUEDATA_FI_AA1,    {STR_VALUEPARAM_AG_FINULL,	U_PERCENT,	NONE_CATEGORY, 	1200,  0,   -1}},
    {VALUEDATA_FIHAL,     {STR_VALUEPARAM_FIHAL,        U_PERCENT,	NONE_CATEGORY,  80,    0,   -1}},
    {VALUEDATA_FIENF,     {STR_VALUEPARAM_FIENF,        U_PERCENT,	NONE_CATEGORY,  80,	   0,   -1}},
    {VALUEDATA_FISEV,     {STR_VALUEPARAM_FISEV,        U_PERCENT,	NONE_CATEGORY,  100,   0,   -1}},
    {VALUEDATA_FIISO,     {STR_VALUEPARAM_FIISO,        U_PERCENT,	NONE_CATEGORY,  80,	   0,   -1}},
    {VALUEDATA_FIDES,     {STR_VALUEPARAM_FIDES,        U_PERCENT,	NONE_CATEGORY,  220,   0,   -1}},


    {VALUEDATA_ET_AA1,    {STR_VALUEPARAM_AG_ETNULL,	U_PERCENT,	NONE_CATEGORY, 1200,   0,   -1}},
    {VALUEDATA_ET_AA2,    {STR_VALUEPARAM_AG_ETNULL,	U_PERCENT,	NONE_CATEGORY, 1200,   0,   -1}},
    {VALUEDATA_ETHAL,     {STR_VALUEPARAM_ETHAL,	U_PERCENT,	NONE_CATEGORY,     80,	   0,   -1}},
    {VALUEDATA_ETENF,     {STR_VALUEPARAM_ETENF,	U_PERCENT,	NONE_CATEGORY,     80,	   0,   -1}},
    {VALUEDATA_ETSEV,     {STR_VALUEPARAM_ETSEV,	U_PERCENT,	NONE_CATEGORY,     100,	   0,   -1}},
    {VALUEDATA_ETISO,     {STR_VALUEPARAM_ETISO,	U_PERCENT,	NONE_CATEGORY,     80,	   0,   -1}},
    {VALUEDATA_ETDES,     {STR_VALUEPARAM_ETDES,	U_PERCENT,	NONE_CATEGORY,     220,	   0,   -1}},

    {VALUEDATA_MAC,             {STR_MAC,	U_MAX, NONE_CATEGORY,  1200,				0,   -2}},

    {VALUEDATA_SPO2,            {STR_GRAPHNAME_SPO2, U_PERCENT,    NONE_CATEGORY,	100,	0,  0}},
    {VALUEDATA_PR,              {STR_VALUEPARAM_PR,	 U_MAX	,      NONE_CATEGORY,   250,	30, 0}},
    {VALUEDATA_FLOWMETER_O2 ,   {STR_GASNAME_O2,     U_LPERMIN,    NONE_CATEGORY,   1320,	0,  -2}},
    {VALUEDATA_FLOWMETER_AIR,   {STR_GASNAME_AIR,    U_LPERMIN,    NONE_CATEGORY,   1320,	0,  -2}},
    {VALUEDATA_FLOWMETER_N2O,   {STR_GASNAME_N2O,    U_LPERMIN,    NONE_CATEGORY,   1320,	0,  -2}},
};

//定点数-》浮点数
static const QMap<DataManager::WAVE_PARAM_TYPE, std::function<qreal(short)>> sWaveParamConvertFuncMap =
{
    {DataManager::WAVE_PAW, [](short data) -> qreal { return data / 10.0;  }},
    {DataManager::WAVE_FLOW,[](short data) -> qreal { return data / 100.0; }},
    {DataManager::WAVE_VOL, [](short data) -> qreal { return data;         }},
    {DataManager::WAVE_CO2, [](short data) -> qreal { return data / 100.0; }}
};


const QMap<DataManager::WAVE_PARAM_TYPE, WaveParamInfoSt> DataManager::sWaveParamInfoMap
{
    {DataManager::WAVE_PAW,    {STR_GRAPHNAME_PAW,    U_CMH2O, PRESSURE_CATEGORY, 120, -20 }},
    {DataManager::WAVE_FLOW,   {STR_GRAPHNAME_FLOW, U_LPERMIN, NONE_CATEGORY,     180, -180}},
    {DataManager::WAVE_VOL,    {STR_GRAPHNAME_VOL,       U_ML, NONE_CATEGORY,     2500, 0  }},
    {DataManager::WAVE_CO2,    {STR_GRAPHNAME_CO2,     U_PERCENT, CO2_CATEGORY,   15,   0  }}
};

static QMutex sDataMutex;

DataManager::DataManager(QObject *parent) : QObject(parent)
{
    RefurbishParamEnable();
    connect(SystemTimeManager::GetInstance(), &SystemTimeManager::SignalDateTimeChanged, this, &DataManager::SlotOnDateTimeChanged);
}

void DataManager::RefurbishParamEnable()
{
    for (int i = 0; i < VALUEDATA_MAX; ++i)
    {
        mMonitorParamEnableMap[(MONITOR_PARAM_TYPE)i] = true;
    }

    for (int i = 0; i < WAVE_PARAM_TYPE::WAVE_MAX; ++i)
    {
        mWaveParamEnableMap[(WAVE_PARAM_TYPE)i] = true;
    }
    //没有AG模块，无效相关设置
    if (ModuleTestManager::GetInstance()->GetAgModuleType() == CO2)
    {
        for (int i = VALUEDATA_FIN2O; i < VALUEDATA_ETDES; ++i)
        {
            mMonitorParamEnableMap[(MONITOR_PARAM_TYPE)i] = false;
        }
    }

    //无AG，无CO2,CO2设置无效
    if (ModuleTestManager::GetInstance()->GetAgModuleType() == None)
    {
        for (int i = VALUEDATA_FICO2; i < VALUEDATA_ETDES; ++i)
        {
            mMonitorParamEnableMap[(MONITOR_PARAM_TYPE)i] = false;
        }
        mWaveParamEnableMap[WAVE_PARAM_TYPE::WAVE_CO2] = false;
    }
    if (!ConfigManager->GetConfig<bool>(SystemConfigManager::O2_BOOL_INDEX))
    {
        mMonitorParamEnableMap[VALUEDATA_FIO2] = false;
    }
}

DataManager *DataManager::GetInstance()
{
    static DataManager sDataManager;
    return &sDataManager;
}

void DataManager::ResetAllParam()
{
    for (int i = VALUEDATA_BEGIN; i < VALUEDATA_MAX; i++)
    {
        SaveMonitorData(i, INVALID_VALUE);
    }
}

void DataManager::TimeRepair(QDateTime preTime)
{
    long timeVal = QDateTime::currentSecsSinceEpoch() - preTime.toSecsSinceEpoch();
    for (int i = VALUEDATA_BEGIN; i < VALUEDATA_MAX; i++)
    {
        sDataMutex.lock();
        mMonitorDataMap[(MONITOR_PARAM_TYPE)i].mTimestamp += timeVal;
        sDataMutex.unlock();
    }
}

void DataManager::SaveMonitorData(int id, int value)
{
    MONITOR_PARAM_TYPE eId = (MONITOR_PARAM_TYPE)id;
    if (id >= VALUEDATA_MAX)
    {
        DebugLog << "id >= VALUEDATA_MAX" << __FUNCTION__;
        DebugAssert(0);
    }

    if (mMonitorParamEnableMap[eId] == false)
        return;

    if(sParamDefaultMap[eId].mMaxValidValue < value || sParamDefaultMap[eId].mMinValidValue > value)
        value = INVALID_VALUE;

    sDataMutex.lock();
    mMonitorDataMap[eId].mValue = value;
    mMonitorDataMap[eId].mTimestamp = QDateTime::currentSecsSinceEpoch();
    sDataMutex.unlock();
}

bool DataManager::GetMonitorTypeEnable(int id)
{
    return mMonitorParamEnableMap[(MONITOR_PARAM_TYPE)id];
}

qreal DataManager::RevertToFloat(int id, int value)
{
    auto exponent = sParamDefaultMap[(MONITOR_PARAM_TYPE)id].mExponent;
    return value * qPow(10, exponent);
}

VALUE_STATUS_ENUM DataManager::GetMonitorData(int id, short &value)
{
    MONITOR_PARAM_TYPE eId = (MONITOR_PARAM_TYPE)id;
    if (id >= VALUEDATA_MAX)
    {
        DebugLog << "id >= VALUEDATA_MAX" << __FUNCTION__;
        DebugAssert(0);
    }

    if (mMonitorParamEnableMap[eId] == false)
    {
        value = INVALID_VALUE;
        return VALUE_STATUS_ENUM::VALUE_EXPIRED;
    }

    constexpr int EXPIRED_TIME = 30;	//30秒 数据过期时间
    value = INVALID_VALUE;

    uint curTimestamp = QDateTime::currentSecsSinceEpoch();

    QMutexLocker locker(&sDataMutex);
    if ((curTimestamp - mMonitorDataMap[eId].mTimestamp) > EXPIRED_TIME)
        return VALUE_STATUS_ENUM::VALUE_EXPIRED;

    value = mMonitorDataMap[eId].mValue;
    return VALUE_STATUS_ENUM::VALUE_VALIDE;
}

int DataManager::GetParamNameId(int id)
{
    DebugAssert (id >= 0 && id < VALUEDATA_MAX);
    return sParamDefaultMap[(MONITOR_PARAM_TYPE)id].mParamNameId;
}

QString DataManager::GetParamName(MONITOR_PARAM_TYPE paramType)
{
    DebugAssert (paramType >= 0 && paramType < VALUEDATA_MAX);
    return UIStrings::GetStr(sParamDefaultMap[paramType].mParamNameId);
}

int DataManager::ConvertIeValueToPct(short &percent, short value)
{
    short  ie_value_insp;
    short  ie_value_exp;

    if (INVALID_VALUE == value)
    {
        percent = -1;

        return 0;
    }
    else
    {
        if (value >100)
        {
            value = value - 100;
            ie_value_insp = 1;
            ie_value_exp = value +10;

            percent = ie_value_insp * 100 * 10 / ie_value_exp;   //I:E百分比，扩大100倍

        }
        else
        {
            value = 100 - value;
            ie_value_exp = 1;
            ie_value_insp = value +10;

            percent = ie_value_insp * 100 / 10;  //I:E百分比，扩大100倍
        }
    }
    return 1;
}

QString DataManager::ConvertIeValueToStr(short value)
{
    short ie_value_insp;
    short ie_value_exp;

    if (INVALID_VALUE == value)
    {
        return UIStrings::GetStr(STR_DASH);
    }
    else
    {
        if (value > 100)
        {
            value = value - 100;
            ie_value_insp = 1;
            ie_value_exp = value +10;
            if (ie_value_exp % 10)
            {
                return QString("%1:%2").arg(QString::number(ie_value_insp), QString::number((float)ie_value_exp/10, 'f', 1));
            }
            else
            {
                return QString("%1:%2").arg(QString::number(ie_value_insp), QString::number(ie_value_exp/10));
            }
        }
        else
        {
            value = 100 - value;
            ie_value_exp = 1;
            ie_value_insp = value +10;
            if (ie_value_insp % 10)
            {
                return QString("%1:%2").arg(QString::number((float)ie_value_insp/10, 'f', 1), QString::number(ie_value_exp));
            }
            else
            {
                return QString("%1:%2").arg(QString::number(ie_value_insp/10), QString::number(ie_value_exp));
            }
        }
    }
}

void DataManager::UpdateWaveData(WAVE_PARAM_TYPE dataType, short data, bool isSpont)
{
    DebugAssert(sWaveParamConvertFuncMap.contains(dataType));
    if ((sWaveParamConvertFuncMap[dataType]))
        mWaveDataMap[dataType] = sWaveParamConvertFuncMap[dataType](data);
    emit SignalWaveDataChanged(dataType, mWaveDataMap[dataType], isSpont);
}

qreal DataManager::GetWaveData(WAVE_PARAM_TYPE dataType)
{
    return mWaveDataMap.contains(dataType) ? mWaveDataMap[dataType] : INVALID_VALUE;
}

QString DataManager::GetParamName(WAVE_PARAM_TYPE dataType)
{
    return UIStrings::GetStr(sWaveParamInfoMap[dataType].mParamNameId);
}

UNIT_TYPE DataManager::GetDefaultUnit(WAVE_PARAM_TYPE paramType)
{
    DebugAssert(sWaveParamInfoMap.contains(paramType));
    return sWaveParamInfoMap[paramType].mDefaultUnit;
}

QString DataManager::GetParamCurUnit(WAVE_PARAM_TYPE paramType)
{
    auto unitId = sWaveParamInfoMap[paramType].mDefaultUnit;
    auto category = DataManager::GetWaveParamDefaultInfo(paramType).mConvertibleUnitCategory;
    if (category != CONVERTIBLE_UNIT_CATEGORY::NONE_CATEGORY)
    {
        unitId = UnitManager::GetInstance()->GetCategoryCurUnit(category);
    }
    auto unitStrId = UnitManager::GetStrId(unitId);
    return UIStrings::GetStr(unitStrId);
}

UNIT_TYPE DataManager::GetDefaultUnit(MONITOR_PARAM_TYPE paramType)
{
    DebugAssert(sParamDefaultMap.contains(paramType));
    return sParamDefaultMap[paramType].mDefaultUnit;
}

QString DataManager::GetParamCurUnitStr(MONITOR_PARAM_TYPE paramType)
{
    DebugAssert(sParamDefaultMap.contains(paramType));
    auto category = sParamDefaultMap[paramType].mConvertibleUnitCategory;
    if (category != NONE_CATEGORY)
    {
        return (UnitManager::GetNameStr(UnitManager::GetInstance()->GetCategoryCurUnit(category)));
    }
    else
    {
        return (UnitManager::GetNameStr(GetDefaultUnit(paramType)));
    }
}

CONVERTIBLE_UNIT_CATEGORY DataManager::GetParamUnitCategory(MONITOR_PARAM_TYPE paramType)
{
    DebugAssert(sParamDefaultMap.contains(paramType));
    return sParamDefaultMap[paramType].mConvertibleUnitCategory;
}

WaveParamInfoSt DataManager::GetWaveParamDefaultInfo(WAVE_PARAM_TYPE paramType)
{
    DebugAssert(sWaveParamInfoMap.contains(paramType));
    return sWaveParamInfoMap[paramType];
}

bool DataManager::GetWaveTypeEnable(int id)
{
    DebugAssert(id >= 0 || id < WAVE_PARAM_TYPE::WAVE_MAX);
    RefurbishParamEnable();
    return mWaveParamEnableMap[(WAVE_PARAM_TYPE)id];
}

QPair<int, int> DataManager::GetRange(int id)
{
    return {sWaveParamInfoMap[(WAVE_PARAM_TYPE)id].mMinValidValue, sWaveParamInfoMap[(WAVE_PARAM_TYPE)id].mMaxValidValue};
}

void DataManager::SlotOnDateTimeChanged(QDateTime preDateTime, QDateTime newDateTime)
{
    Q_UNUSED(newDateTime)
    TimeRepair(preDateTime);
}

int ConvertIeValueToStr(char * IE_str, short value, unsigned short len)
{
    if (len < 4)
    {
        return 0;
    }

    short  ie_value_insp;
    short  ie_value_exp;

    if (INVALID_VALUE == value)
    {
        strcat(IE_str, UIStrings::GetStr(STR_DASH).toLatin1());
    }
    else
    {
        if (value >100)
        {
            value = value - 100;
            ie_value_insp = 1;
            ie_value_exp = value +10;
            if (ie_value_exp%10)
            {
                sprintf(IE_str,"%d:%.1f",ie_value_insp,(float)ie_value_exp/10);
            }
            else
            {
                sprintf(IE_str,"%d:%d",ie_value_insp,ie_value_exp/10);
            }
        }
        else
        {
            value = 100 - value;
            ie_value_exp = 1;
            ie_value_insp = value +10;
            if (ie_value_insp % 10)
            {
                sprintf(IE_str,"%.1f:%d",(float)ie_value_insp/10,ie_value_exp);
            }
            else
            {
                sprintf(IE_str,"%d:%d",ie_value_insp/10,ie_value_exp);
            }
        }
    }
    return 1;
}

/**********************************************
编写者:   	hudongnan
生成日期:   2011/11/25
功能说明:   将IE数值转化为比值
其它说明:   返回0：转化失败；
            返回1：转化成功
*********************************************/

int convert_IE_value_2_percent(short & percent, short value)
{

    short  ie_value_insp;
    short  ie_value_exp;

    if (INVALID_VALUE == value)
    {
        percent = -1;

        return 0;
    }
    else
    {
        if (value >100)
        {
            value = value - 100;
            ie_value_insp = 1;
            ie_value_exp = value +10;

            percent = ie_value_insp * 100 * 10 / ie_value_exp;   //I:E百分比，扩大100倍
        }
        else
        {
            value = 100 - value;
            ie_value_exp = 1;
            ie_value_insp = value +10;

            percent = ie_value_insp * 100 / 10;  //I:E百分比，扩大100倍
        }
    }
    return 1;
}
