﻿#include "TrendTableModel.h"
#include "SystemTimeManager.h"
#include "UiApi.h"
#include "AGModule.h"
#define MIN_COLUMN_COUNT 7
QMap<TrendDataModel::DATA_FILTER_TYPE,QVector<TREND_PARAM_TYPE>> TrendDataModel::mFilterMap={
    {ALL,{TREND_PPEAK,
          TREND_MV,
          TREND_ETCO2,
          TREND_FICO2,
          TREND_VTE,
          TREND_VTI,
          TREND_RATE,
          TREND_FIO2,
          TREND_PPLAT,
          TREND_PMEAN,
          TREND_PEEP,
          TREND_MVSPN,
          TREND_FSPN,
          TREND_R,
          TREND_C,
          TREND_ETN2O,
          TREND_FIN2O,
          TREND_ETSEV,
          TREND_FISEV,
          TREND_ETISO,
          TREND_FIISO,
          TREND_ETENF,
          TREND_FIENF,
          TREND_ETHAL,
          TREND_FIHAL,
          TREND_ETDES,
          TREND_FIDES,
          TREND_FLOWMETER_O2 ,
          TREND_FLOWMETER_AIR,
          TREND_FLOWMETER_N2O
     }
    },
    {FLOWMETER_PARAM,{
         TREND_FLOWMETER_O2 ,
         TREND_FLOWMETER_AIR,
         TREND_FLOWMETER_N2O
     }
    },
    {BREATHING_MECHANICS_PARAM,{
         TREND_PPEAK,
         TREND_PPLAT,
         TREND_PEEP,
         TREND_PMEAN,
         TREND_RATE,
         TREND_FSPN,
         TREND_C,
         TREND_R
     }
    },
    {VENTILATORY_FUNCTION_PARAM,{
         TREND_MV,
         TREND_MVSPN,
         TREND_VTE,
         TREND_VTI,
         TREND_RATE,
         TREND_FSPN
     }
    },
    {EXTERNAL_MODULE_PARAM,{
         TREND_FIO2,
         TREND_ETCO2,
         TREND_FICO2,
         TREND_ETN2O,
         TREND_FIN2O,
         TREND_ETSEV,
         TREND_FISEV,
         TREND_ETISO,
         TREND_FIISO,
         TREND_ETENF,
         TREND_FIENF,
         TREND_ETHAL,
         TREND_FIHAL,
         TREND_ETDES,
         TREND_FIDES
     }
    }
};
TrendDataModel::TrendDataModel(QObject *parent) : QAbstractItemModel(parent) {
    mFilterMapBackup = mFilterMap;
    InitValue();
    InitConnect();
}

int TrendDataModel::columnCount(const QModelIndex &parent) const
{
    int re = mHheadData.count()>MIN_COLUMN_COUNT? mHheadData.count():MIN_COLUMN_COUNT;
    return  re;
}

int TrendDataModel::rowCount(const QModelIndex &parent) const
{
    return mVheadData.length();
}

Qt::ItemFlags TrendDataModel::flags(const QModelIndex &index) const {
    Qt::ItemFlags flags = QAbstractItemModel::flags(index);
    // 在这里可以设置特定索引的标志，比如允许编辑等
    return flags;
}

QVariant TrendDataModel::data(const QModelIndex &index, int role) const {
    // 在这里根据角色返回数据
    int row=index.row();
    int col=index.column();
    if(!index.isValid())
        return QVariant();
    if(role==Qt::DisplayRole)
    {
        if(mHheadData.length()>col)
        {
            qint64 dataTimeTick = mHheadData[col];
            if(mSourData.find(dataTimeTick)!=mSourData.end()&&mSourData[dataTimeTick].count()>row)
            {
                return caclValueToStr((TREND_PARAM_TYPE)row,mSourData.value(dataTimeTick).value((TREND_PARAM_TYPE)row));
            }
        }
        return "";
    }
    else if(role==Qt::UserRole)
    {
        if(mFilterMap[mRowFilterType].indexOf((TREND_PARAM_TYPE)row)!=-1)
            return "1";
        else
            return  "0";
    }
    else if(role==Qt::ForegroundRole)
        return COLOR_BLACK;
    else if(role==Qt::TextAlignmentRole)
        return Qt::AlignCenter;
    else if(role==Qt::FontRole)
    {
        QFont tempFont(FONT_FAMILY_NINA);
        tempFont.setPixelSize(FONT_M);
        return tempFont;
    }
    else if(role==(Qt::UserRole-1))
    {
        return mFilterMap[mRowFilterType].indexOf((TREND_PARAM_TYPE)row);
    }
    return QVariant();
}

bool TrendDataModel::setData(const QModelIndex &index, const QVariant &value, int role) {
    // 在这里根据角色设置数据
    return QAbstractItemModel::setData(index, value, role);
}

QVariant TrendDataModel::headerData(int section, Qt::Orientation orientation, int role) const
{
    if(role==Qt::DisplayRole)
    {
        if(orientation==Qt::Vertical)
        {
            if(section>=0&&section<mVheadData.count())
                return mVheadData[section];
        }
        else if(section>=0)
        {
            if (mHheadData.value(section,-1)!=-1)
            {
                QDateTime dateTime = QDateTime::fromMSecsSinceEpoch(TrendDataManager::GetInstance()->GetShowTime(mHheadData.value(section,-1)));
                QString str = SystemTimeManager::GetDateStr(dateTime.date()) + "\n" + SystemTimeManager::GetTimeStr(dateTime.time());
                return str;
            }
            return "----/--/--\n--:--";
        }

    }
    else if(role==Qt::FontRole)
    {
        QFont horHeaderFont(FONT_FAMILY_NINA);
        horHeaderFont.setPixelSize(FONT_S);
        return horHeaderFont;;
    }
    return QVariant();
}

QModelIndex TrendDataModel::index(int row, int column, const QModelIndex &parent) const
{
    if(hasIndex(row,column))
        return createIndex(row, column);
    return QModelIndex();
}

QModelIndex TrendDataModel::parent(const QModelIndex &child) const
{
    return QModelIndex();
}

void TrendDataModel::SetColumnFilterType(TIME_FILETER_TYPE type)
{  
    if(mSourData.count()>0)
    {
        mTimeColmunFilterType = type;
        mHheadData.clear();
        beginResetModel();
        mHheadData.push_back(mSourData.firstKey());
        for(auto i=++mSourData.begin();i!=mSourData.end();i++)
        {
            if(isShowColumn(i.key()))
            {
                mHheadData.push_back(i.key());
            }
        }
        emit dataChanged(index(0,0),index(rowCount(),columnCount()));
        endResetModel();
    }
}

void TrendDataModel::SetRowFilterType(DATA_FILTER_TYPE type)
{
    mRowFilterType = type;
    beginResetModel();
    endResetModel();
}

void TrendDataModel::Clean()
{
    beginResetModel();
    mSourData.clear();
    mHheadData.clear();
    mVheadData.clear();
    endResetModel();
}

void TrendDataModel::SetIsShow(bool isShow)
{
    if(mViewisShow!=isShow)
    {
        mViewisShow = isShow;
        if(mViewisShow)
        {
            beginResetModel();
            endResetModel();
        }
    }

}

QString TrendDataModel::GetTypeFilterTypeName(DATA_FILTER_TYPE type)
{
    static QMap<TrendDataModel::DATA_FILTER_TYPE, ALL_STRINGS_ENUM> sParamGroupName
    {
        {TrendDataModel::ALL,                           ALL_STRINGS_ENUM::STR_ALL_PARAMS},
        {TrendDataModel::FLOWMETER_PARAM,               ALL_STRINGS_ENUM::STR_FLOWMETER_PARAMS},
        {TrendDataModel::BREATHING_MECHANICS_PARAM,     ALL_STRINGS_ENUM::STR_BREATHING_MECHANICS_PARAM},
        {TrendDataModel::VENTILATORY_FUNCTION_PARAM,    ALL_STRINGS_ENUM::STR_VENTILATORY_FUNCTION_PARAM},
        {TrendDataModel::EXTERNAL_MODULE_PARAM,         ALL_STRINGS_ENUM::STR_EXTERNAL_MODULE},
    };
    ALL_STRINGS_ENUM strid = sParamGroupName.value(type,STR_NULL);
    return UIStrings::GetStr(strid);
}

TrendDataModel::DATA_FILTER_TYPE TrendDataModel::GetFilterTypByTrendType(TREND_PARAM_TYPE trendType)
{
    for(auto ite=++mFilterMap.begin();ite!=mFilterMap.end();ite++)
    {
        if(ite.value().indexOf(trendType)!=-1)
        {
            return ite.key();
        }
    }
    return ALL;
}

QVector<TREND_PARAM_TYPE> TrendDataModel::GetTypeVeByFilterType(DATA_FILTER_TYPE type)
{
    QVector<TREND_PARAM_TYPE> re;
    if(mFilterMap.find(type)!=mFilterMap.end())
    {
        re=mFilterMap[type];
    }
    return re;
}

void TrendDataModel::SlotTrendDataAdd()
{
    TrendDataSt newData =TrendDataManager::GetInstance()->GetCurTrendDataSt();
    if(newData.mRunMode==MODE_RUN_STANDBY || newData.mRunMode==MODE_RUN_SELFTEST)
        newData.SetInvalidValue();
    QMap<TREND_PARAM_TYPE, qreal> tranData = TrendDataManager::ConvertTrendDataSt(newData);
    mSourData.insert(newData.mRelativeTimestamp,tranData);
    if(isShowColumn(newData.mRelativeTimestamp))
    {
        bool isInsert=(mHheadData.count()>=MIN_COLUMN_COUNT);
        if(mViewisShow)
            isInsert==true?beginInsertColumns(QModelIndex(),mHheadData.count(),mHheadData.count()):beginResetModel();
        mHheadData.push_back(newData.mRelativeTimestamp);
        if(mViewisShow)
            isInsert==true?endInsertColumns():endResetModel();
    }
    emit SignalDataChange();
}

void TrendDataModel::SlotTrendDataRemove(const QVector<qint64> &deletedVec)
{
    if(deletedVec.count()>0)
    {
        beginResetModel();
        for(int i=0;i<deletedVec.count();i++)
        {
            mSourData.remove(deletedVec[i]);
            mHheadData.removeAll(deletedVec[i]);
        }
        endResetModel();
    }
    emit SignalDataChange();
}


void TrendDataModel::SlotOnUnitChanged(CONVERTIBLE_UNIT_CATEGORY category, UNIT_TYPE, UNIT_TYPE)
{
    InitVheadData();
    emit dataChanged(index(0,0),index(rowCount(),columnCount()));
}

void TrendDataModel::InitValue()
{
    InitVheadData();

    for(int i = TREND_PARAM_TYPE_START; i < TREND_PARAM_TYPE_END; i++)
    {
        if(!TrendDataManager::GetInstance()->GetTrendParamEnable((TREND_PARAM_TYPE)i))
        {
            for(auto ite = mFilterMap.begin(); ite != mFilterMap.end(); ++ite)
            {
                ite.value().erase(std::remove(ite.value().begin(), ite.value().end(), (TREND_PARAM_TYPE)i), ite.value().end());
            }
        }
    }
}

void TrendDataModel::InitVheadData()
{
    mVheadData.clear();
    for(int trendType = 0;
        trendType < TREND_PARAM_TYPE::TREND_PARAM_TYPE_END;
        trendType ++)
    {
        QString headerTemplate = QString("<div><span style=\"font-size: 15px\">%1</span><br><span style=\"font-size: 12px; text-align:right\">%2</span></div>");

        auto header = headerTemplate.arg(TrendDataManager::GetTrendName((TREND_PARAM_TYPE)trendType),
                                         TrendDataManager::GetTrendUnit((TREND_PARAM_TYPE)trendType));
        mVheadData.push_back(header);
    }
}
void TrendDataModel::InitConnect()
{
    connect(UnitManager::GetInstance(), &UnitManager::SignalCategoryUnitChanged, this, &TrendDataModel::SlotOnUnitChanged);
    connect(TrendDataManager::GetInstance(), &TrendDataManager::SignalDataAdded, this,[=]()
    {
        DEBUG_RUNTIME (SlotTrendDataAdd());
    });
    connect(TrendDataManager::GetInstance(), &TrendDataManager::SignalDataRemoved, this, &TrendDataModel::SlotTrendDataRemove);
    connect(UIStrings::GetInstance(), &UIStrings::SignalLanguageChanged, this, [this]{
        InitVheadData();
    });
}


bool TrendDataModel::isShowColumn(qint64 time)
{
    if(time<60000)
        return false;
    if((time-60000)%(mTimeColmunFilterType*60000)==0)
        return true;
    else
        return false;
}

QString TrendDataModel::caclValueToStr(TREND_PARAM_TYPE trendType, double value) const
{
    QString re;
    if (value != INVALID_VALUE)
    {
        auto eTrendType = trendType;
        auto relevantMonitorParam = TrendDataManager::GetRelevantMonitorParam(eTrendType);
        value = DataManager::RevertToFloat(relevantMonitorParam, value);
        auto category = DataManager::GetParamUnitCategory(relevantMonitorParam);
        if (category != CONVERTIBLE_UNIT_CATEGORY::NONE_CATEGORY)
        {
            auto convertFunc = UnitManager::ConvertFunctor(category,
                                                           DataManager::GetDefaultUnit(relevantMonitorParam),
                                                           UnitManager::GetInstance()->GetCategoryCurUnit(category));
            value = convertFunc(value);
        }

        re = QString::number(value, 'f', TrendDataManager::GetDisplayPrecision(eTrendType));
    }
    else
    {
        re = INVALID_VALUE_STR;
    }
    return re;
}
