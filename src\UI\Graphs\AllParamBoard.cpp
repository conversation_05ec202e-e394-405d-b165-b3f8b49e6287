﻿#include "AllParamBoard.h"
#include "ParamLabelManage.h"
#include "SystemConfigManager.h"
#include "DataManager.h"
#include "ParamLabel/ParamLabelLayoutHorizontal.h"
#include "LoopChart.h"
#include "ModuleTestManager.h"
#include "AGModule.h"

#define PLOT_INDEX  0
#define CPB_INDEX   1

#define LIGHT_COLOR  QColor(COLOR_DARK_GRAY)
#define DARK_COLOR   QColor(COLOR_BLACK)

#define AREA_SPACE  15

#define ALLPARAMBOARD_PAGE_LEFT_MARGIN       (10)
#define ALLPARAMBOARD_PAGE_TOP_MARGIN        (0)
#define AL<PERSON>ARAMBOARD_PAGE_RIGHT_MARGIN      (10)
#define ALLPARAMBOARD_PAGE_BOTTOM_MARGIN     (5)

static constexpr unsigned int ROW_PER_COL = 5;

enum
{
    PARAMLABEL_R1C1_ID = 0,//value page, c1
    PARAMLABEL_R2C1_ID,
    PARAMLABEL_R3C1_ID,
    PARAMLABEL_R4C1_ID,
    PARAMLABEL_R5C1_ID,
    PARAMLABEL_R6C1_ID,
    PARAMLABEL_R7C1_ID,

    PARAMLABEL_R1C2_ID,//value page, c2
    PARAMLABEL_R2C2_ID,
    PARAMLABEL_R3C2_ID,
    PARAMLABEL_R4C2_ID,
    PARAMLABEL_R5C2_ID,
    PARAMLABEL_R6C2_ID,
    PARAMLABEL_R7C2_ID,

    PARAMLABEL_R1C3_ID,//value page, c3
    PARAMLABEL_R2C3_ID,
    PARAMLABEL_R3C3_ID,
    PARAMLABEL_R4C3_ID,
    PARAMLABEL_R5C3_ID,
    PARAMLABEL_R6C3_ID,
    PARAMLABEL_R7C3_ID,
    PARAMLABEL_R8C3_ID,
    PARAM_MAX_COUNT
};

AllParamBoard::AllParamBoard(QWidget *parent) :
    FocusWidget(parent)
{
    InitUi();
}

void AllParamBoard::InitUi()
{
//    mLoop = new LoopChart(this);
//    mLoop->SetLoopType(LOOP_PV);
//    ChartManager::GetInstance()->RegisterChart(ChartManager::CHART_TYPE_ENUM::LOOP_CHART, (LOOP_TYPE)LOOP_PV, mLoop);

    CreateAllParam();
    RegisterAllParam();
    SetParamEnable();

    QGridLayout *gl = new QGridLayout;
    gl->setSpacing(0);
    gl->setContentsMargins(0, 0, 0, 0);
    for (int i = 0, j = 0; i < PARAMLABEL_R1C3_ID; i++, j++)
    {
        if (m_params[i] != NULL)
            gl->addWidget(m_params[i], j % ROW_PER_COL, j / ROW_PER_COL);
        else
            --j;
    }

    auto mainLayout = new QHBoxLayout;
    mainLayout->setSpacing(3);
    mainLayout->setContentsMargins(5, 5, 5, 5);
    mainLayout->addLayout(gl);
    setLayout(mainLayout);
}

//根据配置禁用一些控件
void AllParamBoard::SetParamEnable()
{
    if (0 ==ConfigManager->GetConfig<bool>(SystemConfigManager::O2_BOOL_INDEX))
    {//fio2参数隐藏
        hideControl(VALUEDATA_FIO2);
    }
    //无AG，有CO2模块
    if (ModuleTestManager::GetInstance()->GetAgModuleType() != AG)
    {
        hideControl(VALUEDATA_FIN2O);
        hideControl(VALUEDATA_ETN2O);
        hideControl(VALUEDATA_FI_AA1);
        hideControl(VALUEDATA_ET_AA1);
    }
    //无AG,无CO2模块,CO2相关参数无效
    if(ModuleTestManager::GetInstance()->GetAgModuleType() == None)
    {
        hideControl(VALUEDATA_FICO2);
        hideControl(VALUEDATA_ETCO2);
    }
}

void AllParamBoard::CreateAllParam()
{
    for (int i = 0; i < PARAM_MAX_COUNT; i++)
    {
        m_params << new ParamLabelLayoutHorizontal;
        m_params[i]->SetLabelScale(ParamLabelLayoutHorizontal::LABEL_SCALE_ENUM::MID);
//        connect(m_params[i], &ParamLabelLayoutHorizontal::SignalClicked, this, &AllParamBoard::SignalClicked);
    }
}

//初始化各个参数显示控件
void AllParamBoard::RegisterAllParam()
{
    ParamLabelManage *p_manage = ParamLabelManage::GetInstance();
    p_manage->RegisterParamView(VALUEDATA_PPEAK, m_params[PARAMLABEL_R1C1_ID]);
    p_manage->RegisterParamView(VALUEDATA_PPLAT, m_params[PARAMLABEL_R2C1_ID]);
    p_manage->RegisterParamView(VALUEDATA_PEEP, m_params[PARAMLABEL_R3C1_ID]);
    p_manage->RegisterParamView(VALUEDATA_PMEAN, m_params[PARAMLABEL_R4C1_ID]);
    p_manage->RegisterParamView(VALUEDATA_R, m_params[PARAMLABEL_R5C1_ID]);
    p_manage->RegisterParamView(VALUEDATA_C, m_params[PARAMLABEL_R6C1_ID]);
    p_manage->RegisterParamView(VALUEDATA_FIO2, m_params[PARAMLABEL_R7C1_ID]);
    p_manage->RegisterParamView(VALUEDATA_VTI, m_params[PARAMLABEL_R1C2_ID]);
    p_manage->RegisterParamView(VALUEDATA_VTE, m_params[PARAMLABEL_R2C2_ID]);
    p_manage->RegisterParamView(VALUEDATA_MV, m_params[PARAMLABEL_R3C2_ID]);
    p_manage->RegisterParamView(VALUEDATA_RATE, m_params[PARAMLABEL_R4C2_ID]);
    p_manage->RegisterParamView(VALUEDATA_MVSPN, m_params[PARAMLABEL_R5C2_ID]);
    p_manage->RegisterParamView(VALUEDATA_FSPN, m_params[PARAMLABEL_R6C2_ID]);

    p_manage->RegisterParamView(VALUEDATA_IE, m_params[PARAMLABEL_R7C2_ID]);

    p_manage->RegisterParamView(VALUEDATA_FICO2, m_params[PARAMLABEL_R1C3_ID]);
    p_manage->RegisterParamView(VALUEDATA_ETCO2, m_params[PARAMLABEL_R2C3_ID]);
    p_manage->RegisterParamView(VALUEDATA_PR, m_params[PARAMLABEL_R4C3_ID]);
    p_manage->RegisterParamView(VALUEDATA_FIN2O, m_params[PARAMLABEL_R5C3_ID]);
    p_manage->RegisterParamView(VALUEDATA_ETN2O, m_params[PARAMLABEL_R6C3_ID]);
    p_manage->RegisterParamView(VALUEDATA_FI_AA1, m_params[PARAMLABEL_R7C3_ID]);
    p_manage->RegisterParamView(VALUEDATA_ET_AA1, m_params[PARAMLABEL_R8C3_ID]);

    mFIN2O = m_params[PARAMLABEL_R5C3_ID];
    mETN2O = m_params[PARAMLABEL_R6C3_ID];
    mFIAA1 = m_params[PARAMLABEL_R7C3_ID];
    mETAA1 = m_params[PARAMLABEL_R8C3_ID];
}

//隐藏参数对应的控件
void AllParamBoard::hideControl(int realdataId)
{
    ParamLabelManage *p_manage = ParamLabelManage::GetInstance();
    for (int i = 0; i < PARAM_MAX_COUNT; ++i)
    {
        if (m_params[i] && p_manage->GetRealIdByLabelPtr(m_params[i]) == realdataId)
        {
            m_params[i] =NULL;
        }
    }
}

void AllParamBoard::InitUiText()
{
    //    for (int i = 0; i < PARAM_MAX_COUNT; ++i)
    //    {
    //        if (m_params[i])
    //        {
    //            m_params[i]->InitUiText();
    //        }
    //    }
}

