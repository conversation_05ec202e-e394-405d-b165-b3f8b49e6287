#include "UpgradeManager.h"
#include "../Com/TCP/ToolCommProxy.h"
#include <QSettings>
#include <QDir>
#include <QProcess>
#include "Flowmeter.h"
#include "base.h"
#include "SystemConfigManager.h"
#include <unistd.h>
#include "UploadOwnStatus.h"
#include "Monitoring.h"
#include "HistoryEventManager.h"

#define UPDATE_DIR                      "/usr/local/Update/"
#define CUSTOM_STEP_PATH                "/usr/local/Update/CustomStep"
#define MONITOR_PATH                    "/usr/local/Update/GasControl.bin"
#define FLOWMETER_PATH                  "/usr/local/Update/Flowmeter.hex"
#define MAIN_SOFT_PATH                  "/usr/local/Update/MasterGui"
#define kERNEL_PATH                     "/usr/local/Update/zImage"
#define UBOOT_PATH                      "/usr/local/Update/u-boot.imx"
#define DEVICETREE_PATH                 "/usr/local/Update/imx6dl-sabresd.dtb"
#define POWER_PATH                      "/usr/local/Update/powerBoard.hex"
#define LOGO_PATH                       "Logo.png"
#define LOGO_BIN_PATH                   "Logo.bin"

#define MODEL_INI_NAME                  "Config.ini"
#define MODEL_INI_KEY                   "ProductModel"

#define CHECK_UPDATE_FINISH_INTERVAL    1000

#define UBOOT_SIGN "BaiGeUbootVersion="
#define UBOOT_SIGN_END   "~#"
#define KERNAL_SIGN_BEGIN "BaigeKernalVersion"
#define KERNAL_SIGN_END "~#"
#define FILESYS_SIGN_BEGIN "#~"
#define FILESYS_SIGN_END   "~#"
#define FILESYS_VER_INDEX 2
#define KERNAL_VER_INDEX 1

static QMap <int,  QString> sUpdateFileNameMap =
{
    {E_UPDATE_MAIN_SOFT, MAIN_SOFT_PATH},
    {E_UPDATE_MONITOR_SOFT, MONITOR_PATH},
    {E_UPDATE_KERNEL, kERNEL_PATH},
    {E_UPDATE_UBOOT, UBOOT_PATH},
    {E_UPDATE_DEVICE_TREE, DEVICETREE_PATH},
    {E_UPDATE_FLOWMETER, FLOWMETER_PATH},
    {E_UPDATE_POWER_BOARD, POWER_PATH},
    {E_UPDATE_CUSTOM_STEP, CUSTOM_STEP_PATH},
    {E_UPDATE_LOGO, LOGO_PATH},
    {E_UPDATE_LOGO_BIN, LOGO_BIN_PATH},
};

UpgradeManager *UpgradeManager::GetInstance()
{
    static UpgradeManager instance;
    return &instance;
}

void UpgradeManager::SetUpgradeState(E_UPGRADE_STATE state)
{
    mCurUpgradeState = state;
    emit SignalUpdateStateChange();
}

UpgradeManager::E_UPGRADE_STATE UpgradeManager::GetUpgradeState()
{
    return mCurUpgradeState;
}

bool UpgradeManager::GetIsProcessCustomUpdate()
{
    return mIsCustomUpdate;
}

void UpgradeManager::SetUpdateFileVersion(E_UPDATE_FILE_TYPE updateFileType, QString updateFileVersion)
{
    bool found = false;
    for (auto& updateFileInfo : mUpdateFileInfoVec)
    {
        if (updateFileInfo.mUpdateType == updateFileType)
        {
            updateFileInfo.mUpdateFileVersion = updateFileVersion;
            found = true;
            break;
        }
    }

    if (!found)
    {
        UpdateFileInfoSt newUpdateFileInfo;
        newUpdateFileInfo.mUpdateType = updateFileType;
        newUpdateFileInfo.mUpdateFileVersion = updateFileVersion;

        mUpdateFileInfoVec.append(newUpdateFileInfo);
    }
}

void UpgradeManager::SetUpdateFileProgress(E_UPDATE_FILE_TYPE updateFileType, int updateProgress, E_ERROR_CODE errorCode)
{
    bool needEmitSignal = false;
    
    bool found = false;
    for (auto& updateFileInfo : mUpdateFileInfoVec)
    {
        if (updateFileInfo.mUpdateType == updateFileType)
        {
            if (updateFileInfo.mUpdateProgress != updateProgress || 
                updateFileInfo.mErrorCode != errorCode)
            {
                updateFileInfo.mUpdateProgress = updateProgress;
                updateFileInfo.mErrorCode = errorCode;
                needEmitSignal = true;
            }
            found = true;
            break;
        }
    }

    if (!found)
    {
        UpdateFileInfoSt newUpdateFileInfo;
        newUpdateFileInfo.mUpdateType = updateFileType;
        newUpdateFileInfo.mUpdateProgress = updateProgress;
        newUpdateFileInfo.mErrorCode = errorCode;

        mUpdateFileInfoVec.append(newUpdateFileInfo);
        needEmitSignal = true;
    }
    
    if (updateProgress == 100 && mIsUpdateFlowmeter && updateFileType == E_UPDATE_FLOWMETER)
    {
        mIsUpdateFlowmeter = false;
        needEmitSignal = true;
    }

    if (needEmitSignal)
    {
        emit SignalUpdateProgress();
    }
}

QVector<UpgradeManager::UpdateFileInfoSt> UpgradeManager::GetUpdateFileInfo()
{
    return mUpdateFileInfoVec;
}

ConfigMessage UpgradeManager::GetMachineConfig()
{
    return ConfigManager->GetMachineConfig();
}

VersionInfo UpgradeManager::GetMachineAllVersionInfo()
{
    VersionInfo tmpVersionInfo;
    memset(&tmpVersionInfo ,0 ,sizeof(tmpVersionInfo));

    CopyVersionInfo(tmpVersionInfo.MainSoftVersion, sizeof(tmpVersionInfo.MainSoftVersion), QString(DEV_SOFT_VERSION).toUtf8());
    CopyVersionInfo(tmpVersionInfo.MonitorSoftVersion, sizeof(tmpVersionInfo.MonitorSoftVersion), mMonitorSoftVersion.toUtf8());
    CopyVersionInfo(tmpVersionInfo.KernelVersion, sizeof(tmpVersionInfo.KernelVersion), mKernelVersion.toUtf8());
    CopyVersionInfo(tmpVersionInfo.UbootVersion, sizeof(tmpVersionInfo.UbootVersion), mUbootVersion.toUtf8());
    CopyVersionInfo(tmpVersionInfo.FileSystemVersion, sizeof(tmpVersionInfo.FileSystemVersion), mFileSystemVersion.toUtf8());
    CopyVersionInfo(tmpVersionInfo.FlowMaterVersion, sizeof(tmpVersionInfo.FlowMaterVersion), mFlowmeterSoftVersion.toUtf8());
    CopyVersionInfo(tmpVersionInfo.PowerVersion, sizeof(tmpVersionInfo.PowerVersion), mPowerSoftVersion.toUtf8());
    CopyVersionInfo(tmpVersionInfo.DBVersion, sizeof(tmpVersionInfo.DBVersion), mDbVersion.toUtf8());
    CopyVersionInfo(tmpVersionInfo.DaemonVersion, sizeof(tmpVersionInfo.DaemonVersion), UploadOwnStatus::GetInstance()->GetDeamonVersion().toUtf8());

    return tmpVersionInfo;
}

QString UpgradeManager::GetMachineVersion(VERSION_TABLE_ITEM_ENUM type) const
{
    if(type == MAIN_CONTORL_BOARD)
    {
        return DEV_SOFT_VERSION;
    }
    else if(type == MONITOR_BOARD)
    {
        return mMonitorSoftVersion;
    }
    else if(type == FLOWMETER_BOARD)
    {
        return mFlowmeterSoftVersion;
    }
    else if(type == POWER_BOARD)
    {
        return mPowerSoftVersion;
    }
    else if(type == FLOWMETER_GAS_CONFIG)
    {
        return mFlowmeterGasType;
    }
    else if(type == FLOWMETER_SENSOR_TYPE)
    {
        return mFlowmeterSensorType;
    }
    else if(type == LINUX_KERNEL)
    {
        return mKernelVersion;
    }
    else if(type == U_BOOT)
    {
        return mUbootVersion;
    }
    else if(type == LINUX_FILE_SYSTEM)
    {
        return mFileSystemVersion;
    }
    else if(type == DAEMON_VERSION)
    {
        return UploadOwnStatus::GetInstance()->GetDeamonVersion();
    }
    else
    {
        return "---";
    }
}

void UpgradeManager::SetMachineVersion(VERSION_TABLE_ITEM_ENUM type, QString version)
{
    if(type == MONITOR_BOARD)
    {
        mMonitorSoftVersion = version;
    }
    else if(type == FLOWMETER_BOARD)
    {
        mFlowmeterSoftVersion = version;
    }
    else if(type == POWER_BOARD)
    {
        mPowerSoftVersion = version;
    }
    else if(type == FLOWMETER_GAS_CONFIG)
    {
        mFlowmeterGasType = version;
    }
    else if(type == FLOWMETER_SENSOR_TYPE)
    {
        mFlowmeterSensorType = version;
    }
    else if(type == LINUX_KERNEL)
    {
        mKernelVersion = version;
    }
    else if(type == U_BOOT)
    {
        mUbootVersion = version;
    }
    else if(type == LINUX_FILE_SYSTEM)
    {
        mFileSystemVersion = version;
    }

    emit SignalSoftwareVersionChanged(type, version);
}

void UpgradeManager::OpenMachineConfig(bool isOpen)
{
    mIsOpenMachineConfig = isOpen;
    emit SignalOpenMachineConfig(isOpen);
}

bool UpgradeManager::IsOpenMachineConfig()
{
    return mIsOpenMachineConfig;
}

void UpgradeManager::LoadFile(E_UPDATE_FILE_TYPE updateFileType, QByteArray &data)
{
    QDir dir(UPDATE_DIR);

    if (!dir.exists())
    {
        dir.mkpath(".");
    }

    QString fileName = sUpdateFileNameMap[updateFileType];
    if(QFile::exists(fileName))
        QFile::remove(fileName);

    QFile file;
    file.setFileName(fileName);
    file.open(QIODevice::Append | QIODevice::WriteOnly);
    file.write(data);
    file.close();
    QProcess::execute("chmod 777 " + fileName);
    QProcess::execute("sync");

    if(updateFileType == E_UPDATE_CUSTOM_STEP)
    {
        if(QFile::exists(CUSTOM_STEP_PATH))
        {
            QString program = "/bin/bash";
            QStringList arguments;
            arguments << CUSTOM_STEP_PATH;
            QProcess::execute(CUSTOM_STEP_PATH);
            QProcess::execute(program, arguments);

            QFile::remove(CUSTOM_STEP_PATH);
        }
        mIsCustomUpdate = true;
    }

    if(QFile::exists(MONITOR_PATH) && updateFileType == E_UPDATE_MONITOR_SOFT && !mIsUpdateMonitor)
    {
        mIsUpdateMonitor = true;
        Monitoring::GetInstance()->StartUpgrade();
    }

    if(QFile::exists(FLOWMETER_PATH) && updateFileType == E_UPDATE_FLOWMETER && !mIsUpdateFlowmeter)
    {
        mIsUpdateFlowmeter = true;
        Flowmeter::GetInstance()->StartUpgrade();
    }

    QString versionStr;
    int version1 = 0, version2 = 0, version3 = 0;

    for (auto& updateFileInfo : mUpdateFileInfoVec)
    {
        if (updateFileInfo.mUpdateType == updateFileType && !updateFileInfo.mUpdateFileVersion.isEmpty())
        {
            versionStr = updateFileInfo.mUpdateFileVersion;
            break;
        }
    }

    if (!versionStr.isEmpty())
    {
        if (versionStr.startsWith('V', Qt::CaseInsensitive))
            versionStr = versionStr.mid(1);

        QStringList versions = versionStr.split('.');
        version1 = (versions.size() >= 1 && !versions[0].isEmpty()) ? versions[0].toInt() : 0;
        version2 = (versions.size() >= 2 && !versions[1].isEmpty()) ? versions[1].toInt() : 0;
        version3 = (versions.size() >= 3 && !versions[2].isEmpty()) ? versions[2].toInt() : 0;
    }

    HistoryEventManager::GetInstance()->AddHistoryEvent(
        HistoryEventManager::HISTORY_EVENT_TYPE::OPERATE_EVENT,
        LOG_ONLINE_UPGRADE,
        updateFileType,
        version1,
        version2,
        version3
    );
}

void UpgradeManager::LoadProductModel(QString productModel)
{
    QSettings settings(MODEL_INI_NAME, QSettings::IniFormat);
    settings.setValue(MODEL_INI_KEY, productModel);
    settings.sync();
#ifdef ARM
    sync();
#endif

    HistoryEventManager::GetInstance()->AddHistoryEvent(
        HistoryEventManager::HISTORY_EVENT_TYPE::OPERATE_EVENT,
        LOG_ONLINE_UPGRADE,
        E_UPDATE_PRODUCT_MODEL,
        INVALID_VALUE,
        INVALID_VALUE,
        INVALID_VALUE
    );
    emit SignalUpdateFinish();
}

void UpgradeManager::LoadMachineConfig(ConfigMessage config)
{
    ConfigManager->SetMachineConfig(config);
    ConfigManager->SetMachineConfig(config);
    HistoryEventManager::GetInstance()->AddHistoryEvent(
        HistoryEventManager::HISTORY_EVENT_TYPE::OPERATE_EVENT,
        LOG_ONLINE_UPGRADE,
        E_UPDATE_CONFIG,
        INVALID_VALUE,
        INVALID_VALUE,
        INVALID_VALUE
    );
    emit SignalUpdateFinish();
}

void UpgradeManager::LoadAllFileFinish()
{
    if(QFile::exists(LOGO_BIN_PATH))
    {
        QProcess::execute("dd if=/dev/zero of=/dev/mmcblk3 bs=512 seek=22528 count=16384");
        QProcess::execute(QString("dd if=%1 of=/dev/mmcblk3 bs=512 seek=22528").arg(LOGO_BIN_PATH));
        QProcess::execute("sync");
        QFile::remove(LOGO_BIN_PATH);
    }

    mLoadAllFileFinish = true;

    if(mLoadAllFileFinish && !mIsUpdateFlowmeter && !mIsUpdateMonitor)
    {
        emit SignalUpdateFinish();
    }
    else
    {
        mCheckUpdateFinishTimer->start();
    }
}

UpgradeManager::UpgradeManager(QObject *parent) : QObject(parent)
{
    Init();
    InitConnect();
}

void UpgradeManager::Init()
{
    mCheckUpdateFinishTimer = new QTimer(this);
    mCheckUpdateFinishTimer->setInterval(CHECK_UPDATE_FINISH_INTERVAL);
    GetSystemVersionInfo();

    HistoryEventManager::GetInstance()->RegisterOperateLogConvertFunc(LOG_ONLINE_UPGRADE, [](const HistoryEventSt&paramSt)
    {
        QString typeStr;
        switch ((E_UPDATE_FILE_TYPE)paramSt.mParameter1)
        {
        case E_UPDATE_CONFIG:
            return UIStrings::GetStr(ALL_STRINGS_ENUM::STR_UPDATE_CONFIG);
        case E_UPDATE_PRODUCT_MODEL:
            return UIStrings::GetStr(ALL_STRINGS_ENUM::STR_UPDATE_PRODUCT_MODEL);
        case E_UPDATE_MAIN_SOFT:
            typeStr = UIStrings::GetStr(ALL_STRINGS_ENUM::STR_MAIN_CONTROL_BOARD);
            break;
        case E_UPDATE_MONITOR_SOFT:
            typeStr = UIStrings::GetStr(ALL_STRINGS_ENUM::STR_MONITOR_BOARD);
            break;
        case E_UPDATE_KERNEL:
            typeStr = UIStrings::GetStr(ALL_STRINGS_ENUM::STR_LINUX_KERNEL);
            break;
        case E_UPDATE_UBOOT:
            typeStr = UIStrings::GetStr(ALL_STRINGS_ENUM::STR_U_BOOT);
            break;
        case E_UPDATE_DEVICE_TREE:
            typeStr = UIStrings::GetStr(ALL_STRINGS_ENUM::STR_DEVICE_TREE);
            break;
        case E_UPDATE_FLOWMETER:
            typeStr = UIStrings::GetStr(ALL_STRINGS_ENUM::STR_LABEL_FLOWMETER);
            break;
        case E_UPDATE_POWER_BOARD:
            typeStr = UIStrings::GetStr(ALL_STRINGS_ENUM::STR_POWER_BOARD);
            break;
        case E_UPDATE_CUSTOM_STEP:
            typeStr = UIStrings::GetStr(ALL_STRINGS_ENUM::STR_UPDATE_CUSTOM_STEP);
            break;
        case E_UPDATE_LOGO:
            return UIStrings::GetStr(ALL_STRINGS_ENUM::STR_UPDATE_LOGO);
        case E_UPDATE_LOGO_BIN:
            return UIStrings::GetStr(ALL_STRINGS_ENUM::STR_UPDATE_LOGO_BIN);
        default:
            return UIStrings::GetStr(ALL_STRINGS_ENUM::STR_DASH);
        }

        if (paramSt.mParameter2 != INVALID_VALUE)
        {
            QString versionStr = "V";

            versionStr += QString::number(paramSt.mParameter2);

            if (paramSt.mParameter3 != INVALID_VALUE && paramSt.mParameter3 != 0)
            {
                versionStr += "." + QString("%1").arg(paramSt.mParameter3, 2, 10, QChar('0'));

                if (paramSt.mParameter4 != INVALID_VALUE && paramSt.mParameter4 != 0)
                {
                    versionStr += "." + QString("%1").arg(paramSt.mParameter4, 2, 10, QChar('0'));
                }
            }

            return typeStr + " " + UIStrings::GetStr(ALL_STRINGS_ENUM::STR_UPGRADE) + " " +
                   versionStr + " " + UIStrings::GetStr(ALL_STRINGS_ENUM::STR_MAINMENU_VER);
        }
    });
}

void UpgradeManager::InitConnect()
{
    connect(mCheckUpdateFinishTimer, &QTimer::timeout, this, &UpgradeManager::SlotCheckUpdateIsFinish);
    connect(Monitoring::GetInstance(), &Monitoring::SignalUpgradeFinish, this, &UpgradeManager::SlotUpdateMonitorFinish);
}

void UpgradeManager::GetSystemVersionInfo()
{
#ifdef ARM
    QProcess process;
    QObject::connect(&process, &QProcess::errorOccurred, [&process](QProcess::ProcessError error) {
        DebugLog << "获取信息进程错误退出状态: " << error << process.errorString();
    });

    //读自定义的linux内核版本
    QString command = "cat /proc/version";
    process.start(command);
    process.waitForFinished();
    QString result = process.readAll();
    {
        QString kernalSignBegin = KERNAL_SIGN_BEGIN;
        QString kernalSignEnd = KERNAL_SIGN_END;
        int begin = result.indexOf(kernalSignBegin);
        int end   = result.indexOf(kernalSignEnd,begin) - 1;
        result = result.mid(begin,end-begin+1);
        QStringList finalStrList=result.split(" ");
        if(finalStrList.size()<KERNAL_VER_INDEX+1)
            mKernelVersion = "---";
        else
            mKernelVersion = finalStrList[KERNAL_VER_INDEX];
    }

    //读uBoot版本
    {
        command="cat /proc/cmdline";
        process.start(command);
        process.waitForFinished();
        result=process.readAll();
        QString tmpUbootVersion = "";

        int begin = result.indexOf(UBOOT_SIGN);
        if (begin != -1)
        {
            begin += strlen(UBOOT_SIGN);
            int end = result.indexOf(UBOOT_SIGN_END, begin);

            if (end != -1)
            {
                tmpUbootVersion = result.mid(begin, end - begin);
            }
        }

        if(tmpUbootVersion.isEmpty())
        {
            command="/home/<USER>/fw_printenv";
            process.start(command);
            process.waitForFinished();
            result=process.readAll();
            QString uBootSign = UBOOT_SIGN;
            int begin = result.indexOf(uBootSign);
            int end   = result.indexOf("\n",begin);
            tmpUbootVersion = result.mid(begin+uBootSign.length(),end-begin-uBootSign.length());
        }


        mUbootVersion = tmpUbootVersion;
    }


    //读文件系统版本
    {
        command="cat /etc/profile";
        process.start(command);
        process.waitForFinished();
        result=process.readAll();
        int begin = result.indexOf(FILESYS_SIGN_BEGIN);
        int end = result.indexOf(FILESYS_SIGN_END,begin) + 1;
        result=result.mid(begin,end-begin+1);
        QStringList finalStrList=result.split(" ");
        if(finalStrList.size()<FILESYS_VER_INDEX+1)
            mFileSystemVersion = "---";
        else
            mFileSystemVersion = finalStrList[FILESYS_VER_INDEX];
    }
#endif
}

void UpgradeManager::CopyVersionInfo(unsigned char *dest, size_t destSize, const QByteArray &byteArray)
{
    memset(dest, '\0', destSize);
    int versionLength = byteArray.size() < destSize ? byteArray.size() : destSize;
    memcpy(dest, byteArray.constData(), versionLength);
}

void UpgradeManager::SlotCheckUpdateIsFinish()
{
    if(mLoadAllFileFinish && !mIsUpdateFlowmeter && !mIsUpdateMonitor)
    {
        mCheckUpdateFinishTimer->stop();
        emit SignalUpdateFinish();
    }
}

void UpgradeManager::SlotUpdateMonitorFinish()
{
    if(mIsUpdateMonitor)
        mIsUpdateMonitor = false;
}
