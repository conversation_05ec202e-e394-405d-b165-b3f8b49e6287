﻿#ifndef _DATALIMITMANAGER_H_
#define _DATALIMITMANAGER_H_

#include "AlarmManager.h"
#include "DataManager.h"
#include "UnitManager.h"


enum VALUE_TYPE
{
	NORMAL_TYPE,
	INVALID_LOW_TYPE,
	INVALID_HIG_TYPE
};

struct ALARM_INFO
{
	unsigned short min_off_flag;
    int low_lowest;
    int low_limit;
    int low_highest;
    int high_lowest;
    int high_limit;
    int high_highest;
	unsigned short low_alarm_id;
	unsigned short high_alarm_id;
	char digit;
	unsigned short max_off_flag;
    unsigned short step;
    unsigned short low_high_difference;
};

class RangeController;

class DataLimitManager : public QObject
{
    Q_OBJECT
public:
    static DataLimitManager* GetInstance()
    {
        static DataLimitManager sInstance;
        return &sInstance;
    }

    VALUE_TYPE ValueType(MONITOR_PARAM_TYPE id, short value);

    void register_paw_limit_ctrl(RangeController *ctrl);

    void register_CO2FI_limit_ctrl(RangeController *ctrl);
    void register_CO2ET_limit_ctrl(RangeController *ctrl);

    void SetAllLimitDefault();
    void SetMachineLimitDefault();
    void SetGasLimitDefault();
    int SetAgUnitChange(int unitType);
    int SetHighLimit(MONITOR_PARAM_TYPE id, short value);
    int SetLowLimit(MONITOR_PARAM_TYPE id, short value);

    void SaveLimit(unsigned int id, unsigned int high_or_low, short value);

    int GetHighLimit(MONITOR_PARAM_TYPE id);
    int GetLowLimit(MONITOR_PARAM_TYPE id);
    short GetLowLowest(MONITOR_PARAM_TYPE id);
    short GetLowHighest(MONITOR_PARAM_TYPE id);
    short GetHighLowest(MONITOR_PARAM_TYPE id);
    short GetHighHighest(MONITOR_PARAM_TYPE id);
    short GetHighAlarmId(MONITOR_PARAM_TYPE id);
    short GetLowAlarmId(MONITOR_PARAM_TYPE id);

    unsigned short GetChangeStep(MONITOR_PARAM_TYPE id);
    unsigned short GetDifferenceOfLowHigh(MONITOR_PARAM_TYPE id);
    unsigned short GetLimitDigit(MONITOR_PARAM_TYPE id);

	unsigned short GetMinOffFlag(MONITOR_PARAM_TYPE id);
	unsigned short IsHighLimitClosable(MONITOR_PARAM_TYPE id);

	//返回指定参数的当前报警级别：高、中、低、禁止
    E_ALARMPRIO GetAlarmLevel(MONITOR_PARAM_TYPE id,unsigned int out_range_type);

    static void CheckAllValue(void* ptmr, void* parg);

    void Init();
	void OnPressureUnitChanged(unsigned short unitType);

private:
	static ALARM_INFO sLimitInfo[VALUEDATA_MAX];

    DataLimitManager();
    DataLimitManager(const DataLimitManager&);
    DataLimitManager& operator=(const DataLimitManager&);
    ~DataLimitManager(){}

    int   mPrevAgUnitType =  U_MMHG;
	short m_modify;
};

#endif
