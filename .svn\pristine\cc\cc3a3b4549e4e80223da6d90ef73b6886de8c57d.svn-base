//2025-06-04 14:34:24
//Generated by BAIG<PERSON>
#pragma once
enum ALL_STRINGS_ENUM
{

	STR_NULL,
	STR_LABEL_VCV,
	STR_LABEL_PCV,
	STR_LABEL_PSV,
	STR_LABEL_SIMVVCV,
	STR_<PERSON>BEL_SIMVPCV,
	STR_LABEL_PRVC,
	STR_LABEL_PMODE,
	STR_LABEL_HLM,
	STR_LABEL_CPB,
	STR_MODE_MECHANICAL,
	STR_MODE_ACGO,
	STR_MODE_STANDBY,
	STR_MODE_MANUAL,
	STR_VALUEPARAM_RATE,
	STR_VALUEPARAM_FSPN,
	STR_VALUEPARAM_IE,
	STR_VALUEPARAM_VTE,
	STR_VALUEPARAM_VTI,
	STR_VALUEPARAM_MV,
	STR_VALUEPARAM_MVSPN,
	STR_VALUEPARAM_PPEAK,
	STR_VALUEPARAM_PPLAT,
	STR_VALUEPARAM_PEEP,
	STR_VALUEPARAM_PMEAN,
	STR_VALUEPARAM_PMIN,
	STR_VALUEPARAM_R,
	STR_VALUEPARAM_C,
	STR_VALUEPARAM_FIO2,
	STR_VALUEPARAM_ETCO2,
	STR_VALUEPARAM_FICO2,
	STR_VALUEPARAM_PR,
	STR_VALUEPARAM_AG_NULL,
	STR_VALUEPARAM_AG_FINULL,
	STR_VALUEPARAM_FIN2O,
	STR_VALUEPARAM_FIHAL,
	STR_VALUEPARAM_FIENF,
	STR_VALUEPARAM_FISEV,
	STR_VALUEPARAM_FIISO,
	STR_VALUEPARAM_FIDES,
	STR_VALUEPARAM_AG_ETNULL,
	STR_VALUEPARAM_ETN2O,
	STR_VALUEPARAM_ETHAL,
	STR_VALUEPARAM_ETENF,
	STR_VALUEPARAM_ETSEV,
	STR_VALUEPARAM_ETISO,
	STR_VALUEPARAM_ETDES,
	STR_VALUEPARAM_ASPHY,
	STR_SETTINGPARAM_VT,
	STR_SETTINGPARAM_RATE,
	STR_SETTINGPARAM_IE,
	STR_SETTINGPARAM_TITP,
	STR_SETTINGPARAM_PINSP,
	STR_SETTINGPARAM_CPAP,
	STR_SETTINGPARAM_PLIMIT,
	STR_SETTINGPARAM_PEEP,
	STR_SETTINGPARAM_FTRIG,
	STR_SETTINGPARAM_PTRIG,
	STR_SETTINGPARAM_TINSP,
	STR_SETTINGPARAM_SLOPE,
	STR_SETTINGPARAM_PSUPP,
	STR_SETTINGPARAM_INENDLEVEL,
	STR_SETTINGLIMIT_VTE,
	STR_SETTINGLIMIT_PAW,
	STR_SETTINGLIMIT_FIO2,
	STR_SETTINGLIMIT_RATE,
	STR_SETTINGLIMIT_MV,
	STR_UNIT_1PERMINL,
	STR_UNIT_S,
	STR_UNIT_CMH2O,
	STR_UNIT_CMH2OLS,
	STR_UNIT_MLCMH2O,
	STR_UNIT_MMHG,
	STR_UNIT_PERCENTAGE,
	STR_UNIT_L,
	STR_UNIT_ML,
	STR_UNIT_LPERMIN,
	STR_UNIT_MIN,
	STR_UNIT_BPM,
	STR_UNIT_KG,
	STR_UNIT_LB,
	STR_UNIT_M,
	STR_UNIT_FT,
	STR_UNIT_HPA,
	STR_UNIT_KPA,
	STR_UNIT_MBAR,
	STR_DASH,
	STR_INSP,
	STR_EXP,
	STR_MAC,
	STR_GRAPHNAME_PAW,
	STR_GRAPHNAME_FLOW,
	STR_GRAPHNAME_VOL,
	STR_GRAPHNAME_CO2,
	STR_GRAPHNAME_SPO2,
	STR_GRAPHNAME_C20C,
	STR_GRAPHNAME_VF,
	STR_GRAPHNAME_PV,
	STR_GRAPHNAME_FP,
	STR_GRAPHNAME_VCO2,
	STR_GASNAME_O2,
	STR_GASNAME_N2O,
	STR_GASNAME_CO2,
	STR_GASNAME_AIR,
	STR_CPAP,
	STR_BIO_ALARM,
	STR_SET_TIME,
	STR_OEM,
	STR_ELUNA_60,
	STR_ELUNA_70,
	STR_ELUNA_80,
	STR_CENAR_30,
	STR_CENAR_40,
	STR_CENAR_50,
	STR_PSV_LEVEL,
	STR_BACKUP,
	STR_SN_SHORT_CUT,
	STR_BLANK,
	STR_BACK,
	STR_ENTER,
	STR_DISABLE_TEST,
	STR_LABEL_BST_HEAD,
	STR_LABEL_BST_INIT,
	STR_TEST,
	STR_LABEL_BST_TESTING,
	STR_LABEL_BST_FAIL,
	STR_COMPLIANCE,
	STR_LEAK,
	STR_FAIL,
	STR_QULIFIED,
	STR_UNQULIFIED,
	STR_RecentSTT,
	STR_RECENT_CAL,
	STR_TOTAL,
	STR_EXIT,
	STR_CONFIRM,
	STR_CANCEL,
	STR_START,
	STR_RESET,
	STR_LABEL_WAVEFORMS,
	STR_LABEL_SPIROMETRY,
	STR_LABEL_TREND,
	STR_LABEL_VALUES,
	STR_LOOP_BUTTON,
	STR_LOOP_BUTTONRELEASE,
	STR_LABEL_FLOWMETER,
	STR_ALARM,
	STR_MESSAGE,
	STR_MAINMENU,
	STR_MAINMENU_ZERO,
	STR_MAINMENU_DEFAULT,
	STR_CALIBRATION,
	STR_CAL_DIABLE,
	STR_MAINMENU_CAL,
	STR_MAINMENU_CALDONE,
	STR_MAINMENU_CALFAIL,
	STR_MAINMENU_CAL_TIP,
	STR_MAINMENU_FLOWCAL,
	STR_MAINMENU_FLOWCAL_TXT2,
	STR_MAINMENU_FLOWCAL_TXT3,
	STR_MAINMENU_FLOWCAL_TXT4,
	STR_MAINMENU_O2CAL,
	STR_MAINMENU_21PO2CAL,
	STR_MAINMENU_100PO2CAL,
	STR_MAINMENU_CAL_FIN,
	STR_MAINMENU_O2CAL_TXT3,
	STR_MAINMENU_CAL_IN_PROCESS,
	STR_MAINMENU_CAL_21_DONE,
	STR_MAINMENU_CAL_100_DONE,
	STR_MAINMENU_CAL_21_FAIL,
	STR_MAINMENU_CAL_100_FAIL,
	STR_MAINMENU_CAL_FINISH,
	STR_MAINMENU_AGCO2CAL,
	STR_MAINMENU_AGCO2CAL_TXT1_TITLE,
	STR_MAINMENU_CAL_STATUS_DISABLE,
	STR_MAINMENU_CAL_STATUS_IN_PROG,
	STR_MAINMENU_CAL_STATUS_ZERODONE,
	STR_MAINMENU_CAL_STATUS_FAILED,
	STR_LATEST_ZERO_CAL_TIME,
	STR_MAINMENU_SENSORCAL,
	STR_MAINMENU_SENSORCAL_TXT3,
	STR_MAINMENU_SYSSETTING,
	STR_MAINMENU_EQUIPSET,
	STR_MAINMENU_EQUIPSET_TXT2,
	STR_ON,
	STR_OFF,
	STR_PAW_UNIT,
	STR_CO2_UNIT,
	STR_MAINMENU_TIMEDATE,
	STR_MAINMENU_TIMEDATE_DATE,
	STR_MAINMENU_TIMEDATE_TIME,
	STR_MAINMENU_TIMEDATE_12HOUR_AM,
	STR_MAINMENU_TIMEDATE_12HOUR_PM,
	STR_DATE_FORMAT,
	STR_TIME_FORMAT,
	STR_MAINMENU_YMD,
	STR_MAINMENU_MDY,
	STR_MAINMENU_DMY,
	STR_TIME_FORMAT_12H,
	STR_TIME_FORMAT_24H,
	STR_MAINMENU_TRIGSET,
	STR_MAINMENU_IP,
	STR_MAINMENU_TRIGSET_TXT1,
	STR_MAINMENU_DISPSETTING,
	STR_MAINMENU_WAVESET_HIGH_SPEED,
	STR_MAINMENU_WAVESET_MID_SPEED,
	STR_MAINMENU_WAVESET_LOW_SPEED,
	STR_MAINMENU_LOOPSET,
	STR_MAINMENU_VALUE,
	STR_MAINMENU_TRENDSET,
	STR_MAINMENU_TRENDSET_TXT4,
	STR_MAINMENU_SYSINFO,
	STR_MAINMENU_VER,
	STR_MAINMENU_VER_TITLE1,
	STR_MAINMENU_VER_TITLE1_TXT1,
	STR_MAINMENU_VER_TITLE2,
	STR_MAINMENU_VER_TITLE3,
	STR_MAINMENU_CONFIG,
	STR_MAINMENU_CONFIG_TXT1,
	STR_MAINMENU_SELFTEST,
	STR_MAINMENU_SELFTEST_TXT1,
	STR_MAINMENU_SELFTEST_TXT2,
	STR_MAINMENU_SELFTEST_TXT3,
	STR_MAINMENU_SELFTEST_TXT4,
	STR_MAINMENU_SELFTEST_TXT5,
	STR_MAINMENU_SELFTEST_TXT6,
	STR_MAINMENU_SELFTEST_TXT7,
	STR_MAINMENU_SELFTEST_TXT8,
	STR_MAINMENU_SELFTEST_TXT9,
	STR_MAINMENU_SELFTEST_TXT10,
	STR_FINISH,
	STR_MAINMENU_EGRSETTING,
	STR_EGNEER_SETTING,
	STR_OPER_LOG,
	STR_EG_CAL,
	STR_PRODUCT_CFG,
	STR_FLOW_CAL,
	STR_PRESS_CAL,
	STR_STANDBY,
	STR_STANDBY_STRING1,
	STR_ALARMSET_TIME,
	STR_ALARMSET_ALARMITEM,
	STR_ALARMSET_PRIORITY,
	STR_ALARMSET_LOW,
	STR_ALARMSET_HIGH,
	STR_ALARMSET,
	STR_ALARMSET_ALARMLIMIT,
	STR_ALARMSET_ALARMLOG,
	STR_ALARMINFO_HIGH_FIO2,
	STR_ALARMINFO_LOW_FIO2,
	STR_ALARMINFO_HIGH_PR,
	STR_ALARMINFO_LOW_PR,
	STR_ALARMINFO_HIGH_MV,
	STR_ALARMINFO_LOW_MV,
	STR_ALARMINFO_HIGH_RATE,
	STR_ALARMINFO_LOW_RATE,
	STR_ALARMINFO_HIGH_VTE,
	STR_ALARMINFO_LOW_VTE,
	STR_ALARMINFO_LOW_PAW,
	STR_ALARMINFO_HIGH_PAW,
	STR_PRESSURE_REACH_LIMIT,
	STR_ALARMINFO_HIGH_FIN2O,
	STR_ALARMINFO_LOW_FIN2O,
	STR_ALARMINFO_HIGH_ETN2O,
	STR_ALARMINFO_LOW_ETN2O,
	STR_ALARMINFO_HIGH_FICO2,
	STR_ALARMINFO_LOW_FICO2,
	STR_ALARMINFO_HIGH_ETCO2,
	STR_ALARMINFO_LOW_ETCO2,
	STR_ALARMINFO_HIGH_SPO2,
	STR_ALARMINFO_LOW_SPO2,
	STR_ALARMINFO_HIGH_FIHAL,
	STR_ALARMINFO_LOW_FIHAL,
	STR_ALARMINFO_HIGH_FIENF,
	STR_ALARMINFO_LOW_FIENF,
	STR_ALARMINFO_HIGH_FISEV,
	STR_ALARMINFO_LOW_FISEV,
	STR_ALARMINFO_HIGH_FIISO,
	STR_ALARMINFO_LOW_FIISO,
	STR_ALARMINFO_HIGH_FIDES,
	STR_ALARMINFO_LOW_FIDES,
	STR_ALARMINFO_HIGH_ETHAL,
	STR_ALARMINFO_LOW_ETHAL,
	STR_ALARMINFO_HIGH_ETENF,
	STR_ALARMINFO_LOW_ETENF,
	STR_ALARMINFO_HIGH_ETSEV,
	STR_ALARMINFO_LOW_ETSEV,
	STR_ALARMINFO_HIGH_ETISO,
	STR_ALARMINFO_LOW_ETISO,
	STR_ALARMINFO_HIGH_ETDES,
	STR_ALARMINFO_LOW_ETDES,
	STR_ALARMINFO_O2_FLOW_HIGH,
	STR_ALARMINFO_N2O_FLOW_HIGH,
	STR_ALARMINFO_AIR_FLOW_HIGH,
	STR_ALARMINFO_FLOW_HIGH,
	STR_ALARMINFO_O2_N2O_UNUSUAL_RATIO,
	STR_TECHALARM_BATTERYLOW,
	STR_TECHALARM_BATTERYEMPTY,
	STR_TECHALARM_PLUGOUT,
	STR_TECHALARM_POWERERR,
	STR_TECHALARM_ACGO,
	STR_ALARMINFO_GASHIGH,
	STR_ALARMINFO_GASLOW,
	STR_TECHALARM_GASLOOPERR,
	STR_TECHALARM_GASSOURCELOW,
	STR_TECHALARM_O2_GASSOURCELOW,
	STR_ALARM_NEGPRESS,
	STR_TECHALARM_ADDO2TIMEOUT,
	STR_TECHALARM_ADDO2ERR,
	STR_TECHALARM_BYPASSERR,
	STR_TECHALARM_APNEAALARM,
	STR_TECHALARM_APNEA_120_ALARM,
	STR_FLOW_SENSOR_ERR_ALARM,
	STR_TECHALARM_FAN_STOP,
	STR_TECHALARM_O2_SENSOR_ERR,
	STR_TECHALARM_MINOTORING_MODULE_ERR,
	STR_TECHALARM_PRESSURE_SENSOR_ERR,
	STR_TECHALARM_EXP_VALVE_ERR,
	STR_TECHALARM_EXP_VALVE_CLOSE_OFF,
	STR_TECHALARM_INSP_VALVE_ERR,
	STR_TECHALARM_HEATING_MODULE_ERR,
	STR_TECHALARM_AG_ADAPTOR_ERR,
	STR_TECHALARM_TIME_UP,
	STR_TECHALARM_SPO2_BREAKOFF,
	STR_TECHALARM_AX_OUT_OF_RANGE,
	STR_TECHALARM_CO2_OUT_OF_RANGE,
	STR_TECHALARM_N2O_OUT_OF_RANGE,
	STR_TECHALARM_TMP_OUT_OF_RANGE,
	STR_TECHALARM_PRESS_OUT_OF_RANGE,
	STR_TECHALARM_AG_NEED_RECAIL,
	STR_TECHALARM_AG_AGENT_MIX,
	STR_TECHALARM_AG_UNSPECIFIED_ACCURACY,
	STR_TECHALARM_AG_SW_ERR,
	STR_TECHALARM_AG_HW_ERR,
	STR_TECHALARM_MOTO_ERR,
	STR_TECHALARM_AG_LOST_CAIL,
	STR_TECHALARM_AG_REPLACE,
	STR_TIP_MANUAL,
	STR_HIGH,
	STR_MID,
	STR_LOW,
	STR_TIMER1,
	STR_TIMER2,
	STR_TIMER3,
	STR_OPEN,
	STR_CLOSE,
	STR_BIO_ALARM_CLOSE,
	STR_PARAM_LIMITED,
	STR_SYSTEM_SELFTEST,
	STR_RETRY,
	STR_IGNORE,
	STR_SELFTEST_MONITORING,
	STR_SELFTEST_AC,
	STR_SELFTEST_BATTERY,
	STR_SELFTEST_SYS_TIME,
	STR_SELFTEST_FLOWMETER,
	STR_SELFTEST_AG_CO2,
	STR_SELFTEST_O2,
	STR_SELFTEST_SPO2,
	STR_SELFTEST_TOUCHSCREEN,
	STR_SELFTEST_GAS_SOURCE,
	STR_SELFTEST_DATABASE,
	STR_SELFTEST_EXHALATION_VALUE,
	STR_SELFTEST_INHALATION_VALUE,
	STR_FLOWMETER_STOP,
	STR_MONIT_SENSOR_CAL_FAIL,
	STR_MONITOR_ACCURACY_LOW,
	STR_O2_FM_SENSOR_ERROR,
	STR_N2O_FM_SENSOR_ERROR,
	STR_AIR_FM_SENSOR_ERROR,
	STR_FM_HARDWARE_ERROR,
	STR_SPO2_SENSOR_UNUNITED,
	STR_AG_ERROR,
	STR_O2_CONCENTRATION_ERROR,
	STR_TOUCH_SCREEN_HOLDBACK,
	STR_SYS_TIME_ERR,
	STR_BREATH_LEAK_HIGH,
	STR_BATTERYERR,
	STR_RESULT,
	STR_PASS,
	STR_OPER_EVENT,
	STR_DETAIL_INFO,
	STR_SYS_START,
	STR_RUNMODE_CHAGNE,
	STR_VENTMODE_CHAGNE,
	STR_VENT_PARAM_CHANGE,
	STR_HIGH_ALARM_LIMIT_CHANGE,
	STR_LOW_ALARM_LIMIT_CHANGE,
	STR_HLM_PARAM_CHANGE,
	STR_PRODUCT_MODE,
	STR_SN,
	STR_P_DATE,
	STR_S_VERION,
	STR_SKIP,
	STR_CAL_START,
	STR_CAL_IN_FLOW_PROC,
	STR_CAL_FLOW_FAIL,
	STR_CAL_FLOW_DONE,
	STR_CAL_IN_PEEP_PROC,
	STR_CAL_PEEP_DONE,
	STR_CAL_PEEP_FAIL,
	STR_EXPRT_DATA,
	STR_READ_CAL,
	STR_EXPRT_START,
	STR_FLOWMETER_CAL,
	STR_FLOWMETER_CAL_START,
	STR_FM_CAL_DATA,
	STR_FM_CAL_FAIL,
	STR_FM_CAL_DONE_NEXT,
	STR_FM_CAL_FINISH,
	STR_FM_CAL_WAIT,
	STR_FM_CAL_SUCCEED,
	STR_IPADDR,
	STR_NETMASK,
	STR_GATEWAY,
	STR_MACADDR,
	STR_CONFIRM_NET,
	STR_DOT,
	STR_KEYCODE,
	STR_FIRMUPDATE,
	STR_SOFT_MODE,
	STR_SOFT_MODE_SWITCHED_TIP,
	STR_SOFT_DEMO_MODE,
	STR_SOFT_RUN_MODE,
	STR_CALI,
	STR_CALI_PROC,
	STR_SOFT_CFG_TITLE,
	STR_SOFT_CFG_INPUT_TIP,
	STR_SOFT_CFG_FAIL,
	STR_SOFT_CFG_SUCCEED,
	STR_MAINMENU_EQUIPSET_LCD,
	STR_O2_FLOW_INEXACT,
	STR_BANLANCE_FLOW_INEXACT,
	STR_FLOW_TOO_LOW,
	STR_O2_PRESS_LOW,
	STR_N2O_PRESS_LOW,
	STR_AIR_PRESS_LOW,
	STR_FLOWMETER_O2_SENSOR_ERROR,
	STR_FLOWMETER_N2O_SENSOR_ERROR,
	STR_FLOWMETER_AIR_SENSOR_ERROR,
	STR_BACKUP_FLOW_CONTROL_OPEN,
	STR_HISTORY_DATA,
	STR_SETTINGS,
	STR_FREEZING,
	STR_ALARM_MUTE,
	STR_LOOPINFO,
	STR_LATEST_SELFTEST,
	STR_PRE_STEP,
	STR_NEXT_STEP,
	STR_ACCOMPLISH,
	STR_STOP,
	STR_CONTINUE,
	STR_SELFTEST_SKIP_TIP,
	STR_MANUAL_LEAK_CHECK,
	STR_MANUAL_LEAK_CHECK_TIP,
	STR_GAS_CIRCUIT_CHECK,
	STR_GAS_CIRCUIT_CHECK_TIP,
	STR_FLOWMETER_CHECK,
	STR_FLOWMETER_CHECK_TIP,
	STR_ANESTHETIC_GAS_EVAPORATOR_CHECK,
	STR_ANESTHETIC_GAS_EVAPORATOR_CHECK_TIP,
	STR_TIMER,
	STR_COUNTDOWN,
	STR_VENTILATOR_ALARM_LIMIT,
	STR_ANESTHETIC_GAS_ALARM_LIMIT,
	STR_CUR_ALARM,
	STR_FOUR_WAVE,
	STR_THREE_WAVE,
	STR_LOOP,
	STR_FLOW_CONTROL_MENU,
	STR_QUICK_SET,
	STR_CONTROL_MODE,
	STR_TOTAL_FLOW_CONTROL,
	STR_SINGLE_PIPE_FLOW_CONTROL,
	STR_BALANCE_FLOW,
	STR_TREND_GRAPH,
	STR_MINUTE,
	STR_PRE_EVENT,
	STR_NEXT_EVENT,
	STR_TREND_TABLE,
	STR_ALL_PARAMS,
	STR_PRESSURE_PARAMS,
	STR_VOLUME_PARAMS,
	STR_TIME_PARAMS,
	STR_FLOWMETER_PARAMS,
	STR_AG_PARAMS,
	STR_LOOP_DETAIL,
	STR_SAVE_LOOP,
	STR_DELETE_LOOP,
	STR_SWITCH_REFER_LOOP,
	STR_COMPARE_LOOP,
	STR_LOOP_VF,
	STR_LOOP_PV,
	STR_LOOP_FP,
	STR_CONFIRM_REF_LOOP_TIP,
	STR_CONFIRM_DELETE_LOOP_TIP,
	STR_CONFIRM_COMPARE_LOOP_TIP,
	STR_HISTORY_EVENT,
	STR_DATE,
	STR_ALL_EVENT,
	STR_HIGH_LEVEL_ALARM,
	STR_MID_LEVEL_ALARM,
	STR_LOW_LEVEL_ALARM,
	STR_PROMPT,
	STR_OPERATE,
	STR_SUCCESS,
	STR_VOL_BRIGHT,
	STR_UI_SETTING,
	STR_FACTORY_MAINTENANCE,
	STR_LANGUAGE,
	STR_LANGUAGE_RUS,
	STR_LANGUAGE_CHN,
	STR_LANGUAGE_ENG,
	STR_LANGUAGE_UKR,
	STR_LANGUAGE_FRE,
	STR_PROTOCAL,
	STR_PROTOCAL_SETTINGS,
	STR_SERIAL_PROTOCAL,
	STR_NONE,
	STR_BAUD_RATE,
	STR_DATA_BITS,
	STR_VERIFY,
	STR_ODD,
	STR_EVEN,
	STR_STOP_BIT,
	STR_NETWORK_PROTOCAL,
	STR_SUBNET_MASK,
	STR_LINK_STATE,
	STR_CONNECTED,
	STR_DISCONNECTED,
	STR_SEND_INTERVAL,
	STR_TARGET_IP,
	STR_TARGET_PORT,
	STR_CONFIRM_CONNECT_TIP,
	STR_CONFIRM_DISCONNECT_TIP,
	STR_SYSTEM_INFO,
	STR_CONFIG_INFO,
	STR_CONFIGURED,
	STR_PLIMIT_LINE,
	STR_SHOW,
	STR_HIDE,
	STR_DRAW_CURVE,
	STR_DRAW_LINE,
	STR_FILL_AREA,
	STR_SWEEP_SPEED,
	STR_FLOWMETER_ZERO_CAL,
	STR_START_VENTILATION,
	STR_RETURN,
	STR_SWITCH_BASE_LOOP,
	STR_ONLY_ONE_BATTER,
	STR_DB_FAIL,
	STR_AG_WARMING,
	STR_CO2_WARMING,
	STR_TIMER_MAX,
	STR_COUNTDOWN_END,
	STR_CALIB_USE_ONLY_STANBY,
	STR_ENTER_MAINTENANCE,
	STR_MAINTENANCE_USE_ONLY_STANBY,
	STR_P_SETTINGS,
	STR_RESTORE_FACTORY_SETTING,
	STR_BREAKDOWN,
	STR_NORMAL,
	STR_END_DELETE,
	STR_END_SWITCH,
	STR_ZERO_CAL_TIP,
	STR_CHANGE_VENT_MODE,
	STR_OUT_OF_UPPER_LIMIT,
	STR_OUT_OF_LOWER_LIMIT,
	STR_FORMAT_ERROR,
	STR_INVALID_VALUE,
	STR_O2_CAL_CANCEL,
	STR_HOTKEY_STANDBY,
	STR_SHUTDOWN_DELAY,
	STR_EXTERNAL_MODULE,
	STR_BREATHING_MECHANICS_PARAM,
	STR_VENTILATORY_FUNCTION_PARAM,
	STR_CHANGE_PASSWORD,
	STR_ENTER_NEW_PASSWORD,
	STR_NEW_PASSWORD_CONFIRM,
	STR_CONFIRM_CHANGE,
	STR_PASSWORD_TIP,
	STR_PASSWORD_INPUT_FAULT_TIP,
	STR_PASSWORD_CHANGE_TIP,
	STR_MAIN_CONTROL_BOARD,
	STR_MONITOR_BOARD,
	STR_U_BOOT,
	STR_LINUX_FILE_SYSTEM,
	STR_LINUX_KERNEL,
	STR_FLOWMETER_BOARD,
	STR_POWER_BOARD,
	STR_VT_650,
	STR_VT_PLUS,
	STR_VT_MINI,
	STR_VT_MODEL_CHOICE,
	STR_AUDITION,
	STR_ENTER_PASSWORD,
	STR_TOUCH_PAD,
	STR_TOUCH_SCREEN,
	STR_FLOW_TOO_LOW_PROMPT,
	STR_CHANGE,
	STR_LOG_ENTER_VENT_MODE,
	STR_LOG_SHUTDOWN_DELAY,
	STR_LOG_CANCEL_SHUTDOWN_DELAY,
	STR_VENT,
	STR_ALARM_LIMIT_RESTORE_DEFAULT,
	STR_CHANGE_CONTROL_MODE,
	STR_CHANGE_BALANCE_MODE,
	STR_CHANGE_FLOW_VALUE,
	STR_UPGRADE,
	STR_CENAR_30M,
	STR_SELFTEST_AG,
	STR_SELFTEST_CO2,
	STR_CHANGE_VENT_MODE_PARAM,
	STR_CHANGE_SYSTEM_DATE,
	STR_CHANGE_SYSTEM_TIME,
	STR_PASSWORD_ERROR_TIP,
	STR_PASSWORD_DONOT_MATCH,
	STR_SWITCH_PAW_UNIT,
	STR_SWITCH_CO2_UNIT,
	STR_TIME_AM,
	STR_TIME_PM,
	STR_VENTILATOR_LIMIT_RESTORE_DEFAULT,
	STR_ANESTHETIC_GAS_LIMIT_RESTORE_DEFAULT,
	STR_TIMER_LIMIT,
	STR_INPUT_ALARM_LIMIT,
	STR_CFG_CODE,
	STR_PV_CAL,
	STR_SHUTDOWN_TIME,
	STR_SHUTDOWN_TIP,
	STR_NOT_CONFIG,
	STR_IN,
	STR_OUT,
	STR_CO2_ERROR,
	STR_ADD_FLOW,
	STR_REDUCE_FLOW,
	STR_SEND,
	STR_RESTORE_TIP,
	STR_FLOW_TIP,
	STR_AG_ZEROING_TIP,
	STR_MENUAL_ACGO_STATE_TIP,
	STR_O2_FM_SENSOR_COMMUNICATION_STOP,
	STR_BALANCE_GAS_FM_SENSOR_ERROR,
	STR_BACKUP_FLOW_CONTROL_COMMUNICATION_STOP,
	STR_TECHALARM_CO2_ADAPTOR_ERR,
	STR_FLOWMETER_CHECK_TIP_FOR_80,
	STR_MENUAL_ACGO_LEAK_STATE_TIP,
	STR_FLOWMETER_CHECK_TIP_FOR_C30M,
	STR_AG_ZERO_CAL,
	STR_CO2_ZERO_CAL,
	STR_CO2_ZEROING_TIP,
	STR_OVER,
	STR_ALARM_HIGH_LIMIT,
	STR_ALARM_LOW_LIMIT,
	STR_DIRECT_FLOW,
	STR_EMPTY_INPUT_TIP,
	STR_INVALID_INPUT_TIP,
	STR_SERIAL_C,
	STR_SERIAL_E,
	STR_21PO2CAL,
	STR_100PO2CAL,
	STR_WAVEFORMS_SETTINGS,
	STR_SHUT_DOWN,
	STR_PREOPERATIVE_CHECK,
	STR_CO2_CANNOT_ZERO_TIP,
	STR_FLOWMETER_GAS_CONFIG,
	STR_FLOWMETER_SENSOR_TYPE,
	STR_SOFTWARE_UPDATE,
	STR_UPDATE_SWITCH,
	STR_UPDATE_TYPE,
	STR_SRC_VERSION,
	STR_TARGET_VERSION,
	STR_PROGRESS,
	STR_UPDATE_FINISH_TIP,
	STR_RESTART_NOW,
	STR_UPDATE_COM_ERROR_TIP,
	STR_UPDATE_CRC_ERROR_TIP,
	STR_MACHINE_CONFIGURING,
	STR_DEVICE_TREE,
	STR_CUSTOM_UPDATE,
	STR_LOGO_PNG,
	STR_LOGO_BIN,
	STR_DAEMON_VERSION,
	STR_UPDATE_CONFIG,
	STR_UPDATE_PRODUCT_MODEL,
	STR_UPDATE_CUSTOM_STEP,
	STR_UPDATE_LOGO,
	STR_UPDATE_LOGO_BIN,
	STR_AG_MODULE_TYPE,
	STR_MAIN_STREAM,
	STR_SIDE_STREAM,
	STR_TECHALARM_AG_SAMPLING_ERR,
	STR_TECHALARM_CO2_SAMPLING_ERR,
	STR_TECHALARM_CO2_REPLACE,
	STR_TECHALARM_AG_SAMPLING_CLOGGED,
	STR_TECHALARM_CO2_SAMPLING_CLOGGED,
	STR_FLOWMETER_O2_FLOW_TIPS,
	STR_FLOWMETER_O2_CONCENTRATION_TIPS,

};

