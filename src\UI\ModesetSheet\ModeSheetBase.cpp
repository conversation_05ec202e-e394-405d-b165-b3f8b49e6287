﻿#include "ModeSheetBase.h"
#include "UiConfig.h"
#include "KeyConfig.h"
#include "SettingSpinbox/SettingSpinbox.h"
#include "SettingSpinboxManager.h"
#include "String/UIStrings.h"
#include "ChartManager.h"
#include "RunModeManage.h"
#include "DataLimitManager.h"

#define PROMPT_LABEL_WIDTH    100
#define PROMPT_LABEL_HEIGHT   16
#define PROMPT_LABEL_Y        3
#define SURE_BUTTON_SIZE 52,53
#define SWTICH_BUTTON_BLINK_TIME (500)

ModeSheetBase::ModeSheetBase(QWidget *parent) :
    FocusWidget(parent)
{
    mMainLayout= new QHBoxLayout(this);
    mMainLayout->setContentsMargins(1,1,1,1);
    mMainLayout->setSpacing(MODULE_ITEM_SPACING);
    setLayout(mMainLayout);
    VentModeSettingManager::GetInstance()->GetSettingdata(mModeData);
    SettingSpinboxManager::GetInstance()->RegisterModeSheetBase(this);

    mSureButton = new SwitchModelButton(this);
    mSureButton->setFixedSize(SURE_BUTTON_SIZE);
    mSureButton->setVisible(false);

}

void ModeSheetBase::ShowSwitchButton()
{

    mSureButton->move(width() - mSureButton->width() - 10, height()/2.0-mSureButton->height()/2.0);
    mSureButton->setVisible(true);
}

void ModeSheetBase::CloseSwitchButton()
{
    mSureButton->setVisible(false);
}

void ModeSheetBase::InitConnect()
{
    QList<SettingSpinbox *> spinboxs = findChildren<SettingSpinbox *>();
    for (int i = 0; i < spinboxs.count(); ++i)
    {
        connect(spinboxs.at(i), &SettingSpinbox::SignalValueChanged, this, &ModeSheetBase::onConfirmParam);
        connect(spinboxs.at(i), &SettingSpinbox::SignalEditState, this, &ModeSheetBase::onSelectParam);
        connect(spinboxs.at(i), (void(SettingSpinbox::*)())&SettingSpinbox::SignalValueChanging, this, &ModeSheetBase::SignalValueChanging);
        connect(spinboxs.at(i), (void(SettingSpinbox::*)(int))&SettingSpinbox::SignalValueChanging, this, &ModeSheetBase::SlotValueChanging);
    }
    connect(mSureButton, &QPushButton::clicked, this,[=](){
        CloseSwitchButton();
        emit SignalSwitchButtonClick();
    });
}

void ModeSheetBase::RefurbishParam()
{
    VentModeSettingManager::GetInstance()->GetSettingdata(mModeData);

    mModeData.VentMode = mModeId;

    T_MASTER_SET tmpModeData = mModeData;
    if (tmpModeData.Peep < 4)
    {//关联设限时，peep做处理
        tmpModeData.Peep = 0;
    }

    QList<SettingSpinbox *> spinboxs = findChildren<SettingSpinbox *>();

    for (int i = 0; i < spinboxs.count(); ++i)
    {
        short value = VentModeSettingManager::GetInstance()->GetDataFromSet(&tmpModeData, (E_SETTING_DATA_ID)spinboxs.at(i)->GetSettingDataId());
        short defalutMin,defaultMax;
        SettingSpinboxManager::GetInstance()->GetParamInitRange(spinboxs.at(i)->GetSettingDataId(),defalutMin,defaultMax);
        spinboxs.at(i)->SetRange(defalutMin, defaultMax);
        spinboxs.at(i)->setValue(value, mModeId != VentModeSettingManager::GetInstance()->GetCurMode());
    }

//    for (int level = MODESET_LEVEL_0; level < MODESET_LEVEL_MAX; level++)
//    {
//        for (int i = 0; i < spinboxs.count(); ++i)
//        {//0 -> max 顺序设置限制
//            int id = spinboxs.at(i)->GetSettingDataId();
//            ALL_MODESET_SPN_DISCRIPTOR spnDisc = SettingSpinboxManager::GetInstance()->GetModesetSpnDiscriptor(id);
//            if (level == spnDisc.prio_level)
//            {//先设置限，再设值
//                SettingSpinbox* spn =  spinboxs.at(i);
//                DebugAssert(spn);

//                E_SETTING_DATA_ID dataId = (E_SETTING_DATA_ID)spn->GetSettingDataId();
//                short paramMax=0,paramMin=0;
//                short value = VentModeSettingManager::GetInstance()->GetDataFromSet(&mModeData, dataId);
//                RuleManager::GetInstance()->GetParmRangeByPriority(dataId,mModeData,paramMin,paramMax);
//                short oldValue = value;
//                if(value<paramMin)
//                    value=paramMin;
//                if(value>paramMax)
//                    value=paramMax;
//                if(oldValue!=value)
//                {
//                    spinboxs.at(i)->setValue(value, paramMax);
//                    VentModeSettingManager::GetInstance()->SaveToSpecificSet(&mModeData,dataId,value);
//                }
//            }
//        }
//    }

    for (int level = MODESET_LEVEL_MAX; level >= MODESET_LEVEL_0; --level)
    {
        for (int i = 0; i < spinboxs.count(); ++i)
        {//0 -> max 顺序设置限制
            int id = spinboxs.at(i)->GetSettingDataId();
            ALL_MODESET_SPN_DISCRIPTOR spnDisc = SettingSpinboxManager::GetInstance()->GetModesetSpnDiscriptor(id);
            if (level == spnDisc.prio_level)
            {//先设置限，再设值
                setSpnRange(spinboxs.at(i));
            }
        }
    }

    UpModeDataForUi();
}

void ModeSheetBase::setSpnRange(SettingSpinbox *spn)
{
    DebugAssert(spn);

    UpModeDataForUi();
    int maxRule = RuleManager::NONE_RULE;
    int minRule = RuleManager::NONE_RULE;
    E_SETTING_DATA_ID dataId = (E_SETTING_DATA_ID)spn->GetSettingDataId();
    short paramMax,paramMin;
    if (RuleManager::GetInstance()->GetParamMaxMin(dataId, mModeData, paramMin,paramMax,minRule, maxRule))
    {//关联受限
        if(dataId == SETTINGDATA_TINSP) //与flow成反比
        {
            UpOutMaxErrorTip(dataId,maxRule,false);
            UpOutMinErrorTip(dataId,minRule,false);
        }
        else
        {
            UpOutMaxErrorTip(dataId,maxRule);
            UpOutMinErrorTip(dataId,minRule);
        }

        spn->SetRange(paramMin, paramMax);
    }
    else
    {
        DebugLog<<"error cacal reange for paramId"<<dataId;
        //DebugAssert(0); TBD 暂时屏蔽崩溃
    }
}

void ModeSheetBase::UpModeDataForUi()
{
    QList<SettingSpinbox *> spinboxs = findChildren<SettingSpinbox *>();
    for (int i = 0; i < spinboxs.count(); ++i)
    {
        int param_data_id = (E_SETTING_DATA_ID)spinboxs.at(i)->GetSettingDataId();
        switch (param_data_id)
        {
        case SETTINGDATA_VENTMODE:
            mModeData.VentMode = mModeId;
            break;
        case SETTINGDATA_VT:
            mModeData.VT = spinboxs.at(i)->value();
            break;
        case SETTINGDATA_RATE:
            mModeData.Rate = spinboxs.at(i)->value();
            break;
        case SETTINGDATA_IE:
            mModeData.IE = spinboxs.at(i)->value();
            break;
        case SETTINGDATA_TITP:
            mModeData.TiTp = spinboxs.at(i)->value();
            break;
        case SETTINGDATA_PEEP:
            mModeData.Peep = spinboxs.at(i)->value();
            break;
        case SETTINGDATA_PLIMIT:
            mModeData.Plimit = spinboxs.at(i)->value();
            break;
        case SETTINGDATA_PSUPP:
            mModeData.Psupp = spinboxs.at(i)->value();
            break;
        case SETTINGDATA_SLOPE:
            mModeData.Slope= spinboxs.at(i)->value();
            break;
        case SETTINGDATA_TINSP:
            mModeData.Tinsp= spinboxs.at(i)->value();
            break;
        case SETTINGDATA_PINSP:
            mModeData.Pinsp= spinboxs.at(i)->value();
            break;
        default:
            break;
        }
    }
    if (mModeData.Peep < 4)
    {//关联设限时，peep做处理
        mModeData.Peep = 0;
    }
}

void ModeSheetBase::UpOutMaxErrorTip(unsigned short paramId, unsigned short ruleType, bool opposite)
{
    QList<SettingSpinbox *> spinboxs = findChildren<SettingSpinbox *>();
    SettingSpinbox *spn = nullptr;
    foreach(auto tempSpn, spinboxs)
    {
        if(tempSpn->GetSettingDataId() == paramId)
        {
            spn = tempSpn;
            break;
        }
    }
    if(spn == nullptr) return;
    QString str = UIStrings::GetStr(STR_OUT_OF_UPPER_LIMIT);
    auto convertFunc = UnitManager::GetInstance()->ConvertFunctor(PRESSURE_CATEGORY, U_CMH2O,
                                                                  UnitManager::GetInstance()->GetCurPressureUnitType());
    switch (ruleType)
    {
    case RuleManager::RULE_TYPE::RULE_3:
    {
        str += QString("(PEEP+%1>Plimit)").arg(convertFunc(5));
    }
        break;
    case RuleManager::RULE_TYPE::RULE_5:
    {
        str += QString("(PEEP>Pinsp-%1)").arg(convertFunc(5));
    }
        break;
    case RuleManager::RULE_TYPE::RULE_12:
    {
        str += QString("(Pinsp>Plimit-%1)").arg(convertFunc(5));
    }
        break;
    case RuleManager::RULE_TYPE::RULE_11:
    {
        str += "(Slope>Tinsp)";
    }
        break;
    case RuleManager::RULE_TYPE::RULE_9:
    {
        str += "(Slope>Tinsp)";
    }
        break;
    case RuleManager::RULE_TYPE::RULE_7:
    {
        if(opposite)
            str += "(Flow>100" + UIStrings::GetStr(STR_UNIT_LPERMIN) + ")";
        else
            str += "(Flow<1.35" + UIStrings::GetStr(STR_UNIT_LPERMIN) + ")";
    }
        break;
    case RuleManager::RULE_TYPE::RULE_8:
    {
        if(opposite)
            str += "(Flow>100" + UIStrings::GetStr(STR_UNIT_LPERMIN) + ")";
        else
            str += "(Flow<1.35" + UIStrings::GetStr(STR_UNIT_LPERMIN) + ")";
    }
        break;
    case RuleManager::RULE_TYPE::RULE_6:
    {
        str += "(Texp<0.5" + UIStrings::GetStr(STR_UNIT_S) + ")";
    }
        break;
    case RuleManager::RULE_TYPE::RULE_10:
    {
        str += "(Texp<0.5" + UIStrings::GetStr(STR_UNIT_S) + ")";
    }
        break;
    default:
        break;
    }
    spn->SetErrorValueTipStr(IntraData::OUT_MAX,str);
    spn = nullptr;
    delete spn;
}

void ModeSheetBase::UpOutMinErrorTip(unsigned short paramId, unsigned short ruleType, bool opposite)
{
    QList<SettingSpinbox *> spinboxs = findChildren<SettingSpinbox *>();
    SettingSpinbox *spn = nullptr;
    foreach(auto tempSpn, spinboxs)
    {
        if(tempSpn->GetSettingDataId() == paramId)
        {
            spn = tempSpn;
            break;
        }
    }
    if(spn == nullptr) return;
    auto convertFunc = UnitManager::GetInstance()->ConvertFunctor(PRESSURE_CATEGORY, U_CMH2O,
                                                                  UnitManager::GetInstance()->GetCurPressureUnitType());
    QString str = UIStrings::GetStr(STR_OUT_OF_LOWER_LIMIT);
    switch (ruleType)
    {
    case RuleManager::RULE_TYPE::RULE_3:
    {
        str += QString("(Plimit<PEEP+%1)").arg(convertFunc(5));
    }
        break;
    case RuleManager::RULE_TYPE::RULE_5:
    {
        str += QString("(Pinsp<PEEP+%1)").arg(convertFunc(5));
    }
        break;
    case RuleManager::RULE_TYPE::RULE_12:
    {
        str += QString("(Plimit<Pinsp+%1)").arg(convertFunc(5));
    }
        break;
    case RuleManager::RULE_TYPE::RULE_11:
    {
        str += "(Tinsp<Slope)";
    }
        break;
    case RuleManager::RULE_TYPE::RULE_7:
    {
        if(opposite)
            str += "(Flow<1.35" + UIStrings::GetStr(STR_UNIT_LPERMIN) + ")";
        else
            str += "(Flow>100" + UIStrings::GetStr(STR_UNIT_LPERMIN) + ")";
    }
        break;
    case RuleManager::RULE_TYPE::RULE_8:
    {
        if(opposite)
            str += "(Flow<1.35" + UIStrings::GetStr(STR_UNIT_LPERMIN) + ")";
        else
            str += "(Flow>100" + UIStrings::GetStr(STR_UNIT_LPERMIN) + ")";
    }
        break;
    case RuleManager::RULE_TYPE::RULE_6:
    {
        str += "(Texp<0.5" + UIStrings::GetStr(STR_UNIT_S) + ")";
    }
        break;
    default:
        break;
    }
    spn->SetErrorValueTipStr(IntraData::OUT_MIN,str);
    spn = nullptr;
    delete spn;
}

QString ModeSheetBase::UpRangeTip(unsigned short spnType, int value)
{
    QString reStr = "";
    switch (spnType)
    {
    case SETTINGDATA_VT:
    {
        int VtHighLimit = DataLimitManager::GetInstance()->GetHighLimit(MONITOR_PARAM_TYPE::VALUEDATA_VTE);
        int VtLowLimit  = DataLimitManager::GetInstance()->GetLowLimit(MONITOR_PARAM_TYPE::VALUEDATA_VTE);

        if (value > VtHighLimit && VtHighLimit != INVALID_VALUE)
        {
            reStr = UIStrings::GetStr(STR_OVER) + " " + UIStrings::GetStr(STR_VALUEPARAM_VTE) + " " + UIStrings::GetStr(STR_ALARM_HIGH_LIMIT);
        }
        else if (value < VtLowLimit && VtLowLimit != INVALID_VALUE)
        {
            reStr = UIStrings::GetStr(STR_OVER) + " " + UIStrings::GetStr(STR_VALUEPARAM_VTE) + " " + UIStrings::GetStr(STR_ALARM_LOW_LIMIT);
        }
    }
        break;
    case SETTINGDATA_RATE:
    {
        int RateHighLimit = DataLimitManager::GetInstance()->GetHighLimit(MONITOR_PARAM_TYPE::VALUEDATA_RATE);
        int RateLowLimit  = DataLimitManager::GetInstance()->GetLowLimit(MONITOR_PARAM_TYPE::VALUEDATA_RATE);

        if (value > RateHighLimit && RateHighLimit != INVALID_VALUE)
        {
            reStr = UIStrings::GetStr(STR_OVER) + " " + UIStrings::GetStr(STR_VALUEPARAM_RATE) + " " + UIStrings::GetStr(STR_ALARM_HIGH_LIMIT);
        }
        else if (value < RateLowLimit && RateLowLimit != INVALID_VALUE)
        {
            reStr = UIStrings::GetStr(STR_OVER) + " " + UIStrings::GetStr(STR_VALUEPARAM_RATE) + " " + UIStrings::GetStr(STR_ALARM_LOW_LIMIT);
        }
    }
        break;
    case SETTINGDATA_IE:
    {
        if (value < 100)
        {
            reStr = "I:E>1:1";
        }
    }
        break;
    case SETTINGDATA_TITP:
    case SETTINGDATA_TINSP:
    case SETTINGDATA_SLOPE:
        break;
    case SETTINGDATA_PINSP:
    {
        int pawHighLimit = DataLimitManager::GetInstance()->GetHighLimit(MONITOR_PARAM_TYPE::VALUEDATA_PPEAK);
        int pawLowLimit = DataLimitManager::GetInstance()->GetLowLimit(MONITOR_PARAM_TYPE::VALUEDATA_PPEAK);

        if (value > pawHighLimit)
        {
            reStr = UIStrings::GetStr(STR_OVER) + " " + UIStrings::GetStr(STR_SETTINGLIMIT_PAW) + " " + UIStrings::GetStr(STR_ALARM_HIGH_LIMIT);
        }
        else if (value < pawLowLimit && pawLowLimit != INVALID_VALUE)
        {
            reStr = UIStrings::GetStr(STR_OVER) + " " + UIStrings::GetStr(STR_SETTINGLIMIT_PAW) + " " + UIStrings::GetStr(STR_ALARM_LOW_LIMIT);
        }
    }
        break;
    case SETTINGDATA_PLIMIT:
    {
        int pawHighLimit = DataLimitManager::GetInstance()->GetHighLimit(MONITOR_PARAM_TYPE::VALUEDATA_PPEAK);
        int pawLowLimit = DataLimitManager::GetInstance()->GetLowLimit(MONITOR_PARAM_TYPE::VALUEDATA_PPEAK);

        if (value > pawHighLimit)
        {
            reStr = UIStrings::GetStr(STR_OVER) + " " + UIStrings::GetStr(STR_SETTINGLIMIT_PAW) + " " + UIStrings::GetStr(STR_ALARM_HIGH_LIMIT);
        }
        else if(value < pawLowLimit && pawLowLimit != INVALID_VALUE)
        {
            reStr = UIStrings::GetStr(STR_OVER) + " " + UIStrings::GetStr(STR_SETTINGLIMIT_PAW) + " " + UIStrings::GetStr(STR_ALARM_LOW_LIMIT);
        }
    }
        break;
    case SETTINGDATA_PEEP:
    {
        int pawHighLimit = DataLimitManager::GetInstance()->GetHighLimit(MONITOR_PARAM_TYPE::VALUEDATA_PPEAK);
        int pawLowLimit = DataLimitManager::GetInstance()->GetLowLimit(MONITOR_PARAM_TYPE::VALUEDATA_PPEAK);
        int psuppValue  = VentModeSettingManager::GetInstance()->GetSettingdata(E_SETTING_DATA_ID::SETTINGDATA_PSUPP);

        if(value == 3) break; //OFF
        if(mModeId == VENTMODE_FLAG_VCVSIMV || mModeId == VENTMODE_FLAG_PCVSIMV
                || mModeId == VENTMODE_FLAG_PSV)
        {
            if(psuppValue == 4)
                psuppValue = 0; //OFF
            if(value + psuppValue > pawHighLimit)
            {
                reStr = "Δ" + UIStrings::GetStr(STR_SETTINGPARAM_PSUPP) + "+" + UIStrings::GetStr(STR_SETTINGPARAM_PEEP) + " " + UIStrings::GetStr(STR_OVER) + " " + UIStrings::GetStr(STR_SETTINGLIMIT_PAW) + " " + UIStrings::GetStr(STR_ALARM_HIGH_LIMIT);
            }
            else if(value + psuppValue < pawLowLimit && pawLowLimit != INVALID_VALUE)
            {
                reStr = "Δ" + UIStrings::GetStr(STR_SETTINGPARAM_PSUPP) + "+" + UIStrings::GetStr(STR_SETTINGPARAM_PEEP) + " " + UIStrings::GetStr(STR_OVER) + " " + UIStrings::GetStr(STR_SETTINGLIMIT_PAW) + " " + UIStrings::GetStr(STR_ALARM_LOW_LIMIT);
            }
        }
        else
        {
            if(value > pawHighLimit)
            {
                reStr = UIStrings::GetStr(STR_SETTINGPARAM_PEEP) + " " + UIStrings::GetStr(STR_OVER) + " " + UIStrings::GetStr(STR_SETTINGLIMIT_PAW) + " " + UIStrings::GetStr(STR_ALARM_HIGH_LIMIT);
            }
            else if(value < pawLowLimit && pawLowLimit != INVALID_VALUE)
            {
                reStr = UIStrings::GetStr(STR_SETTINGPARAM_PEEP) + " " + UIStrings::GetStr(STR_OVER) + " " + UIStrings::GetStr(STR_SETTINGLIMIT_PAW) + " " + UIStrings::GetStr(STR_ALARM_LOW_LIMIT);
            }
        }
    }
        break;
    case SETTINGDATA_PSUPP:
    {
        if(value == 4) break; //OFF
        int pawHighLimit = DataLimitManager::GetInstance()->GetHighLimit(MONITOR_PARAM_TYPE::VALUEDATA_PPEAK);
        int pawLowLimit = DataLimitManager::GetInstance()->GetLowLimit(MONITOR_PARAM_TYPE::VALUEDATA_PPEAK);
        int peepValue  = VentModeSettingManager::GetInstance()->GetSettingdata(E_SETTING_DATA_ID::SETTINGDATA_PEEP);

        if(peepValue == 3)
            peepValue = 0; //OFF
        if (value + peepValue > pawHighLimit)
        {
            reStr = "Δ" + UIStrings::GetStr(STR_SETTINGPARAM_PSUPP) + "+" + UIStrings::GetStr(STR_SETTINGPARAM_PEEP) + " " + UIStrings::GetStr(STR_OVER) + " " + UIStrings::GetStr(STR_SETTINGLIMIT_PAW) + " " + UIStrings::GetStr(STR_ALARM_HIGH_LIMIT);
        }
        else if(value + peepValue < pawLowLimit && pawLowLimit != INVALID_VALUE)
        {
            reStr = "Δ" + UIStrings::GetStr(STR_SETTINGPARAM_PSUPP) + "+" + UIStrings::GetStr(STR_SETTINGPARAM_PEEP) + " " + UIStrings::GetStr(STR_OVER) + " " + UIStrings::GetStr(STR_SETTINGLIMIT_PAW) + " " + UIStrings::GetStr(STR_ALARM_LOW_LIMIT);
        }
    }
        break;
    case SETTINGDATA_IN_END_LEVEL: //psv
        break;
    case SETTINGDATA_FLOW_TRIGGERVALUE:
        break;
    case SETTINGDATA_PRESSURE_TRIGGERVALUE:
        break;
    case SETTINGDATA_CPAP:
        break;
    case SETTINGDATA_ASPHY:
        break;
    default:
        break;
    }

    return reStr;
}

QString ModeSheetBase::GetParamStr(unsigned short paramId, int value, int valueDigit)
{
    T_MASTER_SET tempData = mModeData;
    QString paramStr = "";
    switch (paramId)
    {
    case SETTINGDATA_VT:
    {
        tempData.VT = value;
        auto mvValue = VentModeSettingManager::GetInstance()->CalcParam(tempData, CALC_PARAM_MV);
        paramStr += UIStrings::GetStr(STR_VALUEPARAM_MV) +  QString(":%1").arg(mvValue / 1000, 0, 'f', 1) + UIStrings::GetStr(STR_UNIT_LPERMIN) + " ";
        if(mModeId == VENTMODE_FLAG_VCV || mModeId == VENTMODE_FLAG_VCVSIMV)
        {
            auto flowValue = VentModeSettingManager::GetInstance()->CalcParam(tempData, CALC_PARAM_FLOW);
            paramStr += UIStrings::GetStr(STR_GRAPHNAME_FLOW) + QString(":%1").arg(flowValue / 1000, 0, 'f', 2) + UIStrings::GetStr(STR_UNIT_LPERMIN) + " ";
        }
    }
        break;
    case SETTINGDATA_RATE:
    {
        if(mModeId == VENTMODE_FLAG_VCV || mModeId == VENTMODE_FLAG_VCVSIMV || mModeId == VENTMODE_FLAG_PRVC)
        {
            tempData.Rate = value;
            auto mvValue = VentModeSettingManager::GetInstance()->CalcParam(tempData, CALC_PARAM_MV);
            paramStr += UIStrings::GetStr(STR_VALUEPARAM_MV) +  QString(":%1").arg(mvValue / 1000, 0, 'f', 1) + UIStrings::GetStr(STR_UNIT_LPERMIN) + " ";
        }

    }
        break;
    case SETTINGDATA_IE:
    {
        tempData.IE = value;
        auto tinspValue = VentModeSettingManager::GetInstance()->CalcParam(tempData, CALC_PARAM_TINSP);
        paramStr += UIStrings::GetStr(STR_SETTINGPARAM_TINSP) + QString(":%1").arg(tinspValue / 100, 0, 'f', 1) + UIStrings::GetStr(STR_UNIT_S) + " ";
        auto texpValue = VentModeSettingManager::GetInstance()->CalcParam(tempData, CALC_PARAM_TEXP);
        paramStr += "Texp" + QString(":%1").arg(texpValue / 100, 0, 'f', 1) + UIStrings::GetStr(STR_UNIT_S) + " ";
        if(mModeId == VENTMODE_FLAG_VCV)
        {
            auto flowValue = VentModeSettingManager::GetInstance()->CalcParam(tempData, CALC_PARAM_FLOW);
            paramStr += UIStrings::GetStr(STR_GRAPHNAME_FLOW) + QString(":%1").arg(flowValue / 1000, 0, 'f', 2) + UIStrings::GetStr(STR_UNIT_LPERMIN) + " ";
        }
    }
        break;
    case SETTINGDATA_TITP:
    {
        tempData.TiTp = value;
        auto tipValue = VentModeSettingManager::GetInstance()->CalcParam(tempData, CALC_PARAM_TIP);
        paramStr += "Tip" + QString(":%1").arg(tipValue / 1000, 0, 'f', 2) + UIStrings::GetStr(STR_UNIT_S) + " ";
        auto flowValue = VentModeSettingManager::GetInstance()->CalcParam(tempData, CALC_PARAM_FLOW);
        paramStr += UIStrings::GetStr(STR_GRAPHNAME_FLOW) + QString(":%1").arg(flowValue / 1000, 0, 'f', 2) + UIStrings::GetStr(STR_UNIT_LPERMIN) + " ";
    }
        break;
    case SETTINGDATA_PSUPP:
    case SETTINGDATA_PEEP:
    {
        if(mModeId == VENTMODE_FLAG_PCVSIMV || mModeId == VENTMODE_FLAG_VCVSIMV || mModeId == VENTMODE_FLAG_PSV)
        {
            SETTINGDATA_PSUPP == paramId ? tempData.Psupp = value : tempData.Peep = value;
            auto addValue = VentModeSettingManager::GetInstance()->CalcParam(tempData, CALC_PARAM_PSUPP_ADD_PEEP);
            paramStr = "Δ" + UIStrings::GetStr(STR_SETTINGPARAM_PSUPP) + "+" + UIStrings::GetStr(STR_SETTINGPARAM_PEEP)
                    + QString("=%1").arg(addValue / qPow(10, valueDigit), 0, 'f', valueDigit);
        }
    }
        break;
    case SETTINGDATA_TINSP:
    {
        tempData.Tinsp = value;
        auto ieValue = VentModeSettingManager::GetInstance()->CalcParam(tempData, CALC_PARAM_IE);
        short E = (unsigned short )(10 * (float)60 / tempData.Rate - tempData.Tinsp);
        if(E > tempData.Tinsp)
        {
            paramStr += UIStrings::GetStr(STR_SETTINGPARAM_IE) + QString(":1:%1").arg(ieValue) + " ";
        }
        else
            paramStr += UIStrings::GetStr(STR_SETTINGPARAM_IE) + QString(":%1:1").arg(ieValue) + " ";
        auto texpValue = VentModeSettingManager::GetInstance()->CalcParam(tempData, CALC_PARAM_TEXP);
        paramStr += "Texp" + QString(":%1").arg(texpValue / 100, 0, 'f', 1) + UIStrings::GetStr(STR_UNIT_S) + " ";
        if(mModeId == VENTMODE_FLAG_VCVSIMV)
        {
            auto flowValue = VentModeSettingManager::GetInstance()->CalcParam(tempData, CALC_PARAM_FLOW);
            paramStr += UIStrings::GetStr(STR_GRAPHNAME_FLOW) + QString(":%1").arg(flowValue / 1000, 0, 'f', 2) + UIStrings::GetStr(STR_UNIT_LPERMIN) + " ";
        }
    }
        break;
    default:
        break;
    }
    return paramStr;
}


//如果非当前运行模式，只是将值保存，是当前模式则将数据发送
//将值保存到参数关联限制计算处,HLM模式有所不同，要重写
void ModeSheetBase::onConfirmParam(int value)
{
    SettingSpinbox *sendSpinbox = qobject_cast<SettingSpinbox *>(sender());
    E_SETTING_DATA_ID dataId = (E_SETTING_DATA_ID)sendSpinbox->GetSettingDataId();
    if (mModeId == VentModeSettingManager::GetInstance()->GetCurMode())
    {//直接发送
        VentModeSettingManager::GetInstance()->SaveSettingdata(dataId, value);
        VentModeSettingManager::GetInstance()->SendPackage();
    }
    VentModeSettingManager::SaveToSpecificSet(&mModeData, dataId, value);
}

//改变通气模式，下送数据包
void ModeSheetBase::setModeActive(int active)
{
    if (active)
    {
        VentModeSettingManager::GetInstance()->SaveSettingdata(mModeData);
        if (mModeData.Peep < 4)
            VentModeSettingManager::GetInstance()->SaveSettingdata(SETTINGDATA_PEEP, 3);
        VentModeSettingManager::GetInstance()->SendPackage();
    }
}


unsigned short ModeSheetBase::mode()
{
    return mModeId;
}

void ModeSheetBase::InitUiText()
{
    QList<SettingSpinbox *> boxList = findChildren<SettingSpinbox *>();
    for (int i = 0; i < boxList.count(); ++i)
    {
        boxList.at(i)->InitUiText();
    }
}

void ModeSheetBase::ClosePopup()
{
    QList<SettingSpinbox *> boxList = findChildren<SettingSpinbox *>();
    for (int i = 0; i < boxList.count(); ++i)
    {
        if (boxList.at(i)->isEditingState())
        {
            boxList.at(i)->OutEditState(false);
        }
    }
}


T_MASTER_SET ModeSheetBase::getModeData()
{
    return mModeData;
}

void ModeSheetBase::UpTrigMode(int mode)
{
    mModeData.TriggerFlag = mode;
}

//设置控件的限值，控制相应控件的提示信息
void ModeSheetBase::onSelectParam(bool selected)
{
    if(selected)
    {
        SettingSpinbox *sendSpinbox = qobject_cast<SettingSpinbox *>(sender());
        setSpnRange(sendSpinbox);
        SlotValueChanging(sendSpinbox->value());
    }
}

void ModeSheetBase::SlotValueChanging(int value)
{
    SettingSpinbox *sendSpinbox = qobject_cast<SettingSpinbox *>(sender());
    DebugAssert(senderSignalIndex());

    unsigned short data_id = sendSpinbox->GetSettingDataId();

    QString str;
    str += GetParamStr(data_id, value, sendSpinbox->getDigit());

    QString rangeStr = UpRangeTip(data_id, value);
    if(!rangeStr.isEmpty())
    {
        if(str.isEmpty())
        {
            str += rangeStr;
        }
        else
            str += "\n" + rangeStr;
    }
    sendSpinbox->setITimeTipStr(str);

}

void ModeSheetBase::showEvent(QShowEvent *ev)
{
    bool isAutoInit = mIsAutoInitFocusControl;
    FocusWidget::showEvent(ev);
    if(isAutoInit)
        AddFoucusSonWidget(mSureButton);
}
/***************************************************************/
/***************************************************************/
SwitchModelButton::SwitchModelButton(QWidget *parent):QPushButton(parent)
{
    setStyleSheet(GetQssFileStr("SwitchModelButton.qss"));
    setObjectName("SwitchModelButton");
    setProperty("state","normal");
    mblinkTimer.setInterval(SWTICH_BUTTON_BLINK_TIME);
    connect(&mblinkTimer,&QTimer::timeout,this,[=](){
        if(hasFocus())
        {
            if(property("state")=="focus")
                setProperty("state","blink");
            else
                setProperty("state","focus");
            style()->polish(this);
        }
        else if(isVisible())
        {
            if(property("state")=="normal")
                setProperty("state","blink");
            else
                setProperty("state","normal");
            style()->polish(this);
        }
        else
            mblinkTimer.stop();
    });
}

void SwitchModelButton::showEvent(QShowEvent *)
{
    blink();
}

void SwitchModelButton::blink()
{
    setProperty("state","normal");
    mblinkTimer.start();
}

void SwitchModelButton::focusInEvent(QFocusEvent *)
{
    setProperty("state","focus");
    style()->polish(this);
    mblinkTimer.stop();
}

void SwitchModelButton::focusOutEvent(QFocusEvent *)
{
    setProperty("state","normal");
    style()->polish(this);
    mblinkTimer.start();
}

void SwitchModelButton::keyPressEvent(QKeyEvent *ev)
{
    if(ev->key()==KEY_PRESS)
    {
        animateClick();
    }
    ev->ignore();
}
