#ifndef POWER_H
#define POWER_H

#include <QSerialPort>
#include "LowerProxyBase.h"
#include <QMutex>

class Power : public LowerProxyBase
{
public:
    enum BYSTATE
    {
        BY_NONE=-1,
        BYING=1,
        BYNOING,
        BYERROR=0,
    };
public:
    static Power*  GetInstance();
    void CloseSerial();
    ~Power();
    StPowerInof getCurPowerInfo();
    bool SendQueryVersion();
protected:
    void UnpackData(int msgType,QByteArray data);
private:
    explicit Power(QObject *parent =NULL);
    void UnpackVersion(QByteArray data);
private:
    static Power* mObject ;
    StPowerInof mCurPowerInfo{};
    QSerialPort mSerialPort;

    QMutex mMutex;
};

#endif // AGMODULE_H
